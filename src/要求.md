<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-27 09:26:57
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-27 23:39:16
 * @FilePath     : /src/要求.md
 * @Description  : 设计模式演示项目的核心要求
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-27 09:26:57
-->

# 设计模式演示项目 - 核心要求

## 🔒 永远不可改变的两个核心要求

### 1. 三个部分的布局和宽度设置

现代设计模式页面必须与经典设计模式页面保持完全一致的三栏布局宽度：

```scss
.page-content {
  display: grid;
  grid-template-columns: 280px 1fr 800px; // 左侧导航280px，中间自适应，右侧代码800px
  gap: 2rem;
  max-width: none;
  margin: 0 auto;
  padding: 0 50px 50px 50px; // 固定padding
  width: 100%;
  flex: 1;
  overflow: hidden;

  // 响应式断点 - 必须与经典模式完全一致
  @media (max-width: 1800px) {
    grid-template-columns: 260px 1fr 750px;
    gap: 1.8rem;
  }

  @media (max-width: 1600px) {
    grid-template-columns: 260px 1fr 700px;
    gap: 1.8rem;
  }

  @media (max-width: 1400px) {
    grid-template-columns: 260px 1fr 600px;
    gap: 1.5rem;
  }

  @media (max-width: 1200px) {
    grid-template-columns: 260px 1fr;
    gap: 1.5rem;

    .code-panel {
      display: none;
    }
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 1rem 1rem 1rem;
  }
}
```

### 2. 滚动行为设置

整个页面不滚动，三个部分各自独立滚动：

```scss
// 主容器 - 整个页面不滚动
.modern-patterns {
  height: 100vh; // 固定高度，不是 min-height
  overflow: hidden; // 防止整个页面滚动
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

// 左侧导航 - 独立滚动
.pattern-nav {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto; // 可滚动
  display: flex;
  flex-direction: column;
  min-height: 0; // 关键：允许flex收缩
}

.pattern-list {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  overflow-y: auto; // 模式列表可滚动
}

// 中间演示区域 - 独立滚动
.pattern-demo {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; // 关键：允许flex收缩
}

.demo-content {
  padding: 2rem;
  flex: 1;
  overflow-y: auto; // 演示内容可滚动
  min-height: 0; // 关键：允许flex收缩
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

// 右侧代码面板 - 独立滚动
.code-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; // 关键：允许flex收缩
}
```

## ⚠️ 重要说明

### 绝对禁止的操作

1. **禁止修改宽度设置**：

   - 不能将 `800px` 改为其他值（如 `600px`、`700px` 等）
   - 不能修改响应式断点的宽度值
   - 不能修改 padding 设置

2. **禁止修改滚动行为**：
   - 不能将 `height: 100vh` 改为 `min-height: 100vh`
   - 不能移除 `overflow: hidden`
   - 不能移除任何 `min-height: 0` 设置

### 检查清单

在每次修改现代模式页面时，必须检查：

- [ ] 三栏宽度是否为 `280px 1fr 800px`
- [ ] padding 是否为 `0 50px 50px 50px`
- [ ] 主容器是否为 `height: 100vh` + `overflow: hidden`
- [ ] 所有子容器是否有正确的 `min-height: 0` 设置
- [ ] 滚动容器是否有正确的 `overflow-y: auto` 设置

### 违反后果

如果违反这两个核心要求：

1. 页面布局会与经典模式不一致
2. 用户体验会受到严重影响
3. 需要立即回滚并重新修正

## 📝 开发规范

### 添加新设计模式时

1. **只能修改模式相关的内容**：

   - 添加新的组件文件
   - 更新模式列表数据
   - 添加代码示例

2. **绝对不能修改的部分**：
   - `.page-content` 的宽度设置
   - `.modern-patterns` 的滚动设置
   - 任何影响三栏布局的 CSS

### 代码审查要点

每次提交前必须确认：

- 现代模式页面的布局与经典模式页面完全一致
- 整个页面不滚动，三个区域各自独立滚动
- 所有响应式断点的宽度设置正确

---

## 📁 新设计模式的标准实现格式

### 必须包含的文件和内容

参考单例模式的完整实现，每个新的设计模式必须包含以下文件：

#### 1. 🎨 UI 演示组件

**文件位置**:

- 经典模式: `src/components/patterns/[PatternName]Demo.vue`
- 现代模式: `src/components/modern/[PatternName]Demo.vue`

**必须包含的内容**:

```vue
<template>
  <div class="[pattern-name]-demo">
    <!-- 模式信息说明 -->
    <div class="pattern-info">
      <p class="description">模式的详细描述和作用</p>
      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>场景1</li>
          <li>场景2</li>
          <!-- 更多场景 -->
        </ul>
      </div>
    </div>

    <!-- 交互式演示 -->
    <div class="demo-section">
      <h4>交互式演示：</h4>
      <div class="demo-controls">
        <!-- 操作按钮 -->
      </div>
      <div class="demo-output">
        <!-- 演示结果显示 -->
      </div>
    </div>

    <!-- 优缺点分析 -->
    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 优点1</li>
        <li>✅ 优点2</li>
      </ul>
      <h4>缺点：</h4>
      <ul>
        <li>❌ 缺点1</li>
        <li>❌ 缺点2</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
// 导入相关的TypeScript实现
// 实现交互逻辑
</script>

<style lang="scss" scoped>
// 组件样式
</style>
```

#### 2. 💻 TypeScript 核心实现

**文件位置**: `src/patterns/[PatternName].ts`

**必须包含的内容**:

```typescript
/*
 * <AUTHOR> Bruce
 * @Date         : [日期]
 * @LastEditors  : Bruce
 * @LastEditTime : [日期]
 * @FilePath     : /src/patterns/[PatternName].ts
 * @Description  : [模式名称]的TypeScript实现
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 核心类和接口定义
// 完整的模式实现
// 导出实例和类型
```

#### 3. 📊 架构图文档

**文件位置**:

- 经典模式: `src/diagrams/classic/[pattern-name]-pattern.md`
- 现代模式: `src/diagrams/modern/[pattern-name]-pattern.md`

**必须包含的内容**:

````markdown
# [模式名称]架构图

## 📋 模式概述

模式的基本介绍和定义

## 🎯 解决的问题

模式要解决的具体问题

## 🏗️ 架构图

```mermaid
// Mermaid图表代码
```
````

## 🔍 组件说明

详细的组件和类说明

## 📊 实现方式对比

不同实现方式的对比

## 🔄 流程图/时序图

```mermaid
// 流程或时序图
```

## 💡 设计优势

模式的优势分析

## ⚠️ 注意事项

使用时的注意事项

## 🎯 使用场景

具体的应用场景

## 📝 实现要点

关键的实现要点

## 🔧 TypeScript 实现

完整的代码示例

## 🚀 扩展变体

模式的变体实现

````

#### 4. 📝 代码示例数据
**文件位置**:
- 经典模式: `src/data/patternCodes.ts` 中添加条目
- 现代模式: `src/data/modernPatternCodes.ts` 中添加条目

**数据结构**:
```typescript
[patternId]: {
  id: 'pattern-id',
  title: '模式名称实现',
  code: `// 完整的代码示例
// 包含详细注释
// 展示核心实现和使用方法`
}
````

#### 5. 🔗 页面配置更新

**经典模式**: 在 `src/views/ClassicPatterns.vue` 中添加：

```typescript
// 导入组件
import [PatternName]Demo from "../components/patterns/[PatternName]Demo.vue";

// 添加到patterns数组
{
  id: "pattern-id",
  name: "模式名称",
  description: "模式描述",
  category: "模式分类",
  component: [PatternName]Demo,
}
```

**现代模式**: 在 `src/views/ModernPatterns.vue` 中添加：

```typescript
// 导入组件
import [PatternName]Demo from "@/components/modern/[PatternName]Demo.vue";

// 更新对应分类的patterns数组
{
  id: "pattern-id",
  name: "模式名称",
  description: "模式描述",
  category: "模式分类",
  status: "completed",
  component: [PatternName]Demo,
  problem: "解决的问题",
  solution: "解决方案",
  scenarios: ["场景1", "场景2"],
  estimatedDate: "完成日期",
}
```

### 📋 实现检查清单

每个新设计模式必须确保：

- [ ] UI 演示组件完整，包含说明、演示、优缺点
- [ ] TypeScript 核心实现功能完整
- [ ] 架构图文档详细，包含多种图表
- [ ] 代码示例数据完整，注释清晰
- [ ] 页面配置正确更新
- [ ] 所有文件命名规范一致
- [ ] 导入导出关系正确
- [ ] 交互演示功能正常
- [ ] 样式与整体风格一致

### 🎯 质量标准

1. **功能完整性**: 演示所有核心功能
2. **代码质量**: TypeScript 类型安全，注释完整
3. **文档质量**: 架构图清晰，说明详细
4. **用户体验**: 交互流畅，界面美观
5. **教育价值**: 能够帮助理解模式原理

---

**记住：这两个要求是项目的基石，任何时候都不能违反！**
