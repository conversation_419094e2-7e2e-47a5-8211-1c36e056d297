/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 23:19:38
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 23:32:51
 * @FilePath     : /src/router/index.ts
 * @Description  :
 * Copyright 2025 <PERSON>, All Rights Reserved.
 * 2025-07-26 23:19:38
 */
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue')
  },
  {
    path: '/classic-patterns',
    name: 'ClassicPatterns',
    component: () => import('@/views/ClassicPatterns.vue')
  },
  {
    path: '/modern-patterns',
    name: 'ModernPatterns',
    component: () => import('@/views/ModernPatterns.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
