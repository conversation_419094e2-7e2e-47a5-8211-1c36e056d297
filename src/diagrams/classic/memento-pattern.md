# 备忘录模式架构图解

> **模式名称**: Memento Pattern (备忘录模式)  
> **模式类型**: 行为型模式  
> **复杂度**: ⭐⭐⭐  
> **使用频率**: ⭐⭐⭐

## 📋 模式概述

备忘录模式在不破坏封装性的前提下，捕获一个对象的内部状态，并在该对象之外保存这个状态。这样以后就可将该对象恢复到原先保存的状态。

## 🏗️ 核心架构

### 类图结构

```mermaid
classDiagram
    class Memento {
        <<interface>>
        +getState() any
        +getTimestamp() Date
        +getDescription() string
    }
    
    class DocumentMemento {
        -state: DocumentState
        -timestamp: Date
        -description: string
        +getState() DocumentState
        +getTimestamp() Date
        +getDescription() string
    }
    
    class DocumentEditor {
        -state: DocumentState
        +createMemento(description: string) Memento
        +restoreFromMemento(memento: Memento) void
        +setContent(content: string) void
        +getContent() string
        +setFontSize(size: number) void
        +getFontSize() number
        +setFontFamily(family: string) void
        +setTextColor(color: string) void
        +setBackgroundColor(color: string) void
        +getState() DocumentState
    }
    
    class HistoryManager {
        -mementos: Memento[]
        -currentIndex: number
        -maxHistorySize: number
        +save(memento: Memento) void
        +undo() Memento
        +redo() Memento
        +canUndo() boolean
        +canRedo() boolean
        +getHistory() HistoryRecord[]
        +jumpTo(index: number) Memento
        +clear() void
        +getStats() HistoryStats
    }
    
    class DocumentApp {
        -editor: DocumentEditor
        -historyManager: HistoryManager
        -autoSaveEnabled: boolean
        +saveState(description: string) void
        +executeCommand(command: Function, description: string) void
        +undo() boolean
        +redo() boolean
        +jumpToHistory(index: number) boolean
        +setContent(content: string) void
        +setFontSize(size: number) void
        +createSnapshot(description: string) void
    }
    
    class DocumentState {
        +content: string
        +fontSize: number
        +fontFamily: string
        +textColor: string
        +backgroundColor: string
        +wordCount: number
    }
    
    Memento <|.. DocumentMemento
    DocumentEditor --> DocumentState : contains
    DocumentMemento --> DocumentState : stores
    HistoryManager --> Memento : manages
    DocumentApp --> DocumentEditor : uses
    DocumentApp --> HistoryManager : uses
```

### 三角色协作关系

```mermaid
graph TB
    subgraph "原发器 (Originator)"
        O1[DocumentEditor]
        O2[维护内部状态]
        O3[创建备忘录]
        O4[从备忘录恢复状态]
        O1 --> O2
        O1 --> O3
        O1 --> O4
    end
    
    subgraph "备忘录 (Memento)"
        M1[DocumentMemento]
        M2[存储原发器状态]
        M3[提供状态访问接口]
        M4[记录创建时间和描述]
        M1 --> M2
        M1 --> M3
        M1 --> M4
    end
    
    subgraph "管理者 (Caretaker)"
        C1[HistoryManager]
        C2[管理备忘录集合]
        C3[提供撤销/重做功能]
        C4[控制历史记录大小]
        C1 --> C2
        C1 --> C3
        C1 --> C4
    end
    
    O3 -.-> M2
    C2 -.-> M1
    O4 -.-> M3
    
    style O1 fill:#e3f2fd
    style M1 fill:#f3e5f5
    style C1 fill:#e8f5e8
```

## 📝 文档编辑器状态管理

### 状态保存和恢复流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant App as DocumentApp
    participant Editor as DocumentEditor
    participant History as HistoryManager
    participant Memento as DocumentMemento
    
    User->>App: setContent("Hello World")
    App->>Editor: setContent("Hello World")
    App->>Editor: createMemento("设置内容")
    Editor->>Memento: new DocumentMemento(state, "设置内容")
    Memento-->>Editor: memento
    Editor-->>App: memento
    App->>History: save(memento)
    
    User->>App: setFontSize(16)
    App->>Editor: setFontSize(16)
    App->>Editor: createMemento("修改字体大小")
    Editor->>Memento: new DocumentMemento(state, "修改字体大小")
    Memento-->>Editor: memento
    Editor-->>App: memento
    App->>History: save(memento)
    
    User->>App: undo()
    App->>History: undo()
    History-->>App: previousMemento
    App->>Editor: restoreFromMemento(previousMemento)
    Editor->>Memento: getState()
    Memento-->>Editor: previousState
    Editor-->>App: restored
    App-->>User: 恢复到上一状态
```

### 历史记录管理

```mermaid
stateDiagram-v2
    [*] --> Empty : 初始化
    Empty --> HasHistory : 保存第一个状态
    HasHistory --> HasHistory : 保存新状态
    HasHistory --> UndoAvailable : 可以撤销
    UndoAvailable --> UndoAvailable : 继续撤销
    UndoAvailable --> RedoAvailable : 执行撤销
    RedoAvailable --> RedoAvailable : 继续重做
    RedoAvailable --> UndoAvailable : 执行重做
    UndoAvailable --> HasHistory : 新操作(清除重做历史)
    RedoAvailable --> HasHistory : 新操作(清除重做历史)
    
    state HasHistory {
        [*] --> Saving
        Saving --> CheckLimit
        CheckLimit --> RemoveOldest : 超出限制
        CheckLimit --> UpdateIndex : 未超出限制
        RemoveOldest --> UpdateIndex
        UpdateIndex --> [*]
    }
```

## 🔄 撤销/重做机制

### 历史记录结构

```mermaid
graph TB
    subgraph "历史记录数组"
        H0["[0] 初始状态<br/>content: ''<br/>fontSize: 14"]
        H1["[1] 添加内容<br/>content: 'Hello'<br/>fontSize: 14"]
        H2["[2] 修改字体<br/>content: 'Hello'<br/>fontSize: 16"]
        H3["[3] 添加更多内容<br/>content: 'Hello World'<br/>fontSize: 16"]
        H4["[4] 修改颜色<br/>content: 'Hello World'<br/>fontSize: 16<br/>color: red"]
    end
    
    subgraph "当前状态指针"
        CI[currentIndex: 4]
    end
    
    subgraph "操作示例"
        U1[撤销 → currentIndex: 3]
        U2[撤销 → currentIndex: 2]
        R1[重做 → currentIndex: 3]
        N1[新操作 → 删除[4]，添加新状态]
    end
    
    CI -.-> H4
    H0 --> H1 --> H2 --> H3 --> H4
    
    style H4 fill:#ffcccc
    style CI fill:#ccffcc
```

### 内存优化策略

```mermaid
graph LR
    subgraph "无限制历史记录"
        U1[状态1] --> U2[状态2] --> U3[状态3] --> U4[...] --> U5[状态N]
        U6[内存使用: O(N)]
        U5 --> U6
    end
    
    subgraph "固定大小历史记录"
        L1[状态N-4] --> L2[状态N-3] --> L3[状态N-2] --> L4[状态N-1] --> L5[状态N]
        L6[内存使用: O(1)]
        L7[FIFO队列<br/>自动清理旧状态]
        L5 --> L6
        L1 -.-> L7
    end
    
    subgraph "增量备忘录"
        I1[完整状态1] --> I2[差异2] --> I3[差异3] --> I4[差异4]
        I5[内存使用: 基础 + O(变化)]
        I6[适用于大状态对象]
        I4 --> I5
        I1 -.-> I6
    end
    
    style U6 fill:#ffcccc
    style L6 fill:#ffffcc
    style I5 fill:#ccffcc
```

## 🎯 应用场景

### 1. 编辑器撤销/重做
- **场景**: 文本编辑器、图形编辑器
- **优势**: 用户可以自由撤销和重做操作
- **示例**: Word、Photoshop、IDE编辑器

### 2. 游戏存档系统
- **场景**: 游戏进度保存和加载
- **优势**: 玩家可以回到之前的游戏状态
- **示例**: RPG游戏存档、关卡重试

### 3. 事务回滚
- **场景**: 数据库事务、业务操作回滚
- **优势**: 保证数据一致性
- **示例**: 数据库事务、分布式事务

### 4. 配置管理
- **场景**: 系统配置的版本管理
- **优势**: 可以回退到之前的配置
- **示例**: 系统设置、应用配置

## 🔍 状态存储策略

### 完整状态 vs 增量状态

```mermaid
graph TB
    subgraph "完整状态存储"
        F1[状态1: 完整对象]
        F2[状态2: 完整对象]
        F3[状态3: 完整对象]
        F4[优点: 恢复快速<br/>缺点: 内存占用大]
        F1 --> F2 --> F3 --> F4
    end
    
    subgraph "增量状态存储"
        D1[基础状态: 完整对象]
        D2[变化1: 只存储差异]
        D3[变化2: 只存储差异]
        D4[优点: 内存节省<br/>缺点: 恢复需要计算]
        D1 --> D2 --> D3 --> D4
    end
    
    subgraph "混合策略"
        M1[完整状态1]
        M2[增量1]
        M3[增量2]
        M4[完整状态2]
        M5[增量3]
        M6[平衡内存和性能]
        M1 --> M2 --> M3 --> M4 --> M5 --> M6
    end
    
    style F4 fill:#ffcccc
    style D4 fill:#ffffcc
    style M6 fill:#ccffcc
```

## ✅ 优点

1. **封装性**: 不破坏对象的封装性
2. **简化原发器**: 原发器不需要管理历史版本
3. **恢复能力**: 提供了状态恢复机制
4. **用户体验**: 支持撤销/重做操作

## ❌ 缺点

1. **内存消耗**: 需要存储多个状态副本
2. **性能开销**: 创建和恢复备忘录的开销
3. **复杂性**: 增加了系统的复杂性
4. **生命周期管理**: 需要管理备忘录的生命周期

## 🔗 相关模式

- **命令模式**: 常与备忘录模式结合实现撤销功能
- **原型模式**: 可用于创建状态副本
- **迭代器模式**: 可用于遍历历史记录

## 💡 最佳实践

1. **状态压缩**: 对于大状态对象，考虑压缩存储
2. **增量备份**: 只存储状态变化，节省内存
3. **生命周期管理**: 及时清理不需要的历史记录
4. **异步处理**: 对于耗时的状态保存，考虑异步处理
5. **用户控制**: 让用户控制历史记录的大小和范围
