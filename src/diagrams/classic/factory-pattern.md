# 工厂模式架构图

## 📋 模式概述

工厂模式（Factory Pattern）是一种创建型设计模式，它提供了一种创建对象的最佳方式。在工厂模式中，我们在创建对象时不会对客户端暴露创建逻辑，并且是通过使用一个共同的接口来指向新创建的对象。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 需要根据不同条件创建不同类型的对象
- 对象创建过程复杂，需要封装创建逻辑
- 客户端不应该依赖具体的实现类
- 需要统一管理对象的创建过程

## 🏗️ 架构图

```mermaid
classDiagram
    class PaymentProcessor {
        <<interface>>
        +process(amount: number): string
        +getType(): string
        +getProvider(): string
        +getFeeRate(): number
    }
    
    class AlipayProcessor {
        +process(amount: number): string
        +getType(): string
        +getProvider(): string
        +getFeeRate(): number
    }
    
    class WechatProcessor {
        +process(amount: number): string
        +getType(): string
        +getProvider(): string
        +getFeeRate(): number
    }
    
    class UnionPayProcessor {
        +process(amount: number): string
        +getType(): string
        +getProvider(): string
        +getFeeRate(): number
    }
    
    class PaymentProcessorFactory {
        +static createProcessor(type: string): PaymentProcessor
        +static getSupportedTypes(): string[]
    }
    
    class Client {
        +processPayment(type: string, amount: number): void
    }
    
    PaymentProcessor <|.. AlipayProcessor
    PaymentProcessor <|.. WechatProcessor
    PaymentProcessor <|.. UnionPayProcessor
    
    PaymentProcessorFactory --> PaymentProcessor : creates
    PaymentProcessorFactory ..> AlipayProcessor : instantiates
    PaymentProcessorFactory ..> WechatProcessor : instantiates
    PaymentProcessorFactory ..> UnionPayProcessor : instantiates
    
    Client --> PaymentProcessorFactory : uses
    Client --> PaymentProcessor : uses
    
    note for PaymentProcessor "产品接口\n定义所有产品的共同接口"
    note for PaymentProcessorFactory "工厂类\n封装对象创建逻辑"
    note for Client "客户端\n只依赖接口和工厂"
```

## 🔍 组件说明

### 产品接口 (Product Interface)
- **PaymentProcessor**: 支付处理器接口
  - 定义所有支付处理器的共同行为
  - 提供统一的方法签名

### 具体产品 (Concrete Products)
- **AlipayProcessor**: 支付宝支付处理器
- **WechatProcessor**: 微信支付处理器
- **UnionPayProcessor**: 银联支付处理器
- 每个具体产品实现相同的接口，但有不同的实现逻辑

### 工厂类 (Factory Class)
- **PaymentProcessorFactory**: 支付处理器工厂
  - `createProcessor()`: 根据类型创建对应的处理器
  - `getSupportedTypes()`: 获取支持的处理器类型
  - 封装了对象创建的复杂逻辑

### 客户端 (Client)
- 只依赖于接口和工厂
- 不需要知道具体的实现类

## 📊 对象创建流程

```mermaid
sequenceDiagram
    participant C as Client
    participant F as PaymentProcessorFactory
    participant A as AlipayProcessor
    participant W as WechatProcessor
    participant U as UnionPayProcessor
    
    C->>F: createProcessor("alipay")
    F->>A: new AlipayProcessor()
    A-->>F: instance
    F-->>C: PaymentProcessor
    
    C->>F: createProcessor("wechat")
    F->>W: new WechatProcessor()
    W-->>F: instance
    F-->>C: PaymentProcessor
    
    C->>F: createProcessor("unionpay")
    F->>U: new UnionPayProcessor()
    U-->>F: instance
    F-->>C: PaymentProcessor
    
    Note over C: 客户端使用统一接口
```

## 💡 设计优势

1. **封装创建逻辑**: 将对象创建逻辑集中在工厂中
2. **降低耦合度**: 客户端不依赖具体实现类
3. **易于扩展**: 添加新产品只需修改工厂
4. **统一管理**: 所有对象创建都通过工厂进行
5. **符合开闭原则**: 对扩展开放，对修改关闭

## 🔧 TypeScript 实现

### 产品接口
```typescript
interface PaymentProcessor {
  process(amount: number): string;
  getType(): string;
  getProvider(): string;
  getFeeRate(): number;
}
```

### 具体产品实现
```typescript
class AlipayProcessor implements PaymentProcessor {
  process(amount: number): string {
    const fee = (amount * this.getFeeRate()) / 100;
    return `支付宝支付成功！金额：¥${amount}，手续费：¥${fee.toFixed(2)}`;
  }

  getType(): string {
    return "支付宝";
  }

  getProvider(): string {
    return "蚂蚁金服";
  }

  getFeeRate(): number {
    return 0.6;
  }
}

class WechatProcessor implements PaymentProcessor {
  process(amount: number): string {
    const fee = (amount * this.getFeeRate()) / 100;
    return `微信支付成功！金额：¥${amount}，手续费：¥${fee.toFixed(2)}`;
  }

  getType(): string {
    return "微信支付";
  }

  getProvider(): string {
    return "腾讯";
  }

  getFeeRate(): number {
    return 0.6;
  }
}
```

### 工厂类实现
```typescript
class PaymentProcessorFactory {
  static createProcessor(type: string): PaymentProcessor {
    switch (type) {
      case "alipay":
        return new AlipayProcessor();
      case "wechat":
        return new WechatProcessor();
      case "unionpay":
        return new UnionPayProcessor();
      default:
        throw new Error(`不支持的支付类型: ${type}`);
    }
  }

  static getSupportedTypes(): string[] {
    return ["alipay", "wechat", "unionpay"];
  }
}
```

### 客户端使用
```typescript
class PaymentService {
  processPayment(type: string, amount: number): string {
    // 通过工厂创建处理器
    const processor = PaymentProcessorFactory.createProcessor(type);
    
    // 使用统一接口处理支付
    return processor.process(amount);
  }
}

// 使用示例
const paymentService = new PaymentService();
console.log(paymentService.processPayment("alipay", 100));
console.log(paymentService.processPayment("wechat", 200));
```

## 🚀 工厂模式变体

### 简单工厂模式 (Simple Factory)
```typescript
// 当前实现就是简单工厂模式
class SimpleFactory {
  static createProduct(type: string): Product {
    // 创建逻辑
  }
}
```

### 工厂方法模式 (Factory Method)
```typescript
abstract class PaymentFactory {
  abstract createProcessor(): PaymentProcessor;
  
  public processPayment(amount: number): string {
    const processor = this.createProcessor();
    return processor.process(amount);
  }
}

class AlipayFactory extends PaymentFactory {
  createProcessor(): PaymentProcessor {
    return new AlipayProcessor();
  }
}

class WechatFactory extends PaymentFactory {
  createProcessor(): PaymentProcessor {
    return new WechatProcessor();
  }
}
```

### 抽象工厂模式 (Abstract Factory)
```typescript
interface PaymentAbstractFactory {
  createProcessor(): PaymentProcessor;
  createValidator(): PaymentValidator;
  createLogger(): PaymentLogger;
}

class AlipayFactory implements PaymentAbstractFactory {
  createProcessor(): PaymentProcessor {
    return new AlipayProcessor();
  }
  
  createValidator(): PaymentValidator {
    return new AlipayValidator();
  }
  
  createLogger(): PaymentLogger {
    return new AlipayLogger();
  }
}
```

## 🎯 使用场景

- **多种产品创建**: 需要创建多种相关产品
- **创建逻辑复杂**: 对象创建过程复杂，需要封装
- **解耦客户端**: 客户端不应该依赖具体实现
- **配置驱动**: 根据配置创建不同的对象
- **插件系统**: 动态加载和创建插件实例

## 📝 实现要点

1. **定义产品接口**: 确保所有产品有统一的接口
2. **实现具体产品**: 每个产品实现相同的接口
3. **创建工厂类**: 封装对象创建逻辑
4. **错误处理**: 处理不支持的产品类型
5. **扩展性考虑**: 便于添加新的产品类型

## ⚠️ 注意事项

1. **工厂类职责**: 工厂类可能变得过于复杂
2. **类数量增加**: 每个产品都需要对应的类
3. **参数传递**: 创建参数的传递和验证
4. **性能考虑**: 频繁创建对象的性能影响

## 🔄 与其他模式的关系

- **与抽象工厂**: 工厂方法是抽象工厂的特例
- **与建造者模式**: 都是创建型模式，但关注点不同
- **与原型模式**: 可以结合使用，通过克隆创建对象
- **与单例模式**: 工厂本身可以是单例

---

> 工厂模式是面向对象设计中最常用的模式之一，它将对象的创建和使用分离，提高了代码的灵活性和可维护性。
