# 访问者模式架构图解

> **模式名称**: Visitor Pattern (访问者模式)  
> **模式类型**: 行为型模式  
> **复杂度**: ⭐⭐⭐⭐  
> **使用频率**: ⭐⭐

## 📋 模式概述

访问者模式表示一个作用于某对象结构中的各元素的操作。它使你可以在不改变各元素的类的前提下定义作用于这些元素的新操作。

## 🏗️ 核心架构

### 类图结构

```mermaid
classDiagram
    class DocumentVisitor {
        <<interface>>
        +visitTextElement(element: TextElement) void
        +visitImageElement(element: ImageElement) void
        +visitTableElement(element: TableElement) void
        +visitListElement(element: ListElement) void
    }
    
    class DocumentElement {
        <<interface>>
        +accept(visitor: DocumentVisitor) void
        +getType() string
        +getId() string
    }
    
    class TextElement {
        -id: string
        -content: string
        -fontSize: number
        -color: string
        +accept(visitor: DocumentVisitor) void
        +getContent() string
        +getFontSize() number
        +getColor() string
        +setContent(content: string) void
    }
    
    class ImageElement {
        -id: string
        -src: string
        -width: number
        -height: number
        -alt: string
        +accept(visitor: DocumentVisitor) void
        +getSrc() string
        +getWidth() number
        +getHeight() number
        +getAlt() string
    }
    
    class TableElement {
        -id: string
        -rows: string[][]
        -headers: string[]
        +accept(visitor: DocumentVisitor) void
        +getHeaders() string[]
        +getRows() string[][]
        +addRow(row: string[]) void
    }
    
    class ListElement {
        -id: string
        -items: string[]
        -ordered: boolean
        +accept(visitor: DocumentVisitor) void
        +getItems() string[]
        +isOrdered() boolean
        +addItem(item: string) void
    }
    
    class HTMLExportVisitor {
        -html: string
        +visitTextElement(element: TextElement) void
        +visitImageElement(element: ImageElement) void
        +visitTableElement(element: TableElement) void
        +visitListElement(element: ListElement) void
        +getHTML() string
        +reset() void
    }
    
    class MarkdownExportVisitor {
        -markdown: string
        +visitTextElement(element: TextElement) void
        +visitImageElement(element: ImageElement) void
        +visitTableElement(element: TableElement) void
        +visitListElement(element: ListElement) void
        +getMarkdown() string
        +reset() void
    }
    
    class StatisticsVisitor {
        -stats: Statistics
        +visitTextElement(element: TextElement) void
        +visitImageElement(element: ImageElement) void
        +visitTableElement(element: TableElement) void
        +visitListElement(element: ListElement) void
        +getStatistics() Statistics
        +reset() void
    }
    
    class Document {
        -elements: DocumentElement[]
        -title: string
        +addElement(element: DocumentElement) void
        +removeElement(elementId: string) boolean
        +accept(visitor: DocumentVisitor) void
        +getElements() DocumentElement[]
    }
    
    class DocumentProcessor {
        -document: Document
        +exportToHTML() string
        +exportToMarkdown() string
        +getStatistics() Statistics
    }
    
    DocumentVisitor <|.. HTMLExportVisitor
    DocumentVisitor <|.. MarkdownExportVisitor
    DocumentVisitor <|.. StatisticsVisitor
    DocumentElement <|.. TextElement
    DocumentElement <|.. ImageElement
    DocumentElement <|.. TableElement
    DocumentElement <|.. ListElement
    Document --> DocumentElement : contains
    DocumentProcessor --> Document : uses
    DocumentProcessor --> DocumentVisitor : uses
```

### 双分派机制

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Doc as Document
    participant Text as TextElement
    participant Image as ImageElement
    participant Visitor as HTMLExportVisitor
    
    Client->>Doc: accept(htmlVisitor)
    
    loop 遍历所有元素
        Doc->>Text: accept(htmlVisitor)
        Text->>Visitor: visitTextElement(this)
        Note over Visitor: 第一次分派：根据访问者类型<br/>第二次分派：根据元素类型
        Visitor->>Visitor: 处理文本元素
        Visitor-->>Text: 处理完成
        Text-->>Doc: 返回
        
        Doc->>Image: accept(htmlVisitor)
        Image->>Visitor: visitImageElement(this)
        Visitor->>Visitor: 处理图片元素
        Visitor-->>Image: 处理完成
        Image-->>Doc: 返回
    end
    
    Doc-->>Client: 所有元素处理完成
    Client->>Visitor: getHTML()
    Visitor-->>Client: 返回HTML字符串
```

## 📄 文档处理系统架构

### 元素类型与访问者操作矩阵

```mermaid
graph TB
    subgraph "元素类型"
        E1[TextElement]
        E2[ImageElement]
        E3[TableElement]
        E4[ListElement]
    end
    
    subgraph "访问者操作"
        V1[HTMLExportVisitor]
        V2[MarkdownExportVisitor]
        V3[StatisticsVisitor]
    end
    
    subgraph "操作矩阵"
        M11[生成&lt;p&gt;标签]
        M12[生成&lt;img&gt;标签]
        M13[生成&lt;table&gt;标签]
        M14[生成&lt;ul/ol&gt;标签]
        
        M21[生成段落文本]
        M22[生成![图片]语法]
        M23[生成表格语法]
        M24[生成列表语法]
        
        M31[统计字符数]
        M32[统计图片数]
        M33[统计表格行数]
        M34[统计列表项数]
    end
    
    E1 --> M11
    E2 --> M12
    E3 --> M13
    E4 --> M14
    
    E1 --> M21
    E2 --> M22
    E3 --> M23
    E4 --> M24
    
    E1 --> M31
    E2 --> M32
    E3 --> M33
    E4 --> M34
    
    V1 -.-> M11
    V1 -.-> M12
    V1 -.-> M13
    V1 -.-> M14
    
    V2 -.-> M21
    V2 -.-> M22
    V2 -.-> M23
    V2 -.-> M24
    
    V3 -.-> M31
    V3 -.-> M32
    V3 -.-> M33
    V3 -.-> M34
    
    style M11 fill:#e3f2fd
    style M22 fill:#f3e5f5
    style M33 fill:#e8f5e8
```

### 不同访问者的处理策略

```mermaid
flowchart TD
    A[文档元素] --> B{访问者类型}
    
    B -->|HTML导出| C[HTMLExportVisitor]
    B -->|Markdown导出| D[MarkdownExportVisitor]
    B -->|统计分析| E[StatisticsVisitor]
    
    C --> C1{元素类型}
    C1 -->|文本| C2[生成&lt;p&gt;标签]
    C1 -->|图片| C3[生成&lt;img&gt;标签]
    C1 -->|表格| C4[生成&lt;table&gt;结构]
    C1 -->|列表| C5[生成&lt;ul/ol&gt;结构]
    
    D --> D1{元素类型}
    D1 -->|文本| D2[直接输出文本]
    D1 -->|图片| D3[生成![alt](src)语法]
    D1 -->|表格| D4[生成Markdown表格]
    D1 -->|列表| D5[生成- 或1. 语法]
    
    E --> E1{元素类型}
    E1 -->|文本| E2[统计字符和单词数]
    E1 -->|图片| E3[统计图片数量]
    E1 -->|表格| E4[统计行数和列数]
    E1 -->|列表| E5[统计列表项数]
    
    C2 --> F[累积到输出字符串]
    C3 --> F
    C4 --> F
    C5 --> F
    
    D2 --> G[累积到Markdown字符串]
    D3 --> G
    D4 --> G
    D5 --> G
    
    E2 --> H[累积到统计对象]
    E3 --> H
    E4 --> H
    E5 --> H
    
    style C fill:#e3f2fd
    style D fill:#f3e5f5
    style E fill:#e8f5e8
```

## 🔄 扩展性分析

### 添加新元素类型

```mermaid
graph LR
    subgraph "现有结构"
        OE1[TextElement]
        OE2[ImageElement]
        OE3[TableElement]
        OE4[ListElement]
        
        OV1[HTMLExportVisitor]
        OV2[MarkdownExportVisitor]
        OV3[StatisticsVisitor]
    end
    
    subgraph "添加新元素"
        NE[VideoElement]
        
        NM1[visitVideoElement方法]
        NM2[visitVideoElement方法]
        NM3[visitVideoElement方法]
        
        NE --> NM1
        NE --> NM2
        NE --> NM3
        
        OV1 -.-> NM1
        OV2 -.-> NM2
        OV3 -.-> NM3
    end
    
    style NE fill:#ffcccc
    style NM1 fill:#ffcccc
    style NM2 fill:#ffcccc
    style NM3 fill:#ffcccc
```

### 添加新访问者

```mermaid
graph LR
    subgraph "现有结构"
        OE1[TextElement]
        OE2[ImageElement]
        OE3[TableElement]
        OE4[ListElement]
        
        OV1[HTMLExportVisitor]
        OV2[MarkdownExportVisitor]
        OV3[StatisticsVisitor]
    end
    
    subgraph "添加新访问者"
        NV[PDFExportVisitor]
        
        NM1[visitTextElement]
        NM2[visitImageElement]
        NM3[visitTableElement]
        NM4[visitListElement]
        
        NV --> NM1
        NV --> NM2
        NV --> NM3
        NV --> NM4
        
        OE1 -.-> NM1
        OE2 -.-> NM2
        OE3 -.-> NM3
        OE4 -.-> NM4
    end
    
    style NV fill:#ccffcc
    style NM1 fill:#ccffcc
    style NM2 fill:#ccffcc
    style NM3 fill:#ccffcc
    style NM4 fill:#ccffcc
```

## 🎯 应用场景

### 1. 编译器设计
- **场景**: 抽象语法树(AST)的遍历和处理
- **优势**: 不同的编译阶段可以用不同的访问者
- **示例**: 语法检查、代码生成、优化

### 2. 文档处理系统
- **场景**: 文档的多种格式导出
- **优势**: 新增导出格式不需要修改文档结构
- **示例**: HTML、PDF、Word导出

### 3. 图形处理
- **场景**: 图形对象的不同操作
- **优势**: 渲染、计算、变换等操作分离
- **示例**: CAD软件、图形编辑器

### 4. 数据结构遍历
- **场景**: 复杂数据结构的多种操作
- **优势**: 操作与数据结构解耦
- **示例**: 树形菜单、组织架构

## 🔄 与其他模式对比

### 访问者 vs 策略模式

```mermaid
graph TB
    subgraph "访问者模式"
        V1[多种元素类型]
        V2[多种操作]
        V3[双分派机制]
        V4[操作与结构分离]
        V1 --> V2 --> V3 --> V4
    end
    
    subgraph "策略模式"
        S1[单一上下文]
        S2[多种算法]
        S3[运行时切换]
        S4[算法封装]
        S1 --> S2 --> S3 --> S4
    end
    
    style V3 fill:#e3f2fd
    style S3 fill:#f3e5f5
```

### 访问者 vs 命令模式

```mermaid
graph TB
    subgraph "访问者模式"
        V1[遍历对象结构]
        V2[对每个元素执行操作]
        V3[操作依赖于元素类型]
        V4[编译时确定操作]
        V1 --> V2 --> V3 --> V4
    end
    
    subgraph "命令模式"
        C1[封装请求为对象]
        C2[支持撤销/重做]
        C3[操作独立于接收者]
        C4[运行时组合操作]
        C1 --> C2 --> C3 --> C4
    end
    
    style V4 fill:#ffcccc
    style C4 fill:#ccffcc
```

## ✅ 优点

1. **开闭原则**: 易于添加新的操作
2. **单一职责**: 相关操作集中在访问者中
3. **数据与操作分离**: 数据结构与操作解耦
4. **类型安全**: 编译时确定操作类型

## ❌ 缺点

1. **添加新元素困难**: 需要修改所有访问者
2. **破坏封装**: 元素需要暴露内部状态
3. **依赖具体类**: 访问者依赖于具体元素类
4. **复杂性**: 双分派机制增加理解难度

## 🔗 相关模式

- **组合模式**: 常与访问者模式结合使用
- **迭代器模式**: 用于遍历对象结构
- **解释器模式**: AST的处理常用访问者模式

## 💡 最佳实践

1. **稳定的元素结构**: 适用于元素类型相对稳定的场景
2. **操作频繁变化**: 当需要频繁添加新操作时使用
3. **避免过度使用**: 不要为简单操作使用访问者模式
4. **接口设计**: 保持访问者接口的简洁和一致
5. **性能考虑**: 注意双分派的性能开销
