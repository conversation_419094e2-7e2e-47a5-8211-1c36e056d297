# 组合模式架构图

## 📋 模式概述

组合模式（Composite Pattern）是一种结构型设计模式，它将对象组合成树形结构以表示"部分-整体"的层次结构。组合模式使得用户对单个对象和组合对象的使用具有一致性。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 需要表示对象的部分-整体层次结构
- 希望用户忽略组合对象与单个对象的不同
- 需要统一处理树形结构中的所有对象
- 希望简化客户端代码

## 🏗️ 架构图

```mermaid
classDiagram
    class FileSystemComponent {
        <<interface>>
        +getName() string
        +getSize() number
        +getType() string
        +display(indent?: string) string
        +getChildren?() FileSystemComponent[]
        +add?(component: FileSystemComponent) void
        +remove?(component: FileSystemComponent) void
    }
    
    class File {
        -name: string
        -size: number
        -extension: string
        +constructor(name: string, size?: number)
        +getName() string
        +getSize() number
        +getType() string
        +display(indent?: string) string
        +getExtension() string
    }
    
    class Directory {
        -name: string
        -children: FileSystemComponent[]
        +constructor(name: string)
        +getName() string
        +getSize() number
        +getType() string
        +add(component: FileSystemComponent) void
        +remove(component: FileSystemComponent) void
        +getChildren() FileSystemComponent[]
        +display(indent?: string) string
    }
    
    FileSystemComponent <|-- File
    FileSystemComponent <|-- Directory
    Directory --> FileSystemComponent : contains
```

## 🌳 树形结构图

```mermaid
graph TD
    Root[📁 根目录<br/>总大小: 76KB]
    
    Root --> Src[📁 src<br/>48KB]
    Root --> Config[📁 config<br/>15KB]
    Root --> Package[📄 package.json<br/>5KB]
    Root --> Readme[📄 README.md<br/>2KB]
    Root --> Tests[📁 tests<br/>6KB]
    
    Src --> Index[📄 index.ts<br/>15KB]
    Src --> App[📄 app.ts<br/>25KB]
    Src --> Components[📁 components<br/>8KB]
    
    Components --> Header[📄 Header.vue<br/>4KB]
    Components --> Footer[📄 Footer.vue<br/>4KB]
    
    Config --> Webpack[📄 webpack.config.js<br/>12KB]
    Config --> Tsconfig[📄 tsconfig.json<br/>3KB]
    
    Tests --> Unit[📁 unit<br/>4KB]
    Tests --> E2E[📁 e2e<br/>2KB]
    
    Unit --> Test1[📄 app.test.ts<br/>2KB]
    Unit --> Test2[📄 utils.test.ts<br/>2KB]
    
    E2E --> E2ETest[📄 main.e2e.ts<br/>2KB]
    
    style Root fill:#ffa500
    style Src fill:#87ceeb
    style Config fill:#87ceeb
    style Components fill:#87ceeb
    style Tests fill:#87ceeb
    style Unit fill:#87ceeb
    style E2E fill:#87ceeb
    
    style Package fill:#98fb98
    style Readme fill:#98fb98
    style Index fill:#98fb98
    style App fill:#98fb98
    style Header fill:#98fb98
    style Footer fill:#98fb98
    style Webpack fill:#98fb98
    style Tsconfig fill:#98fb98
    style Test1 fill:#98fb98
    style Test2 fill:#98fb98
    style E2ETest fill:#98fb98
```

## 🔄 操作时序图

```mermaid
sequenceDiagram
    participant Client
    participant Root as Directory(root)
    participant Src as Directory(src)
    participant File1 as File(index.ts)
    participant File2 as File(app.ts)
    
    Client->>Root: getSize()
    Root->>Src: getSize()
    Src->>File1: getSize()
    File1-->>Src: 15KB
    Src->>File2: getSize()
    File2-->>Src: 25KB
    Src-->>Root: 40KB (15+25)
    
    Note over Root: 递归计算其他子目录...
    
    Root-->>Client: 76KB (总大小)
    
    Client->>Root: display()
    Root->>Root: display own info
    Root->>Src: display("  ")
    Src->>Src: display own info with indent
    Src->>File1: display("    ")
    File1-->>Src: "    📄 index.ts (15KB)"
    Src->>File2: display("    ")
    File2-->>Src: "    📄 app.ts (25KB)"
    Src-->>Root: formatted string
    Root-->>Client: complete tree display
```

## 🏗️ 组合结构对比

```mermaid
graph LR
    subgraph "叶子节点 (Leaf)"
        direction TB
        L1[📄 File]
        L1 --> L1A[getName()]
        L1 --> L1B[getSize()]
        L1 --> L1C[getType()]
        L1 --> L1D[display()]
        
        L1Note[• 不能包含子节点<br/>• 实现基本操作<br/>• 是树的终端节点]
    end
    
    subgraph "组合节点 (Composite)"
        direction TB
        C1[📁 Directory]
        C1 --> C1A[getName()]
        C1 --> C1B[getSize()]
        C1 --> C1C[getType()]
        C1 --> C1D[display()]
        C1 --> C1E[add()]
        C1 --> C1F[remove()]
        C1 --> C1G[getChildren()]
        
        C1Note[• 可以包含子节点<br/>• 委托操作给子节点<br/>• 管理子节点集合]
    end
    
    subgraph "统一接口"
        direction TB
        I1[FileSystemComponent]
        I1 --> I1A[通用操作接口]
        I1 --> I1B[客户端统一调用]
        
        I1Note[• 定义统一接口<br/>• 叶子和组合都实现<br/>• 客户端无需区分]
    end
    
    style L1 fill:#98fb98
    style C1 fill:#87ceeb
    style I1 fill:#ffa500
```

## 📊 递归操作示例

```mermaid
graph TD
    subgraph "getSize() 递归计算"
        direction TB
        
        A[Directory.getSize()]
        A --> B[遍历所有子节点]
        B --> C{子节点类型?}
        
        C -->|File| D[直接返回文件大小]
        C -->|Directory| E[递归调用getSize()]
        
        D --> F[累加到总大小]
        E --> F
        F --> G{还有子节点?}
        
        G -->|是| B
        G -->|否| H[返回总大小]
    end
    
    subgraph "display() 递归显示"
        direction TB
        
        I[Directory.display()]
        I --> J[显示自己的信息]
        J --> K[遍历所有子节点]
        K --> L{子节点类型?}
        
        L -->|File| M[显示文件信息]
        L -->|Directory| N[递归调用display()]
        
        M --> O[添加到结果字符串]
        N --> O
        O --> P{还有子节点?}
        
        P -->|是| K
        P -->|否| Q[返回完整显示]
    end
    
    style A fill:#87ceeb
    style I fill:#87ceeb
    style D fill:#98fb98
    style M fill:#98fb98
```

## 💡 核心概念

### 组件接口 (Component)
- `FileSystemComponent`: 定义叶子和组合对象的统一接口

### 叶子节点 (Leaf)
- `File`: 表示叶子对象，没有子节点

### 组合节点 (Composite)
- `Directory`: 表示组合对象，可以包含子节点

## ✅ 优点

1. **统一接口**: 客户端可以统一处理单个对象和组合对象
2. **简化客户端**: 客户端无需区分叶子和组合对象
3. **易于扩展**: 容易增加新类型的组件
4. **符合开闭原则**: 对扩展开放，对修改关闭
5. **递归结构**: 天然支持树形结构的递归操作

## ❌ 缺点

1. **设计过于宽泛**: 难以限制组合中的组件类型
2. **类型安全**: 运行时才能确定对象的具体类型
3. **复杂性增加**: 使设计变得更加抽象

## 🔧 适用场景

- 文件系统（文件夹和文件）
- 组织架构（部门和员工）
- UI组件树（容器和控件）
- 菜单系统（菜单和菜单项）
- 表达式解析（操作符和操作数）
- XML/HTML文档结构

## 📝 实现要点

1. **统一接口**: 为叶子和组合对象定义统一接口
2. **递归操作**: 组合对象的操作需要递归处理子节点
3. **子节点管理**: 组合对象需要管理子节点的添加、删除
4. **安全性检查**: 在叶子节点中适当处理不支持的操作
5. **性能考虑**: 对于大型树结构，考虑缓存和懒加载
6. **遍历策略**: 提供不同的遍历方式（深度优先、广度优先）
