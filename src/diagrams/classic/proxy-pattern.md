# 代理模式架构图

## 📋 模式概述

代理模式（Proxy Pattern）是一种结构型设计模式，它允许一个对象代表另一个对象，并控制对这个对象的访问。代理对象充当真实对象的接口，客户端通过代理间接与真实对象交互，从而实现在访问真实对象时附加额外的功能或控制。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 需要延迟初始化开销大的对象
- 需要对敏感对象进行访问控制
- 需要在访问对象时添加额外的操作（如日志、缓存）
- 需要为远程对象提供本地代表

## 🏗️ 架构图

```mermaid
classDiagram
    class Subject {
        <<interface>>
        +operation() Type
        +getProperty() Type
    }

    class RealSubject {
        -data: Type
        +operation() Type
        +getProperty() Type
    }

    class Proxy {
        -realSubject: RealSubject
        -additionalState: Type
        +operation() Type
        +getProperty() Type
        -checkAccess() boolean
        -logCall() void
    }

    class Client {
        +useSubject(subject: Subject)
    }

    Subject <|.. RealSubject
    Subject <|.. Proxy
    Proxy --> RealSubject : delegates calls >
    Client --> Subject : uses >

    note for Subject "主题接口\n定义真实主题和代理共同的接口"
    note for RealSubject "真实主题\n提供核心功能的实际对象"
    note for Proxy "代理\n控制对真实主题的访问"
```

## 🔍 组件说明

### 主题接口 (Subject)
- 定义真实主题和代理对象共同实现的接口
- 使客户端能够透明地使用真实主题或代理对象
- 确保代理可以在真实主题出现的任何地方使用

### 真实主题 (Real Subject)
- 提供核心业务逻辑
- 通常是开销较大的对象（资源密集型、远程对象等）
- 被代理保护的真实服务或资源

### 代理 (Proxy)
- 持有对真实主题的引用
- 实现与真实主题相同的接口
- 控制对真实主题的访问，并在必要时转发请求
- 可以添加额外的功能（如缓存结果、检查权限等）

### 客户端 (Client)
- 通过主题接口与代理或真实主题交互
- 通常不知道是在使用代理还是真实对象

## 🔄 代理模式变体

### 虚拟代理 (Virtual Proxy)
```typescript
class ImageProxy implements Image {
  private realImage: RealImage | null = null;
  private url: string;

  constructor(url: string) {
    this.url = url;
  }

  display(): void {
    if (this.realImage === null) {
      this.realImage = new RealImage(this.url); // 延迟加载
    }
    this.realImage.display();
  }
}
```

### 缓存代理 (Cache Proxy)
```typescript
class CalculationProxy implements Calculator {
  private calculator: RealCalculator;
  private cache: Map<string, number> = new Map();

  constructor(calculator: RealCalculator) {
    this.calculator = calculator;
  }

  calculate(a: number, b: number): number {
    const key = `${a}:${b}`;
    if (!this.cache.has(key)) {
      this.cache.set(key, this.calculator.calculate(a, b));
    }
    return this.cache.get(key)!;
  }
}
```

### 保护代理 (Protection Proxy)
```typescript
class DocumentProxy implements Document {
  private document: RealDocument;
  private userRole: string;

  constructor(document: RealDocument, userRole: string) {
    this.document = document;
    this.userRole = userRole;
  }

  edit(content: string): void {
    if (this.userRole === 'admin' || this.userRole === 'editor') {
      this.document.edit(content);
    } else {
      throw new Error('Access denied');
    }
  }
}
```

### 远程代理 (Remote Proxy)
```typescript
class RemoteServiceProxy implements Service {
  private endpoint: string;

  constructor(endpoint: string) {
    this.endpoint = endpoint;
  }

  async request(data: any): Promise<any> {
    const response = await fetch(this.endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
    return await response.json();
  }
}
```

## 💡 设计优势

1. **延迟初始化（懒加载）**：通过虚拟代理延迟创建开销大的对象，直到真正需要时才创建。
2. **访问控制**：通过保护代理控制对敏感对象的访问权限。
3. **功能增强**：在不修改原有对象的情况下，通过代理添加新功能。
4. **分离关注点**：代理处理与主体业务无关的功能（日志、缓存等）。
5. **远程访问**：通过远程代理隐藏复杂的网络通信细节。

## 🚀 代码示例：图片加载代理

```typescript
// 主题接口
interface ImageInterface {
  display(): void;
  getUrl(): string;
}

// 真实主题
class RealImage implements ImageInterface {
  private url: string;
  private loaded: boolean = false;

  constructor(url: string) {
    this.url = url;
    this.loadImage();
  }

  private loadImage(): void {
    console.log(`Loading image from ${this.url}`);
    // 模拟复杂的图片加载过程
    setTimeout(() => {
      this.loaded = true;
      console.log(`Image loaded from ${this.url}`);
    }, 2000);
  }

  display(): void {
    console.log(`Displaying image: ${this.url}`);
  }

  getUrl(): string {
    return this.url;
  }
}

// 代理
class ImageProxy implements ImageInterface {
  private realImage: RealImage | null = null;
  private url: string;
  private cachedImage: any = null;

  constructor(url: string) {
    this.url = url;
  }

  display(): void {
    if (this.cachedImage) {
      console.log(`[Proxy] Displaying cached image: ${this.url}`);
      return;
    }

    if (this.realImage === null) {
      console.log(`[Proxy] Creating real image: ${this.url}`);
      this.realImage = new RealImage(this.url);
    }

    this.realImage.display();

    // 缓存显示结果
    this.cachedImage = { /* 缓存数据 */ };
  }

  getUrl(): string {
    return this.url;
  }
}

// 客户端代码
function clientCode() {
  const imageProxy = new ImageProxy("https://example.com/large-image.jpg");

  // 首次加载 - 会创建真实对象
  imageProxy.display();

  // 再次加载 - 使用缓存
  imageProxy.display();
}
```

## 🎯 使用场景

- **图片懒加载**：当图片在视口内才真正加载图片资源
- **缓存系统**：缓存计算结果或HTTP请求，避免重复计算或网络请求
- **权限控制**：在访问敏感数据前检查用户权限
- **数据验证**：在数据传入真实对象前进行验证
- **日志记录**：记录对真实对象的所有操作
- **远程服务代理**：封装远程服务调用的复杂性

## 📝 实现要点

1. **接口保持一致**：代理与真实对象必须实现相同的接口
2. **透明性**：客户端无需知道是在使用代理还是真实对象
3. **委托转发**：代理根据需要转发请求给真实对象
4. **额外职责**：代理可以附加额外职责，但应该保持单一职责原则
5. **创建时机**：可以在代理类内部按需创建真实对象

## ⚠️ 注意事项

1. **性能影响**：过度使用代理可能会影响性能
2. **复杂度增加**：添加代理层会增加系统复杂性
3. **延迟增加**：某些类型的代理可能会增加操作延迟
4. **调试困难**：代理可能会使调试更加困难
5. **同步问题**：在多线程环境中，需要处理代理与真实对象之间的同步问题

## 🔄 与其他模式的关系

- **装饰器模式**：与代理模式有相似结构，但装饰器侧重于动态添加职责，代理控制访问。
- **适配器模式**：适配器提供不同的接口，而代理提供相同的接口。
- **外观模式**：外观为子系统提供简化接口，代理控制对对象的访问。
- **委托模式**：代理模式是一种特殊的委托模式。

---

> 代理模式通过添加一个控制层来管理对核心对象的访问，是一种应用广泛且功能强大的结构型设计模式。