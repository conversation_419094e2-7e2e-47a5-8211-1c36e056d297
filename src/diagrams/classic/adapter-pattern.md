# 适配器模式架构图

## 📋 模式概述

适配器模式（Adapter Pattern）是一种结构型设计模式，它允许接口不兼容的类一起工作。适配器将一个类的接口转换成客户希望的另一个接口。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 需要集成第三方服务，但接口格式不统一
- 遗留系统与新系统的接口不兼容
- 不同数据源的数据格式需要统一处理

## 🏗️ 架构图

```mermaid
classDiagram
    class PaymentTarget {
        <<interface>>
        +processPayment(amount, currency) PaymentResult
        +getPaymentStatus(transactionId) PaymentStatus
        +refund(transactionId, amount) RefundResult
    }
    
    class AlipayService {
        +pay(money, coin) AlipayResult
        +queryOrder(tradeNo) AlipayStatus
        +refundOrder(tradeNo, amount) AlipayRefund
    }
    
    class WechatPayService {
        +createOrder(orderAmount, currencyType) WechatResult
        +queryPayment(transactionId) WechatStatus
        +applyRefund(transactionId, refundFee) WechatRefund
    }
    
    class UnionPayService {
        +submitPayment(payAmount, currencyCode) UnionResult
        +queryTransaction(orderId) UnionStatus
        +refundTransaction(orderId, refundAmt) UnionRefund
    }
    
    class AlipayAdapter {
        -alipayService: AlipayService
        +processPayment(amount, currency) PaymentResult
        +getPaymentStatus(transactionId) PaymentStatus
        +refund(transactionId, amount) RefundResult
    }
    
    class WechatPayAdapter {
        -wechatService: WechatPayService
        +processPayment(amount, currency) PaymentResult
        +getPaymentStatus(transactionId) PaymentStatus
        +refund(transactionId, amount) RefundResult
    }
    
    class UnionPayAdapter {
        -unionPayService: UnionPayService
        +processPayment(amount, currency) PaymentResult
        +getPaymentStatus(transactionId) PaymentStatus
        +refund(transactionId, amount) RefundResult
    }
    
    class Client {
        +usePayment(adapter: PaymentTarget)
    }
    
    PaymentTarget <|.. AlipayAdapter
    PaymentTarget <|.. WechatPayAdapter
    PaymentTarget <|.. UnionPayAdapter
    
    AlipayAdapter --> AlipayService : adapts
    WechatPayAdapter --> WechatPayService : adapts
    UnionPayAdapter --> UnionPayService : adapts
    
    Client --> PaymentTarget : uses
    
    note for PaymentTarget "统一的支付接口\n客户端期望的接口"
    note for AlipayService "支付宝原始接口\n不兼容的接口"
    note for WechatPayService "微信支付原始接口\n不兼容的接口"
    note for AlipayAdapter "适配器\n转换支付宝接口"
```

## 🔍 组件说明

### 目标接口 (Target Interface)
- **PaymentTarget**: 客户端期望的统一支付接口
- 定义了标准的支付操作：支付、查询状态、退款

### 被适配者 (Adaptee)
- **AlipayService**: 支付宝的原始接口，方法名和参数格式与目标接口不同
- **WechatPayService**: 微信支付的原始接口，返回格式完全不同
- **UnionPayService**: 银联支付的原始接口，使用不同的状态码体系

### 适配器 (Adapter)
- **AlipayAdapter**: 将支付宝接口适配为统一接口
- **WechatPayAdapter**: 将微信支付接口适配为统一接口
- **UnionPayAdapter**: 将银联接口适配为统一接口

### 客户端 (Client)
- 只依赖于 PaymentTarget 接口
- 无需了解具体的第三方支付实现细节

## 📊 接口对比

### 原始接口差异

#### 支付宝接口
```typescript
// 支付方法
pay(money: number, coin: string): {
  code: number;
  data: { 
    trade_no: string; 
    msg: string; 
    fee_amount: number 
  };
  timestamp: number;
}

// 查询方法
queryOrder(tradeNo: string): { 
  code: number; 
  status: string 
}
```

#### 微信支付接口
```typescript
// 支付方法
createOrder(orderAmount: number, currencyType: string): {
  result_code: string;
  transaction_id: string;
  return_msg: string;
  total_fee: number;
  create_time: string;
}

// 查询方法
queryPayment(transactionId: string): { 
  result_code: string; 
  trade_state: string 
}
```

#### 银联接口
```typescript
// 支付方法
submitPayment(payAmount: number, currencyCode: string): {
  respCode: string;
  orderId: string;
  respMsg: string;
  settleAmt: number;
  settleDate: string;
}

// 查询方法
queryTransaction(orderId: string): { 
  respCode: string; 
  origRespCode: string 
}
```

### 统一后的接口

```typescript
interface PaymentTarget {
  // 统一的支付方法
  processPayment(amount: number, currency: string): PaymentResult;
  
  // 统一的查询方法
  getPaymentStatus(transactionId: string): PaymentStatus;
  
  // 统一的退款方法
  refund(transactionId: string, amount: number): RefundResult;
}

interface PaymentResult {
  success: boolean;
  transactionId: string;
  message: string;
  timestamp: Date;
  fee: number;
}

enum PaymentStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}
```

## 🔄 数据转换示例

### 支付宝适配器的转换逻辑

```typescript
class AlipayAdapter implements PaymentTarget {
  processPayment(amount: number, currency: string): PaymentResult {
    // 调用支付宝原始接口
    const result = this.alipayService.pay(amount, currency);
    
    // 转换为统一格式
    return {
      success: result.code === 200,           // 200 → true/false
      transactionId: result.data.trade_no,    // trade_no → transactionId
      message: result.data.msg,               // msg → message
      timestamp: new Date(result.timestamp),  // timestamp → Date
      fee: result.data.fee_amount             // fee_amount → fee
    };
  }
}
```

### 微信支付适配器的转换逻辑

```typescript
class WechatPayAdapter implements PaymentTarget {
  processPayment(amount: number, currency: string): PaymentResult {
    // 调用微信支付原始接口
    const result = this.wechatService.createOrder(amount, currency);
    
    // 转换为统一格式
    return {
      success: result.result_code === 'SUCCESS',  // 'SUCCESS' → true/false
      transactionId: result.transaction_id,       // transaction_id → transactionId
      message: result.return_msg,                 // return_msg → message
      timestamp: new Date(result.create_time),    // create_time → Date
      fee: result.total_fee                       // total_fee → fee
    };
  }
}
```

## 💡 设计优势

1. **接口统一**: 客户端只需要学习一套接口，就能处理多种支付方式
2. **松耦合**: 客户端代码与具体的第三方服务解耦
3. **易扩展**: 添加新的支付方式只需要实现新的适配器
4. **代码复用**: 客户端的业务逻辑可以复用于所有支付方式
5. **维护性**: 第三方接口变更只影响对应的适配器

## 🎯 使用场景

- **第三方服务集成**: 统一不同供应商的API接口
- **系统迁移**: 新旧系统接口的兼容处理
- **数据格式转换**: 统一不同数据源的格式
- **遗留代码适配**: 让老代码适应新的接口标准

## 📝 实现要点

1. **明确目标接口**: 设计客户端真正需要的接口
2. **分析差异**: 理解各个被适配接口的差异点
3. **数据映射**: 建立原始数据到目标数据的映射关系
4. **错误处理**: 统一不同系统的错误处理机制
5. **性能考虑**: 避免不必要的数据转换开销

---

> 这个架构图展示了适配器模式在实际项目中的应用，通过统一不同第三方支付接口，大大简化了客户端代码的复杂度。
