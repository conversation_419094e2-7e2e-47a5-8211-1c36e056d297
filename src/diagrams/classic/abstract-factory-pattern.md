# 抽象工厂模式架构图

## 📋 模式概述

抽象工厂模式（Abstract Factory Pattern）是一种创建型设计模式，它提供一个创建一系列相关或相互依赖对象的接口，而无需指定它们具体的类。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 需要创建一系列相关的产品对象
- 系统需要支持多个产品族
- 需要确保产品族的一致性
- 希望隐藏产品的具体实现

## 🏗️ 架构图

```mermaid
classDiagram
    class UIComponentFactory {
        <<interface>>
        +createButton() Button
        +createInput() Input
        +createCard() Card
        +getThemeName() string
    }
    
    class Button {
        <<interface>>
        +render() string
        +getStyle() string
        +getType() string
    }
    
    class Input {
        <<interface>>
        +render() string
        +getStyle() string
        +getType() string
    }
    
    class Card {
        <<interface>>
        +render() string
        +getStyle() string
        +getType() string
    }
    
    class ModernThemeFactory {
        +createButton() Button
        +createInput() Input
        +createCard() Card
        +getThemeName() string
    }
    
    class ClassicThemeFactory {
        +createButton() Button
        +createInput() Input
        +createCard() Card
        +getThemeName() string
    }
    
    class DarkThemeFactory {
        +createButton() Button
        +createInput() Input
        +createCard() Card
        +getThemeName() string
    }
    
    class ModernButton {
        +render() string
        +getStyle() string
        +getType() string
    }
    
    class ModernInput {
        +render() string
        +getStyle() string
        +getType() string
    }
    
    class ModernCard {
        +render() string
        +getStyle() string
        +getType() string
    }
    
    class ClassicButton {
        +render() string
        +getStyle() string
        +getType() string
    }
    
    class ClassicInput {
        +render() string
        +getStyle() string
        +getType() string
    }
    
    class ClassicCard {
        +render() string
        +getStyle() string
        +getType() string
    }
    
    class DarkButton {
        +render() string
        +getStyle() string
        +getType() string
    }
    
    class DarkInput {
        +render() string
        +getStyle() string
        +getType() string
    }
    
    class DarkCard {
        +render() string
        +getStyle() string
        +getType() string
    }
    
    class ThemeFactoryCreator {
        +createFactory(theme: string) UIComponentFactory
        +getSupportedThemes() string[]
        +getThemeDisplayNames() Record~string, string~
    }
    
    UIComponentFactory <|-- ModernThemeFactory
    UIComponentFactory <|-- ClassicThemeFactory
    UIComponentFactory <|-- DarkThemeFactory
    
    Button <|-- ModernButton
    Button <|-- ClassicButton
    Button <|-- DarkButton
    
    Input <|-- ModernInput
    Input <|-- ClassicInput
    Input <|-- DarkInput
    
    Card <|-- ModernCard
    Card <|-- ClassicCard
    Card <|-- DarkCard
    
    ModernThemeFactory --> ModernButton : creates
    ModernThemeFactory --> ModernInput : creates
    ModernThemeFactory --> ModernCard : creates
    
    ClassicThemeFactory --> ClassicButton : creates
    ClassicThemeFactory --> ClassicInput : creates
    ClassicThemeFactory --> ClassicCard : creates
    
    DarkThemeFactory --> DarkButton : creates
    DarkThemeFactory --> DarkInput : creates
    DarkThemeFactory --> DarkCard : creates
    
    ThemeFactoryCreator --> UIComponentFactory : creates
```

## 🔄 时序图

```mermaid
sequenceDiagram
    participant Client
    participant Creator as ThemeFactoryCreator
    participant Factory as ModernThemeFactory
    participant Button as ModernButton
    participant Input as ModernInput
    participant Card as ModernCard
    
    Client->>Creator: createFactory("modern")
    Creator->>Factory: new ModernThemeFactory()
    Creator-->>Client: factory
    
    Client->>Factory: createButton()
    Factory->>Button: new ModernButton()
    Factory-->>Client: button
    
    Client->>Factory: createInput()
    Factory->>Input: new ModernInput()
    Factory-->>Client: input
    
    Client->>Factory: createCard()
    Factory->>Card: new ModernCard()
    Factory-->>Client: card
    
    Client->>Button: render()
    Button-->>Client: HTML string
    
    Client->>Input: getStyle()
    Input-->>Client: CSS styles
    
    Client->>Card: getType()
    Card-->>Client: component type
```

## 🎨 产品族关系图

```mermaid
graph TB
    subgraph "现代主题产品族"
        MB[ModernButton<br/>现代按钮]
        MI[ModernInput<br/>现代输入框]
        MC[ModernCard<br/>现代卡片]
    end
    
    subgraph "经典主题产品族"
        CB[ClassicButton<br/>经典按钮]
        CI[ClassicInput<br/>经典输入框]
        CC[ClassicCard<br/>经典卡片]
    end
    
    subgraph "暗黑主题产品族"
        DB[DarkButton<br/>暗黑按钮]
        DI[DarkInput<br/>暗黑输入框]
        DC[DarkCard<br/>暗黑卡片]
    end
    
    MF[ModernThemeFactory] --> MB
    MF --> MI
    MF --> MC
    
    CF[ClassicThemeFactory] --> CB
    CF --> CI
    CF --> CC
    
    DF[DarkThemeFactory] --> DB
    DF --> DI
    DF --> DC
    
    style MB fill:#667eea
    style MI fill:#667eea
    style MC fill:#667eea
    
    style CB fill:#52c41a
    style CI fill:#52c41a
    style CC fill:#52c41a
    
    style DB fill:#722ed1
    style DI fill:#722ed1
    style DC fill:#722ed1
```

## 💡 核心概念

### 抽象工厂 (Abstract Factory)
- `UIComponentFactory`: 定义创建产品族的接口

### 具体工厂 (Concrete Factory)
- `ModernThemeFactory`: 创建现代主题产品族
- `ClassicThemeFactory`: 创建经典主题产品族
- `DarkThemeFactory`: 创建暗黑主题产品族

### 抽象产品 (Abstract Product)
- `Button`: 按钮组件接口
- `Input`: 输入框组件接口
- `Card`: 卡片组件接口

### 具体产品 (Concrete Product)
- 每个主题的具体组件实现

## ✅ 优点

1. **产品族一致性**: 确保同一主题下的组件风格统一
2. **易于扩展**: 新增主题只需添加新的工厂和产品
3. **客户端解耦**: 客户端不依赖具体产品类
4. **符合开闭原则**: 对扩展开放，对修改关闭

## ❌ 缺点

1. **难以支持新产品**: 添加新产品类型需要修改所有工厂
2. **类数量增加**: 每个产品族都需要对应的具体产品类
3. **复杂性增加**: 增加了系统的抽象性和理解难度

## 🔧 适用场景

- UI组件库的主题系统
- 跨平台应用开发
- 数据库驱动程序
- 操作系统相关的API封装
- 游戏中的不同风格资源

## 📝 实现要点

1. **确定产品族**: 明确哪些产品属于同一族
2. **抽象接口**: 为每个产品定义抽象接口
3. **工厂接口**: 定义创建整个产品族的工厂接口
4. **具体实现**: 为每个产品族实现具体的工厂和产品
5. **工厂创建器**: 提供统一的工厂创建入口
