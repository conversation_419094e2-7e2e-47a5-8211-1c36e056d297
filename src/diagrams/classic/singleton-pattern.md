# 单例模式架构图

## 📋 模式概述

单例模式（Singleton Pattern）是一种创建型设计模式，它确保一个类只有一个实例，并提供一个全局访问点来获取该实例。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 需要全局唯一的配置管理器
- 数据库连接池只需要一个实例
- 日志记录器需要全局统一
- 缓存管理器避免重复创建

## 🏗️ 架构图

```mermaid
classDiagram
    class Logger {
        -static instance: Logger
        -logs: string[]
        -id: string
        -constructor()
        +static getInstance(): Logger
        +log(message: string): void
        +getLogs(): string[]
        +clearLogs(): void
        +getId(): string
    }
    
    class Client1 {
        +useLogger(): void
    }
    
    class Client2 {
        +useLogger(): void
    }
    
    class Client3 {
        +useLogger(): void
    }
    
    Client1 --> Logger : getInstance()
    Client2 --> Logger : getInstance()
    Client3 --> Logger : getInstance()
    
    note for Logger "私有构造函数\n静态实例变量\n全局访问点"
    note for Client1 "所有客户端获取的\n都是同一个实例"
```

## 🔍 组件说明

### 单例类 (Singleton Class)
- **Logger**: 日志记录器单例类
  - `static instance`: 静态实例变量，存储唯一实例
  - `constructor()`: 私有构造函数，防止外部直接创建实例
  - `getInstance()`: 静态方法，提供全局访问点
  - `logs[]`: 实例数据，所有客户端共享
  - `id`: 实例标识符，证明实例唯一性

### 客户端 (Clients)
- **Client1, Client2, Client3**: 不同的客户端
- 都通过 `getInstance()` 获取同一个实例
- 共享同一个日志记录器的状态

## 📊 实现方式对比

### 懒汉式（Lazy Initialization）
```typescript
class Logger {
  private static instance: Logger | null = null;
  private logs: string[] = [];
  private id: string;

  private constructor() {
    this.id = Math.random().toString(36).slice(2, 11);
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();  // 第一次调用时创建
    }
    return Logger.instance;
  }
}
```

### 饿汉式（Eager Initialization）
```typescript
class Logger {
  private static instance: Logger = new Logger();  // 类加载时创建
  private logs: string[] = [];
  private id: string;

  private constructor() {
    this.id = Math.random().toString(36).slice(2, 11);
  }

  public static getInstance(): Logger {
    return Logger.instance;
  }
}
```

## 🔄 实例创建流程

```mermaid
sequenceDiagram
    participant C1 as Client1
    participant C2 as Client2
    participant L as Logger
    
    Note over L: instance = null
    
    C1->>L: getInstance()
    alt instance is null
        L->>L: new Logger()
        Note over L: instance created
    end
    L-->>C1: return instance
    
    C2->>L: getInstance()
    alt instance exists
        Note over L: return existing instance
    end
    L-->>C2: return same instance
    
    Note over C1,C2: Both clients have same instance
```

## 💡 设计优势

1. **全局唯一性**: 确保系统中只有一个实例
2. **延迟初始化**: 懒汉式实现可以延迟到需要时才创建
3. **全局访问**: 提供全局访问点，方便使用
4. **资源控制**: 避免重复创建，节省系统资源
5. **状态共享**: 所有客户端共享同一个实例的状态

## ⚠️ 注意事项

1. **线程安全**: 在多线程环境下需要考虑线程安全
2. **测试困难**: 单例可能导致单元测试困难
3. **违反单一职责**: 单例类既要管理实例又要处理业务逻辑
4. **隐藏依赖**: 使用单例可能隐藏类之间的依赖关系

## 🎯 使用场景

- **日志记录器**: 全局统一的日志管理
- **配置管理器**: 应用程序配置的统一管理
- **数据库连接池**: 数据库连接的统一管理
- **缓存管理器**: 缓存数据的统一管理
- **线程池**: 线程资源的统一管理

## 📝 实现要点

1. **私有构造函数**: 防止外部直接创建实例
2. **静态实例变量**: 存储唯一实例
3. **静态访问方法**: 提供全局访问点
4. **线程安全**: 考虑多线程环境下的安全性
5. **延迟初始化**: 根据需要选择创建时机

## 🔧 TypeScript 实现

### 基础实现
```typescript
class Logger {
  private static instance: Logger | null = null;
  private logs: string[] = [];
  private id: string;

  private constructor() {
    this.id = Math.random().toString(36).slice(2, 11);
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public log(message: string): void {
    const timestamp = new Date().toLocaleTimeString();
    this.logs.push(`[${timestamp}] ${message}`);
  }

  public getLogs(): string[] {
    return [...this.logs];
  }

  public clearLogs(): void {
    this.logs = [];
  }

  public getId(): string {
    return this.id;
  }
}
```

### 使用示例
```typescript
// 获取实例
const logger1 = Logger.getInstance();
const logger2 = Logger.getInstance();

// 验证是同一个实例
console.log(logger1 === logger2); // true
console.log(logger1.getId() === logger2.getId()); // true

// 共享状态
logger1.log('第一条日志');
logger2.log('第二条日志');

console.log(logger1.getLogs()); // ['[时间] 第一条日志', '[时间] 第二条日志']
console.log(logger2.getLogs()); // 相同的日志列表
```

## 🚀 扩展变体

### 注册式单例
```typescript
class SingletonRegistry {
  private static instances: Map<string, any> = new Map();

  public static getInstance<T>(key: string, creator: () => T): T {
    if (!this.instances.has(key)) {
      this.instances.set(key, creator());
    }
    return this.instances.get(key);
  }
}
```

### 枚举单例（Java 风格的 TypeScript 实现）
```typescript
enum LoggerSingleton {
  INSTANCE
}

// 扩展枚举（TypeScript 中的替代方案）
namespace LoggerSingleton {
  export function log(message: string): void {
    // 实现日志逻辑
  }
}
```

---

> 单例模式是最简单但也是最容易被误用的设计模式之一。在使用时要权衡其带来的便利性和可能的问题。
