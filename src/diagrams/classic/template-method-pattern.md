# 模板方法模式架构图解

> **模式名称**: Template Method Pattern (模板方法模式)  
> **模式类型**: 行为型模式  
> **复杂度**: ⭐⭐  
> **使用频率**: ⭐⭐⭐⭐

## 📋 模式概述

模板方法模式定义一个操作中算法的骨架，而将一些步骤延迟到子类中。模板方法使得子类可以不改变算法的结构即可重定义算法的某些特定步骤。

## 🏗️ 核心架构

### 类图结构

```mermaid
classDiagram
    class DataProcessor {
        <<abstract>>
        +processData(data: any[]) ProcessResult
        #validateData(data: any[]) boolean
        #preprocessData(data: any[]) any[]
        #processCore(data: any[])* any[]
        #postprocessData(data: any[]) any[]
        #shouldSaveResult() boolean
        #saveResult(data: any[]) void
        #getProcessorType()* string
    }
    
    class UserDataProcessor {
        #processCore(data: any[]) any[]
        #getProcessorType() string
        #validateData(data: any[]) boolean
        #preprocessData(data: any[]) any[]
        #shouldSaveResult() boolean
        #saveResult(data: any[]) void
    }
    
    class OrderDataProcessor {
        -taxRate: number
        #processCore(data: any[]) any[]
        #getProcessorType() string
        #postprocessData(data: any[]) any[]
        #shouldSaveResult() boolean
        #saveResult(data: any[]) void
    }
    
    class LogDataProcessor {
        -logLevel: string
        #processCore(data: any[]) any[]
        #getProcessorType() string
        #shouldSaveResult() boolean
    }
    
    class ProcessResult {
        +originalCount: number
        +processedCount: number
        +errors: string[]
        +processedData: any[]
        +processingTime: number
        +processorType: string
    }
    
    DataProcessor <|-- UserDataProcessor
    DataProcessor <|-- OrderDataProcessor
    DataProcessor <|-- LogDataProcessor
    DataProcessor ..> ProcessResult : creates
```

### 算法骨架流程

```mermaid
flowchart TD
    A[开始处理] --> B[1. 验证数据<br/>validateData()]
    B --> C{验证通过?}
    C -->|否| D[抛出异常]
    C -->|是| E[2. 预处理数据<br/>preprocessData()]
    E --> F[3. 核心处理<br/>processCore() - 抽象方法]
    F --> G[4. 后处理数据<br/>postprocessData()]
    G --> H{需要保存结果?<br/>shouldSaveResult()}
    H -->|是| I[5. 保存结果<br/>saveResult()]
    H -->|否| J[跳过保存]
    I --> K[返回处理结果]
    J --> K
    D --> L[返回错误结果]
    K --> M[结束处理]
    L --> M
    
    style F fill:#ffcccc
    style H fill:#ffffcc
```

## 📊 数据处理流程对比

### 用户数据处理器流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant UDP as UserDataProcessor
    participant Base as DataProcessor
    
    Client->>UDP: processData(userData)
    UDP->>Base: processData() - 模板方法
    
    Note over Base: 1. 验证数据
    Base->>UDP: validateData(data)
    UDP-->>Base: true (检查id, name字段)
    
    Note over Base: 2. 预处理数据
    Base->>UDP: preprocessData(data)
    UDP-->>Base: 去重后的数据
    
    Note over Base: 3. 核心处理
    Base->>UDP: processCore(data)
    UDP-->>Base: 格式化的用户数据
    
    Note over Base: 4. 后处理数据
    Base->>UDP: postprocessData(data)
    UDP-->>Base: 原数据(默认实现)
    
    Note over Base: 5. 检查是否保存
    Base->>UDP: shouldSaveResult()
    UDP-->>Base: true
    
    Note over Base: 6. 保存结果
    Base->>UDP: saveResult(data)
    UDP-->>Base: 保存到数据库
    
    Base-->>UDP: ProcessResult
    UDP-->>Client: ProcessResult
```

### 订单数据处理器流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant ODP as OrderDataProcessor
    participant Base as DataProcessor
    
    Client->>ODP: processData(orderData)
    ODP->>Base: processData() - 模板方法
    
    Note over Base: 1. 验证数据
    Base->>ODP: validateData(data)
    ODP-->>Base: true (检查订单字段)
    
    Note over Base: 2. 预处理数据
    Base->>Base: preprocessData(data) - 默认实现
    
    Note over Base: 3. 核心处理
    Base->>ODP: processCore(data)
    ODP-->>Base: 计算税费和总额
    
    Note over Base: 4. 后处理数据
    Base->>ODP: postprocessData(data)
    ODP-->>Base: 按金额排序的数据
    
    Note over Base: 5. 检查是否保存
    Base->>ODP: shouldSaveResult()
    ODP-->>Base: true
    
    Note over Base: 6. 保存结果
    Base->>ODP: saveResult(data)
    ODP-->>Base: 保存订单数据
    
    Base-->>ODP: ProcessResult
    ODP-->>Client: ProcessResult
```

## 🔧 方法类型分析

### 方法分类图

```mermaid
graph TB
    subgraph "抽象类 DataProcessor"
        TM[模板方法<br/>processData()]
        
        subgraph "具体方法 - 默认实现"
            CM1[validateData()]
            CM2[preprocessData()]
            CM3[postprocessData()]
        end
        
        subgraph "抽象方法 - 必须实现"
            AM1[processCore()]
            AM2[getProcessorType()]
        end
        
        subgraph "钩子方法 - 可选重写"
            HM1[shouldSaveResult()]
            HM2[saveResult()]
        end
    end
    
    TM --> CM1
    TM --> CM2
    TM --> AM1
    TM --> CM3
    TM --> HM1
    TM --> HM2
    
    style TM fill:#e1f5fe
    style AM1 fill:#ffebee
    style AM2 fill:#ffebee
    style HM1 fill:#fff3e0
    style HM2 fill:#fff3e0
```

### 实现策略对比

```mermaid
graph LR
    subgraph "用户数据处理器"
        U1[验证: 检查必要字段]
        U2[预处理: 去重]
        U3[核心: 格式化用户信息]
        U4[后处理: 默认]
        U5[保存: 数据库]
    end
    
    subgraph "订单数据处理器"
        O1[验证: 检查订单字段]
        O2[预处理: 默认]
        O3[核心: 计算税费总额]
        O4[后处理: 按金额排序]
        O5[保存: 订单系统]
    end
    
    subgraph "日志数据处理器"
        L1[验证: 检查日志格式]
        L2[预处理: 默认]
        L3[核心: 按级别过滤]
        L4[后处理: 按时间排序]
        L5[保存: 不保存]
    end
    
    style U3 fill:#ffcccc
    style O3 fill:#ffcccc
    style L3 fill:#ffcccc
    style U5 fill:#ffffcc
    style O5 fill:#ffffcc
    style L5 fill:#cccccc
```

## 🎯 应用场景

### 1. 数据处理管道
- **场景**: 不同类型数据的统一处理流程
- **优势**: 算法结构固定，具体步骤可定制
- **示例**: ETL数据处理、文件格式转换

### 2. 测试框架
- **场景**: 测试用例的执行流程
- **优势**: 统一的测试生命周期管理
- **示例**: 单元测试、集成测试框架

### 3. 生命周期管理
- **场景**: 组件或服务的生命周期控制
- **优势**: 标准化的初始化和清理流程
- **示例**: Spring Bean生命周期、React组件生命周期

## 🔄 与策略模式对比

```mermaid
graph TB
    subgraph "模板方法模式"
        TM1[抽象类定义算法骨架]
        TM2[子类实现具体步骤]
        TM3[继承关系]
        TM4[编译时确定算法]
        TM1 --> TM2 --> TM3 --> TM4
    end
    
    subgraph "策略模式"
        ST1[接口定义算法规范]
        ST2[具体策略实现算法]
        ST3[组合关系]
        ST4[运行时切换算法]
        ST1 --> ST2 --> ST3 --> ST4
    end
    
    style TM3 fill:#ffcccc
    style ST3 fill:#ccffcc
```

## ✅ 优点

1. **代码复用**: 公共代码在父类中实现，避免重复
2. **控制反转**: 父类控制算法结构，子类实现细节
3. **扩展性**: 易于添加新的算法实现
4. **一致性**: 保证算法结构的一致性
5. **开闭原则**: 对扩展开放，对修改封闭

## ❌ 缺点

1. **类数量增加**: 每个变体都需要一个子类
2. **继承限制**: 受到单继承的限制
3. **理解复杂**: 需要理解整个类层次结构
4. **维护成本**: 算法骨架的修改影响所有子类

## 🔗 相关模式

- **策略模式**: 都处理算法变化，但实现方式不同
- **工厂方法模式**: 常用于创建模板方法中使用的对象
- **观察者模式**: 模板方法中可能需要通知其他对象

## 💡 最佳实践

1. **合理抽象**: 找到合适的抽象级别，既不过于具体也不过于抽象
2. **钩子方法**: 使用钩子方法提供可选的扩展点
3. **文档说明**: 清楚地文档化每个抽象方法的职责
4. **异常处理**: 在模板方法中统一处理异常
5. **性能考虑**: 避免在模板方法中进行重复的昂贵操作
