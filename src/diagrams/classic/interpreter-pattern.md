# 解释器模式架构图解

> **模式名称**: Interpret<PERSON> <PERSON>tern (解释器模式)  
> **模式类型**: 行为型模式  
> **复杂度**: ⭐⭐⭐⭐  
> **使用频率**: ⭐⭐

## 📋 模式概述

解释器模式给定一个语言，定义它的文法的一种表示，并定义一个解释器，这个解释器使用该表示来解释语言中的句子。

## 🏗️ 核心架构

### 类图结构

```mermaid
classDiagram
    class Context {
        -variables: Map~string, number~
        -calculationHistory: string[]
        +setVariable(name: string, value: number) void
        +getVariable(name: string) number
        +hasVariable(name: string) boolean
        +getVariables() Map~string, number~
        +addToHistory(expression: string) void
        +getHistory() string[]
        +clearHistory() void
        +clearVariables() void
    }
    
    class Expression {
        <<interface>>
        +interpret(context: Context) number
        +toString() string
    }
    
    class NumberExpression {
        -value: number
        +interpret(context: Context) number
        +toString() string
    }
    
    class VariableExpression {
        -name: string
        +interpret(context: Context) number
        +toString() string
    }
    
    class AddExpression {
        -left: Expression
        -right: Expression
        +interpret(context: Context) number
        +toString() string
    }
    
    class SubtractExpression {
        -left: Expression
        -right: Expression
        +interpret(context: Context) number
        +toString() string
    }
    
    class MultiplyExpression {
        -left: Expression
        -right: Expression
        +interpret(context: Context) number
        +toString() string
    }
    
    class DivideExpression {
        -left: Expression
        -right: Expression
        +interpret(context: Context) number
        +toString() string
    }
    
    class ExpressionCalculator {
        -context: Context
        +setVariable(name: string, value: number) void
        +getVariable(name: string) number
        +calculate(expression: string) number
        +getHistory() string[]
        +clearHistory() void
        +validateExpression(expression: string) ValidationResult
    }
    
    class Lexer {
        -input: string
        -position: number
        +getNextToken() Token
        -readNumber() Token
        -readVariable() Token
    }
    
    class Parser {
        -tokens: Token[]
        -position: number
        +parse() Expression
        -parseExpression() Expression
        -parseTerm() Expression
        -parseFactor() Expression
    }
    
    class Token {
        +type: TokenType
        +value: string
    }
    
    Expression <|.. NumberExpression
    Expression <|.. VariableExpression
    Expression <|.. AddExpression
    Expression <|.. SubtractExpression
    Expression <|.. MultiplyExpression
    Expression <|.. DivideExpression
    AddExpression --> Expression : left/right
    SubtractExpression --> Expression : left/right
    MultiplyExpression --> Expression : left/right
    DivideExpression --> Expression : left/right
    ExpressionCalculator --> Context : uses
    ExpressionCalculator --> Lexer : uses
    ExpressionCalculator --> Parser : uses
    Parser --> Expression : creates
    Lexer --> Token : creates
```

### 表达式树结构

```mermaid
graph TB
    subgraph "表达式: (x + 3) * (y - 2)"
        Root[MultiplyExpression]
        
        Left[AddExpression]
        Right[SubtractExpression]
        
        L1[VariableExpression<br/>x]
        L2[NumberExpression<br/>3]
        
        R1[VariableExpression<br/>y]
        R2[NumberExpression<br/>2]
        
        Root --> Left
        Root --> Right
        Left --> L1
        Left --> L2
        Right --> R1
        Right --> R2
    end
    
    subgraph "解释执行过程"
        E1[Root.interpret(context)]
        E2[Left.interpret(context) = x + 3]
        E3[Right.interpret(context) = y - 2]
        E4[返回: (x + 3) * (y - 2)]
        
        E1 --> E2
        E1 --> E3
        E2 --> E4
        E3 --> E4
    end
    
    style Root fill:#e3f2fd
    style Left fill:#f3e5f5
    style Right fill:#f3e5f5
```

## 🔤 词法分析和语法分析

### 词法分析流程

```mermaid
flowchart TD
    A[输入字符串: "x + 3 * y"] --> B[Lexer 词法分析器]
    B --> C{读取字符}
    C -->|字母| D[识别变量名]
    C -->|数字| E[识别数字]
    C -->|运算符| F[识别操作符]
    C -->|空格| G[跳过空格]
    C -->|结束| H[返回null]
    
    D --> I[Token: VARIABLE, "x"]
    E --> J[Token: NUMBER, "3"]
    F --> K[Token: PLUS/MULTIPLY等]
    G --> C
    
    I --> L[Token序列]
    J --> L
    K --> L
    H --> M[词法分析完成]
    L --> M
    
    style B fill:#e3f2fd
    style L fill:#f3e5f5
```

### 语法分析树构建

```mermaid
sequenceDiagram
    participant Input as 输入: "x + 3 * y"
    participant Lexer as 词法分析器
    participant Parser as 语法分析器
    participant AST as 抽象语法树
    
    Input->>Lexer: "x + 3 * y"
    Lexer->>Parser: [VARIABLE:"x", PLUS:"+", NUMBER:"3", MULTIPLY:"*", VARIABLE:"y"]
    
    Parser->>Parser: parseExpression()
    Parser->>Parser: parseTerm() -> x
    Parser->>AST: VariableExpression("x")
    
    Parser->>Parser: 发现 PLUS
    Parser->>Parser: parseTerm() -> 3 * y
    Parser->>Parser: parseFactor() -> 3
    Parser->>AST: NumberExpression(3)
    
    Parser->>Parser: 发现 MULTIPLY
    Parser->>Parser: parseFactor() -> y
    Parser->>AST: VariableExpression("y")
    
    Parser->>AST: MultiplyExpression(3, y)
    Parser->>AST: AddExpression(x, 3*y)
    
    AST-->>Parser: 完整表达式树
    Parser-->>Input: 解析完成
```

## 🧮 数学表达式计算器

### 运算符优先级处理

```mermaid
graph TB
    subgraph "表达式: 2 + 3 * 4 - 1"
        P1[parseExpression<br/>处理 + -]
        P2[parseTerm<br/>处理 * /]
        P3[parseFactor<br/>处理数字、变量、括号]
        
        P1 --> P2
        P2 --> P3
    end
    
    subgraph "构建过程"
        S1[2] --> S2[2 + (3 * 4)]
        S2 --> S3[(2 + (3 * 4)) - 1]
        
        S4[优先级: * / > + -]
        S5[结合性: 左结合]
        
        S3 --> S4
        S4 --> S5
    end
    
    subgraph "最终树结构"
        Root2[SubtractExpression]
        Left2[AddExpression]
        Right2[NumberExpression: 1]
        LL[NumberExpression: 2]
        LR[MultiplyExpression]
        LRL[NumberExpression: 3]
        LRR[NumberExpression: 4]
        
        Root2 --> Left2
        Root2 --> Right2
        Left2 --> LL
        Left2 --> LR
        LR --> LRL
        LR --> LRR
    end
    
    style P2 fill:#ffcccc
    style LR fill:#ffcccc
```

### 上下文变量管理

```mermaid
stateDiagram-v2
    [*] --> Empty : 创建上下文
    Empty --> HasVariables : 设置变量
    HasVariables --> HasVariables : 添加/更新变量
    HasVariables --> Calculating : 开始计算
    Calculating --> Calculating : 查找变量值
    Calculating --> HasVariables : 计算完成
    HasVariables --> Empty : 清空变量
    Empty --> [*] : 销毁上下文
    
    state HasVariables {
        [*] --> ValidState
        ValidState --> ValidState : setVariable()
        ValidState --> ValidState : getVariable()
        ValidState --> ErrorState : 访问未定义变量
        ErrorState --> ValidState : 定义缺失变量
    }
    
    state Calculating {
        [*] --> Interpreting
        Interpreting --> Interpreting : 递归解释子表达式
        Interpreting --> Recording : 记录计算历史
        Recording --> [*]
    }
```

## 🎯 应用场景

### 1. 配置文件解析
- **场景**: 解析复杂的配置表达式
- **优势**: 支持动态配置和表达式计算
- **示例**: 规则引擎、条件配置

### 2. SQL查询解析
- **场景**: 数据库查询语言的解析和执行
- **优势**: 结构化的查询处理
- **示例**: ORM框架、查询构建器

### 3. 脚本语言解释器
- **场景**: 简单脚本语言的实现
- **优势**: 可扩展的语法支持
- **示例**: 模板引擎、DSL实现

### 4. 正则表达式引擎
- **场景**: 正则表达式的解析和匹配
- **优势**: 复杂模式的结构化处理
- **示例**: 文本处理、数据验证

## 🔄 解释执行过程

### 递归解释流程

```mermaid
flowchart TD
    A[表达式: x + y * 2] --> B[AddExpression.interpret()]
    B --> C[left.interpret() - VariableExpression]
    B --> D[right.interpret() - MultiplyExpression]
    
    C --> E[context.getVariable("x") = 10]
    
    D --> F[left.interpret() - VariableExpression]
    D --> G[right.interpret() - NumberExpression]
    
    F --> H[context.getVariable("y") = 5]
    G --> I[return 2]
    
    H --> J[return 5]
    I --> J
    J --> K[5 * 2 = 10]
    
    E --> L[return 10]
    K --> L
    L --> M[10 + 10 = 20]
    
    style B fill:#e3f2fd
    style D fill:#f3e5f5
    style M fill:#ccffcc
```

### 错误处理机制

```mermaid
graph TB
    subgraph "词法分析错误"
        L1[未知字符] --> L2[抛出词法错误]
        L3[数字格式错误] --> L2
    end
    
    subgraph "语法分析错误"
        S1[括号不匹配] --> S2[抛出语法错误]
        S3[意外的标记] --> S2
        S4[表达式不完整] --> S2
    end
    
    subgraph "解释执行错误"
        I1[未定义变量] --> I2[抛出运行时错误]
        I3[除零错误] --> I2
        I4[类型错误] --> I2
    end
    
    subgraph "错误恢复策略"
        E1[记录错误信息]
        E2[提供错误位置]
        E3[建议修复方案]
        E4[继续解析其他部分]
        
        L2 --> E1
        S2 --> E1
        I2 --> E1
        E1 --> E2 --> E3 --> E4
    end
    
    style L2 fill:#ffcccc
    style S2 fill:#ffcccc
    style I2 fill:#ffcccc
    style E4 fill:#ccffcc
```

## ✅ 优点

1. **扩展性**: 易于添加新的语法规则
2. **结构化**: 语法规则的结构化表示
3. **可复用**: 表达式可以被复用和组合
4. **灵活性**: 支持复杂的语言特性

## ❌ 缺点

1. **复杂性**: 对于复杂语法，类数量会急剧增加
2. **性能**: 递归解释可能导致性能问题
3. **维护成本**: 语法变更需要修改多个类
4. **适用范围**: 只适合简单的语言

## 🔗 相关模式

- **组合模式**: 表达式树的结构类似组合模式
- **访问者模式**: 可用于对表达式树进行不同的操作
- **策略模式**: 不同的解释策略可以用策略模式实现

## 💡 最佳实践

1. **语法设计**: 保持语法简单和一致
2. **错误处理**: 提供清晰的错误信息和恢复机制
3. **性能优化**: 对于复杂表达式考虑缓存和优化
4. **扩展性**: 设计时考虑未来的语法扩展
5. **测试覆盖**: 充分测试各种语法组合和边界情况
