# 享元模式架构图

## 📋 模式概述

享元模式（Flyweight Pattern）是一种结构型设计模式，它通过共享技术有效地支持大量细粒度对象。享元模式将对象的内部状态和外部状态分离，内部状态存储在享元对象中并可以共享，外部状态由客户端保存。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 需要创建大量相似的对象
- 对象创建和存储成本很高
- 大部分对象状态可以外部化
- 希望减少内存使用

## 🏗️ 架构图

```mermaid
classDiagram
    class CharacterFlyweight {
        <<interface>>
        +render(x: number, y: number, fontSize: number, color: string) string
        +getCharacter() string
    }
    
    class Character {
        -char: string
        +constructor(char: string)
        +render(x: number, y: number, fontSize: number, color: string) string
        +getCharacter() string
    }
    
    class CharacterFactory {
        -flyweights: Map~string, Character~
        -createdCount: number
        +getCharacter(char: string) Character
        +getFlyweights() Map~string, Character~
        +getFlyweightCount() number
        +reset() void
    }
    
    class CharacterContext {
        -character: Character
        -x: number
        -y: number
        -fontSize: number
        -color: string
        +constructor(char: string, x: number, y: number, fontSize: number, color: string)
        +render() string
        +getCharacter() string
        +getX() number
        +getY() number
        +getFontSize() number
        +getColor() string
    }
    
    class Document {
        -characters: CharacterContext[]
        +addText(text: string, startX: number, startY: number, fontSize: number, color: string) void
        +getCharacters() CharacterContext[]
        +clear() void
        +getCharacterCount() number
    }
    
    CharacterFlyweight <|-- Character
    CharacterFactory --> Character : creates and manages
    CharacterContext --> Character : uses
    Document --> CharacterContext : contains
```

## 🧠 内部状态 vs 外部状态

```mermaid
graph LR
    subgraph "内部状态 (Intrinsic State)"
        direction TB
        IS[🔒 存储在享元对象中]
        IS --> IS1[字符内容 'A', 'B', 'C']
        IS --> IS2[字体类型 Arial, Times]
        IS --> IS3[字符编码 UTF-8]
        
        ISNote[✅ 可以共享<br/>✅ 不依赖上下文<br/>✅ 存储在享元中]
    end
    
    subgraph "外部状态 (Extrinsic State)"
        direction TB
        ES[🔓 由客户端维护]
        ES --> ES1[字符位置 x, y]
        ES --> ES2[字体大小 12px, 16px]
        ES --> ES3[颜色 red, blue, black]
        
        ESNote[❌ 不可共享<br/>❌ 依赖上下文<br/>❌ 客户端传递]
    end
    
    subgraph "享元对象"
        direction TB
        FO[Character Flyweight]
        FO --> FO1[只存储内部状态]
        FO --> FO2[通过参数接收外部状态]
        FO --> FO3[render(x, y, fontSize, color)]
    end
    
    IS -.->|存储| FO
    ES -.->|传递| FO
    
    style IS fill:#52c41a
    style IS1 fill:#52c41a
    style IS2 fill:#52c41a
    style IS3 fill:#52c41a
    
    style ES fill:#1890ff
    style ES1 fill:#1890ff
    style ES2 fill:#1890ff
    style ES3 fill:#1890ff
    
    style FO fill:#ffa500
    style FO1 fill:#ffa500
    style FO2 fill:#ffa500
    style FO3 fill:#ffa500
```

## 🏭 享元工厂模式

```mermaid
graph TD
    subgraph "享元工厂 (CharacterFactory)"
        direction TB
        Factory[CharacterFactory]
        Factory --> Pool[享元对象池<br/>Map<string, Character>]
        
        Pool --> A[Character('A')]
        Pool --> B[Character('B')]
        Pool --> C[Character('C')]
        Pool --> Space[Character(' ')]
        Pool --> Dot[Character('.')]
    end
    
    subgraph "客户端请求"
        direction TB
        Client1[请求 'A']
        Client2[请求 'B']
        Client3[请求 'A']
        Client4[请求 'C']
        Client5[请求 'A']
    end
    
    subgraph "享元分配"
        direction TB
        Client1 --> A
        Client2 --> B
        Client3 --> A
        Client4 --> C
        Client5 --> A
        
        Note1[多个客户端共享<br/>同一个 'A' 享元对象]
    end
    
    style Factory fill:#ffa500
    style Pool fill:#ffa500
    style A fill:#52c41a
    style B fill:#52c41a
    style C fill:#52c41a
    style Space fill:#52c41a
    style Dot fill:#52c41a
```

## 📊 内存使用对比

```mermaid
graph TB
    subgraph "传统方式 - 每个字符一个对象"
        direction TB
        T1[对象1: 'H' + 位置 + 样式]
        T2[对象2: 'e' + 位置 + 样式]
        T3[对象3: 'l' + 位置 + 样式]
        T4[对象4: 'l' + 位置 + 样式]
        T5[对象5: 'o' + 位置 + 样式]
        T6[对象6: ' ' + 位置 + 样式]
        T7[对象7: 'W' + 位置 + 样式]
        T8[对象8: 'o' + 位置 + 样式]
        T9[对象9: 'r' + 位置 + 样式]
        T10[对象10: 'l' + 位置 + 样式]
        T11[对象11: 'd' + 位置 + 样式]
        
        TTotal[总对象数: 11个<br/>内存使用: 100%]
    end
    
    subgraph "享元模式 - 共享字符对象"
        direction TB
        
        subgraph "享元对象池"
            F1[Character('H')]
            F2[Character('e')]
            F3[Character('l')]
            F4[Character('o')]
            F5[Character(' ')]
            F6[Character('W')]
            F7[Character('r')]
            F8[Character('d')]
        end
        
        subgraph "上下文对象"
            C1[Context1: 位置+样式]
            C2[Context2: 位置+样式]
            C3[Context3: 位置+样式]
            C4[Context4: 位置+样式]
            C5[Context5: 位置+样式]
            C6[Context6: 位置+样式]
            C7[Context7: 位置+样式]
            C8[Context8: 位置+样式]
            C9[Context9: 位置+样式]
            C10[Context10: 位置+样式]
            C11[Context11: 位置+样式]
        end
        
        C1 -.-> F1
        C2 -.-> F2
        C3 -.-> F3
        C4 -.-> F3
        C5 -.-> F4
        C6 -.-> F5
        C7 -.-> F6
        C8 -.-> F4
        C9 -.-> F7
        C10 -.-> F3
        C11 -.-> F8
        
        FTotal[享元对象: 8个<br/>上下文对象: 11个<br/>内存节省: ~45%]
    end
    
    style T1 fill:#ff6b6b
    style T2 fill:#ff6b6b
    style T3 fill:#ff6b6b
    style T4 fill:#ff6b6b
    style T5 fill:#ff6b6b
    style T6 fill:#ff6b6b
    style T7 fill:#ff6b6b
    style T8 fill:#ff6b6b
    style T9 fill:#ff6b6b
    style T10 fill:#ff6b6b
    style T11 fill:#ff6b6b
    style TTotal fill:#ff4d4f
    
    style F1 fill:#52c41a
    style F2 fill:#52c41a
    style F3 fill:#52c41a
    style F4 fill:#52c41a
    style F5 fill:#52c41a
    style F6 fill:#52c41a
    style F7 fill:#52c41a
    style F8 fill:#52c41a
    style FTotal fill:#389e0d
```

## 🔄 享元使用时序图

```mermaid
sequenceDiagram
    participant Client
    participant Document
    participant Context as CharacterContext
    participant Factory as CharacterFactory
    participant Flyweight as Character
    
    Client->>Document: addText("Hello", 10, 20, 16, "black")
    
    loop for each character in "Hello"
        Document->>Context: new CharacterContext(char, x, y, fontSize, color)
        Context->>Factory: getCharacter(char)
        
        alt Character exists in pool
            Factory-->>Context: existing Character flyweight
        else Character not in pool
            Factory->>Flyweight: new Character(char)
            Factory->>Factory: store in pool
            Factory-->>Context: new Character flyweight
        end
        
        Context->>Context: store flyweight reference + extrinsic state
        Document->>Document: add context to characters array
    end
    
    Client->>Document: render()
    
    loop for each character context
        Document->>Context: render()
        Context->>Flyweight: render(x, y, fontSize, color)
        Flyweight-->>Context: rendered string
        Context-->>Document: rendered string
    end
    
    Document-->>Client: complete rendered text
```

## 📈 性能分析图

```mermaid
graph LR
    subgraph "字符数量 vs 内存使用"
        direction TB
        
        X[字符数量]
        Y[内存使用量]
        
        X --> X1[100字符]
        X --> X2[1000字符]
        X --> X3[10000字符]
        
        Y --> Y1[传统方式]
        Y --> Y2[享元模式]
        
        X1 -.-> Y1T[100MB]
        X2 -.-> Y1T2[1000MB]
        X3 -.-> Y1T3[10000MB]
        
        X1 -.-> Y2F[30MB]
        X2 -.-> Y2F2[100MB]
        X3 -.-> Y2F3[500MB]
        
        Savings[内存节省率<br/>70% - 95%]
    end
    
    style Y1T fill:#ff6b6b
    style Y1T2 fill:#ff6b6b
    style Y1T3 fill:#ff6b6b
    
    style Y2F fill:#52c41a
    style Y2F2 fill:#52c41a
    style Y2F3 fill:#52c41a
    
    style Savings fill:#389e0d
```

## 💡 核心概念

### 享元接口 (Flyweight)
- `CharacterFlyweight`: 定义享元对象的接口

### 具体享元 (Concrete Flyweight)
- `Character`: 实现享元接口，存储内部状态

### 享元工厂 (Flyweight Factory)
- `CharacterFactory`: 创建和管理享元对象

### 上下文 (Context)
- `CharacterContext`: 存储外部状态，维护对享元的引用

## ✅ 优点

1. **大幅减少对象数量**: 通过共享减少内存占用
2. **提高性能**: 减少对象创建和垃圾回收的开销
3. **状态外部化**: 增加了系统的灵活性
4. **节省内存**: 特别适合大量细粒度对象的场景

## ❌ 缺点

1. **增加复杂性**: 需要分离内部状态和外部状态
2. **时间换空间**: 可能增加查找享元对象的时间
3. **外部状态管理**: 增加了外部状态的维护复杂性

## 🔧 适用场景

- 文本编辑器字符渲染
- 游戏中的粒子系统
- 网页中的图标系统
- 数据库连接池
- 缓存系统
- 大量相似对象的场景

## 📝 实现要点

1. **状态分离**: 正确识别和分离内部状态与外部状态
2. **享元工厂**: 实现享元对象的创建和管理
3. **对象池**: 使用对象池技术管理享元实例
4. **线程安全**: 考虑多线程环境下的安全性
5. **内存管理**: 合理控制享元对象的生命周期
6. **性能监控**: 监控内存使用和性能提升效果
