# 装饰器模式架构图

## 📋 模式概述

装饰器模式（Decorator Pattern）是一种结构型设计模式，它允许向一个现有的对象添加新的功能，同时又不改变其结构。这种模式创建了一个装饰类，用来包装原有的类，并在保持类方法签名完整性的前提下，提供了额外的功能。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 需要动态地给对象添加功能
- 继承方式会导致类数量爆炸
- 需要组合多种功能
- 功能的添加和移除需要灵活控制

## 🏗️ 架构图

```mermaid
classDiagram
    class Coffee {
        <<interface>>
        +getDescription(): string
        +getCost(): number
    }
    
    class BaseCoffee {
        <<abstract>>
        #description: string
        #cost: number
        +getDescription(): string
        +getCost(): number
    }
    
    class Espresso {
        +getDescription(): string
        +getCost(): number
    }
    
    class Americano {
        +getDescription(): string
        +getCost(): number
    }
    
    class Latte {
        +getDescription(): string
        +getCost(): number
    }
    
    class Cappuccino {
        +getDescription(): string
        +getCost(): number
    }
    
    class CoffeeDecorator {
        <<abstract>>
        #coffee: Coffee
        +CoffeeDecorator(coffee: Coffee)
        +getDescription(): string
        +getCost(): number
    }
    
    class MilkDecorator {
        +getDescription(): string
        +getCost(): number
    }
    
    class SugarDecorator {
        +getDescription(): string
        +getCost(): number
    }
    
    class WhipDecorator {
        +getDescription(): string
        +getCost(): number
    }
    
    class VanillaDecorator {
        +getDescription(): string
        +getCost(): number
    }
    
    class CaramelDecorator {
        +getDescription(): string
        +getCost(): number
    }
    
    class CoffeeFactory {
        +static createBaseCoffee(type: string): Coffee
        +static createDecorator(type: string, coffee: Coffee): Coffee
        +static getBaseCoffeeTypes(): CoffeeType[]
        +static getDecoratorTypes(): DecoratorType[]
    }
    
    Coffee <|.. BaseCoffee
    BaseCoffee <|-- Espresso
    BaseCoffee <|-- Americano
    BaseCoffee <|-- Latte
    BaseCoffee <|-- Cappuccino
    
    Coffee <|.. CoffeeDecorator
    CoffeeDecorator <|-- MilkDecorator
    CoffeeDecorator <|-- SugarDecorator
    CoffeeDecorator <|-- WhipDecorator
    CoffeeDecorator <|-- VanillaDecorator
    CoffeeDecorator <|-- CaramelDecorator
    
    CoffeeDecorator --> Coffee : wraps
    CoffeeFactory --> Coffee : creates
    
    note for Coffee "组件接口\n定义基本行为"
    note for BaseCoffee "具体组件\n基础咖啡实现"
    note for CoffeeDecorator "装饰器基类\n包装组件对象"
    note for MilkDecorator "具体装饰器\n添加牛奶功能"
```

## 🔍 组件说明

### 组件接口 (Component Interface)
- **Coffee**: 咖啡组件接口
  - 定义所有咖啡对象的基本行为
  - `getDescription()`: 获取描述
  - `getCost()`: 获取价格

### 具体组件 (Concrete Components)
- **BaseCoffee**: 抽象基础咖啡类
- **Espresso**: 浓缩咖啡
- **Americano**: 美式咖啡
- **Latte**: 拿铁咖啡
- **Cappuccino**: 卡布奇诺
- 提供基础的咖啡功能实现

### 装饰器基类 (Base Decorator)
- **CoffeeDecorator**: 抽象装饰器类
  - 实现 Coffee 接口
  - 包含一个 Coffee 对象的引用
  - 将请求委托给被装饰的对象

### 具体装饰器 (Concrete Decorators)
- **MilkDecorator**: 牛奶装饰器
- **SugarDecorator**: 糖装饰器
- **WhipDecorator**: 奶泡装饰器
- **VanillaDecorator**: 香草糖浆装饰器
- **CaramelDecorator**: 焦糖糖浆装饰器
- 每个装饰器添加特定的功能和成本

### 工厂类 (Factory)
- **CoffeeFactory**: 咖啡工厂
  - 创建基础咖啡和装饰器
  - 管理可用的咖啡类型和装饰器类型

## 📊 装饰过程流程

```mermaid
sequenceDiagram
    participant C as Client
    participant F as CoffeeFactory
    participant E as Espresso
    participant M as MilkDecorator
    participant S as SugarDecorator
    participant V as VanillaDecorator
    
    C->>F: createBaseCoffee("espresso")
    F->>E: new Espresso()
    E-->>F: espresso instance
    F-->>C: Coffee (Espresso)
    
    C->>F: createDecorator("milk", coffee)
    F->>M: new MilkDecorator(coffee)
    M-->>F: decorated coffee
    F-->>C: Coffee (Espresso + Milk)
    
    C->>F: createDecorator("sugar", coffee)
    F->>S: new SugarDecorator(coffee)
    S-->>F: decorated coffee
    F-->>C: Coffee (Espresso + Milk + Sugar)
    
    C->>F: createDecorator("vanilla", coffee)
    F->>V: new VanillaDecorator(coffee)
    V-->>F: decorated coffee
    F-->>C: Coffee (Espresso + Milk + Sugar + Vanilla)
    
    Note over C: 最终得到多层装饰的咖啡
```

## 💡 设计优势

1. **动态扩展**: 运行时动态添加功能
2. **灵活组合**: 可以任意组合不同的装饰器
3. **单一职责**: 每个装饰器只负责一个功能
4. **开闭原则**: 对扩展开放，对修改关闭
5. **避免类爆炸**: 不需要为每种组合创建子类

## 🔧 TypeScript 实现

### 组件接口
```typescript
interface Coffee {
  getDescription(): string;
  getCost(): number;
}
```

### 基础组件实现
```typescript
abstract class BaseCoffee implements Coffee {
  protected description: string;
  protected cost: number;

  constructor(description: string, cost: number) {
    this.description = description;
    this.cost = cost;
  }

  getDescription(): string {
    return this.description;
  }

  getCost(): number {
    return this.cost;
  }
}

class Espresso extends BaseCoffee {
  constructor() {
    super("浓缩咖啡", 15.0);
  }
}

class Latte extends BaseCoffee {
  constructor() {
    super("拿铁咖啡", 25.0);
  }
}
```

### 装饰器基类
```typescript
abstract class CoffeeDecorator implements Coffee {
  protected coffee: Coffee;

  constructor(coffee: Coffee) {
    this.coffee = coffee;
  }

  getDescription(): string {
    return this.coffee.getDescription();
  }

  getCost(): number {
    return this.coffee.getCost();
  }
}
```

### 具体装饰器实现
```typescript
class MilkDecorator extends CoffeeDecorator {
  getDescription(): string {
    return this.coffee.getDescription() + " + 牛奶";
  }

  getCost(): number {
    return this.coffee.getCost() + 3.0;
  }
}

class SugarDecorator extends CoffeeDecorator {
  getDescription(): string {
    return this.coffee.getDescription() + " + 糖";
  }

  getCost(): number {
    return this.coffee.getCost() + 1.0;
  }
}

class VanillaDecorator extends CoffeeDecorator {
  getDescription(): string {
    return this.coffee.getDescription() + " + 香草糖浆";
  }

  getCost(): number {
    return this.coffee.getCost() + 5.0;
  }
}
```

### 工厂类实现
```typescript
class CoffeeFactory {
  static createBaseCoffee(type: string): Coffee {
    switch (type) {
      case "espresso":
        return new Espresso();
      case "latte":
        return new Latte();
      default:
        throw new Error(`不支持的咖啡类型: ${type}`);
    }
  }

  static createDecorator(type: string, coffee: Coffee): Coffee {
    switch (type) {
      case "milk":
        return new MilkDecorator(coffee);
      case "sugar":
        return new SugarDecorator(coffee);
      case "vanilla":
        return new VanillaDecorator(coffee);
      default:
        throw new Error(`不支持的装饰器类型: ${type}`);
    }
  }
}
```

### 使用示例
```typescript
// 创建基础咖啡
let coffee: Coffee = CoffeeFactory.createBaseCoffee("espresso");
console.log(`${coffee.getDescription()} - ¥${coffee.getCost()}`);
// 输出: 浓缩咖啡 - ¥15

// 添加牛奶
coffee = CoffeeFactory.createDecorator("milk", coffee);
console.log(`${coffee.getDescription()} - ¥${coffee.getCost()}`);
// 输出: 浓缩咖啡 + 牛奶 - ¥18

// 添加糖
coffee = CoffeeFactory.createDecorator("sugar", coffee);
console.log(`${coffee.getDescription()} - ¥${coffee.getCost()}`);
// 输出: 浓缩咖啡 + 牛奶 + 糖 - ¥19

// 添加香草糖浆
coffee = CoffeeFactory.createDecorator("vanilla", coffee);
console.log(`${coffee.getDescription()} - ¥${coffee.getCost()}`);
// 输出: 浓缩咖啡 + 牛奶 + 糖 + 香草糖浆 - ¥24
```

## 🚀 装饰器模式变体

### 函数式装饰器
```typescript
type CoffeeFunction = () => { description: string; cost: number };

const createEspresso = (): CoffeeFunction => () => ({
  description: "浓缩咖啡",
  cost: 15.0
});

const withMilk = (coffee: CoffeeFunction): CoffeeFunction => () => {
  const base = coffee();
  return {
    description: base.description + " + 牛奶",
    cost: base.cost + 3.0
  };
};

const withSugar = (coffee: CoffeeFunction): CoffeeFunction => () => {
  const base = coffee();
  return {
    description: base.description + " + 糖",
    cost: base.cost + 1.0
  };
};

// 使用函数式装饰器
const decoratedCoffee = withSugar(withMilk(createEspresso()));
console.log(decoratedCoffee()); // { description: "浓缩咖啡 + 牛奶 + 糖", cost: 19 }
```

### 链式装饰器
```typescript
class FluentCoffee implements Coffee {
  private description: string;
  private cost: number;

  constructor(description: string, cost: number) {
    this.description = description;
    this.cost = cost;
  }

  withMilk(): FluentCoffee {
    return new FluentCoffee(this.description + " + 牛奶", this.cost + 3.0);
  }

  withSugar(): FluentCoffee {
    return new FluentCoffee(this.description + " + 糖", this.cost + 1.0);
  }

  withVanilla(): FluentCoffee {
    return new FluentCoffee(this.description + " + 香草糖浆", this.cost + 5.0);
  }

  getDescription(): string {
    return this.description;
  }

  getCost(): number {
    return this.cost;
  }
}

// 链式调用
const coffee = new FluentCoffee("浓缩咖啡", 15.0)
  .withMilk()
  .withSugar()
  .withVanilla();
```

### 注解式装饰器（TypeScript Decorator）
```typescript
function addMilk(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value;
  descriptor.value = function(...args: any[]) {
    const result = method.apply(this, args);
    return {
      description: result.description + " + 牛奶",
      cost: result.cost + 3.0
    };
  };
}

class AnnotatedCoffee {
  @addMilk
  getEspresso() {
    return { description: "浓缩咖啡", cost: 15.0 };
  }
}
```

## 🎯 使用场景

- **UI 组件**: 给组件添加边框、滚动条等功能
- **数据流处理**: 给数据添加加密、压缩等处理
- **中间件**: Web 框架中的中间件机制
- **缓存系统**: 给对象添加缓存功能
- **日志系统**: 给方法添加日志记录功能

## 📝 实现要点

1. **接口一致性**: 装饰器和被装饰对象实现相同接口
2. **透明性**: 装饰器对客户端应该是透明的
3. **组合顺序**: 考虑装饰器的组合顺序
4. **性能考虑**: 避免过度装饰影响性能
5. **内存管理**: 注意装饰器链的内存占用

## ⚠️ 注意事项

1. **复杂性增加**: 多层装饰可能导致调试困难
2. **对象标识**: 装饰后的对象与原对象不同
3. **装饰顺序**: 不同的装饰顺序可能产生不同结果
4. **性能开销**: 每层装饰都有方法调用开销
5. **类型安全**: 需要确保装饰器的类型安全

## 🔄 与其他模式的关系

- **与适配器模式**: 都改变对象接口，但目的不同
- **与组合模式**: 都处理对象组合，但结构不同
- **与策略模式**: 都改变对象行为，但方式不同
- **与代理模式**: 都包装对象，但意图不同

---

> 装饰器模式提供了一种灵活的方式来扩展对象功能，它比继承更加灵活，可以在运行时动态组合功能。
