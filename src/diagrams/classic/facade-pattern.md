# 外观模式架构图

## 📋 模式概述

外观模式（Facade Pattern）是一种结构型设计模式，它为复杂子系统提供一个简单的接口。外观模式定义了一个高层接口，让子系统更容易使用。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 子系统复杂，客户端难以使用
- 需要简化复杂系统的接口
- 希望减少客户端与子系统的依赖
- 需要为复杂系统提供统一入口

## 🏗️ 架构图

```mermaid
classDiagram
    class SmartHomeFacade {
        -lighting: LightingSystem
        -airConditioning: AirConditioningSystem
        -audio: AudioSystem
        +constructor()
        +arriveHome() string[]
        +leaveHome() string[]
        +sleepMode() string[]
        +partyMode() string[]
        +getSystemStatus() string[]
    }
    
    class LightingSystem {
        -lights: Map~string, object~
        +constructor()
        +turnOn(room: string) string
        +turnOff(room: string) string
        +dimLights(room: string, level: number) string
        +getStatus() string
        +toggle(room: string) string
    }
    
    class AirConditioningSystem {
        -temperature: number
        -isOn: boolean
        +turnOn() string
        +turnOff() string
        +setTemperature(temp: number) string
        +getStatus() object
        +toggle() string
    }
    
    class AudioSystem {
        -volume: number
        -isPlaying: boolean
        -currentTrack: string
        +play(track: string) string
        +stop() string
        +setVolume(volume: number) string
        +getStatus() object
    }
    
    SmartHomeFacade --> LightingSystem : uses
    SmartHomeFacade --> AirConditioningSystem : uses
    SmartHomeFacade --> AudioSystem : uses
```

## 🏠 智能家居系统架构

```mermaid
graph TB
    subgraph "客户端层"
        Client[👤 用户]
        Client --> HomeApp[📱 智能家居App]
    end
    
    subgraph "外观层"
        Facade[🏠 SmartHomeFacade<br/>智能家居外观]
        
        Facade --> Mode1[🏠 回家模式]
        Facade --> Mode2[🚪 离家模式]
        Facade --> Mode3[😴 睡眠模式]
        Facade --> Mode4[🎉 聚会模式]
    end
    
    subgraph "子系统层"
        Lighting[💡 LightingSystem<br/>灯光控制系统]
        AC[❄️ AirConditioningSystem<br/>空调控制系统]
        Audio[🎵 AudioSystem<br/>音响控制系统]
        
        Lighting --> L1[客厅灯]
        Lighting --> L2[卧室灯]
        Lighting --> L3[厨房灯]
        
        AC --> AC1[温度控制]
        AC --> AC2[开关控制]
        
        Audio --> A1[音乐播放]
        Audio --> A2[音量控制]
        Audio --> A3[曲目选择]
    end
    
    HomeApp --> Facade
    
    Facade -.-> Lighting
    Facade -.-> AC
    Facade -.-> Audio
    
    style Client fill:#667eea
    style HomeApp fill:#667eea
    style Facade fill:#ffa500
    style Lighting fill:#52c41a
    style AC fill:#1890ff
    style Audio fill:#722ed1
```

## 🔄 模式执行时序图

```mermaid
sequenceDiagram
    participant Client
    participant Facade as SmartHomeFacade
    participant Lighting as LightingSystem
    participant AC as AirConditioningSystem
    participant Audio as AudioSystem
    
    Client->>Facade: arriveHome()
    
    Note over Facade: 执行回家模式
    
    Facade->>Lighting: turnOn("客厅")
    Lighting-->>Facade: "客厅灯光已开启"
    
    Facade->>Lighting: turnOn("卧室")
    Lighting-->>Facade: "卧室灯光已开启"
    
    Facade->>AC: turnOn()
    AC-->>Facade: "空调已开启，当前温度25°C"
    
    Facade->>AC: setTemperature(24)
    AC-->>Facade: "空调温度设置为24°C"
    
    Facade->>Audio: play("轻松音乐")
    Audio-->>Facade: "正在播放: 轻松音乐"
    
    Facade->>Audio: setVolume(30)
    Audio-->>Facade: "音量设置为30%"
    
    Facade-->>Client: ["回家模式启动", "客厅灯光已开启", "卧室灯光已开启", ...]
```

## 🎭 不同模式对比

```mermaid
graph LR
    subgraph "回家模式 🏠"
        direction TB
        H1[开启客厅灯]
        H2[开启卧室灯]
        H3[开启空调 24°C]
        H4[播放轻松音乐 30%]
        
        H1 --> H2 --> H3 --> H4
    end
    
    subgraph "离家模式 🚪"
        direction TB
        L1[关闭所有灯光]
        L2[关闭空调]
        L3[停止音乐播放]
        
        L1 --> L2 --> L3
    end
    
    subgraph "睡眠模式 😴"
        direction TB
        S1[关闭客厅灯]
        S2[关闭厨房灯]
        S3[卧室灯调至10%]
        S4[空调设为22°C]
        S5[播放白噪音 15%]
        
        S1 --> S2 --> S3 --> S4 --> S5
    end
    
    subgraph "聚会模式 🎉"
        direction TB
        P1[开启客厅灯 80%]
        P2[开启厨房灯]
        P3[空调设为20°C]
        P4[播放派对音乐 70%]
        
        P1 --> P2 --> P3 --> P4
    end
    
    style H1 fill:#52c41a
    style H2 fill:#52c41a
    style H3 fill:#52c41a
    style H4 fill:#52c41a
    
    style L1 fill:#ff6b6b
    style L2 fill:#ff6b6b
    style L3 fill:#ff6b6b
    
    style S1 fill:#722ed1
    style S2 fill:#722ed1
    style S3 fill:#722ed1
    style S4 fill:#722ed1
    style S5 fill:#722ed1
    
    style P1 fill:#fa8c16
    style P2 fill:#fa8c16
    style P3 fill:#fa8c16
    style P4 fill:#fa8c16
```

## 🔧 传统方式 vs 外观模式

```mermaid
graph TB
    subgraph "传统方式 - 客户端直接调用"
        direction TB
        TC[客户端]
        TC --> TL[LightingSystem]
        TC --> TAC[AirConditioningSystem]
        TC --> TA[AudioSystem]
        
        TL --> TL1[turnOn('客厅')]
        TL --> TL2[turnOn('卧室')]
        TAC --> TAC1[turnOn()]
        TAC --> TAC2[setTemperature(24)]
        TA --> TA1[play('轻松音乐')]
        TA --> TA2[setVolume(30)]
        
        TNote[❌ 客户端需要了解所有子系统<br/>❌ 调用复杂，容易出错<br/>❌ 耦合度高，难以维护]
    end
    
    subgraph "外观模式 - 统一接口"
        direction TB
        FC[客户端]
        FC --> FF[SmartHomeFacade]
        FF --> FMode[arriveHome()]
        
        FF -.-> FL[LightingSystem]
        FF -.-> FAC[AirConditioningSystem]
        FF -.-> FA[AudioSystem]
        
        FNote[✅ 客户端只需调用一个方法<br/>✅ 隐藏子系统复杂性<br/>✅ 降低耦合度，易于维护]
    end
    
    style TC fill:#ff6b6b
    style TL fill:#ff6b6b
    style TAC fill:#ff6b6b
    style TA fill:#ff6b6b
    
    style FC fill:#52c41a
    style FF fill:#ffa500
    style FL fill:#52c41a
    style FAC fill:#52c41a
    style FA fill:#52c41a
```

## 📊 系统复杂度对比

```mermaid
graph LR
    subgraph "没有外观模式"
        direction TB
        N1[客户端需要了解]
        N1 --> N2[3个子系统]
        N1 --> N3[15个方法]
        N1 --> N4[复杂的调用顺序]
        N1 --> N5[错误处理逻辑]
        
        NComplexity[复杂度: 高 📈]
    end
    
    subgraph "使用外观模式"
        direction TB
        F1[客户端只需了解]
        F1 --> F2[1个外观类]
        F1 --> F3[4个模式方法]
        F1 --> F4[简单的方法调用]
        F1 --> F5[统一的返回格式]
        
        FComplexity[复杂度: 低 📉]
    end
    
    style N1 fill:#ff6b6b
    style N2 fill:#ff6b6b
    style N3 fill:#ff6b6b
    style N4 fill:#ff6b6b
    style N5 fill:#ff6b6b
    style NComplexity fill:#ff4d4f
    
    style F1 fill:#52c41a
    style F2 fill:#52c41a
    style F3 fill:#52c41a
    style F4 fill:#52c41a
    style F5 fill:#52c41a
    style FComplexity fill:#389e0d
```

## 💡 核心概念

### 外观 (Facade)
- `SmartHomeFacade`: 为复杂子系统提供简单接口

### 子系统 (Subsystem)
- `LightingSystem`: 灯光控制子系统
- `AirConditioningSystem`: 空调控制子系统
- `AudioSystem`: 音响控制子系统

## ✅ 优点

1. **简化接口**: 为复杂系统提供简单易用的接口
2. **降低耦合**: 减少客户端与子系统的依赖关系
3. **更好的分层**: 明确划分了访问层次
4. **易于使用**: 客户端无需了解子系统的复杂性
5. **符合迪米特法则**: 减少对象间的交互

## ❌ 缺点

1. **不符合开闭原则**: 增加新功能可能需要修改外观类
2. **可能违背单一职责**: 外观类可能承担过多责任
3. **过度使用复杂**: 不当使用会导致系统过于复杂

## 🔧 适用场景

- 智能家居控制系统
- 编译器前端接口
- 数据库访问层
- API网关
- 第三方库封装
- 复杂业务流程的简化

## 📝 实现要点

1. **识别子系统**: 确定需要封装的复杂子系统
2. **设计外观接口**: 提供简单、易用的高层接口
3. **封装复杂性**: 在外观类中处理子系统的复杂交互
4. **保持简单**: 外观接口应该简单明了
5. **避免过度封装**: 不要隐藏所有子系统功能
6. **考虑扩展性**: 为未来的功能扩展留出空间
