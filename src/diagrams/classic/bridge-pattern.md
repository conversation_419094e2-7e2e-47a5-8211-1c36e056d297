# 桥接模式架构图

## 📋 模式概述

桥接模式（Bridge Pattern）是一种结构型设计模式，它将抽象与实现分离，使它们可以独立变化。通过组合而不是继承来连接抽象和实现。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 抽象和实现都需要独立扩展
- 不希望在抽象和实现之间建立编译时绑定
- 需要在多个对象间共享实现
- 希望对客户端隐藏实现细节

## 🏗️ 架构图

```mermaid
classDiagram
    class DrawingAPI {
        <<interface>>
        +drawCircle(x: number, y: number, radius: number) string
        +drawRectangle(x: number, y: number, width: number, height: number) string
        +drawTriangle(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number) string
        +getAPIName() string
    }
    
    class CanvasAPI {
        +drawCircle(x: number, y: number, radius: number) string
        +drawRectangle(x: number, y: number, width: number, height: number) string
        +drawTriangle(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number) string
        +getAPIName() string
    }
    
    class SVGAPI {
        +drawCircle(x: number, y: number, radius: number) string
        +drawRectangle(x: number, y: number, width: number, height: number) string
        +drawTriangle(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number) string
        +getAPIName() string
    }
    
    class WebGLAPI {
        +drawCircle(x: number, y: number, radius: number) string
        +drawRectangle(x: number, y: number, width: number, height: number) string
        +drawTriangle(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number) string
        +getAPIName() string
    }
    
    class Shape {
        <<abstract>>
        #drawingAPI: DrawingAPI
        +constructor(drawingAPI: DrawingAPI)
        +draw() string
        +getShapeInfo() string
        +changeAPI(newAPI: DrawingAPI) void
    }
    
    class Circle {
        -x: number
        -y: number
        -radius: number
        +constructor(x: number, y: number, radius: number, drawingAPI: DrawingAPI)
        +draw() string
        +getShapeInfo() string
    }
    
    class Rectangle {
        -x: number
        -y: number
        -width: number
        -height: number
        +constructor(x: number, y: number, width: number, height: number, drawingAPI: DrawingAPI)
        +draw() string
        +getShapeInfo() string
    }
    
    class Triangle {
        -x1: number
        -y1: number
        -x2: number
        -y2: number
        -x3: number
        -y3: number
        +constructor(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, drawingAPI: DrawingAPI)
        +draw() string
        +getShapeInfo() string
    }
    
    DrawingAPI <|-- CanvasAPI
    DrawingAPI <|-- SVGAPI
    DrawingAPI <|-- WebGLAPI
    
    Shape <|-- Circle
    Shape <|-- Rectangle
    Shape <|-- Triangle
    
    Shape --> DrawingAPI : uses
```

## 🌉 桥接关系图

```mermaid
graph LR
    subgraph "抽象层 (Abstraction)"
        S[Shape 抽象类]
        C[Circle 圆形]
        R[Rectangle 矩形]
        T[Triangle 三角形]
        
        S --> C
        S --> R
        S --> T
    end
    
    subgraph "桥接"
        Bridge[🌉 Bridge<br/>DrawingAPI接口]
    end
    
    subgraph "实现层 (Implementation)"
        Canvas[Canvas API<br/>2D绘图]
        SVG[SVG API<br/>矢量图形]
        WebGL[WebGL API<br/>3D渲染]
    end
    
    S -.->|bridge| Bridge
    Bridge -.-> Canvas
    Bridge -.-> SVG
    Bridge -.-> WebGL
    
    style S fill:#667eea
    style C fill:#667eea
    style R fill:#667eea
    style T fill:#667eea
    
    style Bridge fill:#ffa500
    
    style Canvas fill:#52c41a
    style SVG fill:#52c41a
    style WebGL fill:#52c41a
```

## 🔄 时序图

```mermaid
sequenceDiagram
    participant Client
    participant Circle
    participant DrawingAPI as DrawingAPI(Canvas)
    
    Client->>Circle: new Circle(50, 50, 25, canvasAPI)
    Circle->>Circle: store drawingAPI reference
    
    Client->>Circle: draw()
    Circle->>DrawingAPI: drawCircle(50, 50, 25)
    DrawingAPI-->>Circle: "Canvas: ctx.arc(50, 50, 25, 0, 2*Math.PI)"
    Circle-->>Client: drawing result
    
    Client->>Circle: changeAPI(svgAPI)
    Circle->>Circle: this.drawingAPI = svgAPI
    
    Client->>Circle: draw()
    Circle->>DrawingAPI: drawCircle(50, 50, 25)
    Note over DrawingAPI: Now using SVG API
    DrawingAPI-->>Circle: "SVG: <circle cx='50' cy='50' r='25' />"
    Circle-->>Client: drawing result
```

## 🎨 多维度变化图

```mermaid
graph TB
    subgraph "形状维度扩展"
        direction TB
        S1[Circle 圆形]
        S2[Rectangle 矩形]
        S3[Triangle 三角形]
        S4[Polygon 多边形]
        S5[Ellipse 椭圆]
    end
    
    subgraph "API维度扩展"
        direction TB
        A1[Canvas API]
        A2[SVG API]
        A3[WebGL API]
        A4[Direct2D API]
        A5[OpenGL API]
    end
    
    subgraph "组合结果"
        direction TB
        Matrix[5 × 5 = 25种组合<br/>无需25个类]
    end
    
    S1 -.-> Matrix
    S2 -.-> Matrix
    S3 -.-> Matrix
    S4 -.-> Matrix
    S5 -.-> Matrix
    
    A1 -.-> Matrix
    A2 -.-> Matrix
    A3 -.-> Matrix
    A4 -.-> Matrix
    A5 -.-> Matrix
    
    style Matrix fill:#ffa500
```

## 🔧 传统继承 vs 桥接模式

```mermaid
graph TB
    subgraph "传统继承方式 (类爆炸)"
        direction TB
        TS[Shape]
        TS --> TCC[CanvasCircle]
        TS --> TSC[SVGCircle]
        TS --> TWC[WebGLCircle]
        TS --> TCR[CanvasRectangle]
        TS --> TSR[SVGRectangle]
        TS --> TWR[WebGLRectangle]
        TS --> TCT[CanvasTriangle]
        TS --> TST[SVGTriangle]
        TS --> TWT[WebGLTriangle]
        
        TNote[需要 3×3 = 9个类<br/>添加新形状或API需要<br/>修改多个类]
    end
    
    subgraph "桥接模式"
        direction TB
        BS[Shape]
        BS --> BC[Circle]
        BS --> BR[Rectangle]
        BS --> BT[Triangle]
        
        BAPI[DrawingAPI]
        BAPI --> BCanvas[CanvasAPI]
        BAPI --> BSVG[SVGAPI]
        BAPI --> BWebGL[WebGLAPI]
        
        BC -.->|bridge| BAPI
        BR -.->|bridge| BAPI
        BT -.->|bridge| BAPI
        
        BNote[只需要 3+3 = 6个类<br/>形状和API可以<br/>独立扩展]
    end
    
    style TS fill:#ff6b6b
    style TCC fill:#ff6b6b
    style TSC fill:#ff6b6b
    style TWC fill:#ff6b6b
    style TCR fill:#ff6b6b
    style TSR fill:#ff6b6b
    style TWR fill:#ff6b6b
    style TCT fill:#ff6b6b
    style TST fill:#ff6b6b
    style TWT fill:#ff6b6b
    
    style BS fill:#52c41a
    style BC fill:#52c41a
    style BR fill:#52c41a
    style BT fill:#52c41a
    style BAPI fill:#1890ff
    style BCanvas fill:#1890ff
    style BSVG fill:#1890ff
    style BWebGL fill:#1890ff
```

## 💡 核心概念

### 抽象 (Abstraction)
- `Shape`: 定义抽象接口，维护对实现的引用

### 扩展抽象 (Refined Abstraction)
- `Circle`, `Rectangle`, `Triangle`: 扩展抽象接口

### 实现接口 (Implementation)
- `DrawingAPI`: 定义实现类的接口

### 具体实现 (Concrete Implementation)
- `CanvasAPI`, `SVGAPI`, `WebGLAPI`: 具体的实现类

## ✅ 优点

1. **分离抽象和实现**: 两者可以独立变化
2. **运行时切换**: 可以在运行时切换实现
3. **扩展性好**: 抽象和实现都可以独立扩展
4. **隐藏实现细节**: 对客户端隐藏实现细节
5. **避免类爆炸**: 减少类的数量

## ❌ 缺点

1. **增加复杂性**: 增加了系统的复杂性
2. **理解难度**: 需要正确识别系统中的变化维度
3. **设计难度**: 需要预先识别独立变化的维度

## 🔧 适用场景

- 图形绘制系统（不同渲染引擎）
- 数据库驱动程序（不同数据库）
- 跨平台应用开发
- 消息发送系统（不同通道）
- 设备驱动程序

## 📝 实现要点

1. **识别变化维度**: 找出系统中独立变化的两个维度
2. **定义抽象接口**: 为抽象层定义接口
3. **定义实现接口**: 为实现层定义接口
4. **建立桥接关系**: 在抽象中维护对实现的引用
5. **支持运行时切换**: 提供切换实现的方法
6. **保持接口稳定**: 确保接口的稳定性和一致性
