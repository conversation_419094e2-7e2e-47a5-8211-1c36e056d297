# 责任链模式架构图

## 📋 模式概述

责任链模式（Chain of Responsibility Pattern）是一种行为型设计模式，它沿着处理者链传递请求，直到某个处理者处理它为止。这种模式将请求的发送者和接收者解耦，让多个对象都有机会处理请求。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 多个对象可以处理同一请求
- 不想指定具体的处理者
- 希望动态指定处理者集合
- 需要按照特定顺序尝试处理请求

## 🏗️ 架构图

```mermaid
classDiagram
    class LeaveRequest {
        +employeeName: string
        +days: number
        +reason: string
        +type: string
        +constructor(employeeName: string, days: number, reason: string, type: string)
        +getInfo() string
    }
    
    class ApprovalHandler {
        <<abstract>>
        #nextHandler: ApprovalHandler
        +setNext(handler: ApprovalHandler) ApprovalHandler
        +handle(request: LeaveRequest) object
        #canHandle(request: LeaveRequest) boolean
        #processRequest(request: LeaveRequest) object
        #getHandlerName() string
    }
    
    class DirectSupervisor {
        #canHandle(request: LeaveRequest) boolean
        #processRequest(request: LeaveRequest) object
        #getHandlerName() string
    }
    
    class DepartmentManager {
        #canHandle(request: LeaveRequest) boolean
        #processRequest(request: LeaveRequest) object
        #getHandlerName() string
    }
    
    class HRDirector {
        #canHandle(request: LeaveRequest) boolean
        #processRequest(request: LeaveRequest) object
        #getHandlerName() string
    }
    
    class GeneralManager {
        #canHandle(request: LeaveRequest) boolean
        #processRequest(request: LeaveRequest) object
        #getHandlerName() string
    }
    
    class ApprovalChainBuilder {
        +buildChain() ApprovalHandler
    }
    
    ApprovalHandler <|-- DirectSupervisor
    ApprovalHandler <|-- DepartmentManager
    ApprovalHandler <|-- HRDirector
    ApprovalHandler <|-- GeneralManager
    
    ApprovalHandler --> ApprovalHandler : nextHandler
    ApprovalChainBuilder --> ApprovalHandler : creates
```

## 🔗 责任链结构图

```mermaid
graph LR
    subgraph "请假审批责任链"
        direction LR
        
        Request[📝 请假申请]
        
        H1[👨‍💼 直接主管<br/>≤3天 病假/事假]
        H2[👔 部门经理<br/>≤7天 所有类型]
        H3[👩‍💼 人事总监<br/>≤15天 所有类型]
        H4[🤵 总经理<br/>≤30天 所有类型]
        
        Reject[❌ 拒绝申请]
        
        Request --> H1
        H1 -->|无权限| H2
        H2 -->|无权限| H3
        H3 -->|无权限| H4
        H4 -->|无权限| Reject
        
        H1 -.->|有权限| Approve1[✅ 主管批准]
        H2 -.->|有权限| Approve2[✅ 经理批准]
        H3 -.->|有权限| Approve3[✅ 总监批准]
        H4 -.->|有权限| Approve4[✅ 总经理批准]
    end
    
    style Request fill:#1890ff
    style H1 fill:#52c41a
    style H2 fill:#faad14
    style H3 fill:#722ed1
    style H4 fill:#ff6b6b
    style Reject fill:#ff4d4f
    style Approve1 fill:#52c41a
    style Approve2 fill:#52c41a
    style Approve3 fill:#52c41a
    style Approve4 fill:#52c41a
```

## 🔄 处理流程时序图

```mermaid
sequenceDiagram
    participant Client
    participant Request as LeaveRequest
    participant Supervisor as DirectSupervisor
    participant Manager as DepartmentManager
    participant Director as HRDirector
    participant General as GeneralManager
    
    Client->>Request: new LeaveRequest("张三", 10, "年假", "annual")
    Client->>Supervisor: handle(request)
    
    Supervisor->>Supervisor: canHandle(request)
    Note over Supervisor: 10天 > 3天，无权限
    
    Supervisor->>Manager: handle(request)
    Manager->>Manager: canHandle(request)
    Note over Manager: 10天 > 7天，无权限
    
    Manager->>Director: handle(request)
    Director->>Director: canHandle(request)
    Note over Director: 10天 ≤ 15天，有权限
    
    Director->>Director: processRequest(request)
    Director-->>Manager: approval result
    Manager-->>Supervisor: approval result
    Supervisor-->>Client: "人事总监批准了张三的10天年假申请"
```

## 🎯 权限等级图

```mermaid
graph TB
    subgraph "审批权限层级"
        direction TB
        
        L1[👨‍💼 直接主管<br/>Level 1]
        L2[👔 部门经理<br/>Level 2]
        L3[👩‍💼 人事总监<br/>Level 3]
        L4[🤵 总经理<br/>Level 4]
        
        L1 --> L1A[≤ 3天]
        L1 --> L1B[病假/事假]
        L1 --> L1C[基础权限]
        
        L2 --> L2A[≤ 7天]
        L2 --> L2B[所有类型]
        L2 --> L2C[中级权限]
        
        L3 --> L3A[≤ 15天]
        L3 --> L3B[所有类型]
        L3 --> L3C[高级权限]
        
        L4 --> L4A[≤ 30天]
        L4 --> L4B[所有类型]
        L4 --> L4C[最高权限]
    end
    
    subgraph "请假类型"
        direction TB
        
        T1[🤒 病假 sick]
        T2[📋 事假 personal]
        T3[🏖️ 年假 annual]
        T4[🚨 紧急假 emergency]
    end
    
    L1A -.-> T1
    L1A -.-> T2
    
    L2A -.-> T1
    L2A -.-> T2
    L2A -.-> T3
    L2A -.-> T4
    
    L3A -.-> T1
    L3A -.-> T2
    L3A -.-> T3
    L3A -.-> T4
    
    L4A -.-> T1
    L4A -.-> T2
    L4A -.-> T3
    L4A -.-> T4
    
    style L1 fill:#52c41a
    style L2 fill:#faad14
    style L3 fill:#722ed1
    style L4 fill:#ff6b6b
```

## 📊 不同请求的处理路径

```mermaid
graph TD
    subgraph "请求示例"
        direction TB
        
        R1[张三 - 2天病假]
        R2[李四 - 5天年假]
        R3[王五 - 12天紧急假]
        R4[赵六 - 25天事假]
        R5[钱七 - 35天个人假]
    end
    
    subgraph "处理路径"
        direction TB
        
        Chain[👨‍💼 → 👔 → 👩‍💼 → 🤵]
        
        P1[👨‍💼 直接处理]
        P2[👔 部门经理处理]
        P3[👩‍💼 人事总监处理]
        P4[🤵 总经理处理]
        P5[❌ 被拒绝]
    end
    
    R1 --> P1
    R2 --> P2
    R3 --> P3
    R4 --> P4
    R5 --> P5
    
    style R1 fill:#52c41a
    style R2 fill:#faad14
    style R3 fill:#722ed1
    style R4 fill:#ff6b6b
    style R5 fill:#ff4d4f
    
    style P1 fill:#52c41a
    style P2 fill:#faad14
    style P3 fill:#722ed1
    style P4 fill:#ff6b6b
    style P5 fill:#ff4d4f
```

## 🔧 链式构建过程

```mermaid
graph LR
    subgraph "ApprovalChainBuilder.buildChain()"
        direction LR
        
        Step1[创建处理者实例]
        Step2[设置链式关系]
        Step3[返回链头]
        
        Step1 --> Step2 --> Step3
    end
    
    subgraph "链式关系构建"
        direction TB
        
        Create[创建所有处理者]
        Create --> C1[supervisor = new DirectSupervisor()]
        Create --> C2[manager = new DepartmentManager()]
        Create --> C3[hrDirector = new HRDirector()]
        Create --> C4[generalManager = new GeneralManager()]
        
        Link[建立链式关系]
        Link --> L1[supervisor.setNext(manager)]
        Link --> L2[manager.setNext(hrDirector)]
        Link --> L3[hrDirector.setNext(generalManager)]
        
        Return[返回链头]
        Return --> R1[return supervisor]
    end
    
    style Step1 fill:#1890ff
    style Step2 fill:#52c41a
    style Step3 fill:#faad14
```

## 🎭 处理者状态图

```mermaid
stateDiagram-v2
    [*] --> 接收请求
    
    接收请求 --> 检查权限: handle(request)
    
    检查权限 --> 有权限处理: canHandle() = true
    检查权限 --> 无权限处理: canHandle() = false
    
    有权限处理 --> 处理请求: processRequest()
    处理请求 --> 返回结果: 批准/拒绝
    返回结果 --> [*]
    
    无权限处理 --> 检查下级: nextHandler != null
    检查下级 --> 传递请求: 存在下级处理者
    检查下级 --> 拒绝请求: 不存在下级处理者
    
    传递请求 --> 接收请求: nextHandler.handle()
    拒绝请求 --> [*]
```

## 💡 核心概念

### 处理者 (Handler)
- `ApprovalHandler`: 定义处理请求的接口和链式关系

### 具体处理者 (Concrete Handler)
- `DirectSupervisor`: 直接主管处理者
- `DepartmentManager`: 部门经理处理者
- `HRDirector`: 人事总监处理者
- `GeneralManager`: 总经理处理者

### 请求 (Request)
- `LeaveRequest`: 封装请求信息的对象

### 链构建器 (Chain Builder)
- `ApprovalChainBuilder`: 负责构建责任链

## ✅ 优点

1. **降低耦合度**: 请求发送者和接收者解耦
2. **增强灵活性**: 可以动态地增加或删除处理者
3. **简化对象**: 对象无需知道链的结构
4. **职责分离**: 每个处理者只关注自己能处理的请求
5. **符合开闭原则**: 易于扩展新的处理者

## ❌ 缺点

1. **不保证处理**: 请求可能到达链尾仍未被处理
2. **性能影响**: 可能需要遍历整个链
3. **调试困难**: 运行时特征不容易观察
4. **链过长**: 可能影响系统性能

## 🔧 适用场景

- 请假审批系统
- 异常处理机制
- 事件处理系统
- 中间件处理管道
- 权限验证链
- 日志处理系统

## 📝 实现要点

1. **抽象处理者**: 定义处理接口和链式关系
2. **具体处理者**: 实现具体的处理逻辑
3. **链式关系**: 正确设置处理者之间的关系
4. **处理条件**: 明确每个处理者的处理条件
5. **链的构建**: 提供便捷的链构建方式
6. **异常处理**: 处理链断裂或异常情况
7. **性能优化**: 避免过长的处理链
