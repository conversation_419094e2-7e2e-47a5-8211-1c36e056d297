# 命令模式架构图

## 📋 模式概述

命令模式（Command Pattern）是一种行为型设计模式，它将请求封装为对象，从而允许将操作参数化、延迟请求执行、将请求排队以及支持可撤销操作。这种模式将发出请求的对象与执行请求的对象解耦，使系统更加灵活。

## 🎯 解决的问题

命令模式主要解决以下问题：
1. 需要将发出请求的对象与执行请求的对象解耦
2. 需要支持操作的撤销（undo）和重做（redo）
3. 需要将操作参数化，支持在运行时选择不同操作
4. 需要支持请求的排队、日志记录或事务处理

## 🏗️ 架构图

```mermaid
classDiagram
    class Command {
        <<interface>>
        +execute() void
        +undo() void
    }

    class ConcreteCommand {
        -receiver: Receiver
        -parameters
        +ConcreteCommand(receiver)
        +execute() void
        +undo() void
    }

    class Invoker {
        -command: Command
        +setCommand(Command)
        +executeCommand()
        +undoCommand()
    }

    class Receiver {
        +action() void
    }

    class Client {
    }

    Client --> Receiver : creates >
    Client --> ConcreteCommand : creates >
    Client --> Invoker : configures >
    Invoker o-- Command : has >
    ConcreteCommand --|> Command : implements >
    ConcreteCommand --> Receiver : calls >

    note for Command "命令接口\n定义执行和撤销操作"
    note for Receiver "接收者\n知道如何执行实际操作"
    note for Invoker "调用者\n触发命令执行"
```

## 🔍 组件说明

### 命令接口 (Command)
命令接口定义了执行命令和撤销命令的方法。所有具体命令类都必须实现这个接口。

```typescript
interface Command {
  execute(): void;
  undo(): void;
}
```

### 具体命令 (ConcreteCommand)
具体命令类实现了命令接口，并封装了接收者对象和执行所需参数。具体命令负责调用接收者的相应操作。

```typescript
class InsertTextCommand implements Command {
  constructor(private editor: TextEditor, private text: string) {
    this.prevState = editor.getState(); // 存储执行前状态
  }

  execute(): void {
    this.editor.insertText(this.text);
  }

  undo(): void {
    this.editor.restoreState(this.prevState);
  }
}
```

### 接收者 (Receiver)
接收者知道如何执行与请求相关的操作。在命令模式中，命令对象将请求委托给接收者。

```typescript
class TextEditor {
  private content: string = "";

  insertText(text: string): void {
    this.content += text;
  }

  deleteText(position: number, length: number): void {
    this.content = this.content.substring(0, position) +
                  this.content.substring(position + length);
  }

  getState(): EditorState {
    return { content: this.content };
  }

  restoreState(state: EditorState): void {
    this.content = state.content;
  }
}
```

### 调用者 (Invoker)
调用者持有命令对象的引用，并在需要时触发命令的执行。调用者不需要知道命令如何执行或由谁执行。

```typescript
class CommandHistory {
  private history: Command[] = [];
  private undone: Command[] = [];

  execute(command: Command): void {
    command.execute();
    this.history.push(command);
    this.undone = []; // 清除已撤销的命令
  }

  undo(): void {
    const command = this.history.pop();
    if (command) {
      command.undo();
      this.undone.push(command);
    }
  }

  redo(): void {
    const command = this.undone.pop();
    if (command) {
      command.execute();
      this.history.push(command);
    }
  }
}
```

### 客户端 (Client)
客户端创建具体命令对象，并设置命令的接收者。然后，客户端将命令对象传递给调用者或直接调用命令。

```typescript
// 创建接收者
const editor = new TextEditor();

// 创建具体命令
const insertCommand = new InsertTextCommand(editor, "Hello World");

// 创建调用者并执行命令
const history = new CommandHistory();
history.execute(insertCommand);
```

## 🔄 命令模式变体

### 撤销/重做命令

支持撤销和重做操作的命令实现。每个命令存储执行前的状态，以便在需要时恢复。

```typescript
class DeleteCommand implements Command {
  private deletedText: string;
  private position: number;

  constructor(private editor: TextEditor, start: number, end: number) {
    this.position = start;
  }

  execute(): void {
    this.deletedText = this.editor.getTextBetween(this.position, this.position + this.length);
    this.editor.deleteText(this.position, this.length);
  }

  undo(): void {
    this.editor.insertTextAt(this.position, this.deletedText);
  }
}
```

### 宏命令

将多个命令组合成一个复合命令的实现。宏命令允许一次执行多个命令。

```typescript
class MacroCommand implements Command {
  private commands: Command[] = [];

  add(command: Command): void {
    this.commands.push(command);
  }

  execute(): void {
    for (const command of this.commands) {
      command.execute();
    }
  }

  undo(): void {
    // 倒序撤销
    for (let i = this.commands.length - 1; i >= 0; i--) {
      this.commands[i].undo();
    }
  }
}
```

### 队列命令

支持命令排队和延迟执行的实现。

```typescript
class CommandQueue {
  private queue: Command[] = [];

  addCommand(command: Command): void {
    this.queue.push(command);
  }

  processCommands(): void {
    while (this.queue.length > 0) {
      const command = this.queue.shift();
      if (command) {
        command.execute();
      }
    }
  }
}
```

## 💡 设计优势

1. **解耦调用者和接收者**：命令模式将请求发起者与请求执行者解耦，使系统更灵活。

2. **支持可撤销操作**：通过在命令中实现撤销方法，可以轻松支持撤销和重做功能。

3. **支持事务操作**：可以将多个命令组合在一起，作为一个事务执行或撤销。

4. **请求队列和延迟执行**：命令可以存储在队列中，并在需要时执行，支持请求日志和延迟处理。

5. **扩展性**：可以方便地添加新的命令，而无需修改现有代码，符合开闭原则。

## 🚀 代码示例：文本编辑器

以下是使用命令模式实现文本编辑器操作的示例：

```typescript
// 命令接口
interface Command {
  execute(): void;
  undo(): void;
  getName(): string;
}

// 文本编辑器 - 接收者
class TextEditor {
  private content: string = "";
  private cursorPosition: number = 0;

  getContent(): string {
    return this.content;
  }

  setCursorPosition(position: number): void {
    this.cursorPosition = Math.min(position, this.content.length);
  }

  getCursorPosition(): number {
    return this.cursorPosition;
  }

  insertText(text: string): void {
    this.content =
      this.content.substring(0, this.cursorPosition) +
      text +
      this.content.substring(this.cursorPosition);
    this.cursorPosition += text.length;
  }

  deleteText(start: number, end: number): string {
    const deleted = this.content.substring(start, end);
    this.content =
      this.content.substring(0, start) +
      this.content.substring(end);
    this.cursorPosition = start;
    return deleted;
  }
}

// 具体命令 - 插入文本
class InsertTextCommand implements Command {
  private cursorPosition: number;

  constructor(
    private editor: TextEditor,
    private text: string
  ) {
    this.cursorPosition = editor.getCursorPosition();
  }

  execute(): void {
    this.editor.setCursorPosition(this.cursorPosition);
    this.editor.insertText(this.text);
  }

  undo(): void {
    this.editor.deleteText(
      this.cursorPosition,
      this.cursorPosition + this.text.length
    );
  }

  getName(): string {
    return "插入文本";
  }
}

// 具体命令 - 删除文本
class DeleteTextCommand implements Command {
  private deletedText: string = "";

  constructor(
    private editor: TextEditor,
    private start: number,
    private end: number
  ) {}

  execute(): void {
    this.deletedText = this.editor.deleteText(this.start, this.end);
  }

  undo(): void {
    this.editor.setCursorPosition(this.start);
    this.editor.insertText(this.deletedText);
  }

  getName(): string {
    return "删除文本";
  }
}

// 命令历史 - 调用者
class CommandHistory {
  private history: Command[] = [];
  private redoStack: Command[] = [];

  executeCommand(command: Command): void {
    command.execute();
    this.history.push(command);
    this.redoStack = []; // 清除重做栈
  }

  undo(): void {
    if (this.history.length > 0) {
      const command = this.history.pop()!;
      command.undo();
      this.redoStack.push(command);
    }
  }

  redo(): void {
    if (this.redoStack.length > 0) {
      const command = this.redoStack.pop()!;
      command.execute();
      this.history.push(command);
    }
  }
}

// 客户端代码
const editor = new TextEditor();
const history = new CommandHistory();

// 执行插入命令
const insertCmd = new InsertTextCommand(editor, "Hello, world!");
history.executeCommand(insertCmd);
console.log(editor.getContent()); // "Hello, world!"

// 执行删除命令
const deleteCmd = new DeleteTextCommand(editor, 7, 13);
history.executeCommand(deleteCmd);
console.log(editor.getContent()); // "Hello, !"

// 撤销删除
history.undo();
console.log(editor.getContent()); // "Hello, world!"

// 撤销插入
history.undo();
console.log(editor.getContent()); // ""

// 重做插入
history.redo();
console.log(editor.getContent()); // "Hello, world!"
```

## 🎯 使用场景

- **文本编辑器**：实现撤销/重做功能
- **事务处理系统**：将事务封装为命令对象
- **GUI按钮和菜单项**：按钮点击触发命令执行
- **宏录制功能**：将一系列操作记录为宏命令
- **多级撤销功能**：维护命令历史
- **任务调度系统**：将任务封装为命令，支持延迟执行
- **日志系统**：记录命令执行历史，支持系统恢复

## 📝 实现要点

1. **命令接口设计**：确保命令接口简单清晰，通常包含execute()和undo()方法
2. **命令状态保存**：在执行命令前保存接收者状态，以支持撤销操作
3. **参数封装**：命令对象应封装所有执行操作所需的参数
4. **命令历史管理**：设计合适的数据结构存储命令历史
5. **组合命令**：考虑支持命令组合，如宏命令
6. **命令队列**：考虑命令排队、优先级排序等高级功能

## ⚠️ 注意事项

1. **内存消耗**：如果命令数量很大，可能导致内存消耗增加
2. **撤销复杂性**：某些操作的撤销可能非常复杂，需要谨慎设计
3. **命令粒度**：选择适当的命令粒度，过细或过粗都可能导致系统复杂性增加
4. **共享状态**：处理多个命令间共享状态的变化可能很棘手
5. **性能考虑**：对于高性能要求的系统，命令对象的创建和执行可能带来额外开销

## 🔄 与其他模式的关系

- **备忘录模式**：常与命令模式结合使用，保存命令执行前的状态
- **组合模式**：可用于实现宏命令（组合多个命令）
- **策略模式**：命令模式关注请求封装，策略模式关注算法封装
- **责任链模式**：命令可以沿着责任链传递和处理
- **观察者模式**：命令执行后可以通知观察者状态变化

---

> 命令模式通过将请求封装为对象，为请求排队、记录、撤销等需求提供了灵活的解决方案。它是实现可撤销操作、事务处理和GUI交互的理想选择。