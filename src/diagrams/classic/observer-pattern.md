# 观察者模式架构图

## 📋 模式概述

观察者模式（Observer Pattern）是一种行为型设计模式，它定义了对象之间的一对多依赖关系，当一个对象的状态发生改变时，所有依赖于它的对象都会得到通知并自动更新。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 一个对象状态改变需要通知多个其他对象
- 需要实现松耦合的通知机制
- 动态添加或删除观察者
- 实现发布-订阅模式

## 🏗️ 架构图

```mermaid
classDiagram
    class Subject {
        <<interface>>
        +addObserver(observer: Observer): void
        +removeObserver(observerId: string): void
        +notifyObservers(): void
    }
    
    class Observer {
        <<interface>>
        +id: string
        +name: string
        +type: string
        +notifications: string[]
        +update(symbol: string, price: number, change: number): void
    }
    
    class Stock {
        -observers: Observer[]
        +symbol: string
        +name: string
        +price: number
        +previousPrice: number
        +addObserver(observer: Observer): void
        +removeObserver(observerId: string): void
        +notifyObservers(): void
        +setPrice(newPrice: number): void
        +getObservers(): Observer[]
    }
    
    class StockObserver {
        +id: string
        +name: string
        +type: string
        +notifications: string[]
        +update(symbol: string, price: number, change: number): void
    }
    
    class InvestorObserver {
        +id: string
        +name: string
        +type: string
        +notifications: string[]
        +update(symbol: string, price: number, change: number): void
    }
    
    class TraderObserver {
        +id: string
        +name: string
        +type: string
        +notifications: string[]
        +update(symbol: string, price: number, change: number): void
    }
    
    class AnalystObserver {
        +id: string
        +name: string
        +type: string
        +notifications: string[]
        +update(symbol: string, price: number, change: number): void
    }
    
    Subject <|.. Stock
    Observer <|.. StockObserver
    StockObserver <|-- InvestorObserver
    StockObserver <|-- TraderObserver
    StockObserver <|-- AnalystObserver
    
    Stock --> Observer : notifies
    Stock o-- Observer : maintains list
    
    note for Subject "主题接口\n管理观察者列表"
    note for Observer "观察者接口\n接收状态变化通知"
    note for Stock "具体主题\n股票价格管理"
    note for StockObserver "具体观察者\n股票价格监听者"
```

## 🔍 组件说明

### 主题接口 (Subject Interface)
- **Subject**: 定义主题的基本操作
  - `addObserver()`: 添加观察者
  - `removeObserver()`: 移除观察者
  - `notifyObservers()`: 通知所有观察者

### 观察者接口 (Observer Interface)
- **Observer**: 定义观察者的基本结构
  - `update()`: 接收主题状态变化的通知
  - 包含观察者的基本信息和通知历史

### 具体主题 (Concrete Subject)
- **Stock**: 股票价格管理器
  - 维护观察者列表
  - 管理股票价格状态
  - 价格变化时通知所有观察者

### 具体观察者 (Concrete Observers)
- **InvestorObserver**: 投资者观察者
- **TraderObserver**: 交易员观察者
- **AnalystObserver**: 分析师观察者
- 每种观察者对价格变化有不同的反应逻辑

## 📊 通知流程

```mermaid
sequenceDiagram
    participant S as Stock
    participant I as InvestorObserver
    participant T as TraderObserver
    participant A as AnalystObserver
    
    Note over S: 价格从 150 变为 155
    
    S->>S: setPrice(155)
    S->>S: notifyObservers()
    
    par 并行通知所有观察者
        S->>I: update("AAPL", 155, +5)
        Note over I: 处理投资者逻辑
        I->>I: 记录价格上涨通知
    and
        S->>T: update("AAPL", 155, +5)
        Note over T: 处理交易员逻辑
        T->>T: 记录交易信号
    and
        S->>A: update("AAPL", 155, +5)
        Note over A: 处理分析师逻辑
        A->>A: 记录分析报告
    end
    
    Note over I,A: 所有观察者都收到了通知
```

## 💡 设计优势

1. **松耦合**: 主题和观察者之间松耦合
2. **动态关系**: 可以在运行时添加或删除观察者
3. **广播通信**: 一次通知可以到达多个观察者
4. **开闭原则**: 可以独立扩展主题和观察者
5. **抽象耦合**: 主题只知道观察者接口，不知道具体实现

## 🔧 TypeScript 实现

### 接口定义
```typescript
interface Observer {
  id: string;
  name: string;
  type: string;
  notifications: string[];
  update(symbol: string, price: number, change: number): void;
}

interface Subject {
  addObserver(observer: Observer): void;
  removeObserver(observerId: string): void;
  notifyObservers(): void;
}
```

### 具体主题实现
```typescript
class Stock implements Subject {
  private observers: Observer[] = [];
  symbol: string;
  name: string;
  price: number;
  previousPrice: number;

  constructor(symbol: string, name: string, initialPrice: number) {
    this.symbol = symbol;
    this.name = name;
    this.price = initialPrice;
    this.previousPrice = initialPrice;
  }

  addObserver(observer: Observer): void {
    this.observers.push(observer);
  }

  removeObserver(observerId: string): void {
    this.observers = this.observers.filter(obs => obs.id !== observerId);
  }

  notifyObservers(): void {
    const change = this.price - this.previousPrice;
    this.observers.forEach(observer => {
      observer.update(this.symbol, this.price, change);
    });
  }

  setPrice(newPrice: number): void {
    this.previousPrice = this.price;
    this.price = newPrice;
    this.notifyObservers();
  }
}
```

### 具体观察者实现
```typescript
class StockObserver implements Observer {
  id: string;
  name: string;
  type: string;
  notifications: string[] = [];

  constructor(name: string, type: string) {
    this.id = Math.random().toString(36).slice(2, 11);
    this.name = name;
    this.type = type;
  }

  update(symbol: string, price: number, change: number): void {
    const timestamp = new Date().toLocaleTimeString();
    let message = "";

    switch (this.type) {
      case "investor":
        if (change > 0) {
          message = `📈 ${symbol} 上涨了 ¥${change.toFixed(2)}，当前价格 ¥${price.toFixed(2)}`;
        } else if (change < 0) {
          message = `📉 ${symbol} 下跌了 ¥${Math.abs(change).toFixed(2)}，当前价格 ¥${price.toFixed(2)}`;
        }
        break;
      case "trader":
        const changePercent = ((change / (price - change)) * 100).toFixed(2);
        message = `🔄 交易提醒：${symbol} 变动 ${changePercent}%，价格 ¥${price.toFixed(2)}`;
        break;
      case "analyst":
        if (Math.abs(change) > 5) {
          message = `⚠️ 分析师警告：${symbol} 出现大幅波动 ¥${change.toFixed(2)}`;
        } else {
          message = `📊 ${symbol} 正常波动 ¥${change.toFixed(2)}`;
        }
        break;
    }

    this.notifications.unshift(`[${timestamp}] ${message}`);
    if (this.notifications.length > 10) {
      this.notifications = this.notifications.slice(0, 10);
    }
  }
}
```

### 使用示例
```typescript
// 创建股票主题
const stock = new Stock("AAPL", "苹果公司", 150.0);

// 创建观察者
const investor = new StockObserver("张三", "investor");
const trader = new StockObserver("李四", "trader");
const analyst = new StockObserver("王五", "analyst");

// 添加观察者
stock.addObserver(investor);
stock.addObserver(trader);
stock.addObserver(analyst);

// 价格变化，自动通知所有观察者
stock.setPrice(155.0);  // 所有观察者都会收到通知
stock.setPrice(148.0);  // 再次通知
```

## 🚀 观察者模式变体

### 推模型 (Push Model)
```typescript
// 主题推送所有相关数据给观察者
interface Observer {
  update(data: StockData): void;
}

class Stock {
  notifyObservers(): void {
    const data = {
      symbol: this.symbol,
      price: this.price,
      change: this.price - this.previousPrice,
      timestamp: new Date()
    };
    this.observers.forEach(observer => observer.update(data));
  }
}
```

### 拉模型 (Pull Model)
```typescript
// 观察者主动从主题拉取需要的数据
interface Observer {
  update(subject: Stock): void;
}

class StockObserver implements Observer {
  update(stock: Stock): void {
    // 观察者主动获取需要的数据
    const price = stock.getPrice();
    const change = stock.getChange();
    // 处理逻辑...
  }
}
```

### 事件驱动模型
```typescript
class EventEmitter {
  private events: Map<string, Function[]> = new Map();

  on(event: string, callback: Function): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(callback);
  }

  emit(event: string, ...args: any[]): void {
    const callbacks = this.events.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(...args));
    }
  }
}
```

## 🎯 使用场景

- **MVC 架构**: Model 变化通知 View 更新
- **事件系统**: GUI 事件处理
- **消息队列**: 发布-订阅消息系统
- **数据绑定**: 数据变化自动更新 UI
- **缓存更新**: 数据变化时更新相关缓存

## 📝 实现要点

1. **接口设计**: 定义清晰的主题和观察者接口
2. **通知时机**: 确定何时通知观察者
3. **数据传递**: 选择推模型还是拉模型
4. **内存管理**: 避免观察者内存泄漏
5. **异常处理**: 处理观察者更新时的异常

## ⚠️ 注意事项

1. **内存泄漏**: 忘记移除观察者可能导致内存泄漏
2. **循环依赖**: 观察者之间可能形成循环依赖
3. **性能问题**: 大量观察者时通知性能问题
4. **更新顺序**: 观察者更新顺序可能影响结果
5. **异常传播**: 一个观察者异常可能影响其他观察者

## 🔄 与其他模式的关系

- **与中介者模式**: 都处理对象间通信，但方式不同
- **与命令模式**: 可以结合使用，命令作为通知内容
- **与状态模式**: 状态变化可以通过观察者通知
- **与策略模式**: 观察者的处理逻辑可以使用策略模式

---

> 观察者模式是实现松耦合设计的重要模式，它让对象之间的依赖关系变得更加灵活和可维护。
