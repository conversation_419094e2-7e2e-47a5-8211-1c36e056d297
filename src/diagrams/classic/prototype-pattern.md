# 原型模式架构图

## 📋 模式概述

原型模式（Prototype Pattern）是一种创建型设计模式，它通过复制现有实例来创建新对象，而不是通过实例化类。这在创建对象的成本较高时特别有用。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 对象的创建成本很高（如需要复杂的初始化过程）
- 需要创建大量相似的对象
- 希望避免重复的初始化工作
- 需要在运行时动态配置对象

## 🏗️ 架构图

```mermaid
classDiagram
    class Prototype {
        <<interface>>
        +clone() Prototype
        +getInfo() string
    }
    
    class Equipment {
        +name: string
        +type: string
        +attack: number
        +defense: number
        +clone() Equipment
        +getInfo() string
    }
    
    class GameCharacter {
        -name: string
        -level: number
        -health: number
        -mana: number
        -equipment: Equipment[]
        +clone() GameCharacter
        +addEquipment(equipment: Equipment) void
        +levelUp() void
        +getName() string
        +getLevel() number
        +getHealth() number
        +getMana() number
        +getEquipment() Equipment[]
    }
    
    class CharacterPrototypeManager {
        -prototypes: Map~string, GameCharacter~
        +registerPrototype(key: string, prototype: GameCharacter) void
        +createCharacter(key: string) GameCharacter
        +getAvailableTypes() string[]
    }
    
    Prototype <|-- GameCharacter
    GameCharacter --> Equipment : contains
    CharacterPrototypeManager --> GameCharacter : manages
    Equipment --> Equipment : clones
    GameCharacter --> GameCharacter : clones
```

## 🔄 克隆过程时序图

```mermaid
sequenceDiagram
    participant Client
    participant Manager as CharacterPrototypeManager
    participant Prototype as GameCharacter
    participant Equipment
    participant Clone as GameCharacter(Clone)
    participant EquipClone as Equipment(Clone)
    
    Client->>Manager: createCharacter("warrior")
    Manager->>Prototype: clone()
    
    Prototype->>Clone: new GameCharacter(name, level)
    Prototype->>Clone: setHealth(health)
    Prototype->>Clone: setMana(mana)
    
    loop for each equipment
        Prototype->>Equipment: clone()
        Equipment->>EquipClone: new Equipment(name, type, attack, defense)
        Equipment-->>Prototype: cloned equipment
        Prototype->>Clone: addEquipment(cloned equipment)
    end
    
    Prototype-->>Manager: cloned character
    Manager-->>Client: cloned character
    
    Client->>Clone: levelUp()
    Clone->>Clone: level++, health+=100, mana+=50
    
    Note over Prototype: 原型对象保持不变
    Note over Clone: 克隆对象独立变化
```

## 🎮 游戏角色原型系统

```mermaid
graph TB
    subgraph "原型管理器"
        PM[CharacterPrototypeManager]
        PM --> WP[战士原型]
        PM --> MP[法师原型]
        PM --> AP[弓箭手原型]
    end
    
    subgraph "战士原型 (Level 10)"
        WP --> WE1[钢铁剑<br/>攻击+50]
        WP --> WE2[钢铁盾<br/>防御+30]
        WP --> WS[生命值: 1000<br/>魔法值: 500]
    end
    
    subgraph "法师原型 (Level 8)"
        MP --> ME1[魔法杖<br/>攻击+30]
        MP --> ME2[法师袍<br/>防御+15]
        MP --> MS[生命值: 800<br/>魔法值: 400]
    end
    
    subgraph "弓箭手原型 (Level 9)"
        AP --> AE1[精灵弓<br/>攻击+40]
        AP --> AE2[皮甲<br/>防御+20]
        AP --> AS[生命值: 900<br/>魔法值: 450]
    end
    
    subgraph "克隆实例"
        WP -.->|clone| W1[玩家1战士]
        WP -.->|clone| W2[玩家2战士]
        MP -.->|clone| M1[玩家1法师]
        AP -.->|clone| A1[玩家1弓箭手]
    end
    
    W1 --> W1E[独立装备副本]
    W2 --> W2E[独立装备副本]
    M1 --> M1E[独立装备副本]
    A1 --> A1E[独立装备副本]
    
    style WP fill:#ff6b6b
    style MP fill:#1890ff
    style AP fill:#52c41a
    
    style W1 fill:#ffcccb
    style W2 fill:#ffcccb
    style M1 fill:#cce7ff
    style A1 fill:#d9f7be
```

## 🔧 深度克隆 vs 浅度克隆

```mermaid
graph LR
    subgraph "浅度克隆 (Shallow Clone)"
        SC[原始对象]
        SCC[克隆对象]
        SE[共享装备对象]
        
        SC --> SE
        SCC --> SE
        
        note1[修改装备会影响<br/>原始对象和克隆对象]
    end
    
    subgraph "深度克隆 (Deep Clone)"
        DC[原始对象]
        DCC[克隆对象]
        DE1[原始装备对象]
        DE2[克隆装备对象]
        
        DC --> DE1
        DCC --> DE2
        
        note2[修改装备只影响<br/>对应的对象]
    end
    
    style SC fill:#ff6b6b
    style SCC fill:#ff6b6b
    style SE fill:#ffa500
    
    style DC fill:#52c41a
    style DCC fill:#52c41a
    style DE1 fill:#87ceeb
    style DE2 fill:#87ceeb
```

## 📊 内存使用对比

```mermaid
graph TB
    subgraph "传统创建方式"
        T1[创建对象1<br/>完整初始化]
        T2[创建对象2<br/>完整初始化]
        T3[创建对象3<br/>完整初始化]
        T4[创建对象4<br/>完整初始化]
        
        TC[总成本: 4 × 100% = 400%]
    end
    
    subgraph "原型模式"
        P[原型对象<br/>100%初始化成本]
        C1[克隆对象1<br/>30%复制成本]
        C2[克隆对象2<br/>30%复制成本]
        C3[克隆对象3<br/>30%复制成本]
        
        PC[总成本: 100% + 3 × 30% = 190%]
        
        P -.->|clone| C1
        P -.->|clone| C2
        P -.->|clone| C3
    end
    
    style T1 fill:#ff6b6b
    style T2 fill:#ff6b6b
    style T3 fill:#ff6b6b
    style T4 fill:#ff6b6b
    style TC fill:#ff4d4f
    
    style P fill:#52c41a
    style C1 fill:#95de64
    style C2 fill:#95de64
    style C3 fill:#95de64
    style PC fill:#389e0d
```

## 💡 核心概念

### 原型接口 (Prototype)
- `Prototype`: 定义克隆方法的接口

### 具体原型 (Concrete Prototype)
- `GameCharacter`: 实现克隆方法的具体类
- `Equipment`: 装备类，也需要支持克隆

### 原型管理器 (Prototype Manager)
- `CharacterPrototypeManager`: 管理和创建原型对象

## ✅ 优点

1. **性能优化**: 避免重复的初始化工作，提高对象创建效率
2. **运行时配置**: 可以在运行时动态添加和删除原型
3. **减少子类**: 不需要为每种配置创建子类
4. **简化创建**: 客户端无需知道具体的创建细节

## ❌ 缺点

1. **深度克隆复杂**: 包含循环引用的对象克隆很复杂
2. **克隆方法实现**: 每个类都需要实现克隆方法
3. **内存占用**: 需要维护原型对象实例

## 🔧 适用场景

- 游戏角色系统
- 图形编辑器中的对象复制
- 数据库记录模板
- 配置对象的快速创建
- 复杂对象的批量创建

## 📝 实现要点

1. **克隆接口**: 定义统一的克隆接口
2. **深度克隆**: 确保对象的完整独立复制
3. **原型注册**: 提供原型的注册和管理机制
4. **循环引用处理**: 处理对象间的循环引用问题
5. **克隆验证**: 确保克隆对象的正确性
6. **性能考虑**: 在克隆效率和内存使用间找到平衡
