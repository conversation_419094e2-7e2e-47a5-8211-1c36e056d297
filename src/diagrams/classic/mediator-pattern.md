# 中介者模式架构图解

> **模式名称**: Mediator Pattern (中介者模式)  
> **模式类型**: 行为型模式  
> **复杂度**: ⭐⭐⭐  
> **使用频率**: ⭐⭐⭐

## 📋 模式概述

中介者模式定义一个中介对象来封装一系列对象之间的交互。中介者使各对象不需要显式地相互引用，从而使其耦合松散，而且可以独立地改变它们之间的交互。

## 🏗️ 核心架构

### 类图结构

```mermaid
classDiagram
    class ChatMediator {
        <<interface>>
        +sendMessage(message: string, user: User) void
        +addUser(user: User) void
        +removeUser(user: User) void
        +getUsers() User[]
        +getUserCount() number
    }
    
    class ChatRoom {
        -users: Map~string, User~
        -messageHistory: ChatMessage[]
        -roomName: string
        -maxUsers: number
        +sendMessage(message: string, sender: User) void
        +addUser(user: User) void
        +removeUser(user: User) void
        +getUsers() User[]
        +getUserCount() number
        +getOnlineUserCount() number
        +getMessageHistory() ChatMessage[]
        +getStats() RoomStats
        -isUserMuted(userName: string) boolean
        -broadcastSystemMessage(message: string) void
    }
    
    class User {
        <<abstract>>
        #name: string
        #mediator: ChatMediator
        #isOnline: boolean
        +send(message: string)* void
        +receive(message: string, from: User)* void
        +getName() string
        +setOnlineStatus(status: boolean) void
        +isUserOnline() boolean
    }
    
    class RegularUser {
        -messageHistory: ChatMessage[]
        +send(message: string) void
        +receive(message: string, from: User) void
        +getMessageHistory() ChatMessage[]
        +clearHistory() void
    }
    
    class AdminUser {
        -messageHistory: ChatMessage[]
        -mutedUsers: Set~string~
        +send(message: string) void
        +receive(message: string, from: User) void
        +muteUser(userName: string) void
        +unmuteUser(userName: string) void
        +isUserMuted(userName: string) boolean
        +getMutedUsers() string[]
        +getMessageHistory() ChatMessage[]
    }
    
    class ChatMessage {
        +content: string
        +sender: string
        +receiver: string
        +timestamp: Date
        +type: MessageType
    }
    
    class ChatRoomManager {
        -rooms: Map~string, ChatRoom~
        +createRoom(roomName: string, maxUsers?: number) ChatRoom
        +getRoom(roomName: string) ChatRoom
        +deleteRoom(roomName: string) boolean
        +getRooms() ChatRoom[]
        +getTotalUsers() number
        +getSystemStats() SystemStats
    }
    
    ChatMediator <|.. ChatRoom
    User <|-- RegularUser
    User <|-- AdminUser
    User --> ChatMediator : uses
    ChatRoom --> User : manages
    ChatRoom --> ChatMessage : creates
    ChatRoomManager --> ChatRoom : manages
```

### 通信流程对比

```mermaid
graph TB
    subgraph "无中介者 - 直接通信"
        U1[用户A] <--> U2[用户B]
        U1 <--> U3[用户C]
        U1 <--> U4[用户D]
        U2 <--> U3
        U2 <--> U4
        U3 <--> U4
        U5[复杂度: O(N²)]
        U4 --> U5
    end
    
    subgraph "中介者模式 - 中心化通信"
        M1[用户A] --> M5[ChatRoom<br/>中介者]
        M2[用户B] --> M5
        M3[用户C] --> M5
        M4[用户D] --> M5
        M5 --> M1
        M5 --> M2
        M5 --> M3
        M5 --> M4
        M6[复杂度: O(N)]
        M5 --> M6
    end
    
    style U5 fill:#ffcccc
    style M6 fill:#ccffcc
```

## 💬 聊天室系统架构

### 消息传递流程

```mermaid
sequenceDiagram
    participant Alice as Alice(RegularUser)
    participant Bob as Bob(RegularUser)
    participant Admin as Admin(AdminUser)
    participant ChatRoom as ChatRoom
    participant Charlie as Charlie(RegularUser)
    
    Note over ChatRoom: 用户加入聊天室
    Alice->>ChatRoom: 加入聊天室
    ChatRoom->>ChatRoom: addUser(Alice)
    ChatRoom-->>Alice: 加入成功
    ChatRoom->>Bob: 系统消息: Alice加入了聊天室
    ChatRoom->>Admin: 系统消息: Alice加入了聊天室
    ChatRoom->>Charlie: 系统消息: Alice加入了聊天室
    
    Note over ChatRoom: 普通消息传递
    Alice->>ChatRoom: send("大家好！")
    ChatRoom->>ChatRoom: sendMessage("大家好！", Alice)
    ChatRoom->>Bob: receive("大家好！", Alice)
    ChatRoom->>Admin: receive("大家好！", Alice)
    ChatRoom->>Charlie: receive("大家好！", Alice)
    
    Note over ChatRoom: 管理员操作
    Admin->>ChatRoom: send("欢迎Alice！")
    ChatRoom->>ChatRoom: sendMessage("欢迎Alice！", Admin)
    ChatRoom->>Alice: receive("欢迎Alice！", Admin)
    ChatRoom->>Bob: receive("欢迎Alice！", Admin)
    ChatRoom->>Charlie: receive("欢迎Alice！", Admin)
    
    Note over ChatRoom: 禁言功能
    Admin->>Admin: muteUser("Charlie")
    Charlie->>ChatRoom: send("我想说话")
    ChatRoom->>ChatRoom: 检查禁言状态
    ChatRoom-->>Charlie: 消息被阻止(已被禁言)
```

### 用户权限管理

```mermaid
stateDiagram-v2
    [*] --> Offline : 用户创建
    Offline --> Online : 加入聊天室
    Online --> Speaking : 发送消息
    Speaking --> Online : 消息发送完成
    Online --> Muted : 被管理员禁言
    Muted --> Online : 解除禁言
    Online --> Offline : 离开聊天室
    Offline --> [*] : 用户销毁
    
    state Online {
        [*] --> RegularUser
        [*] --> AdminUser
        
        state RegularUser {
            [*] --> CanSpeak
            CanSpeak --> CanSpeak : 发送普通消息
        }
        
        state AdminUser {
            [*] --> CanManage
            CanManage --> CanManage : 发送管理员消息
            CanManage --> CanManage : 禁言/解禁用户
        }
    }
    
    state Muted {
        [*] --> Silenced
        Silenced --> Silenced : 消息被阻止
    }
```

## 🏢 系统架构层次

### 多聊天室管理

```mermaid
graph TB
    subgraph "ChatRoomManager - 系统级中介者"
        CRM[ChatRoomManager]
        CRM --> R1[技术讨论室]
        CRM --> R2[休闲聊天室]
        CRM --> R3[项目协作室]
    end
    
    subgraph "ChatRoom1 - 房间级中介者"
        R1 --> U1[Alice - Admin]
        R1 --> U2[Bob - User]
        R1 --> U3[Charlie - User]
    end
    
    subgraph "ChatRoom2 - 房间级中介者"
        R2 --> U4[David - User]
        R2 --> U5[Eve - Admin]
        R2 --> U6[Frank - User]
    end
    
    subgraph "ChatRoom3 - 房间级中介者"
        R3 --> U7[Grace - Admin]
        R3 --> U8[Henry - User]
    end
    
    style CRM fill:#e3f2fd
    style R1 fill:#f3e5f5
    style R2 fill:#f3e5f5
    style R3 fill:#f3e5f5
```

### 消息路由策略

```mermaid
flowchart TD
    A[用户发送消息] --> B{检查用户状态}
    B -->|离线| C[拒绝消息]
    B -->|在线| D{检查禁言状态}
    D -->|已禁言| E[阻止消息]
    D -->|未禁言| F[接受消息]
    F --> G{确定消息类型}
    G -->|普通消息| H[广播给所有在线用户]
    G -->|管理员消息| I[标记为管理员消息并广播]
    G -->|系统消息| J[系统级广播]
    H --> K[记录消息历史]
    I --> K
    J --> K
    K --> L[消息发送完成]
    
    style E fill:#ffcccc
    style C fill:#ffcccc
    style L fill:#ccffcc
```

## 🎯 应用场景

### 1. 用户界面组件协调
- **场景**: 复杂UI界面中组件间的交互
- **优势**: 减少组件间的直接依赖
- **示例**: 表单验证、对话框管理

### 2. 工作流系统
- **场景**: 业务流程中各个步骤的协调
- **优势**: 集中管理流程逻辑
- **示例**: 审批流程、订单处理流程

### 3. 游戏系统
- **场景**: 游戏中各个系统的协调
- **优势**: 统一管理游戏状态和事件
- **示例**: 战斗系统、任务系统

### 4. 微服务协调
- **场景**: 微服务架构中的服务协调
- **优势**: 减少服务间的直接调用
- **示例**: API网关、消息总线

## 🔄 与观察者模式对比

```mermaid
graph LR
    subgraph "观察者模式"
        O1[Subject] --> O2[Observer1]
        O1 --> O3[Observer2]
        O1 --> O4[Observer3]
        O5[一对多通知]
        O1 --> O5
    end
    
    subgraph "中介者模式"
        M1[Colleague1] --> M5[Mediator]
        M2[Colleague2] --> M5
        M3[Colleague3] --> M5
        M4[Colleague4] --> M5
        M5 --> M1
        M5 --> M2
        M5 --> M3
        M5 --> M4
        M6[多对多协调]
        M5 --> M6
    end
    
    style O5 fill:#ffffcc
    style M6 fill:#ccffcc
```

## ✅ 优点

1. **降低耦合**: 减少对象间的直接依赖关系
2. **集中控制**: 将复杂的交互逻辑集中管理
3. **可复用性**: 中介者可以被多个对象复用
4. **易于维护**: 交互逻辑的修改只需要修改中介者
5. **符合迪米特法则**: 对象只与中介者通信

## ❌ 缺点

1. **中介者复杂化**: 中介者可能变得过于复杂
2. **单点故障**: 中介者成为系统的关键点
3. **性能开销**: 增加了一层间接调用
4. **过度设计**: 对于简单交互可能是过度设计

## 🔗 相关模式

- **观察者模式**: 都处理对象间的通信，但方式不同
- **外观模式**: 都提供统一接口，但目的不同
- **命令模式**: 中介者可以使用命令模式来处理请求

## 💡 最佳实践

1. **职责分离**: 保持中介者职责单一，避免过度复杂
2. **接口设计**: 设计清晰的中介者接口
3. **状态管理**: 合理管理中介者的状态
4. **性能优化**: 对于高频交互，考虑性能优化
5. **扩展性**: 设计时考虑未来的扩展需求
