# 迭代器模式架构图解

> **模式名称**: Iterator Pattern (迭代器模式)  
> **模式类型**: 行为型模式  
> **复杂度**: ⭐⭐⭐  
> **使用频率**: ⭐⭐⭐⭐⭐

## 📋 模式概述

迭代器模式提供一种方法顺序访问聚合对象中的各个元素，而又不暴露该对象的内部表示。它将遍历逻辑从聚合对象中分离出来，使得可以独立地改变遍历算法。

## 🏗️ 核心架构

### 类图结构

```mermaid
classDiagram
    class Iterator~T~ {
        <<interface>>
        +hasNext() boolean
        +next() T
        +reset() void
        +current() T
    }
    
    class Iterable~T~ {
        <<interface>>
        +createIterator() Iterator~T~
        +getItems() T[]
        +getCount() number
    }
    
    class SequentialIterator {
        -position: number
        -playlist: Playlist
        +hasNext() boolean
        +next() Song
        +reset() void
        +current() Song
    }
    
    class RandomIterator {
        -visitedIndices: Set~number~
        -currentIndex: number
        -playlist: Playlist
        +hasNext() boolean
        +next() Song
        +reset() void
        +current() Song
    }
    
    class GenreFilterIterator {
        -position: number
        -filteredSongs: Song[]
        -currentSong: Song
        +hasNext() boolean
        +next() Song
        +reset() void
        +current() Song
    }
    
    class Playlist {
        -songs: Song[]
        -name: string
        +addSong(song: Song) void
        +removeSong(songId: string) boolean
        +createIterator() Iterator~Song~
        +createRandomIterator() Iterator~Song~
        +createGenreIterator(genre: string) Iterator~Song~
        +getItems() Song[]
        +getCount() number
    }
    
    class Song {
        +id: string
        +title: string
        +artist: string
        +duration: number
        +genre: string
        +getInfo() string
        +getDurationString() string
    }
    
    class MusicPlayer {
        -currentPlaylist: Playlist
        -currentIterator: Iterator~Song~
        -isPlaying: boolean
        -playMode: string
        +setPlaylist(playlist: Playlist) void
        +setPlayMode(mode: string, genre?: string) void
        +play() Song
        +next() Song
        +hasNext() boolean
        +reset() void
    }
    
    Iterator~T~ <|.. SequentialIterator
    Iterator~T~ <|.. RandomIterator
    Iterator~T~ <|.. GenreFilterIterator
    Iterable~T~ <|.. Playlist
    Playlist --> Song : contains
    Playlist ..> SequentialIterator : creates
    Playlist ..> RandomIterator : creates
    Playlist ..> GenreFilterIterator : creates
    MusicPlayer --> Playlist : uses
    MusicPlayer --> Iterator~T~ : uses
```

### 迭代器策略对比

```mermaid
graph TB
    subgraph "顺序迭代器"
        A1[开始] --> A2[position = 0]
        A2 --> A3{position < count?}
        A3 -->|是| A4[返回 songs[position]]
        A4 --> A5[position++]
        A5 --> A3
        A3 -->|否| A6[结束]
    end
    
    subgraph "随机迭代器"
        B1[开始] --> B2[visitedIndices = Set()]
        B2 --> B3{visitedIndices.size < count?}
        B3 -->|是| B4[生成随机索引]
        B4 --> B5{索引已访问?}
        B5 -->|否| B6[返回 songs[randomIndex]]
        B6 --> B7[添加到已访问集合]
        B7 --> B3
        B5 -->|是| B4
        B3 -->|否| B8[结束]
    end
    
    subgraph "过滤迭代器"
        C1[开始] --> C2[按条件过滤歌曲]
        C2 --> C3[position = 0]
        C3 --> C4{position < filteredCount?}
        C4 -->|是| C5[返回 filteredSongs[position]]
        C5 --> C6[position++]
        C6 --> C4
        C4 -->|否| C7[结束]
    end
```

## 🎵 音乐播放器系统架构

### 播放模式切换流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Player as MusicPlayer
    participant Playlist as Playlist
    participant SeqIter as SequentialIterator
    participant RandIter as RandomIterator
    participant GenreIter as GenreFilterIterator
    
    Client->>Player: setPlayMode("sequential")
    Player->>Playlist: createIterator()
    Playlist->>SeqIter: new SequentialIterator(this)
    SeqIter-->>Playlist: iterator
    Playlist-->>Player: iterator
    
    Client->>Player: setPlayMode("random")
    Player->>Playlist: createRandomIterator()
    Playlist->>RandIter: new RandomIterator(this)
    RandIter-->>Playlist: iterator
    Playlist-->>Player: iterator
    
    Client->>Player: setPlayMode("genre", "Rock")
    Player->>Playlist: createGenreIterator("Rock")
    Playlist->>GenreIter: new GenreFilterIterator(this, "Rock")
    GenreIter-->>Playlist: iterator
    Playlist-->>Player: iterator
    
    Client->>Player: next()
    Player->>SeqIter: next()
    SeqIter-->>Player: song
    Player-->>Client: song
```

### 数据流向图

```mermaid
flowchart LR
    subgraph "数据层"
        Songs[(歌曲集合)]
    end
    
    subgraph "聚合层"
        Playlist[播放列表]
    end
    
    subgraph "迭代器层"
        SeqIter[顺序迭代器]
        RandIter[随机迭代器]
        GenreIter[流派迭代器]
    end
    
    subgraph "客户端层"
        Player[音乐播放器]
        UI[用户界面]
    end
    
    Songs --> Playlist
    Playlist --> SeqIter
    Playlist --> RandIter
    Playlist --> GenreIter
    SeqIter --> Player
    RandIter --> Player
    GenreIter --> Player
    Player --> UI
    
    style Songs fill:#e1f5fe
    style Playlist fill:#f3e5f5
    style SeqIter fill:#e8f5e8
    style RandIter fill:#fff3e0
    style GenreIter fill:#fce4ec
    style Player fill:#f1f8e9
    style UI fill:#e3f2fd
```

## 🔄 迭代器状态管理

### 状态转换图

```mermaid
stateDiagram-v2
    [*] --> Created : 创建迭代器
    Created --> Ready : reset()
    Ready --> Iterating : hasNext() = true
    Iterating --> Iterating : next()
    Iterating --> Finished : hasNext() = false
    Finished --> Ready : reset()
    Ready --> [*] : 销毁
    
    state Iterating {
        [*] --> CheckNext
        CheckNext --> ReturnCurrent : hasNext() = true
        CheckNext --> NoMore : hasNext() = false
        ReturnCurrent --> UpdatePosition
        UpdatePosition --> CheckNext
        NoMore --> [*]
    }
```

### 内存使用对比

```mermaid
graph TB
    subgraph "传统方式"
        T1[客户端直接访问集合]
        T2[需要了解内部结构]
        T3[遍历逻辑分散]
        T4[难以扩展新的遍历方式]
        T1 --> T2 --> T3 --> T4
    end
    
    subgraph "迭代器模式"
        I1[统一的迭代器接口]
        I2[封装遍历逻辑]
        I3[支持多种遍历策略]
        I4[易于扩展新迭代器]
        I1 --> I2 --> I3 --> I4
    end
    
    style T1 fill:#ffebee
    style T2 fill:#ffebee
    style T3 fill:#ffebee
    style T4 fill:#ffebee
    style I1 fill:#e8f5e8
    style I2 fill:#e8f5e8
    style I3 fill:#e8f5e8
    style I4 fill:#e8f5e8
```

## 🎯 应用场景

### 适用场景
- 需要访问聚合对象的内容而无需暴露其内部表示
- 需要为聚合对象提供多种遍历方式
- 需要为不同的聚合结构提供统一的遍历接口
- 需要支持对聚合对象的并行遍历

### 实际应用
- **音乐播放器**: 播放列表的不同播放模式
- **数据库**: 结果集的遍历
- **文件系统**: 目录树的遍历
- **集合框架**: 各种数据结构的统一遍历
- **分页系统**: 大数据集的分页访问

## ⚖️ 优缺点分析

### ✅ 优点
- **统一接口**: 为不同的聚合结构提供统一的遍历接口
- **封装性**: 隐藏聚合对象的内部结构
- **多样性**: 支持多种遍历方式
- **并发安全**: 每个迭代器维护独立的遍历状态
- **扩展性**: 易于添加新的迭代策略

### ❌ 缺点
- **复杂性**: 对于简单的遍历，使用迭代器可能过于复杂
- **内存开销**: 每个迭代器都需要维护状态信息
- **性能**: 间接访问可能带来轻微的性能损失

## 🔧 实现要点

### 关键设计原则
1. **单一职责**: 每个迭代器只负责一种遍历策略
2. **开闭原则**: 易于扩展新的迭代器类型
3. **依赖倒置**: 客户端依赖抽象的迭代器接口

### 最佳实践
- 为不同的遍历需求创建专门的迭代器
- 确保迭代器的状态独立性
- 提供重置功能以支持重复遍历
- 考虑并发访问的安全性
- 合理处理集合修改时的迭代器失效问题
