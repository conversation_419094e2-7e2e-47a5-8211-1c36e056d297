# 状态模式架构图

## 📋 模式概述

状态模式（State Pattern）是一种行为型设计模式，它允许对象在内部状态改变时改变它的行为，对象看起来好像修改了它的类。状态模式将状态相关的行为局部化，并且将不同状态的行为分割开来。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 对象的行为依赖于它的状态
- 需要根据状态改变对象的行为
- 状态转换逻辑复杂，使用条件语句难以维护
- 需要避免大量的条件分支语句

## 🏗️ 架构图

```mermaid
classDiagram
    class OrderState {
        <<interface>>
        +getStateName(): string
        +getDescription(): string
        +getAvailableActions(): string[]
        +handleAction(context: OrderContext, action: string): boolean
        +getStateColor(): string
        +getStateIcon(): string
    }
    
    class OrderContext {
        -state: OrderState
        -orderInfo: OrderInfo
        +setState(state: OrderState, action?: string): void
        +getState(): OrderState
        +executeAction(action: string): boolean
        +getOrderInfo(): OrderInfo
        -addToHistory(stateName: string, action?: string): void
    }
    
    class PendingPaymentState {
        +getStateName(): string
        +getDescription(): string
        +getAvailableActions(): string[]
        +handleAction(context: OrderContext, action: string): boolean
        +getStateColor(): string
        +getStateIcon(): string
    }
    
    class PaidState {
        +getStateName(): string
        +getDescription(): string
        +getAvailableActions(): string[]
        +handleAction(context: OrderContext, action: string): boolean
        +getStateColor(): string
        +getStateIcon(): string
    }
    
    class ShippedState {
        +getStateName(): string
        +getDescription(): string
        +getAvailableActions(): string[]
        +handleAction(context: OrderContext, action: string): boolean
        +getStateColor(): string
        +getStateIcon(): string
    }
    
    class DeliveredState {
        +getStateName(): string
        +getDescription(): string
        +getAvailableActions(): string[]
        +handleAction(context: OrderContext, action: string): boolean
        +getStateColor(): string
        +getStateIcon(): string
    }
    
    class CancelledState {
        +getStateName(): string
        +getDescription(): string
        +getAvailableActions(): string[]
        +handleAction(context: OrderContext, action: string): boolean
        +getStateColor(): string
        +getStateIcon(): string
    }
    
    class RefundingState {
        +getStateName(): string
        +getDescription(): string
        +getAvailableActions(): string[]
        +handleAction(context: OrderContext, action: string): boolean
        +getStateColor(): string
        +getStateIcon(): string
    }
    
    class RefundedState {
        +getStateName(): string
        +getDescription(): string
        +getAvailableActions(): string[]
        +handleAction(context: OrderContext, action: string): boolean
        +getStateColor(): string
        +getStateIcon(): string
    }
    
    class ReturningState {
        +getStateName(): string
        +getDescription(): string
        +getAvailableActions(): string[]
        +handleAction(context: OrderContext, action: string): boolean
        +getStateColor(): string
        +getStateIcon(): string
    }
    
    class OrderFactory {
        +static createOrder(orderId: string, amount: number, items: string[], customerName: string): OrderContext
        +static getStateByName(stateName: string): OrderState
    }
    
    OrderState <|.. PendingPaymentState
    OrderState <|.. PaidState
    OrderState <|.. ShippedState
    OrderState <|.. DeliveredState
    OrderState <|.. CancelledState
    OrderState <|.. RefundingState
    OrderState <|.. RefundedState
    OrderState <|.. ReturningState
    
    OrderContext --> OrderState : current state
    OrderContext ..> OrderState : transitions to
    OrderFactory --> OrderContext : creates
    OrderFactory --> OrderState : creates
    
    note for OrderState "状态接口\n定义状态行为"
    note for OrderContext "上下文类\n维护当前状态"
    note for PendingPaymentState "具体状态\n待支付状态"
    note for OrderFactory "工厂类\n创建订单和状态"
```

## 🔍 组件说明

### 状态接口 (State Interface)
- **OrderState**: 订单状态接口
  - 定义所有状态的公共行为
  - `handleAction()`: 处理状态转换
  - `getAvailableActions()`: 获取可执行操作
  - 提供状态的描述信息和UI属性

### 上下文类 (Context)
- **OrderContext**: 订单上下文
  - 维护当前状态的引用
  - 委托状态相关的行为给当前状态对象
  - 管理状态转换和历史记录
  - 提供订单的基本信息

### 具体状态 (Concrete States)
- **PendingPaymentState**: 待支付状态
- **PaidState**: 已支付状态
- **ShippedState**: 已发货状态
- **DeliveredState**: 已完成状态
- **CancelledState**: 已取消状态
- **RefundingState**: 退款中状态
- **RefundedState**: 已退款状态
- **ReturningState**: 退货中状态

每个状态实现不同的行为逻辑和状态转换规则。

### 工厂类 (Factory)
- **OrderFactory**: 订单工厂
  - 创建订单上下文
  - 根据名称创建状态对象

## 📊 状态转换图

```mermaid
stateDiagram-v2
    [*] --> 待支付 : 创建订单
    
    待支付 --> 已支付 : 支付
    待支付 --> 已取消 : 取消订单
    
    已支付 --> 已发货 : 发货
    已支付 --> 退款中 : 申请退款
    
    已发货 --> 已完成 : 确认收货
    已发货 --> 退货中 : 申请退货
    
    退款中 --> 已退款 : 同意退款
    退款中 --> 已支付 : 拒绝退款
    
    退货中 --> 已退款 : 同意退货
    退货中 --> 已完成 : 拒绝退货
    
    已完成 --> 已完成 : 评价商品
    
    已取消 --> [*]
    已退款 --> [*]
    已完成 --> [*]
```

## 💡 设计优势

1. **状态封装**: 将状态相关的行为封装在状态类中
2. **消除条件语句**: 避免大量的 if-else 或 switch 语句
3. **状态转换清晰**: 状态转换逻辑明确且易于理解
4. **易于扩展**: 添加新状态不影响现有代码
5. **符合开闭原则**: 对扩展开放，对修改关闭

## 🔧 TypeScript 实现

### 状态接口
```typescript
interface OrderState {
  getStateName(): string;
  getDescription(): string;
  getAvailableActions(): string[];
  handleAction(context: OrderContext, action: string): boolean;
  getStateColor(): string;
  getStateIcon(): string;
}
```

### 上下文类实现
```typescript
class OrderContext {
  private state: OrderState;
  private orderInfo: {
    id: string;
    amount: number;
    items: string[];
    customerName: string;
    createdAt: Date;
    history: Array<{ state: string; timestamp: Date; action?: string }>;
  };

  constructor(orderId: string, amount: number, items: string[], customerName: string) {
    this.orderInfo = {
      id: orderId,
      amount,
      items,
      customerName,
      createdAt: new Date(),
      history: []
    };
    
    this.state = new PendingPaymentState();
    this.addToHistory(this.state.getStateName(), '订单创建');
  }

  setState(state: OrderState, action?: string): void {
    this.state = state;
    this.addToHistory(state.getStateName(), action);
  }

  getState(): OrderState {
    return this.state;
  }

  executeAction(action: string): boolean {
    return this.state.handleAction(this, action);
  }

  getOrderInfo() {
    return { ...this.orderInfo };
  }

  private addToHistory(stateName: string, action?: string): void {
    this.orderInfo.history.push({
      state: stateName,
      timestamp: new Date(),
      action
    });
  }
}
```

### 具体状态实现
```typescript
class PendingPaymentState implements OrderState {
  getStateName(): string {
    return '待支付';
  }

  getDescription(): string {
    return '订单已创建，等待用户支付';
  }

  getAvailableActions(): string[] {
    return ['支付', '取消订单'];
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '支付':
        context.setState(new PaidState(), '用户完成支付');
        return true;
      case '取消订单':
        context.setState(new CancelledState(), '用户取消订单');
        return true;
      default:
        return false;
    }
  }

  getStateColor(): string {
    return '#f59e0b';
  }

  getStateIcon(): string {
    return '💳';
  }
}

class PaidState implements OrderState {
  getStateName(): string {
    return '已支付';
  }

  getDescription(): string {
    return '支付成功，准备发货';
  }

  getAvailableActions(): string[] {
    return ['发货', '申请退款'];
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '发货':
        context.setState(new ShippedState(), '商家发货');
        return true;
      case '申请退款':
        context.setState(new RefundingState(), '用户申请退款');
        return true;
      default:
        return false;
    }
  }

  getStateColor(): string {
    return '#10b981';
  }

  getStateIcon(): string {
    return '✅';
  }
}
```

### 使用示例
```typescript
// 创建订单
const order = new OrderContext('ORD-001', 299.99, ['商品A', '商品B'], '张三');

console.log(`当前状态: ${order.getState().getStateName()}`);
console.log(`可执行操作: ${order.getState().getAvailableActions().join(', ')}`);

// 执行支付操作
order.executeAction('支付');
console.log(`支付后状态: ${order.getState().getStateName()}`);

// 执行发货操作
order.executeAction('发货');
console.log(`发货后状态: ${order.getState().getStateName()}`);

// 确认收货
order.executeAction('确认收货');
console.log(`最终状态: ${order.getState().getStateName()}`);
```

## 🚀 状态模式变体

### 状态机实现
```typescript
class StateMachine<T> {
  private currentState: string;
  private states: Map<string, T> = new Map();
  private transitions: Map<string, Map<string, string>> = new Map();

  constructor(initialState: string) {
    this.currentState = initialState;
  }

  addState(name: string, state: T): void {
    this.states.set(name, state);
  }

  addTransition(from: string, action: string, to: string): void {
    if (!this.transitions.has(from)) {
      this.transitions.set(from, new Map());
    }
    this.transitions.get(from)!.set(action, to);
  }

  executeAction(action: string): boolean {
    const transitions = this.transitions.get(this.currentState);
    if (transitions && transitions.has(action)) {
      this.currentState = transitions.get(action)!;
      return true;
    }
    return false;
  }

  getCurrentState(): T | undefined {
    return this.states.get(this.currentState);
  }
}
```

### 函数式状态模式
```typescript
type StateFunction = (action: string) => StateFunction | null;

const createPendingPaymentState = (): StateFunction => (action: string) => {
  switch (action) {
    case '支付':
      return createPaidState();
    case '取消订单':
      return createCancelledState();
    default:
      return null;
  }
};

const createPaidState = (): StateFunction => (action: string) => {
  switch (action) {
    case '发货':
      return createShippedState();
    case '申请退款':
      return createRefundingState();
    default:
      return null;
  }
};
```

### 枚举状态模式
```typescript
enum OrderStateEnum {
  PENDING_PAYMENT = 'pending_payment',
  PAID = 'paid',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

class EnumBasedOrderContext {
  private state: OrderStateEnum = OrderStateEnum.PENDING_PAYMENT;

  executeAction(action: string): boolean {
    switch (this.state) {
      case OrderStateEnum.PENDING_PAYMENT:
        if (action === '支付') {
          this.state = OrderStateEnum.PAID;
          return true;
        } else if (action === '取消订单') {
          this.state = OrderStateEnum.CANCELLED;
          return true;
        }
        break;
      case OrderStateEnum.PAID:
        if (action === '发货') {
          this.state = OrderStateEnum.SHIPPED;
          return true;
        }
        break;
      // ... 其他状态处理
    }
    return false;
  }
}
```

## 🎯 使用场景

- **订单管理**: 订单的生命周期管理
- **游戏开发**: 游戏角色状态管理
- **工作流引擎**: 流程状态管理
- **UI 组件**: 组件状态管理
- **网络连接**: 连接状态管理

## 📝 实现要点

1. **状态接口设计**: 确保所有状态有统一的接口
2. **状态转换规则**: 明确定义状态之间的转换条件
3. **上下文职责**: 上下文类应该简单，主要负责状态管理
4. **状态数据**: 考虑状态相关数据的存储位置
5. **异常处理**: 处理非法的状态转换

## ⚠️ 注意事项

1. **类数量增加**: 每个状态都需要一个类
2. **状态共享**: 状态对象通常应该是无状态的
3. **状态转换复杂性**: 复杂的状态转换可能难以理解
4. **内存开销**: 频繁的状态对象创建可能影响性能
5. **调试困难**: 状态转换的调试可能比较困难

## 🔄 与其他模式的关系

- **与策略模式**: 结构相似，但意图不同
- **与命令模式**: 可以结合使用，命令触发状态转换
- **与观察者模式**: 状态变化可以通知观察者
- **与单例模式**: 状态对象通常可以是单例

---

> 状态模式将复杂的状态转换逻辑分散到各个状态类中，使得状态转换更加清晰和易于维护。
