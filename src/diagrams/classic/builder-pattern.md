# 建造者模式架构图

## 📋 模式概述

建造者模式（Builder Pattern）是一种创建型设计模式，它将复杂对象的构建与表示分离，使得同样的构建过程可以创建不同的表示。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 需要创建复杂对象，包含多个组成部分
- 对象的创建过程复杂，需要多个步骤
- 同一构建过程需要创建不同表示的对象
- 希望隐藏复杂对象的创建细节

## 🏗️ 架构图

```mermaid
classDiagram
    class Computer {
        -cpu: string
        -memory: string
        -storage: string
        -graphics: string
        -price: number
        +setCPU(cpu: string, price: number) void
        +setMemory(memory: string, price: number) void
        +setStorage(storage: string, price: number) void
        +setGraphics(graphics: string, price: number) void
        +getSpecifications() string
        +getPerformanceLevel() string
    }
    
    class ComputerBuilder {
        <<interface>>
        +reset() ComputerBuilder
        +buildCPU() ComputerBuilder
        +buildMemory() ComputerBuilder
        +buildStorage() ComputerBuilder
        +buildGraphics() ComputerBuilder
        +getResult() Computer
    }
    
    class GamingComputerBuilder {
        -computer: Computer
        +reset() ComputerBuilder
        +buildCPU() ComputerBuilder
        +buildMemory() ComputerBuilder
        +buildStorage() ComputerBuilder
        +buildGraphics() ComputerBuilder
        +getResult() Computer
    }
    
    class OfficeComputerBuilder {
        -computer: Computer
        +reset() ComputerBuilder
        +buildCPU() ComputerBuilder
        +buildMemory() ComputerBuilder
        +buildStorage() ComputerBuilder
        +buildGraphics() ComputerBuilder
        +getResult() Computer
    }
    
    class WorkstationBuilder {
        -computer: Computer
        +reset() ComputerBuilder
        +buildCPU() ComputerBuilder
        +buildMemory() ComputerBuilder
        +buildStorage() ComputerBuilder
        +buildGraphics() ComputerBuilder
        +getResult() Computer
    }
    
    class ComputerDirector {
        -builder: ComputerBuilder
        +constructor(builder: ComputerBuilder)
        +buildFullComputer() Computer
        +buildBasicComputer() Computer
        +buildHighEndComputer() Computer
    }
    
    ComputerBuilder <|-- GamingComputerBuilder
    ComputerBuilder <|-- OfficeComputerBuilder
    ComputerBuilder <|-- WorkstationBuilder
    
    ComputerDirector --> ComputerBuilder : uses
    GamingComputerBuilder --> Computer : builds
    OfficeComputerBuilder --> Computer : builds
    WorkstationBuilder --> Computer : builds
```

## 🔄 时序图

```mermaid
sequenceDiagram
    participant Client
    participant Director as ComputerDirector
    participant Builder as GamingComputerBuilder
    participant Product as Computer
    
    Client->>Builder: new GamingComputerBuilder()
    Client->>Director: new ComputerDirector(builder)
    
    Client->>Director: buildFullComputer()
    
    Director->>Builder: reset()
    Builder->>Product: new Computer()
    Builder-->>Director: this
    
    Director->>Builder: buildCPU()
    Builder->>Product: setCPU("Intel i7", 3200)
    Builder-->>Director: this
    
    Director->>Builder: buildMemory()
    Builder->>Product: setMemory("32GB DDR5", 1200)
    Builder-->>Director: this
    
    Director->>Builder: buildStorage()
    Builder->>Product: setStorage("1TB SSD", 800)
    Builder-->>Director: this
    
    Director->>Builder: buildGraphics()
    Builder->>Product: setGraphics("RTX 4070", 6500)
    Builder-->>Director: this
    
    Director->>Builder: getResult()
    Builder-->>Director: computer
    Director-->>Client: computer
    
    Client->>Product: getSpecifications()
    Product-->>Client: specifications string
```

## 🏭 构建过程图

```mermaid
graph TD
    A[开始构建] --> B[重置建造者]
    B --> C[构建CPU]
    C --> D[构建内存]
    D --> E[构建存储]
    E --> F[构建显卡]
    F --> G[获取结果]
    G --> H[完成构建]
    
    subgraph "游戏电脑配置"
        C1[Intel i7-13700K<br/>¥3,200]
        D1[32GB DDR5-5600<br/>¥1,200]
        E1[1TB NVMe SSD<br/>¥800]
        F1[RTX 4070 Ti<br/>¥6,500]
    end
    
    subgraph "办公电脑配置"
        C2[Intel i5-12400<br/>¥1,500]
        D2[16GB DDR4-3200<br/>¥600]
        E2[512GB SSD<br/>¥400]
        F2[集成显卡<br/>¥0]
    end
    
    subgraph "工作站配置"
        C3[Intel Xeon<br/>¥5,000]
        D3[64GB ECC<br/>¥3,000]
        E3[2TB NVMe<br/>¥1,500]
        F3[Quadro RTX<br/>¥15,000]
    end
    
    C --> C1
    C --> C2
    C --> C3
    
    style C1 fill:#ff6b6b
    style D1 fill:#ff6b6b
    style E1 fill:#ff6b6b
    style F1 fill:#ff6b6b
    
    style C2 fill:#52c41a
    style D2 fill:#52c41a
    style E2 fill:#52c41a
    style F2 fill:#52c41a
    
    style C3 fill:#1890ff
    style D3 fill:#1890ff
    style E3 fill:#1890ff
    style F3 fill:#1890ff
```

## 🎯 建造者类型对比

```mermaid
graph LR
    subgraph "游戏电脑建造者"
        GB[GamingComputerBuilder]
        GB --> GC[高性能CPU]
        GB --> GM[大容量内存]
        GB --> GS[高速存储]
        GB --> GG[独立显卡]
        GTotal[总价: ¥11,700]
    end
    
    subgraph "办公电脑建造者"
        OB[OfficeComputerBuilder]
        OB --> OC[中等性能CPU]
        OB --> OM[标准内存]
        OB --> OS[标准存储]
        OB --> OG[集成显卡]
        OTotal[总价: ¥2,500]
    end
    
    subgraph "工作站建造者"
        WB[WorkstationBuilder]
        WB --> WC[专业CPU]
        WB --> WM[大容量ECC内存]
        WB --> WS[企业级存储]
        WB --> WG[专业显卡]
        WTotal[总价: ¥24,500]
    end
    
    style GB fill:#ff6b6b
    style OB fill:#52c41a
    style WB fill:#1890ff
```

## 💡 核心概念

### 产品 (Product)
- `Computer`: 要构建的复杂对象

### 抽象建造者 (Abstract Builder)
- `ComputerBuilder`: 定义构建步骤的接口

### 具体建造者 (Concrete Builder)
- `GamingComputerBuilder`: 构建游戏电脑
- `OfficeComputerBuilder`: 构建办公电脑
- `WorkstationBuilder`: 构建工作站

### 指挥者 (Director)
- `ComputerDirector`: 控制构建过程

## ✅ 优点

1. **分离构建和表示**: 同样的构建过程可以创建不同的产品
2. **精细控制**: 可以精细控制产品的构建过程
3. **代码复用**: 不同的具体建造者可以复用相同的构建过程
4. **单一职责**: 每个建造者只负责构建特定类型的产品

## ❌ 缺点

1. **产品差异大时不适用**: 产品内部变化复杂时，建造者也会变得复杂
2. **增加系统复杂性**: 增加了多个新的类
3. **建造者接口稳定性**: 产品属性变化时，建造者接口也需要修改

## 🔧 适用场景

- 电脑配置系统
- 汽车制造系统
- 房屋建造系统
- SQL查询构建器
- 复杂配置对象的创建

## 📝 实现要点

1. **确定产品结构**: 明确要构建的复杂对象的组成部分
2. **定义构建步骤**: 将构建过程分解为多个步骤
3. **抽象建造者**: 定义所有构建步骤的接口
4. **具体建造者**: 实现不同产品的具体构建逻辑
5. **指挥者**: 控制构建过程的顺序和逻辑
6. **链式调用**: 支持流畅的API调用方式
