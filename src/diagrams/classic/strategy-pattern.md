# 策略模式架构图

## 📋 模式概述

策略模式（Strategy Pattern）是一种行为型设计模式，它定义了一系列算法，把它们一个个封装起来，并且使它们可相互替换。策略模式让算法的变化独立于使用算法的客户。

## 🎯 解决的问题

在实际开发中，我们经常遇到以下情况：
- 需要在运行时选择不同的算法
- 有多种方式完成同一个任务
- 避免使用大量的条件判断语句
- 算法需要独立变化和扩展

## 🏗️ 架构图

```mermaid
classDiagram
    class DiscountStrategy {
        <<interface>>
        +calculate(total: number, items: CartItem[]): DiscountResult
    }
    
    class CartItem {
        +name: string
        +price: number
    }
    
    class DiscountResult {
        +discount: number
        +details: string
    }
    
    class NoDiscountStrategy {
        +calculate(total: number, items: CartItem[]): DiscountResult
    }
    
    class PercentageDiscountStrategy {
        -percentage: number
        +calculate(total: number, items: CartItem[]): DiscountResult
    }
    
    class FixedAmountDiscountStrategy {
        -amount: number
        -minTotal: number
        +calculate(total: number, items: CartItem[]): DiscountResult
    }
    
    class VipDiscountStrategy {
        +calculate(total: number, items: CartItem[]): DiscountResult
    }
    
    class DiscountCalculator {
        -strategy: DiscountStrategy
        +setStrategy(strategy: DiscountStrategy): void
        +calculateDiscount(total: number, items: CartItem[]): DiscountResult
    }
    
    class StrategyFactory {
        +static createStrategy(type: string): DiscountStrategy
        +static getSupportedStrategies(): StrategyInfo[]
    }
    
    class Client {
        +processOrder(items: CartItem[], strategyType: string): void
    }
    
    DiscountStrategy <|.. NoDiscountStrategy
    DiscountStrategy <|.. PercentageDiscountStrategy
    DiscountStrategy <|.. FixedAmountDiscountStrategy
    DiscountStrategy <|.. VipDiscountStrategy
    
    DiscountCalculator --> DiscountStrategy : uses
    StrategyFactory --> DiscountStrategy : creates
    Client --> DiscountCalculator : uses
    Client --> StrategyFactory : uses
    
    DiscountStrategy ..> DiscountResult : returns
    DiscountStrategy ..> CartItem : uses
    
    note for DiscountStrategy "策略接口\n定义算法族的公共接口"
    note for DiscountCalculator "上下文类\n维护策略引用"
    note for StrategyFactory "策略工厂\n创建具体策略"
```

## 🔍 组件说明

### 策略接口 (Strategy Interface)
- **DiscountStrategy**: 折扣策略接口
  - 定义所有具体策略的公共接口
  - `calculate()`: 计算折扣的核心方法

### 具体策略 (Concrete Strategies)
- **NoDiscountStrategy**: 无折扣策略
- **PercentageDiscountStrategy**: 百分比折扣策略
- **FixedAmountDiscountStrategy**: 固定金额折扣策略
- **VipDiscountStrategy**: VIP 会员折扣策略
- 每个策略实现不同的折扣计算逻辑

### 上下文类 (Context)
- **DiscountCalculator**: 折扣计算器
  - 维护对策略对象的引用
  - 提供设置和使用策略的方法
  - 将客户端请求委托给策略对象

### 策略工厂 (Strategy Factory)
- **StrategyFactory**: 策略工厂
  - 根据类型创建对应的策略实例
  - 管理支持的策略类型

### 数据类 (Data Classes)
- **CartItem**: 购物车商品
- **DiscountResult**: 折扣计算结果

## 📊 策略选择流程

```mermaid
sequenceDiagram
    participant C as Client
    participant F as StrategyFactory
    participant DC as DiscountCalculator
    participant S as Strategy
    
    C->>F: createStrategy("vip")
    F->>S: new VipDiscountStrategy()
    S-->>F: strategy instance
    F-->>C: strategy
    
    C->>DC: setStrategy(strategy)
    DC->>DC: this.strategy = strategy
    
    C->>DC: calculateDiscount(total, items)
    DC->>S: calculate(total, items)
    S->>S: 执行VIP折扣逻辑
    S-->>DC: DiscountResult
    DC-->>C: DiscountResult
    
    Note over C: 客户端获得折扣结果
```

## 💡 设计优势

1. **算法可替换**: 运行时可以切换不同的算法
2. **避免条件语句**: 消除大量的 if-else 或 switch 语句
3. **易于扩展**: 添加新算法不影响现有代码
4. **算法独立**: 算法的实现独立于使用它的客户端
5. **符合开闭原则**: 对扩展开放，对修改关闭

## 🔧 TypeScript 实现

### 策略接口和数据类
```typescript
interface CartItem {
  name: string;
  price: number;
}

interface DiscountStrategy {
  calculate(total: number, items: CartItem[]): { discount: number; details: string };
}
```

### 具体策略实现
```typescript
class NoDiscountStrategy implements DiscountStrategy {
  calculate(total: number): { discount: number; details: string } {
    return {
      discount: 0,
      details: "无折扣优惠"
    };
  }
}

class PercentageDiscountStrategy implements DiscountStrategy {
  private percentage: number;

  constructor(percentage: number) {
    this.percentage = percentage;
  }

  calculate(total: number): { discount: number; details: string } {
    const discount = total * (this.percentage / 100);
    return {
      discount,
      details: `${this.percentage}% 折扣优惠: ¥${total.toFixed(2)} × ${this.percentage}% = ¥${discount.toFixed(2)}`
    };
  }
}

class FixedAmountDiscountStrategy implements DiscountStrategy {
  private amount: number;
  private minTotal: number;

  constructor(amount: number, minTotal: number) {
    this.amount = amount;
    this.minTotal = minTotal;
  }

  calculate(total: number): { discount: number; details: string } {
    if (total >= this.minTotal) {
      return {
        discount: this.amount,
        details: `满¥${this.minTotal.toFixed(2)}减¥${this.amount.toFixed(2)}优惠`
      };
    }
    return {
      discount: 0,
      details: `需满¥${this.minTotal.toFixed(2)}才能享受¥${this.amount.toFixed(2)}优惠`
    };
  }
}

class VipDiscountStrategy implements DiscountStrategy {
  calculate(total: number, items: CartItem[]): { discount: number; details: string } {
    const baseDiscount = total * 0.15; // VIP 85折
    const extraDiscount = Math.floor(total / 100) * 5; // 每满100减5
    const totalDiscount = baseDiscount + extraDiscount;

    return {
      discount: totalDiscount,
      details: `VIP专享: 85折优惠¥${baseDiscount.toFixed(2)} + 每满100减5优惠¥${extraDiscount.toFixed(2)}`
    };
  }
}
```

### 上下文类实现
```typescript
class DiscountCalculator {
  private strategy: DiscountStrategy;

  constructor(strategy: DiscountStrategy) {
    this.strategy = strategy;
  }

  setStrategy(strategy: DiscountStrategy): void {
    this.strategy = strategy;
  }

  calculateDiscount(total: number, items: CartItem[]): { discount: number; details: string } {
    return this.strategy.calculate(total, items);
  }
}
```

### 策略工厂实现
```typescript
class StrategyFactory {
  static createStrategy(type: string): DiscountStrategy {
    switch (type) {
      case "none":
        return new NoDiscountStrategy();
      case "percentage":
        return new PercentageDiscountStrategy(10);
      case "fixed":
        return new FixedAmountDiscountStrategy(20, 100);
      case "vip":
        return new VipDiscountStrategy();
      default:
        return new NoDiscountStrategy();
    }
  }

  static getSupportedStrategies(): Array<{
    id: string;
    name: string;
    description: string;
  }> {
    return [
      { id: "none", name: "无折扣", description: "原价购买" },
      { id: "percentage", name: "百分比折扣", description: "享受10%折扣" },
      { id: "fixed", name: "满减优惠", description: "满100元减20元" },
      { id: "vip", name: "VIP专享", description: "85折 + 每满100减5" }
    ];
  }
}
```

### 使用示例
```typescript
// 创建购物车商品
const items: CartItem[] = [
  { name: "商品A", price: 50 },
  { name: "商品B", price: 80 }
];
const total = 130;

// 创建折扣计算器
const calculator = new DiscountCalculator(new NoDiscountStrategy());

// 使用不同的折扣策略
console.log("=== 无折扣 ===");
let result = calculator.calculateDiscount(total, items);
console.log(`折扣: ¥${result.discount}, 详情: ${result.details}`);

console.log("=== 百分比折扣 ===");
calculator.setStrategy(new PercentageDiscountStrategy(10));
result = calculator.calculateDiscount(total, items);
console.log(`折扣: ¥${result.discount}, 详情: ${result.details}`);

console.log("=== VIP折扣 ===");
calculator.setStrategy(new VipDiscountStrategy());
result = calculator.calculateDiscount(total, items);
console.log(`折扣: ¥${result.discount}, 详情: ${result.details}`);
```

## 🚀 策略模式变体

### 函数式策略模式
```typescript
type DiscountFunction = (total: number, items: CartItem[]) => { discount: number; details: string };

const strategies: Record<string, DiscountFunction> = {
  none: (total) => ({ discount: 0, details: "无折扣" }),
  percentage: (total) => {
    const discount = total * 0.1;
    return { discount, details: `10%折扣: ¥${discount.toFixed(2)}` };
  },
  vip: (total, items) => {
    const discount = total * 0.15 + Math.floor(total / 100) * 5;
    return { discount, details: `VIP折扣: ¥${discount.toFixed(2)}` };
  }
};

class FunctionalDiscountCalculator {
  calculateDiscount(type: string, total: number, items: CartItem[]) {
    const strategy = strategies[type] || strategies.none;
    return strategy(total, items);
  }
}
```

### 带参数的策略模式
```typescript
interface ParameterizedStrategy {
  calculate(total: number, items: CartItem[], params: any): DiscountResult;
}

class ConfigurablePercentageStrategy implements ParameterizedStrategy {
  calculate(total: number, items: CartItem[], params: { percentage: number }) {
    const discount = total * (params.percentage / 100);
    return {
      discount,
      details: `${params.percentage}%折扣: ¥${discount.toFixed(2)}`
    };
  }
}
```

### 组合策略模式
```typescript
class CompositeDiscountStrategy implements DiscountStrategy {
  private strategies: DiscountStrategy[] = [];

  addStrategy(strategy: DiscountStrategy): void {
    this.strategies.push(strategy);
  }

  calculate(total: number, items: CartItem[]): { discount: number; details: string } {
    let totalDiscount = 0;
    const details: string[] = [];

    this.strategies.forEach(strategy => {
      const result = strategy.calculate(total, items);
      totalDiscount += result.discount;
      details.push(result.details);
    });

    return {
      discount: totalDiscount,
      details: details.join(" + ")
    };
  }
}
```

## 🎯 使用场景

- **支付方式**: 不同的支付算法
- **排序算法**: 不同的排序策略
- **压缩算法**: 不同的压缩方式
- **验证规则**: 不同的验证策略
- **定价策略**: 不同的定价算法

## 📝 实现要点

1. **策略接口设计**: 确保所有策略有统一的接口
2. **上下文类职责**: 上下文类应该简单，主要负责策略的管理
3. **策略选择**: 提供便捷的策略选择和切换机制
4. **参数传递**: 考虑策略所需的参数传递方式
5. **性能考虑**: 避免频繁创建策略对象

## ⚠️ 注意事项

1. **策略数量**: 策略过多可能导致类数量膨胀
2. **客户端复杂性**: 客户端需要了解不同策略的差异
3. **策略选择**: 需要合适的机制来选择策略
4. **对象创建**: 考虑策略对象的创建和管理
5. **状态管理**: 策略对象通常应该是无状态的

## 🔄 与其他模式的关系

- **与状态模式**: 结构相似，但意图不同
- **与命令模式**: 都封装了行为，但关注点不同
- **与工厂模式**: 常结合使用，工厂创建策略
- **与模板方法**: 都处理算法变化，但方式不同

---

> 策略模式是处理算法变化的优雅方案，它让算法的选择和使用分离，提高了代码的灵活性和可维护性。
