# 经典GoF设计模式架构图

本目录包含经典GoF设计模式的架构图和详细说明文档。这些模式是软件设计的基础，由"四人帮"(Gang of Four)在1994年提出，至今仍是软件设计的重要指导原则。

## 📚 模式分类

### 🏭 创建型模式 (Creational Patterns)
关注对象的创建过程，使系统独立于对象的创建、组合和表示

- **[单例模式](./singleton-pattern.md)** (Singleton) - 确保类只有一个实例
- **[工厂模式](./factory-pattern.md)** (Factory Method) - 创建对象的接口
- **[抽象工厂模式](./abstract-factory-pattern.md)** (Abstract Factory) - 创建相关对象族
- **[建造者模式](./builder-pattern.md)** (Builder) - 构建复杂对象
- **[原型模式](./prototype-pattern.md)** (Prototype) - 通过复制创建对象

### 🔗 结构型模式 (Structural Patterns)
关注类和对象的组合，形成更大的结构

- **[适配器模式](./adapter-pattern.md)** (Adapter) - 接口转换适配
- **[桥接模式](./bridge-pattern.md)** (Bridge) - 抽象与实现分离
- **[组合模式](./composite-pattern.md)** (Composite) - 树形结构处理
- **[装饰器模式](./decorator-pattern.md)** (Decorator) - 动态添加功能
- **[外观模式](./facade-pattern.md)** (Facade) - 简化复杂接口
- **[享元模式](./flyweight-pattern.md)** (Flyweight) - 共享对象减少内存
- **[代理模式](./proxy-pattern.md)** (Proxy) - 控制对象访问

### 🎭 行为型模式 (Behavioral Patterns)
关注对象间的通信和职责分配

- **[责任链模式](./chain-of-responsibility-pattern.md)** (Chain of Responsibility) - 请求处理链
- **[命令模式](./command-pattern.md)** (Command) - 请求封装为对象
- **[解释器模式](./interpreter-pattern.md)** (Interpreter) - 语言解释器
- **[迭代器模式](./iterator-pattern.md)** (Iterator) - 顺序访问集合
- **[中介者模式](./mediator-pattern.md)** (Mediator) - 对象间交互中介
- **[备忘录模式](./memento-pattern.md)** (Memento) - 保存和恢复状态
- **[观察者模式](./observer-pattern.md)** (Observer) - 一对多依赖通知
- **[状态模式](./state-pattern.md)** (State) - 状态驱动行为
- **[策略模式](./strategy-pattern.md)** (Strategy) - 算法族封装
- **[模板方法模式](./template-method-pattern.md)** (Template Method) - 算法骨架定义
- **[访问者模式](./visitor-pattern.md)** (Visitor) - 操作与结构分离

## 📊 完成状态

### ✅ 已完成架构图 (23/23)

所有23个经典GoF设计模式的架构图都已完成，每个模式都包含：

- **类图** - 展示模式的类结构和关系
- **时序图** - 展示模式的执行流程
- **用例图** - 展示模式的应用场景
- **详细说明** - 模式的原理和实现要点

## 🎯 架构图特色

### 统一的文档结构
每个模式的架构图文档都遵循统一的结构：

1. **模式概述** - 模式的定义和作用
2. **解决的问题** - 模式要解决的具体问题
3. **架构图** - 多种类型的Mermaid图表
4. **组件说明** - 各个组件的职责
5. **代码示例** - 关键代码片段
6. **设计优势** - 模式的优点
7. **使用场景** - 适用的场景
8. **实现要点** - 实现时的注意事项

### 丰富的图表类型
- **类图 (Class Diagram)** - UML类图展示结构
- **时序图 (Sequence Diagram)** - 对象交互时序
- **状态图 (State Diagram)** - 状态转换过程
- **组件图 (Component Diagram)** - 组件关系
- **用例图 (Use Case Diagram)** - 使用场景

### 实际项目场景
所有架构图都基于实际的项目场景：
- 电商系统
- 支付系统
- 日志系统
- 游戏开发
- 文档处理
- 等等...

## 🔍 重点推荐模式

### 🌟 最常用的模式 (必须掌握)

1. **[单例模式](./singleton-pattern.md)** - 全局唯一实例
2. **[工厂模式](./factory-pattern.md)** - 对象创建封装
3. **[观察者模式](./observer-pattern.md)** - 事件通知机制
4. **[策略模式](./strategy-pattern.md)** - 算法可替换
5. **[装饰器模式](./decorator-pattern.md)** - 功能动态扩展

### ⭐ 重要的模式 (建议掌握)

6. **[适配器模式](./adapter-pattern.md)** - 接口适配转换
7. **[代理模式](./proxy-pattern.md)** - 访问控制代理
8. **[命令模式](./command-pattern.md)** - 请求对象化
9. **[状态模式](./state-pattern.md)** - 状态机实现
10. **[模板方法模式](./template-method-pattern.md)** - 算法框架

### 💡 特殊场景模式 (了解即可)

11. **[享元模式](./flyweight-pattern.md)** - 内存优化
12. **[访问者模式](./visitor-pattern.md)** - 操作与数据分离
13. **[解释器模式](./interpreter-pattern.md)** - 语言解释
14. **[备忘录模式](./memento-pattern.md)** - 状态保存恢复
15. **[中介者模式](./mediator-pattern.md)** - 复杂交互简化

## 📖 学习路径建议

### 初学者路径
1. 从**创建型模式**开始，理解对象创建
2. 学习**结构型模式**，掌握对象组合
3. 最后学习**行为型模式**，理解对象协作

### 实践者路径
1. 先学习**最常用的5个模式**
2. 在实际项目中应用和练习
3. 逐步学习其他模式

### 架构师路径
1. 深入理解所有模式的设计思想
2. 掌握模式间的组合使用
3. 能够创造性地应用和扩展模式

## 🎨 图表设计原则

### 清晰性
- 图表结构清晰，层次分明
- 使用统一的颜色和样式
- 避免过于复杂的关系线

### 准确性
- 严格遵循UML规范
- 准确反映代码结构
- 保持图表与代码的一致性

### 美观性
- 合理的布局和间距
- 协调的颜色搭配
- 专业的视觉效果

## 🔧 使用指南

### 查看架构图
1. 点击对应的模式链接
2. 在支持Mermaid的编辑器中查看
3. 推荐使用VS Code + Mermaid插件

### 学习建议
1. **先看图后看代码** - 从整体到细节
2. **对比不同模式** - 理解模式间的区别
3. **动手实践** - 在项目中应用模式
4. **思考变化** - 考虑模式的变体和扩展

### 实践应用
1. 识别项目中的设计问题
2. 选择合适的设计模式
3. 参考架构图进行设计
4. 根据实际情况调整实现

## 📚 参考资源

- **经典书籍**: 《设计模式：可复用面向对象软件的基础》
- **在线资源**: [Refactoring.Guru](https://refactoring.guru/design-patterns)
- **实践指南**: 《Head First 设计模式》
- **进阶阅读**: 《企业应用架构模式》

---

> 设计模式不是银弹，但它们是软件设计的重要工具。通过架构图，我们可以更好地理解和应用这些经典的设计智慧。
