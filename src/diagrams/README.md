# 设计模式架构图

本目录包含了所有设计模式的架构图和说明文档，分为经典 GoF 设计模式和现代扩展设计模式两大类。每个模式都有详细的 Mermaid 图表来展示其结构和工作流程。

## 📁 目录结构

```
src/diagrams/
├── classic/          # 经典GoF设计模式 (23个)
├── modern/           # 现代扩展设计模式 (15个)
└── README.md         # 本文档
```

## 🏛️ 经典 GoF 设计模式 (Classic Patterns)

### 创建型模式 (Creational Patterns)

- [单例模式 (Singleton)](./classic/singleton-pattern.md)
- [工厂模式 (Factory Method)](./classic/factory-pattern.md)
- [抽象工厂模式 (Abstract Factory)](./classic/abstract-factory-pattern.md)
- [建造者模式 (Builder)](./classic/builder-pattern.md)
- [原型模式 (Prototype)](./classic/prototype-pattern.md)

### 结构型模式 (Structural Patterns)

- [适配器模式 (Adapter)](./classic/adapter-pattern.md)
- [桥接模式 (Bridge)](./classic/bridge-pattern.md)
- [组合模式 (Composite)](./classic/composite-pattern.md)
- [装饰器模式 (Decorator)](./classic/decorator-pattern.md)
- [外观模式 (Facade)](./classic/facade-pattern.md)
- [享元模式 (Flyweight)](./classic/flyweight-pattern.md)
- [代理模式 (Proxy)](./classic/proxy-pattern.md)

### 行为型模式 (Behavioral Patterns)

- [责任链模式 (Chain of Responsibility)](./classic/chain-of-responsibility-pattern.md)
- [命令模式 (Command)](./classic/command-pattern.md)
- [解释器模式 (Interpreter)](./classic/interpreter-pattern.md)
- [迭代器模式 (Iterator)](./classic/iterator-pattern.md)
- [中介者模式 (Mediator)](./classic/mediator-pattern.md)
- [备忘录模式 (Memento)](./classic/memento-pattern.md)
- [观察者模式 (Observer)](./classic/observer-pattern.md)
- [状态模式 (State)](./classic/state-pattern.md)
- [策略模式 (Strategy)](./classic/strategy-pattern.md)
- [模板方法模式 (Template Method)](./classic/template-method-pattern.md)
- [访问者模式 (Visitor)](./classic/visitor-pattern.md)

## 🚀 现代扩展设计模式 (Modern Patterns)

### 并发型模式 (Concurrency Patterns)

- 生产者-消费者模式 (Producer-Consumer) - 已完成
- 读写锁模式 (Reader-Writer Lock) - 开发中
- 线程池模式 (Thread Pool) - 开发中
- Future/Promise 模式 (Future/Promise) - 已完成

### 架构型模式 (Architectural Patterns)

- MVC 模式 (Model-View-Controller) - 已完成
- MVP 模式 (Model-View-Presenter) - 开发中
- MVVM 模式 (Model-View-ViewModel) - 开发中
- 微服务模式 (Microservices) - 开发中

### 函数式模式 (Functional Patterns)

- 函子模式 (Functor) - 开发中
- 单子模式 (Monad) - 开发中
- 柯里化模式 (Currying) - 开发中

### 响应式模式 (Reactive Patterns)

- [发布-订阅模式 (Publish-Subscribe)](./modern/publish-subscribe-pattern.md) - ✅ 已完成
- 事件溯源模式 (Event Sourcing) - 开发中
- CQRS 模式 (Command Query Responsibility Segregation) - 开发中

## 📊 图表类型说明

每个模式的文档都包含以下类型的图表：

### 1. **系统架构图 (System Architecture)**

- 展示模式的整体架构和组件关系
- 突出核心组件和数据流

### 2. **类图 (Class Diagram)**

- 展示模式中各个类的结构和关系
- 包含属性、方法和类间关系

### 3. **时序图 (Sequence Diagram)**

- 展示模式的执行流程和交互过程
- 体现时间顺序和消息传递

### 4. **用例图 (Use Case Diagram)**

- 展示模式的应用场景和用户交互
- 说明模式解决的具体问题

## 🛠️ 如何阅读图表

- **语法**：使用 Mermaid 语法编写
- **预览**：可以在支持 Mermaid 的 Markdown 查看器中查看
- **工具推荐**：
  - VS Code + Mermaid 插件
  - GitHub/GitLab 原生支持
  - Mermaid Live Editor

## 📈 开发进度

### 经典 GoF 模式

- ✅ 已完成：23/23 (100%)
- 📊 架构图：完整覆盖

### 现代扩展模式

- ✅ 已完成：10/15 (67%)
- 🚧 开发中：5/15 (33%)
- 📊 架构图：10/10 已完成 (100%)

## 🤝 贡献指南

如果您想要添加新的架构图或改进现有的图表，请：

1. **遵循目录结构**：

   - 经典模式放在 `classic/` 目录
   - 现代模式放在 `modern/` 目录

2. **文档格式**：

   - 使用统一的 Markdown 模板
   - 包含模式概述、核心特点、架构图等

3. **图表质量**：

   - 使用清晰的 Mermaid 语法
   - 添加必要的说明文字
   - 确保图表的准确性和可读性

4. **命名规范**：
   - 文件名使用小写字母和连字符
   - 例如：`publish-subscribe-pattern.md`

## 📚 相关资源

- [Mermaid 官方文档](https://mermaid-js.github.io/mermaid/)
- [设计模式：可复用面向对象软件的基础](https://book.douban.com/subject/1052241/)
- [现代软件架构模式](https://martinfowler.com/architecture/)
