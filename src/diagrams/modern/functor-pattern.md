# 函子模式架构图

## 📋 模式概述

函子模式（Functor Pattern）是函数式编程中的核心概念，它定义了一个可以被映射的容器。函子提供map操作，允许我们将函数应用到容器中的值，同时保持容器的结构不变。这种模式在数据转换、错误处理、异步操作等场景中非常有用。

## 🎯 解决的问题

在编程中，我们经常需要：
- 对容器中的值进行转换，但保持容器结构
- 安全地处理可能为空的值
- 构建复杂的数据转换管道
- 统一处理不同类型的容器操作
- 避免嵌套的条件判断和空值检查

## 🏗️ 函子架构图

```mermaid
classDiagram
    class Functor~T~ {
        <<interface>>
        +map(fn: T => U): Functor~U~
    }
    
    class Maybe~T~ {
        -value: T | null
        +static of(value: T): Maybe~T~
        +static none(): Maybe~T~
        +static fromNullable(value: T?): Maybe~T~
        +map(fn: T => U): Maybe~U~
        +getValue(): T | null
        +hasValue(): boolean
        +getOrElse(defaultValue: T): T
        +flatMap(fn: T => Maybe~U~): Maybe~U~
        +filter(predicate: T => boolean): Maybe~T~
    }
    
    class ListFunctor~T~ {
        -items: T[]
        +static of(items: T[]): ListFunctor~T~
        +static single(item: T): ListFunctor~T~
        +static empty(): ListFunctor~T~
        +map(fn: T => U): ListFunctor~U~
        +getValue(): T[]
        +length(): number
        +isEmpty(): boolean
        +filter(predicate: T => boolean): ListFunctor~T~
        +flatMap(fn: T => ListFunctor~U~): ListFunctor~U~
        +fold(initial: U, fn: (U, T) => U): U
        +concat(other: ListFunctor~T~): ListFunctor~T~
    }
    
    class Either~L,R~ {
        <<abstract>>
        +map(fn: R => U): Either~L,U~
        +isLeft(): boolean
        +isRight(): boolean
    }
    
    class Left~L,R~ {
        -value: L
        +map(fn: R => U): Either~L,U~
        +isLeft(): boolean
        +isRight(): boolean
        +getValue(): L
    }
    
    class Right~L,R~ {
        -value: R
        +map(fn: R => U): Either~L,U~
        +isLeft(): boolean
        +isRight(): boolean
        +getValue(): R
    }
    
    class IO~T~ {
        -effect: () => T
        +static of(value: T): IO~T~
        +static from(effect: () => T): IO~T~
        +map(fn: T => U): IO~U~
        +run(): T
        +flatMap(fn: T => IO~U~): IO~U~
    }
    
    Functor <|.. Maybe
    Functor <|.. ListFunctor
    Functor <|.. Either
    Functor <|.. IO
    Either <|-- Left
    Either <|-- Right
    
    note for Functor "函子接口定义了map操作\n保持容器结构不变"
    note for Maybe "处理可能为空的值\n避免空指针异常"
    note for ListFunctor "处理数组/列表\n支持批量转换"
    note for Either "处理错误或成功的值\nLeft表示错误，Right表示成功"
    note for IO "处理副作用\n延迟执行"
```

## 🔄 函子操作流程图

```mermaid
flowchart TD
    A[原始容器 Functor&lt;T&gt;] --> B{检查容器类型}
    
    B -->|Maybe| C[Maybe&lt;T&gt;]
    B -->|List| D[ListFunctor&lt;T&gt;]
    B -->|Either| E[Either&lt;L,R&gt;]
    B -->|IO| F[IO&lt;T&gt;]
    
    C --> C1{hasValue?}
    C1 -->|true| C2[应用函数 f: T → U]
    C1 -->|false| C3[返回 Maybe.none&lt;U&gt;]
    C2 --> C4[Maybe&lt;U&gt;]
    C3 --> C4
    
    D --> D1[遍历数组]
    D1 --> D2[对每个元素应用函数 f: T → U]
    D2 --> D3[收集结果]
    D3 --> D4[ListFunctor&lt;U&gt;]
    
    E --> E1{isRight?}
    E1 -->|true| E2[应用函数到Right值]
    E1 -->|false| E3[保持Left不变]
    E2 --> E4[Either&lt;L,U&gt;]
    E3 --> E4
    
    F --> F1[创建新的副作用函数]
    F1 --> F2[组合原副作用和转换函数]
    F2 --> F3[IO&lt;U&gt;]
    
    C4 --> G[结果容器 Functor&lt;U&gt;]
    D4 --> G
    E4 --> G
    F3 --> G
    
    style A fill:#e1f5fe
    style G fill:#c8e6c9
    style C2 fill:#fff3e0
    style D2 fill:#fff3e0
    style E2 fill:#fff3e0
    style F2 fill:#fff3e0
```

## 🔍 组件说明

### 核心接口
- **Functor&lt;T&gt;**: 定义了map操作的接口，是所有函子的基础

### 具体实现

#### Maybe函子
- **用途**: 处理可能为空的值，避免空指针异常
- **特点**: 
  - `Some(value)`: 包含值的状态
  - `None`: 空值状态
  - map操作只在有值时执行

#### ListFunctor
- **用途**: 处理数组/列表，支持批量转换
- **特点**:
  - 对数组中每个元素应用转换函数
  - 保持数组结构
  - 支持链式操作

#### Either函子
- **用途**: 处理可能失败的操作，表示成功或错误
- **特点**:
  - `Left`: 表示错误状态
  - `Right`: 表示成功状态
  - map操作只对Right值执行

#### IO函子
- **用途**: 处理副作用，延迟执行
- **特点**:
  - 包装副作用函数
  - 延迟执行直到调用run()
  - 支持函数组合

## 📊 函子法则

函子必须遵循两个重要法则：

### 1. 恒等法则 (Identity Law)
```typescript
functor.map(identity) === functor
```

### 2. 组合法则 (Composition Law)
```typescript
functor.map(f).map(g) === functor.map(compose(g, f))
```

## 🔄 时序图 - Maybe函子操作

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Maybe as Maybe&lt;number&gt;
    participant Function as 转换函数
    
    Note over Client,Function: Maybe函子的map操作流程
    
    Client->>Maybe: Maybe.of(42)
    Maybe-->>Client: Maybe&lt;number&gt;(42)
    
    Client->>Maybe: map(x => x * 2)
    Maybe->>Maybe: 检查是否有值
    
    alt 有值
        Maybe->>Function: 调用转换函数(42)
        Function-->>Maybe: 返回84
        Maybe->>Maybe: 创建新的Maybe(84)
        Maybe-->>Client: Maybe&lt;number&gt;(84)
    else 无值
        Maybe-->>Client: Maybe.none&lt;number&gt;()
    end
    
    Note over Client,Function: 链式调用
    Client->>Maybe: map(x => x + 1)
    Maybe->>Function: 调用转换函数(84)
    Function-->>Maybe: 返回85
    Maybe-->>Client: Maybe&lt;number&gt;(85)
```

## 💡 设计优势

1. **类型安全**: 编译时检查，避免运行时错误
2. **函数组合**: 支持复杂的数据转换管道
3. **统一接口**: 不同容器类型使用相同的map操作
4. **不可变性**: 操作不修改原容器，返回新容器
5. **可预测性**: 遵循数学法则，行为一致
6. **错误处理**: Either函子提供优雅的错误处理方式

## ⚠️ 注意事项

1. **学习曲线**: 需要理解函数式编程概念
2. **性能考虑**: 频繁的对象创建可能影响性能
3. **调试复杂性**: 链式调用可能增加调试难度
4. **过度抽象**: 简单场景可能不需要函子
5. **内存使用**: 不可变操作可能增加内存使用

## 🎯 使用场景

### 1. 数据转换管道
```typescript
const result = Maybe.of(user)
  .map(u => u.email)
  .map(email => email.toLowerCase())
  .map(email => email.trim())
  .getOrElse('<EMAIL>');
```

### 2. 错误处理
```typescript
const divide = (a: number, b: number): Either<string, number> =>
  b === 0 ? left('Division by zero') : right(a / b);

const result = divide(10, 2)
  .map(x => x * 2)
  .map(x => x + 1);
```

### 3. 异步操作
```typescript
const fetchUser = (id: string): IO<User> =>
  IO.from(() => api.getUser(id));

const processUser = fetchUser('123')
  .map(user => user.name)
  .map(name => name.toUpperCase());
```

## 📝 实现要点

1. **map操作**: 核心方法，应用函数到容器中的值
2. **类型参数**: 使用泛型确保类型安全
3. **不可变性**: 操作返回新实例，不修改原实例
4. **法则遵循**: 确保实现满足函子法则
5. **错误处理**: 在map操作中适当处理异常
6. **链式调用**: 支持流畅的API设计

## 🚀 扩展变体

### 应用函子 (Applicative Functor)
```typescript
class ApplicativeMaybe<T> extends Maybe<T> {
  apply<U>(fn: Maybe<(value: T) => U>): Maybe<U> {
    return fn.flatMap(f => this.map(f));
  }
}
```

### 可遍历函子 (Traversable Functor)
```typescript
interface Traversable<T> extends Functor<T> {
  traverse<U>(fn: (value: T) => Maybe<U>): Maybe<Traversable<U>>;
}
```

---

> 函子模式是函数式编程的基石，它提供了一种优雅的方式来处理容器中的值，同时保持代码的安全性和可组合性。
