# 事件溯源模式架构图

## 概述
事件溯源（Event Sourcing）是一种数据存储模式，它不存储当前状态，而是存储导致当前状态的所有事件序列。通过重放这些事件，可以重建任何时间点的状态。

## 核心架构图

```mermaid
graph TB
    subgraph "Event Sourcing Architecture"
        subgraph "Command Side"
            CMD[Commands]
            CH[Command Handlers]
            AR[Aggregate Root]
            CMD --> CH
            CH --> AR
        end
        
        subgraph "Event Store"
            ES[Event Store]
            EV1[Event 1]
            EV2[Event 2]
            EV3[Event 3]
            EVN[Event N]
            ES --> EV1
            ES --> EV2
            ES --> EV3
            ES --> EVN
        end
        
        subgraph "Event Processing"
            EP[Event Publisher]
            EH[Event Handlers]
            PROJ[Projections]
            EP --> EH
            EH --> PROJ
        end
        
        subgraph "Query Side"
            QH[Query Handlers]
            RM[Read Models]
            QH --> RM
        end
        
        subgraph "External Systems"
            SAGA[Sagas]
            NOTIF[Notifications]
            AUDIT[Audit Log]
        end
    end
    
    %% 数据流
    AR --> ES : "Store Events"
    ES --> EP : "Publish Events"
    EH --> RM : "Update Projections"
    EH --> SAGA : "Trigger Sagas"
    EH --> NOTIF : "Send Notifications"
    EH --> AUDIT : "Audit Trail"
    
    %% 查询流
    QH --> RM : "Query Read Models"
    
    %% 重建流
    ES -.-> AR : "Replay Events"
    
    %% 样式
    classDef commandClass fill:#e3f2fd
    classDef eventClass fill:#f3e5f5
    classDef queryClass fill:#e8f5e8
    classDef externalClass fill:#fff3e0
    
    class CMD,CH,AR commandClass
    class ES,EV1,EV2,EV3,EVN,EP,EH,PROJ eventClass
    class QH,RM queryClass
    class SAGA,NOTIF,AUDIT externalClass
```

## 事件流处理

```mermaid
sequenceDiagram
    participant C as Client
    participant CH as Command Handler
    participant AR as Aggregate Root
    participant ES as Event Store
    participant EP as Event Publisher
    participant EH as Event Handler
    participant RM as Read Model
    
    C->>CH: Send Command
    CH->>AR: Load Aggregate
    ES-->>AR: Replay Events
    CH->>AR: Execute Business Logic
    AR->>AR: Generate Events
    AR->>ES: Store New Events
    ES->>EP: Publish Events
    EP->>EH: Notify Event Handlers
    EH->>RM: Update Read Models
    EH-->>C: Send Notifications
    CH-->>C: Command Result
```

## 事件存储结构

```mermaid
graph TB
    subgraph "Event Store Schema"
        subgraph "Events Table"
            ET[Events Table]
            EID[Event ID]
            AID[Aggregate ID]
            TYPE[Event Type]
            DATA[Event Data]
            VER[Version]
            TS[Timestamp]
            META[Metadata]
            
            ET --> EID
            ET --> AID
            ET --> TYPE
            ET --> DATA
            ET --> VER
            ET --> TS
            ET --> META
        end
        
        subgraph "Snapshots Table"
            ST[Snapshots Table]
            SID[Snapshot ID]
            SAID[Aggregate ID]
            SDATA[Snapshot Data]
            SVER[Version]
            STS[Timestamp]
            
            ST --> SID
            ST --> SAID
            ST --> SDATA
            ST --> SVER
            ST --> STS
        end
        
        subgraph "Projections Tables"
            PT1[User Projection]
            PT2[Order Projection]
            PT3[Analytics Projection]
        end
    end
    
    %% 关系
    ET -.-> ST : "Create Snapshots"
    ET --> PT1 : "Update Projections"
    ET --> PT2
    ET --> PT3
```

## 聚合生命周期

```mermaid
stateDiagram-v2
    [*] --> Created : UserCreatedEvent
    Created --> Active : UserActivatedEvent
    Active --> Suspended : UserSuspendedEvent
    Suspended --> Active : UserReactivatedEvent
    Active --> Deactivated : UserDeactivatedEvent
    Suspended --> Deactivated : UserDeactivatedEvent
    Deactivated --> [*]
    
    Created --> Updated : UserUpdatedEvent
    Updated --> Active : UserActivatedEvent
    Updated --> Suspended : UserSuspendedEvent
    Updated --> Deactivated : UserDeactivatedEvent
```

## 事件版本管理

```mermaid
graph TB
    subgraph "Event Evolution"
        subgraph "Version 1"
            V1[UserCreatedEvent v1]
            V1_FIELDS["{id, name, email}"]
            V1 --> V1_FIELDS
        end
        
        subgraph "Version 2"
            V2[UserCreatedEvent v2]
            V2_FIELDS["{id, name, email, role}"]
            V2 --> V2_FIELDS
        end
        
        subgraph "Version 3"
            V3[UserCreatedEvent v3]
            V3_FIELDS["{id, name, email, role, preferences}"]
            V3 --> V3_FIELDS
        end
        
        subgraph "Upcasting"
            UC[Event Upcaster]
            UC --> V1 : "Convert v1 to v3"
            UC --> V2 : "Convert v2 to v3"
        end
    end
    
    %% 版本演进
    V1 -.-> V2 : "Add role field"
    V2 -.-> V3 : "Add preferences"
```

## 投影更新策略

```mermaid
graph TB
    subgraph "Projection Strategies"
        subgraph "Real-time Projections"
            RT[Real-time Handler]
            RT_PROJ[User List Projection]
            RT_CACHE[Cache Layer]
            RT --> RT_PROJ
            RT_PROJ --> RT_CACHE
        end
        
        subgraph "Batch Projections"
            BATCH[Batch Processor]
            BATCH_PROJ[Analytics Projection]
            BATCH_SCHED[Scheduler]
            BATCH_SCHED --> BATCH
            BATCH --> BATCH_PROJ
        end
        
        subgraph "On-demand Projections"
            OD[On-demand Handler]
            OD_PROJ[Report Projection]
            OD_REQ[Request Trigger]
            OD_REQ --> OD
            OD --> OD_PROJ
        end
    end
    
    subgraph "Event Stream"
        EVENTS[Event Stream]
        EVENTS --> RT
        EVENTS --> BATCH
        EVENTS --> OD
    end
```

## 快照机制

```mermaid
graph TB
    subgraph "Snapshot Strategy"
        subgraph "Event Replay"
            ER[Event Replay]
            E1[Event 1]
            E2[Event 2]
            E3[Event 3]
            EN[Event N]
            ER --> E1
            ER --> E2
            ER --> E3
            ER --> EN
        end
        
        subgraph "Snapshot Creation"
            SC[Snapshot Creator]
            SNAP[Snapshot]
            TRIGGER[Trigger Condition]
            TRIGGER --> SC
            SC --> SNAP
        end
        
        subgraph "Optimized Replay"
            OR[Optimized Replay]
            LOAD_SNAP[Load Snapshot]
            REPLAY_DELTA[Replay Delta Events]
            OR --> LOAD_SNAP
            OR --> REPLAY_DELTA
        end
    end
    
    %% 流程
    ER -.-> SC : "After N events"
    SNAP --> LOAD_SNAP
    EN --> REPLAY_DELTA
```

## 优势与挑战

### 优势
1. **完整审计日志**: 所有变更都有记录
2. **时间旅行**: 可以重建任何时间点的状态
3. **事件重放**: 支持调试和分析
4. **高性能写入**: 只需追加事件
5. **多个读模型**: 支持不同的查询需求

### 挑战
1. **复杂性**: 系统设计和实现复杂
2. **事件版本管理**: 事件结构演进
3. **最终一致性**: 读写模型的一致性
4. **存储增长**: 事件数据持续增长
5. **查询复杂性**: 需要构建投影

## 适用场景
- 需要完整审计日志的系统
- 金融和会计系统
- 协作和版本控制系统
- 复杂业务流程管理
- 需要事件分析的系统

## 最佳实践
1. **事件设计**: 事件应该表达业务意图
2. **版本管理**: 使用事件版本和转换器
3. **快照策略**: 定期创建快照优化性能
4. **投影管理**: 根据查询需求设计投影
5. **错误处理**: 处理事件重放和投影失败
6. **监控**: 监控事件流和投影状态
