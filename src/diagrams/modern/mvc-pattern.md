# MVC模式架构图

## 模式概述

MVC模式（Model-View-Controller）是一种架构设计模式，将应用程序分为三个核心组件：模型（Model）、视图（View）和控制器（Controller）。这种分离使得代码更加模块化，提高了可维护性和可测试性。

## 核心特点

- **关注点分离**：数据、展示和控制逻辑分离
- **松耦合**：各组件间依赖关系清晰
- **可重用性**：模型和视图可以独立重用
- **可测试性**：各组件可以独立测试

## 系统架构图

```mermaid
graph TB
    %% 用户交互层
    subgraph UserLayer["👤 用户交互层"]
        USER[用户<br/>User]
        BROWSER[浏览器<br/>Browser]
    end
    
    %% 视图层
    subgraph ViewLayer["🖼️ 视图层 (View)"]
        UV[用户视图<br/>UserView]
        UL[用户列表<br/>UserListView]
        UF[用户表单<br/>UserFormView]
        UD[用户详情<br/>UserDetailView]
    end
    
    %% 控制器层
    subgraph ControllerLayer["🎮 控制器层 (Controller)"]
        UC[用户控制器<br/>UserController<br/>+ getUsers()<br/>+ createUser()<br/>+ updateUser()<br/>+ deleteUser()]
        
        subgraph Actions["控制器动作"]
            GET[GET /users<br/>获取用户列表]
            POST[POST /users<br/>创建用户]
            PUT[PUT /users/:id<br/>更新用户]
            DELETE[DELETE /users/:id<br/>删除用户]
        end
    end
    
    %% 模型层
    subgraph ModelLayer["📊 模型层 (Model)"]
        UM[用户模型<br/>UserModel<br/>+ findAll()<br/>+ findById()<br/>+ create()<br/>+ update()<br/>+ delete()]
        
        subgraph DataAccess["数据访问"]
            DB[(数据库<br/>Database)]
            API[外部API<br/>External API]
        end
    end
    
    %% 数据流向
    USER -->|1. 用户操作| BROWSER
    BROWSER -->|2. HTTP请求| UC
    UC -->|3. 调用模型| UM
    UM -->|4. 数据操作| DB
    UM -->|4. API调用| API
    
    DB -->|5. 返回数据| UM
    API -->|5. 返回数据| UM
    UM -->|6. 返回结果| UC
    UC -->|7. 选择视图| UV
    UV -->|8. 渲染页面| BROWSER
    BROWSER -->|9. 显示结果| USER
    
    %% 组件关系
    UC -.->|控制| UV
    UC -.->|控制| UL
    UC -.->|控制| UF
    UC -.->|控制| UD
    
    UV -.->|显示数据| UM
    UL -.->|显示数据| UM
    UF -.->|显示数据| UM
    UD -.->|显示数据| UM
    
    %% 样式
    classDef userLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef viewLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef controllerLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef modelLayer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef dataLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class USER,BROWSER userLayer
    class UV,UL,UF,UD viewLayer
    class UC,GET,POST,PUT,DELETE controllerLayer
    class UM modelLayer
    class DB,API dataLayer
```

## 时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant View as 视图(View)
    participant Controller as 控制器(Controller)
    participant Model as 模型(Model)
    participant DB as 数据库

    Note over User,DB: 用户查看用户列表的完整流程
    
    User->>View: 1. 访问用户列表页面
    View->>Controller: 2. 路由到getUserList()
    
    Controller->>Model: 3. 调用findAll()
    Model->>DB: 4. 执行SELECT查询
    DB-->>Model: 5. 返回用户数据
    Model-->>Controller: 6. 返回用户列表
    
    Controller->>View: 7. 传递数据到视图
    View-->>User: 8. 渲染用户列表页面
    
    Note over User,DB: 用户创建新用户的流程
    
    User->>View: 9. 点击"添加用户"
    View->>Controller: 10. 路由到createUserForm()
    Controller->>View: 11. 返回创建表单视图
    View-->>User: 12. 显示用户创建表单
    
    User->>View: 13. 填写表单并提交
    View->>Controller: 14. POST请求到createUser()
    
    Controller->>Controller: 15. 验证表单数据
    Controller->>Model: 16. 调用create(userData)
    Model->>DB: 17. 执行INSERT操作
    DB-->>Model: 18. 返回创建结果
    Model-->>Controller: 19. 返回新用户信息
    
    alt 创建成功
        Controller->>View: 20a. 重定向到用户列表
        View-->>User: 21a. 显示成功消息
    else 创建失败
        Controller->>View: 20b. 返回表单视图
        View-->>User: 21b. 显示错误消息
    end
```

## 类图

```mermaid
classDiagram
    class User {
        +string id
        +string name
        +string email
        +string phone
        +Date createdAt
        +Date updatedAt
    }
    
    class UserModel {
        -Database db
        +constructor(db: Database)
        +findAll() Promise~User[]~
        +findById(id: string) Promise~User~
        +create(userData: Partial~User~) Promise~User~
        +update(id: string, userData: Partial~User~) Promise~User~
        +delete(id: string) Promise~boolean~
        +validate(userData: Partial~User~) ValidationResult
        -buildQuery(conditions: object) string
        -mapRowToUser(row: any) User
    }
    
    class UserController {
        -UserModel userModel
        -UserView userView
        +constructor(userModel: UserModel, userView: UserView)
        +getUsers(req: Request, res: Response) Promise~void~
        +getUserById(req: Request, res: Response) Promise~void~
        +createUser(req: Request, res: Response) Promise~void~
        +updateUser(req: Request, res: Response) Promise~void~
        +deleteUser(req: Request, res: Response) Promise~void~
        +showCreateForm(req: Request, res: Response) Promise~void~
        +showEditForm(req: Request, res: Response) Promise~void~
        -handleError(error: Error, res: Response) void
        -validateRequest(req: Request) ValidationResult
    }
    
    class UserView {
        -TemplateEngine templateEngine
        +constructor(templateEngine: TemplateEngine)
        +renderUserList(users: User[]) string
        +renderUserDetail(user: User) string
        +renderCreateForm() string
        +renderEditForm(user: User) string
        +renderError(error: Error) string
        +renderSuccess(message: string) string
        -loadTemplate(templateName: string) string
        -compileTemplate(template: string, data: object) string
    }
    
    class Database {
        -Connection connection
        +constructor(config: DatabaseConfig)
        +query(sql: string, params: any[]) Promise~any[]~
        +insert(table: string, data: object) Promise~any~
        +update(table: string, data: object, where: object) Promise~any~
        +delete(table: string, where: object) Promise~boolean~
        +beginTransaction() Promise~Transaction~
        +close() Promise~void~
    }
    
    class Router {
        -Route[] routes
        +constructor()
        +get(path: string, handler: Function) void
        +post(path: string, handler: Function) void
        +put(path: string, handler: Function) void
        +delete(path: string, handler: Function) void
        +route(req: Request, res: Response) void
        -matchRoute(path: string, method: string) Route
    }
    
    class ValidationResult {
        +boolean isValid
        +string[] errors
        +object validData
    }
    
    %% 关系
    UserController "1" --> "1" UserModel : uses
    UserController "1" --> "1" UserView : uses
    UserModel "1" --> "1" Database : uses
    UserModel "1" --> "*" User : manages
    Router "1" --> "*" UserController : routes to
    UserController "1" --> "*" ValidationResult : creates
    UserModel "1" --> "*" ValidationResult : creates
    
    %% 样式
    classDef modelClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef viewClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef controllerClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef dataClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef utilityClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    
    class UserModel,User modelClass
    class UserView viewClass
    class UserController controllerClass
    class Database dataClass
    class Router,ValidationResult utilityClass
```

## 核心组件说明

### 1. Model（模型）
- **职责**：管理应用程序的数据和业务逻辑
- **功能**：
  - 数据访问和持久化
  - 业务规则验证
  - 数据格式化和转换
- **特点**：独立于用户界面，可重用

### 2. View（视图）
- **职责**：负责数据的展示和用户界面
- **功能**：
  - 渲染HTML页面
  - 处理模板和样式
  - 用户交互界面
- **特点**：只负责展示，不包含业务逻辑

### 3. Controller（控制器）
- **职责**：处理用户输入，协调Model和View
- **功能**：
  - 路由处理
  - 请求验证
  - 业务流程控制
  - 错误处理
- **特点**：连接Model和View的桥梁

## 应用场景

1. **Web应用开发**：传统的服务端渲染应用
2. **桌面应用程序**：GUI应用程序架构
3. **移动应用开发**：原生移动应用架构
4. **游戏开发**：游戏逻辑、界面、控制分离
5. **企业级应用**：复杂业务系统的架构设计

## 优势

- ✅ **关注点分离**：清晰的职责划分
- ✅ **可维护性**：代码结构清晰，易于维护
- ✅ **可测试性**：各组件可以独立测试
- ✅ **可重用性**：Model和View可以重用
- ✅ **团队协作**：不同角色可以并行开发

## 注意事项

- ⚠️ **复杂性**：小型应用可能过度设计
- ⚠️ **性能**：多层架构可能影响性能
- ⚠️ **学习成本**：需要理解架构模式
- ⚠️ **依赖管理**：需要合理管理组件间依赖
- ⚠️ **状态管理**：复杂状态需要额外考虑

## 现代变体

### MVC的演进
- **MVP**：Model-View-Presenter
- **MVVM**：Model-View-ViewModel
- **MVA**：Model-View-Adapter
- **Component-based**：基于组件的架构

### 前端框架中的MVC
```typescript
// React中的MVC思想
const UserComponent = () => {
  // Controller逻辑
  const [users, setUsers] = useState([]);
  
  const fetchUsers = async () => {
    // Model交互
    const userData = await userService.getUsers();
    setUsers(userData);
  };
  
  // View渲染
  return (
    <div>
      {users.map(user => (
        <UserCard key={user.id} user={user} />
      ))}
    </div>
  );
};
```
