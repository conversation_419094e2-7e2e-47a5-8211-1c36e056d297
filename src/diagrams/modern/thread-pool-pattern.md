# 线程池模式架构图

## 📋 模式概述

线程池模式（Thread Pool Pattern）是一种并发设计模式，通过预先创建固定数量的线程来重复使用，避免频繁创建和销毁线程的开销。线程池维护一个任务队列，工作线程从队列中获取任务执行，这种模式在高并发场景下能显著提升性能和资源利用率。

## 🎯 解决的问题

在多线程编程中，我们经常遇到：
- 频繁创建和销毁线程的性能开销
- 无限制的线程创建导致资源耗尽
- 难以控制并发线程的数量
- 任务执行的不均匀分布
- 系统资源的浪费和竞争

## 🏗️ 线程池架构图

```mermaid
classDiagram
    class ThreadPool {
        -config: ThreadPoolConfig
        -threads: WorkerThread[]
        -taskQueue: Task[]
        -isShutdown: boolean
        -completedTaskCount: number
        +submit(task: Task): Promise~void~
        +resize(newSize: number): void
        +getThreads(): WorkerThread[]
        +getQueuedTasks(): Task[]
        +getActiveCount(): number
        +getPoolStatus(): PoolStatus
        +shutdown(): void
        +awaitTermination(timeout?: number): Promise~boolean~
        -tryAssignTask(thread: WorkerThread): void
        -defaultRejectedHandler(task: Task): void
    }
    
    class WorkerThread {
        +readonly id: number
        -busy: boolean
        -currentTask: Task | null
        -threadPool: ThreadPool
        +isBusy(): boolean
        +getCurrentTask(): Task | null
        +executeTask(task: Task): Promise~void~
    }
    
    class Task {
        +readonly id: string
        +readonly name: string
        -taskFunction: () => Promise~any~
        +execute(): Promise~any~
    }
    
    class ThreadPoolConfig {
        +corePoolSize: number
        +maximumPoolSize?: number
        +queueCapacity: number
        +keepAliveTime?: number
        +rejectedExecutionHandler?: (task: Task) => void
    }
    
    class ThreadPoolFactory {
        +static newFixedThreadPool(nThreads: number): ThreadPool
        +static newSingleThreadExecutor(): ThreadPool
        +static newCachedThreadPool(): ThreadPool
    }
    
    class ThreadPoolMonitor {
        -threadPool: ThreadPool
        -metrics: PoolMetrics
        +getMetrics(): PoolMetrics
        +reset(): void
        -updateAverageExecutionTime(time: number): void
        -updatePeakActiveThreads(): void
    }
    
    class PoolMetrics {
        +submittedTasks: number
        +completedTasks: number
        +rejectedTasks: number
        +averageExecutionTime: number
        +peakActiveThreads: number
    }
    
    ThreadPool "1" *-- "*" WorkerThread : contains
    ThreadPool "1" *-- "*" Task : queues
    ThreadPool "1" --> "1" ThreadPoolConfig : uses
    ThreadPoolFactory ..> ThreadPool : creates
    ThreadPoolMonitor "1" --> "1" ThreadPool : monitors
    ThreadPoolMonitor "1" *-- "1" PoolMetrics : contains
    WorkerThread "1" --> "0..1" Task : executes
    
    note for ThreadPool "核心组件：管理线程和任务队列\n负责任务分配和线程生命周期"
    note for WorkerThread "工作线程：执行具体任务\n维护忙碌状态和当前任务"
    note for Task "任务单元：封装具体的工作\n提供统一的执行接口"
```

## 🔄 线程池工作流程图

```mermaid
flowchart TD
    A[客户端提交任务] --> B{线程池是否关闭?}
    B -->|是| C[抛出异常]
    B -->|否| D{是否有空闲线程?}
    
    D -->|是| E[直接分配给空闲线程]
    D -->|否| F{任务队列是否已满?}
    
    F -->|否| G[任务加入队列]
    F -->|是| H[执行拒绝策略]
    
    E --> I[线程执行任务]
    G --> J[等待线程空闲]
    J --> K[从队列获取任务]
    K --> I
    
    I --> L[任务执行完成]
    L --> M{队列中是否有任务?}
    M -->|是| N[获取下一个任务]
    M -->|否| O[线程变为空闲状态]
    
    N --> I
    O --> P[等待新任务]
    
    H --> Q{拒绝策略类型}
    Q -->|ABORT| R[抛出异常]
    Q -->|DISCARD| S[丢弃任务]
    Q -->|DISCARD_OLDEST| T[丢弃最老任务，加入新任务]
    Q -->|CALLER_RUNS| U[调用者线程执行]
    
    style A fill:#e1f5fe
    style I fill:#c8e6c9
    style L fill:#c8e6c9
    style C fill:#ffcdd2
    style R fill:#ffcdd2
```

## 🔍 组件说明

### 核心组件

#### ThreadPool（线程池）
- **职责**: 管理工作线程和任务队列
- **功能**:
  - 任务提交和分配
  - 线程生命周期管理
  - 队列容量控制
  - 拒绝策略执行

#### WorkerThread（工作线程）
- **职责**: 执行具体的任务
- **功能**:
  - 维护线程状态（忙碌/空闲）
  - 执行任务并处理异常
  - 任务完成后自动获取下一个任务

#### Task（任务）
- **职责**: 封装具体的工作单元
- **功能**:
  - 提供统一的执行接口
  - 支持异步操作
  - 任务标识和命名

### 辅助组件

#### ThreadPoolConfig（配置）
- **核心线程数**: 始终保持活跃的线程数量
- **最大线程数**: 线程池允许的最大线程数
- **队列容量**: 任务队列的最大容量
- **拒绝策略**: 队列满时的处理策略

#### ThreadPoolFactory（工厂）
- **固定线程池**: 创建固定大小的线程池
- **单线程池**: 创建只有一个线程的线程池
- **缓存线程池**: 创建可动态调整的线程池

## 📊 时序图 - 任务执行流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Pool as 线程池
    participant Queue as 任务队列
    participant Thread as 工作线程
    participant Task as 任务
    
    Note over Client,Task: 任务提交和执行的完整流程
    
    Client->>Pool: submit(task)
    Pool->>Pool: 检查是否有空闲线程
    
    alt 有空闲线程
        Pool->>Thread: executeTask(task)
        Thread->>Task: execute()
        Task-->>Thread: 执行结果
        Thread->>Pool: 通知任务完成
        Pool->>Pool: tryAssignTask()
    else 无空闲线程
        Pool->>Queue: 检查队列容量
        alt 队列未满
            Pool->>Queue: enqueue(task)
            Queue-->>Pool: 任务已入队
        else 队列已满
            Pool->>Pool: 执行拒绝策略
            Pool-->>Client: 拒绝任务
        end
    end
    
    Note over Thread,Task: 线程空闲时自动获取任务
    
    Thread->>Pool: 请求下一个任务
    Pool->>Queue: dequeue()
    Queue-->>Pool: 返回任务
    Pool->>Thread: assignTask(task)
    Thread->>Task: execute()
    Task-->>Thread: 执行结果
    Thread->>Pool: 通知任务完成
    
    Note over Client,Task: 性能监控
    
    Pool->>Pool: 更新统计信息
    Pool->>Pool: 记录执行时间
    Pool->>Pool: 更新活跃线程数
```

## 💡 设计优势

1. **性能优化**: 避免频繁创建和销毁线程的开销
2. **资源控制**: 限制并发线程数量，防止资源耗尽
3. **响应速度**: 预创建的线程可以立即执行任务
4. **吞吐量提升**: 通过任务队列处理突发请求
5. **可配置性**: 支持多种配置参数和拒绝策略
6. **监控能力**: 提供详细的运行时统计信息

## ⚠️ 注意事项

1. **线程数配置**: 需要根据CPU核心数和任务类型合理配置
2. **队列容量**: 过大可能导致内存问题，过小可能频繁拒绝任务
3. **任务类型**: CPU密集型和I/O密集型任务需要不同的配置
4. **死锁风险**: 避免任务间的相互依赖导致死锁
5. **异常处理**: 确保任务异常不会影响线程池的正常运行
6. **资源清理**: 应用关闭时需要正确关闭线程池

## 🎯 使用场景

### 1. Web服务器请求处理
```typescript
const requestPool = ThreadPoolFactory.newFixedThreadPool(10, 100);

app.use(async (req, res, next) => {
  const task = new Task('handle-request', async () => {
    return await handleRequest(req);
  });
  
  const result = await requestPool.submit(task);
  res.json(result);
});
```

### 2. 批量数据处理
```typescript
const dataPool = ThreadPoolFactory.newFixedThreadPool(4, 50);

const processBatch = async (dataItems: any[]) => {
  const tasks = dataItems.map(item => 
    new Task(`process-${item.id}`, () => processDataItem(item))
  );
  
  const promises = tasks.map(task => dataPool.submit(task));
  return await Promise.all(promises);
};
```

### 3. 异步任务调度
```typescript
const schedulerPool = ThreadPoolFactory.newCachedThreadPool(200);

const scheduleTask = (taskName: string, delay: number) => {
  setTimeout(() => {
    const task = new Task(taskName, async () => {
      return await executeScheduledTask(taskName);
    });
    schedulerPool.submit(task);
  }, delay);
};
```

## 📝 实现要点

1. **线程安全**: 确保任务队列和线程状态的线程安全访问
2. **任务分配**: 实现高效的任务分配算法
3. **异常隔离**: 单个任务的异常不应影响其他任务
4. **优雅关闭**: 支持等待任务完成的优雅关闭
5. **监控统计**: 提供详细的运行时监控信息
6. **配置验证**: 验证配置参数的合理性

## 🚀 扩展变体

### 优先级线程池
```typescript
class PriorityThreadPool extends ThreadPool {
  private priorityQueue: PriorityQueue<Task>;
  
  submit(task: Task, priority: number): Promise<void> {
    this.priorityQueue.enqueue(task, priority);
    return this.tryAssignHighPriorityTask();
  }
}
```

### 动态调整线程池
```typescript
class DynamicThreadPool extends ThreadPool {
  private loadMonitor: LoadMonitor;
  
  private autoAdjustPoolSize(): void {
    const load = this.loadMonitor.getCurrentLoad();
    if (load > 0.8) {
      this.resize(this.getThreads().length + 1);
    } else if (load < 0.3) {
      this.resize(Math.max(1, this.getThreads().length - 1));
    }
  }
}
```

### 分区线程池
```typescript
class PartitionedThreadPool {
  private pools: Map<string, ThreadPool> = new Map();
  
  submitToPartition(partition: string, task: Task): Promise<void> {
    const pool = this.pools.get(partition) || this.createPartition(partition);
    return pool.submit(task);
  }
}
```

---

> 线程池模式是并发编程的基础设施，它通过合理的资源管理和任务调度，显著提升了多线程应用的性能和稳定性。
