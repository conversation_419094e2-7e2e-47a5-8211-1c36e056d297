# 生产者-消费者模式架构图

## 模式概述

生产者-消费者模式（Producer-Consumer Pattern）是一种并发设计模式，用于解决生产数据的速度与消费数据的速度不匹配的问题。通过在生产者和消费者之间引入缓冲区（通常是队列），实现了两者的解耦，提高了系统的并发性和稳定性。

## 核心特点

- **解耦合**：生产者和消费者不直接交互，通过缓冲区通信
- **异步处理**：生产者和消费者可以以不同的速度工作
- **缓冲机制**：队列作为缓冲区，平衡生产和消费速度
- **并发安全**：支持多个生产者和消费者同时工作

## 系统架构图

```mermaid
graph TB
    %% 生产者层
    subgraph Producers["🏭 生产者层"]
        P1[日志生产者1<br/>LogProducer1]
        P2[日志生产者2<br/>LogProducer2]
        P3[日志生产者3<br/>LogProducer3]
    end
    
    %% 缓冲区核心
    subgraph Buffer["📦 缓冲区 (Message Queue)"]
        Q[MessageQueue<br/>- queue: T[]<br/>- maxSize: number<br/>+ enqueue(item: T)<br/>+ dequeue(): T<br/>+ size(): number<br/>+ isEmpty(): boolean]
        
        subgraph QueueState["队列状态"]
            QS[当前大小: 5/10<br/>等待处理的消息]
        end
    end
    
    %% 消费者层
    subgraph Consumers["⚙️ 消费者层"]
        C1[日志消费者1<br/>LogConsumer1<br/>处理ERROR日志]
        C2[日志消费者2<br/>LogConsumer2<br/>处理WARN日志]
        C3[日志消费者3<br/>LogConsumer3<br/>处理INFO日志]
    end
    
    %% 消息流
    subgraph MessageFlow["📄 消息对象"]
        M[LogMessage<br/>- id: string<br/>- level: LogLevel<br/>- message: string<br/>- timestamp: Date]
    end
    
    %% 连接关系
    P1 -->|enqueue| Q
    P2 -->|enqueue| Q
    P3 -->|enqueue| Q
    
    Q -->|dequeue| C1
    Q -->|dequeue| C2
    Q -->|dequeue| C3
    
    Q -.->|contains| M
    
    %% 样式
    classDef producer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef buffer fill:#f3e5f5,stroke:#4a148c,stroke-width:3px
    classDef consumer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef message fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef state fill:#fce4ec,stroke:#880e4f,stroke-width:1px
    
    class P1,P2,P3 producer
    class Q buffer
    class C1,C2,C3 consumer
    class M message
    class QS state
```

## 时序图

```mermaid
sequenceDiagram
    participant P1 as 生产者1
    participant P2 as 生产者2
    participant Q as 消息队列
    participant C1 as 消费者1
    participant C2 as 消费者2

    Note over Q: 系统启动，队列为空
    
    par 生产者并发生产
        P1->>Q: enqueue(ErrorMessage)
        Q-->>P1: 入队成功
    and
        P2->>Q: enqueue(InfoMessage)
        Q-->>P2: 入队成功
    end
    
    Note over Q: 队列中有2个消息
    
    par 消费者并发消费
        C1->>Q: dequeue()
        Q-->>C1: 返回ErrorMessage
        C1->>C1: processErrorMessage()
    and
        C2->>Q: dequeue()
        Q-->>C2: 返回InfoMessage
        C2->>C2: processInfoMessage()
    end
    
    Note over Q: 队列为空
    
    P1->>Q: enqueue(WarnMessage)
    Q-->>P1: 入队成功
    
    Note over Q: 队列满的情况
    loop 队列已满时
        P2->>Q: enqueue(Message)
        Q-->>P2: 入队失败，队列已满
        P2->>P2: 等待或重试
    end
    
    C1->>Q: dequeue()
    Q-->>C1: 返回WarnMessage
    
    Note over Q: 队列有空间，生产者可以继续
    P2->>Q: enqueue(Message)
    Q-->>P2: 入队成功
```

## 类图

```mermaid
classDiagram
    class LogMessage {
        +string id
        +LogLevel level
        +string message
        +Date timestamp
    }
    
    class LogLevel {
        <<enumeration>>
        INFO
        WARN
        ERROR
    }
    
    class MessageQueue~T~ {
        -T[] queue
        -number maxSize
        +constructor(maxSize: number)
        +enqueue(item: T) boolean
        +dequeue() T | null
        +size() number
        +isEmpty() boolean
        +isFull() boolean
    }
    
    class LogProducer {
        -MessageQueue~LogMessage~ queue
        -boolean isRunning
        +constructor(queue: MessageQueue)
        +start() void
        +stop() void
        -produce() Promise~void~
        -generateLogMessage() LogMessage
    }
    
    class LogConsumer {
        -MessageQueue~LogMessage~ queue
        -boolean isRunning
        -string name
        +constructor(name: string, queue: MessageQueue)
        +start() void
        +stop() void
        -consume() Promise~void~
        -processMessage(message: LogMessage) Promise~void~
        -saveToErrorLog(message: LogMessage) void
        -saveToWarningLog(message: LogMessage) void
        -saveToInfoLog(message: LogMessage) void
        -sendAlert(message: LogMessage) void
    }
    
    class LogProcessingSystem {
        -MessageQueue~LogMessage~ queue
        -LogProducer producer
        -LogConsumer[] consumers
        +constructor()
        +start() void
        +stop() void
        +getQueueStatus() object
    }
    
    %% 关系
    MessageQueue "1" *-- "*" LogMessage : contains
    LogProducer "1" --> "1" MessageQueue : uses
    LogConsumer "*" --> "1" MessageQueue : uses
    LogProcessingSystem "1" *-- "1" MessageQueue : contains
    LogProcessingSystem "1" *-- "1" LogProducer : manages
    LogProcessingSystem "1" *-- "*" LogConsumer : manages
    LogMessage "1" --> "1" LogLevel : has
    
    %% 样式
    classDef coreClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef producerClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef consumerClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef systemClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef dataClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class MessageQueue coreClass
    class LogProducer producerClass
    class LogConsumer consumerClass
    class LogProcessingSystem systemClass
    class LogMessage,LogLevel dataClass
```

## 核心组件说明

### 1. MessageQueue（消息队列）
- **职责**：作为生产者和消费者之间的缓冲区
- **核心方法**：
  - `enqueue()`: 生产者添加消息
  - `dequeue()`: 消费者获取消息
  - `size()`: 获取队列大小
- **特点**：线程安全、容量限制、FIFO顺序

### 2. LogProducer（日志生产者）
- **职责**：生成日志消息并放入队列
- **特点**：
  - 异步生产消息
  - 处理队列满的情况
  - 可配置生产速率

### 3. LogConsumer（日志消费者）
- **职责**：从队列中获取消息并处理
- **特点**：
  - 异步消费消息
  - 根据日志级别分类处理
  - 支持多个消费者并发工作

### 4. LogProcessingSystem（日志处理系统）
- **职责**：统一管理整个生产者-消费者系统
- **功能**：系统启动/停止、状态监控、资源管理

## 应用场景

1. **日志处理系统**：高并发日志收集和处理
2. **消息队列**：异步消息传递系统
3. **数据流处理**：实时数据处理管道
4. **批量任务处理**：大量任务的分批处理
5. **Web服务器**：请求处理和响应生成

## 优势

- ✅ **解耦合**：生产者和消费者独立工作
- ✅ **缓冲机制**：平衡不同的处理速度
- ✅ **并发性**：支持多生产者多消费者
- ✅ **可扩展性**：易于增加生产者或消费者
- ✅ **容错性**：单个组件故障不影响整体

## 注意事项

- ⚠️ **队列大小**：需要合理设置队列容量
- ⚠️ **内存管理**：避免队列无限增长
- ⚠️ **线程安全**：确保队列操作的原子性
- ⚠️ **死锁预防**：避免生产者和消费者相互等待
- ⚠️ **性能监控**：监控队列长度和处理速度

## 实现要点

### 队列容量管理
```typescript
// 队列满时的处理策略
if (this.queue.length >= this.maxSize) {
  // 策略1: 阻塞等待
  // 策略2: 丢弃最旧的消息
  // 策略3: 返回失败
  return false;
}
```

### 并发安全
```typescript
// 使用锁或原子操作确保线程安全
private readonly lock = new Mutex();

async enqueue(item: T): Promise<boolean> {
  return await this.lock.runExclusive(() => {
    // 原子操作
  });
}
```

### 优雅关闭
```typescript
async stop(): Promise<void> {
  this.isRunning = false;
  // 等待当前任务完成
  // 清理资源
}
```
