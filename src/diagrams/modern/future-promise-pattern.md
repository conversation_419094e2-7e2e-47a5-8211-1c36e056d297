# Future/Promise模式架构图

## 模式概述

Future/Promise模式是一种并发设计模式，用于处理异步操作的结果。它提供了一个代表异步计算结果的占位符，允许程序在异步操作完成之前继续执行其他任务，并在结果可用时进行处理。

## 核心特点

- **异步非阻塞**：不阻塞主线程，提高程序响应性
- **链式操作**：支持 then、catch、finally 等链式调用
- **状态管理**：明确的 pending、fulfilled、rejected 状态
- **错误处理**：统一的异常处理机制

## 系统架构图

```mermaid
graph TB
    %% 异步操作层
    subgraph AsyncOps["🔄 异步操作层"]
        API[API请求<br/>fetchUserData()]
        DB[数据库查询<br/>queryDatabase()]
        FILE[文件操作<br/>readFile()]
        TIMER[定时器<br/>setTimeout()]
    end
    
    %% Promise核心
    subgraph PromiseCore["⚡ Promise核心"]
        P[Promise<T><br/>- state: PromiseState<br/>- value: T | Error<br/>+ then(onFulfilled, onRejected)<br/>+ catch(onRejected)<br/>+ finally(onFinally)]
        
        subgraph States["Promise状态"]
            PENDING[Pending<br/>初始状态]
            FULFILLED[Fulfilled<br/>成功完成]
            REJECTED[Rejected<br/>失败状态]
        end
    end
    
    %% 链式操作层
    subgraph ChainOps["🔗 链式操作层"]
        THEN[.then()<br/>处理成功结果]
        CATCH[.catch()<br/>处理错误]
        FINALLY[.finally()<br/>清理操作]
        ALL[Promise.all()<br/>并行等待]
        RACE[Promise.race()<br/>竞速执行]
    end
    
    %% 结果处理层
    subgraph ResultHandlers["📊 结果处理层"]
        SUCCESS[成功处理器<br/>onFulfilled]
        ERROR[错误处理器<br/>onRejected]
        CLEANUP[清理处理器<br/>onFinally]
    end
    
    %% 连接关系
    API --> P
    DB --> P
    FILE --> P
    TIMER --> P
    
    P --> PENDING
    PENDING --> FULFILLED
    PENDING --> REJECTED
    
    P --> THEN
    P --> CATCH
    P --> FINALLY
    
    THEN --> SUCCESS
    CATCH --> ERROR
    FINALLY --> CLEANUP
    
    P -.-> ALL
    P -.-> RACE
    
    %% 样式
    classDef asyncOp fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef promiseCore fill:#f3e5f5,stroke:#4a148c,stroke-width:3px
    classDef chainOp fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef handler fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef state fill:#fce4ec,stroke:#880e4f,stroke-width:1px
    
    class API,DB,FILE,TIMER asyncOp
    class P promiseCore
    class THEN,CATCH,FINALLY,ALL,RACE chainOp
    class SUCCESS,ERROR,CLEANUP handler
    class PENDING,FULFILLED,REJECTED state
```

## 时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Promise as Promise对象
    participant AsyncOp as 异步操作
    participant Handler as 处理器

    Note over Promise: 创建Promise，状态为Pending
    
    Client->>Promise: new Promise(executor)
    Promise->>AsyncOp: 执行异步操作
    
    Note over AsyncOp: 异步操作进行中...
    
    Client->>Promise: .then(onSuccess, onError)
    Promise-->>Client: 返回新的Promise
    
    Client->>Promise: .catch(onError)
    Promise-->>Client: 返回新的Promise
    
    Client->>Promise: .finally(onFinally)
    Promise-->>Client: 返回新的Promise
    
    Note over AsyncOp: 异步操作完成
    
    alt 操作成功
        AsyncOp->>Promise: resolve(result)
        Promise->>Promise: 状态变为Fulfilled
        Promise->>Handler: 调用onSuccess(result)
        Handler-->>Promise: 处理完成
    else 操作失败
        AsyncOp->>Promise: reject(error)
        Promise->>Promise: 状态变为Rejected
        Promise->>Handler: 调用onError(error)
        Handler-->>Promise: 错误处理完成
    end
    
    Promise->>Handler: 调用onFinally()
    Handler-->>Promise: 清理完成
    
    Promise-->>Client: 最终结果
```

## 类图

```mermaid
classDiagram
    class PromiseState {
        <<enumeration>>
        PENDING
        FULFILLED
        REJECTED
    }
    
    class Promise~T~ {
        -PromiseState state
        -T | Error value
        -Function[] onFulfilledCallbacks
        -Function[] onRejectedCallbacks
        +constructor(executor: Function)
        +then(onFulfilled?: Function, onRejected?: Function) Promise
        +catch(onRejected: Function) Promise
        +finally(onFinally: Function) Promise
        +resolve(value: T) void
        +reject(reason: Error) void
        +static all(promises: Promise[]) Promise
        +static race(promises: Promise[]) Promise
        +static resolve(value: T) Promise
        +static reject(reason: Error) Promise
    }
    
    class AsyncOperation {
        +fetchUserData(userId: string) Promise~User~
        +queryDatabase(sql: string) Promise~any[]~
        +readFile(path: string) Promise~string~
        +delay(ms: number) Promise~void~
    }
    
    class PromiseChain {
        -Promise[] promises
        +addStep(operation: Function) PromiseChain
        +execute() Promise
        +parallel(operations: Function[]) Promise
        +sequential(operations: Function[]) Promise
    }
    
    class ErrorHandler {
        +handleNetworkError(error: NetworkError) void
        +handleTimeoutError(error: TimeoutError) void
        +handleValidationError(error: ValidationError) void
        +logError(error: Error) void
    }
    
    class ResultProcessor {
        +processUserData(user: User) ProcessedUser
        +formatResponse(data: any) FormattedResponse
        +validateResult(result: any) boolean
        +transformData(data: any) any
    }
    
    %% 关系
    Promise "1" --> "1" PromiseState : has
    Promise "1" --> "*" AsyncOperation : executes
    PromiseChain "1" *-- "*" Promise : contains
    Promise "1" --> "1" ErrorHandler : uses
    Promise "1" --> "1" ResultProcessor : uses
    
    %% 样式
    classDef coreClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef operationClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef handlerClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef utilityClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef enumClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class Promise coreClass
    class AsyncOperation operationClass
    class ErrorHandler,ResultProcessor handlerClass
    class PromiseChain utilityClass
    class PromiseState enumClass
```

## 核心组件说明

### 1. Promise核心
- **职责**：管理异步操作的状态和结果
- **状态转换**：
  - Pending → Fulfilled (成功)
  - Pending → Rejected (失败)
  - 状态一旦改变就不可逆
- **特点**：支持链式调用、错误传播

### 2. 异步操作层
- **API请求**：HTTP请求、RESTful API调用
- **数据库查询**：SQL查询、NoSQL操作
- **文件操作**：读写文件、上传下载
- **定时器**：延时操作、定时任务

### 3. 链式操作
- **then()**：处理成功结果，支持值传递和Promise返回
- **catch()**：捕获和处理错误
- **finally()**：无论成功失败都执行的清理操作

### 4. 组合操作
- **Promise.all()**：等待所有Promise完成
- **Promise.race()**：返回最先完成的Promise结果

## 应用场景

1. **HTTP请求处理**：API调用、数据获取
2. **文件I/O操作**：文件读写、上传下载
3. **数据库查询**：异步数据库操作
4. **微服务调用**：服务间异步通信
5. **定时任务**：延时执行、定时操作

## 优势

- ✅ **非阻塞**：不阻塞主线程执行
- ✅ **链式调用**：优雅的异步代码组织
- ✅ **错误处理**：统一的异常处理机制
- ✅ **组合能力**：支持并行和串行操作组合
- ✅ **状态明确**：清晰的异步操作状态管理

## 注意事项

- ⚠️ **错误处理**：必须正确处理Promise的rejected状态
- ⚠️ **内存泄漏**：避免创建永不resolve的Promise
- ⚠️ **异常传播**：理解Promise链中的错误传播机制
- ⚠️ **并发控制**：合理控制并发Promise的数量
- ⚠️ **调试困难**：异步调用栈的调试相对复杂

## 实现要点

### 基本Promise实现
```typescript
class MyPromise<T> {
  private state: 'pending' | 'fulfilled' | 'rejected' = 'pending';
  private value: T | Error | undefined;
  private onFulfilledCallbacks: Function[] = [];
  private onRejectedCallbacks: Function[] = [];

  constructor(executor: (resolve: Function, reject: Function) => void) {
    try {
      executor(this.resolve.bind(this), this.reject.bind(this));
    } catch (error) {
      this.reject(error);
    }
  }

  private resolve(value: T): void {
    if (this.state === 'pending') {
      this.state = 'fulfilled';
      this.value = value;
      this.onFulfilledCallbacks.forEach(callback => callback(value));
    }
  }

  private reject(reason: Error): void {
    if (this.state === 'pending') {
      this.state = 'rejected';
      this.value = reason;
      this.onRejectedCallbacks.forEach(callback => callback(reason));
    }
  }
}
```

### 链式调用实现
```typescript
then<U>(
  onFulfilled?: (value: T) => U | Promise<U>,
  onRejected?: (reason: Error) => U | Promise<U>
): Promise<U> {
  return new MyPromise<U>((resolve, reject) => {
    // 实现链式调用逻辑
  });
}
```

### 错误处理最佳实践
```typescript
fetchUserData(userId: string)
  .then(user => processUser(user))
  .then(processedUser => saveUser(processedUser))
  .catch(error => {
    console.error('操作失败:', error);
    // 错误恢复逻辑
  })
  .finally(() => {
    // 清理资源
  });
```
