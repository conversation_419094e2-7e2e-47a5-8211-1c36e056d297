# 单子模式架构图

## 📋 模式概述

单子模式（Monad Pattern）是函数式编程中的高级抽象，它扩展了函子的概念，提供了flatMap（或bind）操作来处理嵌套的计算上下文。单子遵循三个法则：左单位元、右单位元和结合律，能够优雅地处理副作用、异步操作、错误处理等复杂场景。

## 🎯 解决的问题

在函数式编程中：
- 处理嵌套的计算上下文（如Maybe<Maybe<T>>）
- 管理副作用和异步操作
- 提供统一的错误处理机制
- 支持安全的链式操作
- 避免回调地狱和嵌套结构

## 🏗️ 单子架构图

```mermaid
classDiagram
    class Monad~T~ {
        <<interface>>
        +flatMap(fn: T => Monad~U~): Monad~U~
        +map(fn: T => U): Monad~U~
    }
    
    class Maybe~T~ {
        -value: T | null
        +static of(value: T): Maybe~T~
        +static none(): Maybe~T~
        +static fromNullable(value: T?): Maybe~T~
        +flatMap(fn: T => Maybe~U~): Maybe~U~
        +map(fn: T => U): Maybe~U~
        +getValue(): T | null
        +hasValue(): boolean
        +getOrElse(defaultValue: T): T
        +filter(predicate: T => boolean): Maybe~T~
        +apply(fn: Maybe~(T => U)~): Maybe~U~
    }
    
    class IO~T~ {
        -effect: () => T
        +static of(value: T): IO~T~
        +static from(effect: () => T): IO~T~
        +flatMap(fn: T => IO~U~): IO~U~
        +map(fn: T => U): IO~U~
        +run(): T
    }
    
    class State~S,T~ {
        -computation: (S) => [T, S]
        +static of(value: T): State~S,T~
        +static get(): State~S,S~
        +static put(newState: S): State~S,void~
        +static modify(fn: S => S): State~S,void~
        +flatMap(fn: T => State~S,U~): State~S,U~
        +map(fn: T => U): State~S,U~
        +run(initialState: S): T
        +runState(initialState: S): [T, S]
        +execState(initialState: S): S
    }
    
    class Either~L,R~ {
        <<abstract>>
        +flatMap(fn: R => Either~L,U~): Either~L,U~
        +map(fn: R => U): Either~L,U~
        +isLeft(): boolean
        +isRight(): boolean
    }
    
    class Left~L,R~ {
        -value: L
        +flatMap(fn: R => Either~L,U~): Either~L,U~
        +map(fn: R => U): Either~L,U~
        +getValue(): L
    }
    
    class Right~L,R~ {
        -value: R
        +flatMap(fn: R => Either~L,U~): Either~L,U~
        +map(fn: R => U): Either~L,U~
        +getValue(): R
    }
    
    class List~T~ {
        -items: T[]
        +static of(...items: T[]): List~T~
        +static fromArray(items: T[]): List~T~
        +flatMap(fn: T => List~U~): List~U~
        +map(fn: T => U): List~U~
        +toArray(): T[]
        +filter(predicate: T => boolean): List~T~
        +fold(initial: U, fn: (U, T) => U): U
    }
    
    Monad <|.. Maybe
    Monad <|.. IO
    Monad <|.. State
    Monad <|.. Either
    Monad <|.. List
    Either <|-- Left
    Either <|-- Right
    
    note for Monad "单子接口：定义flatMap和map操作\n遵循单子法则"
    note for Maybe "可选值单子：处理可能为空的值\n避免空指针异常"
    note for IO "IO单子：处理副作用\n延迟执行和组合"
    note for State "状态单子：处理状态计算\n函数式状态管理"
    note for Either "错误处理单子：Left表示错误\nRight表示成功值"
```

## 🔄 单子操作流程图

```mermaid
flowchart TD
    A[原始单子 Monad&lt;T&gt;] --> B{单子类型?}
    
    B -->|Maybe| C[Maybe&lt;T&gt;]
    B -->|IO| D[IO&lt;T&gt;]
    B -->|State| E[State&lt;S,T&gt;]
    B -->|Either| F[Either&lt;L,R&gt;]
    
    C --> C1{hasValue?}
    C1 -->|true| C2[应用函数 f: T → Maybe&lt;U&gt;]
    C1 -->|false| C3[返回 Maybe.none&lt;U&gt;]
    C2 --> C4[Maybe&lt;U&gt;]
    C3 --> C4
    
    D --> D1[创建新的副作用函数]
    D1 --> D2[组合原副作用和转换函数]
    D2 --> D3[IO&lt;U&gt;]
    
    E --> E1[应用状态转换函数]
    E1 --> E2[组合状态计算]
    E2 --> E3[State&lt;S,U&gt;]
    
    F --> F1{isRight?}
    F1 -->|true| F2[应用函数到Right值]
    F1 -->|false| F3[保持Left不变]
    F2 --> F4[Either&lt;L,U&gt;]
    F3 --> F4
    
    C4 --> G[结果单子 Monad&lt;U&gt;]
    D3 --> G
    E3 --> G
    F4 --> G
    
    style A fill:#e1f5fe
    style G fill:#c8e6c9
    style C2 fill:#fff3e0
    style D2 fill:#fff3e0
    style E2 fill:#fff3e0
    style F2 fill:#fff3e0
```

## 📊 单子法则验证

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Monad as 单子实例
    participant Function as 转换函数
    
    Note over Client,Function: 左单位元法则验证
    
    Client->>Monad: unit(a).flatMap(f)
    Monad->>Function: 调用f(a)
    Function-->>Monad: 返回Monad&lt;U&gt;
    Monad-->>Client: 结果1
    
    Client->>Function: 直接调用f(a)
    Function-->>Client: 结果2
    
    Note over Client,Function: 验证：结果1 === 结果2
    
    Note over Client,Function: 右单位元法则验证
    
    Client->>Monad: m.flatMap(unit)
    Monad->>Monad: 应用unit函数
    Monad-->>Client: 结果应等于原始m
    
    Note over Client,Function: 结合律验证
    
    Client->>Monad: m.flatMap(f).flatMap(g)
    Monad->>Function: 先应用f
    Function-->>Monad: 中间结果
    Monad->>Function: 再应用g
    Function-->>Client: 最终结果1
    
    Client->>Monad: m.flatMap(x => f(x).flatMap(g))
    Monad->>Function: 应用组合函数
    Function-->>Client: 最终结果2
    
    Note over Client,Function: 验证：结果1 === 结果2
```

## 💡 设计优势

1. **统一接口**: 提供一致的API处理不同类型的计算上下文
2. **链式操作**: 支持安全的链式调用，避免嵌套回调
3. **错误处理**: 优雅的错误传播和处理机制
4. **副作用管理**: 将副作用封装在可控的上下文中
5. **可组合性**: 高度可组合，支持复杂的计算流程
6. **类型安全**: 编译时类型检查，减少运行时错误

## ⚠️ 注意事项

1. **学习曲线**: 概念抽象，需要深入理解函数式编程
2. **性能开销**: 额外的抽象层可能带来性能开销
3. **调试困难**: 链式调用可能增加调试复杂性
4. **过度设计**: 简单场景可能不需要单子抽象
5. **团队接受度**: 需要团队具备函数式编程基础

## 🎯 使用场景

### 1. 异步操作链
```typescript
const fetchUserProfile = (userId: string): IO<User> =>
  IO.from(() => api.getUser(userId))
    .flatMap(user => IO.from(() => api.getUserProfile(user.id)))
    .flatMap(profile => IO.from(() => api.getUserPreferences(profile.id)));
```

### 2. 错误处理链
```typescript
const processData = (input: string): Either<Error, ProcessedData> =>
  validateInput(input)
    .flatMap(parseData)
    .flatMap(transformData)
    .flatMap(saveData);
```

### 3. 状态管理
```typescript
const updateCounter = State.get<number>()
  .flatMap(count => State.put(count + 1))
  .flatMap(() => State.get<number>())
  .map(newCount => `Updated to: ${newCount}`);
```

## 📝 实现要点

1. **法则遵循**: 确保实现满足单子的三个法则
2. **类型安全**: 使用泛型确保类型正确性
3. **异常处理**: 在flatMap中适当处理异常
4. **性能优化**: 避免不必要的对象创建
5. **API设计**: 提供直观易用的API接口

## 🚀 扩展变体

### 异步单子
```typescript
class AsyncMonad<T> implements Monad<T> {
  constructor(private promise: Promise<T>) {}
  
  flatMap<U>(fn: (value: T) => AsyncMonad<U>): AsyncMonad<U> {
    return new AsyncMonad(
      this.promise.then(value => fn(value).promise)
    );
  }
}
```

### 验证单子
```typescript
class Validation<E, T> implements Monad<T> {
  constructor(private errors: E[], private value?: T) {}
  
  flatMap<U>(fn: (value: T) => Validation<E, U>): Validation<E, U> {
    if (this.errors.length > 0) {
      return new Validation<E, U>(this.errors);
    }
    const result = fn(this.value!);
    return new Validation<E, U>(result.errors, result.value);
  }
}
```

---

> 单子模式是函数式编程的核心抽象，它提供了处理复杂计算上下文的统一方法，虽然学习曲线较陡，但掌握后能显著提升代码的可组合性和安全性。
