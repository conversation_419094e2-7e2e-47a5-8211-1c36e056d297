# MVVM模式架构图

## 模式概述

MVVM模式（Model-View-ViewModel）是一种架构设计模式，特别适用于现代前端框架。它通过ViewModel作为View和Model之间的桥梁，实现了数据绑定和命令绑定，使得UI开发更加高效和可维护。

## 核心特点

- **数据绑定**：自动同步View和Model之间的数据
- **命令绑定**：将用户操作封装为可执行的命令
- **关注点分离**：View专注展示，Model专注数据，ViewModel处理逻辑
- **可测试性**：ViewModel可以独立于View进行单元测试

## 系统架构图

```mermaid
graph TB
    %% 用户交互层
    subgraph UserLayer["👤 用户交互层"]
        USER[用户<br/>User]
        INPUT[用户输入<br/>User Input]
        DISPLAY[界面显示<br/>UI Display]
    end
    
    %% View层
    subgraph ViewLayer["🖼️ View层 (用户界面)"]
        UF[用户表单<br/>UserForm<br/>- 姓名输入框<br/>- 邮箱输入框<br/>- 年龄输入框]
        PF[产品表单<br/>ProductForm<br/>- 产品名称<br/>- 价格输入<br/>- 库存管理]
        
        subgraph ViewElements["界面元素"]
            INPUTS[输入控件<br/>Input Controls]
            BUTTONS[命令按钮<br/>Command Buttons]
            DISPLAYS[显示控件<br/>Display Controls]
        end
        
        subgraph DataBinding["数据绑定"]
            TWOWAY[双向绑定<br/>Two-way Binding]
            ONEWAY[单向绑定<br/>One-way Binding]
        end
    end
    
    %% ViewModel层
    subgraph ViewModelLayer["🎮 ViewModel层 (视图模型)"]
        UVM[用户ViewModel<br/>UserViewModel<br/>+ fullName (computed)<br/>+ isValid (computed)<br/>+ displayStatus (computed)<br/>+ updateUser()<br/>+ resetUser()]
        
        PVM[产品ViewModel<br/>ProductViewModel<br/>+ formattedPrice (computed)<br/>+ stockStatus (computed)<br/>+ recommendLevel (computed)<br/>+ updateProduct()<br/>+ resetProduct()]
        
        subgraph VMFeatures["ViewModel特性"]
            COMPUTED[计算属性<br/>Computed Properties]
            COMMANDS[命令处理<br/>Command Handlers]
            VALIDATION[数据验证<br/>Data Validation]
            NOTIFY[变更通知<br/>Change Notification]
        end
    end
    
    %% Model层
    subgraph ModelLayer["📊 Model层 (数据模型)"]
        UM[用户模型<br/>UserModel<br/>- firstName: string<br/>- lastName: string<br/>- email: string<br/>- age: number]
        
        PM[产品模型<br/>ProductModel<br/>- name: string<br/>- price: number<br/>- stock: number<br/>- rating: number]
        
        subgraph ModelServices["模型服务"]
            VALIDATOR[数据验证器<br/>ModelValidator]
            PERSISTENCE[数据持久化<br/>Data Persistence]
            API[API服务<br/>API Services]
        end
    end
    
    %% 命令系统
    subgraph CommandSystem["⚡ 命令系统"]
        SAVE[保存命令<br/>SaveCommand]
        VALIDATE[验证命令<br/>ValidateCommand]
        EXPORT[导出命令<br/>ExportCommand]
        UNDO[撤销命令<br/>UndoCommand]
    end
    
    %% 数据流向
    USER -->|用户操作| INPUT
    INPUT -->|事件触发| UF
    INPUT -->|事件触发| PF
    
    UF <-->|双向绑定| UVM
    PF <-->|双向绑定| PVM
    
    UVM <-->|数据同步| UM
    PVM <-->|数据同步| PM
    
    UVM -->|计算属性| DISPLAYS
    PVM -->|计算属性| DISPLAYS
    DISPLAYS -->|界面更新| DISPLAY
    DISPLAY -->|视觉反馈| USER
    
    %% 命令流
    BUTTONS -->|命令执行| SAVE
    BUTTONS -->|命令执行| VALIDATE
    BUTTONS -->|命令执行| EXPORT
    BUTTONS -->|命令执行| UNDO
    
    SAVE -->|操作数据| UM
    SAVE -->|操作数据| PM
    VALIDATE -->|验证数据| VALIDATOR
    EXPORT -->|导出数据| API
    
    %% 样式
    classDef userLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef viewLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef viewModelLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef modelLayer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef commandLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class USER,INPUT,DISPLAY userLayer
    class UF,PF,INPUTS,BUTTONS,DISPLAYS,TWOWAY,ONEWAY viewLayer
    class UVM,PVM,COMPUTED,COMMANDS,VALIDATION,NOTIFY viewModelLayer
    class UM,PM,VALIDATOR,PERSISTENCE,API modelLayer
    class SAVE,VALIDATE,EXPORT,UNDO commandLayer
```

## 时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant View as View(界面)
    participant ViewModel as ViewModel(视图模型)
    participant Model as Model(数据模型)
    participant Command as Command(命令)

    Note over User,Command: 用户修改数据的完整流程
    
    User->>View: 1. 在输入框中输入数据
    View->>ViewModel: 2. 双向绑定触发数据更新
    ViewModel->>ViewModel: 3. 更新内部状态
    ViewModel->>Model: 4. 同步数据到Model
    
    Note over ViewModel: 计算属性自动更新
    ViewModel->>ViewModel: 5. 重新计算派生属性
    ViewModel->>View: 6. 通知View更新显示
    View->>User: 7. 界面实时反映变化
    
    Note over User,Command: 用户执行命令的流程
    
    User->>View: 8. 点击"保存"按钮
    View->>ViewModel: 9. 触发保存命令
    ViewModel->>ViewModel: 10. 检查命令可执行性
    
    alt 数据有效
        ViewModel->>Command: 11a. 执行保存命令
        Command->>Model: 12a. 获取数据进行保存
        Model-->>Command: 13a. 返回保存结果
        Command-->>ViewModel: 14a. 返回执行结果
        ViewModel->>View: 15a. 更新命令状态
        View->>User: 16a. 显示保存成功
    else 数据无效
        ViewModel->>View: 11b. 显示验证错误
        View->>User: 12b. 提示用户修正数据
    end
    
    Note over User,Command: 响应式更新演示
    
    User->>View: 17. 触发批量更新
    View->>ViewModel: 18. 调用批量更新方法
    
    par 并行更新多个属性
        ViewModel->>Model: 19a. 更新用户数据
        ViewModel->>Model: 19b. 更新产品数据
    end
    
    ViewModel->>ViewModel: 20. 重新计算所有计算属性
    ViewModel->>View: 21. 批量通知界面更新
    View->>User: 22. 界面同步更新所有相关显示
```

## 类图

```mermaid
classDiagram
    class UserModel {
        +string firstName
        +string lastName
        +string email
        +number age
    }
    
    class ProductModel {
        +string name
        +number price
        +number stock
        +number rating
    }
    
    class ModelValidator {
        +static validateUser(user: UserModel) ValidationResult
        +static validateProduct(product: ProductModel) ValidationResult
        -static isValidEmail(email: string) boolean
    }
    
    class ValidationResult {
        +boolean isValid
        +string[] errors
    }
    
    class UserViewModel {
        -UserModel _model
        -Set~Function~ _listeners
        +constructor(model: UserModel)
        +string firstName
        +string lastName
        +string email
        +number age
        +string fullName
        +boolean isValid
        +string[] validationErrors
        +string displayStatus
        +string statusClass
        +updateFromModel(model: UserModel) void
        +reset() void
        +toModel() UserModel
        +subscribe(listener: Function) Function
        -notifyChange() void
    }
    
    class ProductViewModel {
        -ProductModel _model
        -Set~Function~ _listeners
        +constructor(model: ProductModel)
        +string name
        +number price
        +number stock
        +number rating
        +string formattedPrice
        +string stockStatus
        +string stockClass
        +string recommendLevel
        +string recommendClass
        +boolean isValid
        +updateFromModel(model: ProductModel) void
        +reset() void
        +toModel() ProductModel
        +subscribe(listener: Function) Function
        -notifyChange() void
    }
    
    class Command {
        <<interface>>
        +string name
        +execute() Promise~boolean~
        +canExecute() boolean
    }
    
    class SaveCommand {
        +string name
        -UserViewModel userVM
        -ProductViewModel productVM
        +constructor(userVM: UserViewModel, productVM: ProductViewModel)
        +canExecute() boolean
        +execute() Promise~boolean~
    }
    
    class ValidateCommand {
        +string name
        -UserViewModel userVM
        -ProductViewModel productVM
        +constructor(userVM: UserViewModel, productVM: ProductViewModel)
        +canExecute() boolean
        +execute() Promise~boolean~
    }
    
    class ExportCommand {
        +string name
        -UserViewModel userVM
        -ProductViewModel productVM
        +constructor(userVM: UserViewModel, productVM: ProductViewModel)
        +canExecute() boolean
        +execute() Promise~boolean~
    }
    
    class MVVMViewController {
        -UserViewModel userVM
        -ProductViewModel productVM
        -Map~string,Command~ commands
        -CommandHistory[] commandHistory
        +constructor()
        +getUserViewModel() UserViewModel
        +getProductViewModel() ProductViewModel
        +executeCommand(commandName: string) Promise~boolean~
        +canExecuteCommand(commandName: string) boolean
        +getCommandHistory() CommandHistory[]
        +batchUpdate(userData: Partial~UserModel~, productData: Partial~ProductModel~) void
        +resetAll() void
        +loadSampleData() void
        -setupCommands() void
        -setupDataBinding() void
        -updateView() void
    }
    
    class CommandHistory {
        +string name
        +Date timestamp
        +boolean success
    }
    
    %% 关系
    UserViewModel "1" --> "1" UserModel : manages
    ProductViewModel "1" --> "1" ProductModel : manages
    ModelValidator "1" --> "*" ValidationResult : creates
    UserViewModel "1" --> "*" ValidationResult : uses
    ProductViewModel "1" --> "*" ValidationResult : uses
    
    SaveCommand ..|> Command : implements
    ValidateCommand ..|> Command : implements
    ExportCommand ..|> Command : implements
    
    SaveCommand "1" --> "1" UserViewModel : uses
    SaveCommand "1" --> "1" ProductViewModel : uses
    ValidateCommand "1" --> "1" UserViewModel : uses
    ValidateCommand "1" --> "1" ProductViewModel : uses
    ExportCommand "1" --> "1" UserViewModel : uses
    ExportCommand "1" --> "1" ProductViewModel : uses
    
    MVVMViewController "1" *-- "1" UserViewModel : contains
    MVVMViewController "1" *-- "1" ProductViewModel : contains
    MVVMViewController "1" *-- "*" Command : manages
    MVVMViewController "1" --> "*" CommandHistory : creates
    
    %% 样式
    classDef modelClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef viewModelClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef commandClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef controllerClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef utilityClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    
    class UserModel,ProductModel modelClass
    class UserViewModel,ProductViewModel viewModelClass
    class Command,SaveCommand,ValidateCommand,ExportCommand commandClass
    class MVVMViewController controllerClass
    class ModelValidator,ValidationResult,CommandHistory utilityClass
```

## 核心组件说明

### 1. Model（数据模型）
- **UserModel**: 用户数据结构，包含基本信息字段
- **ProductModel**: 产品数据结构，包含商品信息
- **ModelValidator**: 数据验证器，提供统一的验证逻辑
- **特点**: 纯数据对象，不包含UI逻辑

### 2. ViewModel（视图模型）
- **UserViewModel**: 用户数据的视图模型
  - 计算属性：fullName、isValid、displayStatus
  - 数据绑定：双向绑定用户输入
  - 变更通知：观察者模式实现响应式更新
- **ProductViewModel**: 产品数据的视图模型
  - 计算属性：formattedPrice、stockStatus、recommendLevel
  - 业务逻辑：库存状态判断、推荐等级计算
- **特点**: 连接View和Model的桥梁，包含展示逻辑

### 3. View（视图）
- **用户界面**: 表单输入、数据显示、命令按钮
- **数据绑定**: v-model实现双向绑定
- **事件处理**: 用户操作触发ViewModel方法
- **特点**: 纯展示层，不包含业务逻辑

### 4. Command（命令系统）
- **SaveCommand**: 保存数据命令
- **ValidateCommand**: 验证数据命令
- **ExportCommand**: 导出数据命令
- **特点**: 封装用户操作，支持撤销和重做

## 应用场景

1. **前端框架开发**：Vue.js、Angular、React等现代框架
2. **桌面应用开发**：WPF、Electron等桌面应用
3. **移动应用开发**：原生移动应用的架构设计
4. **表单密集型应用**：数据录入、配置管理等场景
5. **实时数据展示**：仪表板、监控界面等

## 优势

- ✅ **数据绑定**：自动同步数据，减少手动DOM操作
- ✅ **关注点分离**：View、ViewModel、Model职责清晰
- ✅ **可测试性**：ViewModel可以独立测试，不依赖UI
- ✅ **可维护性**：代码结构清晰，易于维护和扩展
- ✅ **响应式**：数据变化自动触发UI更新

## 注意事项

- ⚠️ **复杂性**：小型应用可能过度设计
- ⚠️ **学习成本**：需要理解数据绑定和响应式概念
- ⚠️ **性能考虑**：大量计算属性可能影响性能
- ⚠️ **内存管理**：注意解除事件监听，避免内存泄漏
- ⚠️ **调试困难**：数据绑定链路较长，调试相对复杂

## 实现要点

### 数据绑定实现
```typescript
// 双向绑定的核心实现
get firstName(): string { 
  return this._model.firstName; 
}

set firstName(value: string) {
  this._model.firstName = value;
  this.notifyChange(); // 触发响应式更新
}
```

### 计算属性实现
```typescript
// 基于基础数据计算派生属性
get fullName(): string {
  return `${this._model.firstName} ${this._model.lastName}`.trim() || '未设置';
}
```

### 命令模式集成
```typescript
// 命令的可执行性检查
canExecute(): boolean {
  return this.userVM.isValid && this.productVM.isValid;
}

// 异步命令执行
async execute(): Promise<boolean> {
  if (!this.canExecute()) return false;
  // 执行具体的业务逻辑
}
```
