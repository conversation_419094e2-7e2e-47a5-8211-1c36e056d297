# 发布-订阅模式架构图

## 模式概述

发布-订阅模式（Publish-Subscribe Pattern）是一种消息传递模式，其中发送者（发布者）不直接向特定的接收者（订阅者）发送消息，而是将消息发布到一个中介（事件总线），由中介负责将消息传递给所有感兴趣的订阅者。

## 核心特点

- **松耦合**：发布者和订阅者之间没有直接依赖
- **动态订阅**：订阅者可以在运行时动态订阅和取消订阅
- **一对多通信**：一个发布者可以向多个订阅者发送消息
- **事件驱动**：基于事件的异步通信机制

## 系统架构图

```mermaid
graph TB
    %% 发布者层
    subgraph Publishers["📢 发布者层"]
        TP[科技新闻发布者<br/>TechPublisher]
        SP[体育新闻发布者<br/>SportsPublisher]
        FP[财经新闻发布者<br/>FinancePublisher]
    end
    
    %% 事件总线核心
    subgraph EventBus["🚌 事件总线 (Event Bus)"]
        EB[EventBus<br/>- subscribers: Map<br/>- messageHistory: Array<br/>+ subscribe()<br/>+ unsubscribe()<br/>+ publish()]
        
        subgraph EventTypes["事件类型"]
            TE[tech-news]
            SE[sports-news] 
            FE[finance-news]
            UE[urgent-alert]
        end
    end
    
    %% 订阅者层
    subgraph Subscribers["📱 订阅者层"]
        S1[科技爱好者小王<br/>interests: [tech-news]]
        S2[体育迷小李<br/>interests: [sports-news]]
        S3[投资者小张<br/>interests: [finance, tech]]
        S4[新闻记者小刘<br/>interests: [tech-news]]
    end
    
    %% 消息流
    subgraph MessageFlow["📡 消息流"]
        M1[Message<br/>- id: string<br/>- type: string<br/>- data: any<br/>- timestamp: Date<br/>- source: string]
    end
    
    %% 连接关系
    TP -->|publish tech-news| EB
    SP -->|publish sports-news| EB
    FP -->|publish finance-news| EB
    
    EB -->|notify| S1
    EB -->|notify| S2
    EB -->|notify| S3
    EB -->|notify| S4
    
    EB -.->|creates| M1
    M1 -.->|delivers to| S1
    M1 -.->|delivers to| S2
    M1 -.->|delivers to| S3
    M1 -.->|delivers to| S4
    
    %% 订阅关系
    S1 -.->|subscribe to| TE
    S2 -.->|subscribe to| SE
    S3 -.->|subscribe to| TE
    S3 -.->|subscribe to| FE
    S4 -.->|subscribe to| TE
    
    %% 样式
    classDef publisher fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef eventbus fill:#f3e5f5,stroke:#4a148c,stroke-width:3px
    classDef subscriber fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef message fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef event fill:#fce4ec,stroke:#880e4f,stroke-width:1px
    
    class TP,SP,FP publisher
    class EB eventbus
    class S1,S2,S3,S4 subscriber
    class M1 message
    class TE,SE,FE,UE event
```

## 时序图

```mermaid
sequenceDiagram
    participant TP as 科技新闻发布者
    participant EB as 事件总线
    participant S1 as 科技爱好者小王
    participant S2 as 体育迷小李
    participant S3 as 投资者小张
    participant S4 as 新闻记者小刘

    Note over EB: 系统初始化阶段
    
    S1->>EB: subscribe('tech-news', callback)
    EB-->>S1: 返回取消订阅函数
    
    S2->>EB: subscribe('sports-news', callback)
    EB-->>S2: 返回取消订阅函数
    
    S3->>EB: subscribe('tech-news', callback)
    EB-->>S3: 返回取消订阅函数
    S3->>EB: subscribe('finance-news', callback)
    EB-->>S3: 返回取消订阅函数
    
    Note over EB: 新订阅者加入
    S4->>EB: subscribe('tech-news', callback)
    EB-->>S4: 返回取消订阅函数
    
    Note over EB: 消息发布阶段
    
    TP->>EB: publish('tech-news', {title: 'AI技术突破', ...})
    
    Note over EB: 事件总线处理消息
    EB->>EB: 创建Message对象
    EB->>EB: 保存到messageHistory
    EB->>EB: 查找tech-news订阅者
    
    Note over EB: 通知所有相关订阅者
    EB->>S1: callback(message)
    S1->>S1: handleMessage()
    S1-->>EB: 处理完成
    
    EB->>S3: callback(message)
    S3->>S3: handleMessage()
    S3-->>EB: 处理完成
    
    EB->>S4: callback(message)
    S4->>S4: handleMessage()
    S4-->>EB: 处理完成
    
    Note over S2: 体育迷小李没有收到消息<br/>(未订阅tech-news)
    
    Note over EB: 第二条消息发布
    
    TP->>EB: publish('tech-news', {title: '量子计算进展', ...})
    
    EB->>EB: 创建Message对象
    EB->>EB: 保存到messageHistory
    EB->>EB: 查找tech-news订阅者
    
    par 并行通知所有订阅者
        EB->>S1: callback(message)
        S1->>S1: handleMessage()
    and
        EB->>S3: callback(message)
        S3->>S3: handleMessage()
    and
        EB->>S4: callback(message)
        S4->>S4: handleMessage()
    end
    
    Note over EB: 取消订阅演示
    S1->>EB: unsubscribe('tech-news')
    EB->>EB: 从订阅列表中移除S1
    EB-->>S1: 取消订阅成功
    
    Note over EB: 再次发布消息
    TP->>EB: publish('tech-news', {title: '新技术发布', ...})
    
    Note over S1: 小王不再收到消息<br/>(已取消订阅)
    
    EB->>S3: callback(message)
    S3->>S3: handleMessage()
    
    EB->>S4: callback(message)
    S4->>S4: handleMessage()
```

## 类图

```mermaid
classDiagram
    class Message {
        +string id
        +string type
        +any data
        +Date timestamp
        +string source
    }
    
    class Subscriber {
        +string id
        +string name
        +callback(message: Message) void
    }
    
    class EventBus {
        -Map~string, Set~Subscriber~~ subscribers
        -Message[] messageHistory
        +subscribe(eventType: string, subscriber: Subscriber) Function
        +unsubscribe(eventType: string, subscriber: Subscriber) void
        +publish(eventType: string, data: any, source: string) void
        +getSubscriberCount(eventType: string) number
        +getEventTypes() string[]
        +getMessageHistory(eventType?: string) Message[]
        +clearHistory() void
    }
    
    class NewsPublisher {
        -EventBus eventBus
        -string publisherId
        +constructor(eventBus: EventBus, publisherId: string)
        +publishTechNews(title: string, content: string) void
        +publishSportsNews(title: string, content: string) void
        +publishFinanceNews(title: string, content: string) void
        +publishUrgentAlert(message: string) void
    }
    
    class NewsSubscriber {
        -string id
        -string name
        -string[] interests
        -Message[] receivedMessages
        -Function[] unsubscribeFunctions
        +constructor(id: string, name: string, interests: string[])
        +subscribeToNews(eventBus: EventBus) void
        -handleMessage(message: Message) void
        -handleUrgentMessage(message: Message) void
        +unsubscribeAll() void
        +getReceivedMessages() Message[]
        +clearMessages() void
        +getInfo() object
    }
    
    class NewsSystem {
        -EventBus eventBus
        -Map~string, NewsPublisher~ publishers
        -Map~string, NewsSubscriber~ subscribers
        +constructor()
        +addPublisher(id: string) NewsPublisher
        +addSubscriber(id: string, name: string, interests: string[]) NewsSubscriber
        +removeSubscriber(id: string) void
        +getSystemStats() object
        +getEventBus() EventBus
    }
    
    %% 关系
    EventBus "1" *-- "*" Message : creates
    EventBus "1" *-- "*" Subscriber : manages
    NewsPublisher "1" --> "1" EventBus : uses
    NewsSubscriber "*" --> "1" EventBus : subscribes to
    NewsSystem "1" *-- "1" EventBus : contains
    NewsSystem "1" *-- "*" NewsPublisher : manages
    NewsSystem "1" *-- "*" NewsSubscriber : manages
    NewsSubscriber "1" *-- "*" Message : receives
    
    %% 样式
    classDef coreClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef publisherClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef subscriberClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef systemClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef dataClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class EventBus coreClass
    class NewsPublisher publisherClass
    class NewsSubscriber subscriberClass
    class NewsSystem systemClass
    class Message,Subscriber dataClass
```

## 核心组件说明

### 1. EventBus（事件总线）
- **职责**：管理订阅者和消息分发
- **核心方法**：
  - `subscribe()`: 注册订阅者
  - `unsubscribe()`: 取消订阅
  - `publish()`: 发布消息
- **特点**：线程安全、支持多种事件类型

### 2. NewsPublisher（新闻发布者）
- **职责**：发布不同类型的新闻
- **支持的新闻类型**：科技、体育、财经、紧急通知
- **特点**：与订阅者完全解耦

### 3. NewsSubscriber（新闻订阅者）
- **职责**：接收和处理感兴趣的新闻
- **特点**：可订阅多种类型、支持动态取消订阅
- **消息处理**：异步处理、错误隔离

### 4. NewsSystem（新闻系统）
- **职责**：统一管理发布者和订阅者
- **功能**：系统统计、订阅者管理、发布者管理

## 应用场景

1. **事件系统**：UI 组件间的事件通信
2. **消息队列**：异步消息处理系统
3. **状态管理**：应用状态变化通知
4. **微服务通信**：服务间的松耦合通信
5. **实时通知**：推送通知系统

## 优势

- ✅ **松耦合**：发布者和订阅者互不依赖
- ✅ **可扩展**：易于添加新的发布者和订阅者
- ✅ **灵活性**：支持动态订阅和取消订阅
- ✅ **错误隔离**：单个订阅者错误不影响其他订阅者
- ✅ **性能优化**：支持异步消息处理

## 注意事项

- ⚠️ **内存管理**：及时取消订阅避免内存泄漏
- ⚠️ **消息顺序**：不保证消息的处理顺序
- ⚠️ **错误处理**：需要妥善处理订阅者的异常
- ⚠️ **性能考虑**：大量订阅者时需要考虑性能优化
