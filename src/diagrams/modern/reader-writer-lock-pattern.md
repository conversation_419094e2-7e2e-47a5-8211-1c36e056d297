# 读写锁模式架构图

## 📋 模式概述

读写锁模式（Reader-Writer Lock Pattern）是一种并发控制机制，允许多个读者同时访问共享资源，但写者需要独占访问。这种模式在读多写少的场景下能显著提升性能，避免了传统互斥锁的读者间不必要的阻塞。

## 🎯 解决的问题

在多线程环境中：
- 传统互斥锁导致读者间不必要的阻塞
- 读操作通常不会修改数据，可以安全并发
- 写操作需要独占访问以保证数据一致性
- 需要在性能和一致性之间找到平衡

## 🏗️ 读写锁架构图

```mermaid
classDiagram
    class ReaderWriterLock {
        -policy: LockPolicy
        -maxReaders: number
        -activeReaders: number
        -isWriting: boolean
        -waitingQueue: WaitingItem[]
        +readLock(): Promise~void~
        +readUnlock(): void
        +writeLock(): Promise~void~
        +writeUnlock(): void
        +getStatus(): LockStatus
        +setPolicy(policy: LockPolicy): void
        -canAcquireReadLock(): boolean
        -canAcquireWriteLock(): boolean
        -tryWakeUpNext(): void
    }
    
    class LockStatus {
        +activeReaders: number
        +waitingReaders: number
        +isWriting: boolean
        +waitingWriters: number
    }
    
    class WaitingItem {
        +id: string
        +type: 'read' | 'write'
        +resolve: () => void
        +timestamp: number
    }
    
    class ReaderWriterLockFactory {
        +static createReaderPriorityLock(): ReaderWriterLock
        +static createWriterPriorityLock(): ReaderWriterLock
        +static createFairLock(): ReaderWriterLock
    }
    
    class ReaderWriterLockMonitor {
        -lock: ReaderWriterLock
        -metrics: PoolMetrics
        +monitorRead(operation: () => Promise~T~): Promise~T~
        +monitorWrite(operation: () => Promise~T~): Promise~T~
        +getMetrics(): PoolMetrics
        +reset(): void
    }
    
    ReaderWriterLock "1" *-- "*" WaitingItem : contains
    ReaderWriterLock "1" --> "1" LockStatus : provides
    ReaderWriterLockFactory ..> ReaderWriterLock : creates
    ReaderWriterLockMonitor "1" --> "1" ReaderWriterLock : monitors
    
    note for ReaderWriterLock "核心组件：管理读写访问\n支持多种优先策略"
    note for WaitingItem "等待队列项：记录等待的读者和写者\n按策略排序"
```

## 🔄 读写锁工作流程图

```mermaid
flowchart TD
    A[请求锁] --> B{锁类型?}
    
    B -->|读锁| C{可以获得读锁?}
    B -->|写锁| D{可以获得写锁?}
    
    C -->|是| E[获得读锁，增加读者计数]
    C -->|否| F[加入读者等待队列]
    
    D -->|是| G[获得写锁，设置写入状态]
    D -->|否| H[加入写者等待队列]
    
    E --> I[执行读操作]
    G --> J[执行写操作]
    
    I --> K[释放读锁，减少读者计数]
    J --> L[释放写锁，清除写入状态]
    
    K --> M{读者计数为0?}
    L --> N[尝试唤醒等待的线程]
    
    M -->|是| N
    M -->|否| O[继续其他读操作]
    
    F --> P[根据策略排序队列]
    H --> P
    P --> Q[等待被唤醒]
    Q --> R[被唤醒，重新尝试获取锁]
    
    N --> S{优先策略?}
    S -->|读者优先| T[优先唤醒读者]
    S -->|写者优先| U[优先唤醒写者]
    S -->|公平策略| V[按时间顺序唤醒]
    
    style A fill:#e1f5fe
    style E fill:#c8e6c9
    style G fill:#c8e6c9
    style N fill:#fff3e0
```

## 💡 设计优势

1. **并发性能**: 允许多个读者同时访问，提高读取性能
2. **数据一致性**: 写者独占访问，确保数据一致性
3. **策略灵活**: 支持读者优先、写者优先、公平策略
4. **饥饿控制**: 通过不同策略避免读者或写者饥饿
5. **资源优化**: 在读多写少场景下显著优于互斥锁

## ⚠️ 注意事项

1. **策略选择**: 需要根据应用场景选择合适的优先策略
2. **饥饿问题**: 读者优先可能导致写者饥饿，写者优先可能导致读者饥饿
3. **死锁预防**: 避免嵌套锁和循环依赖
4. **性能权衡**: 在写操作频繁时可能不如简单互斥锁
5. **实现复杂**: 比简单互斥锁实现更复杂

## 🎯 使用场景

### 1. 缓存系统
```typescript
const cache = new Map();
const rwLock = new ReaderWriterLock('reader-priority');

// 读取缓存
async function getFromCache(key: string) {
  await rwLock.readLock();
  try {
    return cache.get(key);
  } finally {
    rwLock.readUnlock();
  }
}

// 更新缓存
async function updateCache(key: string, value: any) {
  await rwLock.writeLock();
  try {
    cache.set(key, value);
  } finally {
    rwLock.writeUnlock();
  }
}
```

### 2. 配置管理
```typescript
class ConfigManager {
  private config: any = {};
  private rwLock = new ReaderWriterLock('reader-priority');

  async getConfig(key: string) {
    await this.rwLock.readLock();
    try {
      return this.config[key];
    } finally {
      this.rwLock.readUnlock();
    }
  }

  async updateConfig(updates: any) {
    await this.rwLock.writeLock();
    try {
      Object.assign(this.config, updates);
    } finally {
      this.rwLock.writeUnlock();
    }
  }
}
```

## 📝 实现要点

1. **锁状态管理**: 准确维护读者计数和写者状态
2. **等待队列**: 实现高效的等待队列和唤醒机制
3. **策略实现**: 正确实现不同的优先策略
4. **异常安全**: 确保异常情况下锁能正确释放
5. **性能优化**: 减少锁竞争和上下文切换

## 🚀 扩展变体

### 升级锁
```typescript
class UpgradeableLock extends ReaderWriterLock {
  async upgradeToWriteLock(): Promise<void> {
    // 将读锁升级为写锁
  }
}
```

### 超时锁
```typescript
class TimeoutReaderWriterLock extends ReaderWriterLock {
  async readLockWithTimeout(timeout: number): Promise<boolean> {
    // 带超时的读锁获取
  }
}
```

---

> 读写锁模式是并发编程中的重要工具，它在读多写少的场景下能显著提升性能，但需要根据具体应用选择合适的策略以避免饥饿问题。
