# 柯里化模式架构图

## 概述
柯里化（Currying）是函数式编程中的一种技术，它将接受多个参数的函数转换为一系列接受单个参数的函数。这种模式提高了函数的可复用性和组合性。

## 柯里化转换过程

```mermaid
graph TB
    subgraph "Currying Transformation"
        subgraph "Original Function"
            OF[Original Function]
            PARAMS[f(a, b, c) → result]
            OF --> PARAMS
        end
        
        subgraph "Curried Function"
            CF[Curried Function]
            CHAIN[f(a) → g(b) → h(c) → result]
            CF --> CHAIN
        end
        
        subgraph "Partial Application"
            PA1[f(a)]
            PA2[g(b)]
            PA3[h(c)]
            RESULT[result]
            
            PA1 --> PA2
            PA2 --> PA3
            PA3 --> RESULT
        end
    end
    
    OF -.-> CF : "Curry Transform"
    CF --> PA1 : "Apply First Arg"
    
    %% 样式
    classDef originalClass fill:#e3f2fd
    classDef curriedClass fill:#e8f5e8
    classDef partialClass fill:#fff3e0
    
    class OF,PARAMS originalClass
    class CF,CHAIN curriedClass
    class PA1,PA2,PA3,RESULT partialClass
```

## 函数组合架构

```mermaid
graph TB
    subgraph "Function Composition with Currying"
        subgraph "Base Functions"
            ADD[add(x, y)]
            MUL[multiply(x, y)]
            FILTER[filter(predicate, array)]
            MAP[map(transform, array)]
        end
        
        subgraph "Curried Versions"
            C_ADD[curriedAdd(x)(y)]
            C_MUL[curriedMultiply(x)(y)]
            C_FILTER[curriedFilter(predicate)(array)]
            C_MAP[curriedMap(transform)(array)]
        end
        
        subgraph "Specialized Functions"
            ADD5[add5 = curriedAdd(5)]
            MUL2[double = curriedMultiply(2)]
            IS_EVEN[filterEven = curriedFilter(isEven)]
            TO_UPPER[mapToUpper = curriedMap(toUpperCase)]
        end
        
        subgraph "Function Pipeline"
            PIPE[Pipeline]
            STEP1[Step 1: filterEven]
            STEP2[Step 2: mapToUpper]
            STEP3[Step 3: add5 to each]
            
            PIPE --> STEP1
            STEP1 --> STEP2
            STEP2 --> STEP3
        end
    end
    
    %% 转换关系
    ADD -.-> C_ADD : "Curry"
    MUL -.-> C_MUL : "Curry"
    FILTER -.-> C_FILTER : "Curry"
    MAP -.-> C_MAP : "Curry"
    
    %% 特化关系
    C_ADD --> ADD5 : "Partial Apply"
    C_MUL --> MUL2 : "Partial Apply"
    C_FILTER --> IS_EVEN : "Partial Apply"
    C_MAP --> TO_UPPER : "Partial Apply"
    
    %% 管道使用
    IS_EVEN --> STEP1
    TO_UPPER --> STEP2
    ADD5 --> STEP3
```

## 事件处理中的柯里化

```mermaid
graph TB
    subgraph "Event Handling with Currying"
        subgraph "Event Handlers"
            EH[Event Handler]
            CLICK[onClick(element, event)]
            SUBMIT[onSubmit(form, event)]
            CHANGE[onChange(input, event)]
        end
        
        subgraph "Curried Handlers"
            C_CLICK[curriedOnClick(element)(event)]
            C_SUBMIT[curriedOnSubmit(form)(event)]
            C_CHANGE[curriedOnChange(input)(event)]
        end
        
        subgraph "Specialized Handlers"
            BTN_CLICK[buttonClick = curriedOnClick(button)]
            FORM_SUBMIT[formSubmit = curriedOnSubmit(form)]
            INPUT_CHANGE[inputChange = curriedOnChange(input)]
        end
        
        subgraph "Event Binding"
            BIND[Event Binding]
            BTN[Button Element]
            FORM[Form Element]
            INPUT[Input Element]
            
            BTN --> BTN_CLICK
            FORM --> FORM_SUBMIT
            INPUT --> INPUT_CHANGE
        end
    end
    
    %% 转换关系
    CLICK -.-> C_CLICK : "Curry"
    SUBMIT -.-> C_SUBMIT : "Curry"
    CHANGE -.-> C_CHANGE : "Curry"
    
    %% 特化关系
    C_CLICK --> BTN_CLICK : "Bind Element"
    C_SUBMIT --> FORM_SUBMIT : "Bind Form"
    C_CHANGE --> INPUT_CHANGE : "Bind Input"
```

## 配置和验证中的应用

```mermaid
graph TB
    subgraph "Configuration and Validation"
        subgraph "Validation Functions"
            VAL[Validation]
            MIN_LEN[minLength(min, value)]
            MAX_LEN[maxLength(max, value)]
            PATTERN[matchPattern(regex, value)]
            REQUIRED[required(value)]
        end
        
        subgraph "Curried Validators"
            C_MIN[curriedMinLength(min)(value)]
            C_MAX[curriedMaxLength(max)(value)]
            C_PATTERN[curriedMatchPattern(regex)(value)]
            C_REQUIRED[curriedRequired(value)]
        end
        
        subgraph "Field Validators"
            NAME_VAL[nameValidator]
            EMAIL_VAL[emailValidator]
            PHONE_VAL[phoneValidator]
            PASSWORD_VAL[passwordValidator]
        end
        
        subgraph "Validation Rules"
            NAME_RULES[Name Rules]
            EMAIL_RULES[Email Rules]
            PHONE_RULES[Phone Rules]
            PASS_RULES[Password Rules]
            
            NAME_RULES --> NAME_VAL
            EMAIL_RULES --> EMAIL_VAL
            PHONE_RULES --> PHONE_VAL
            PASS_RULES --> PASSWORD_VAL
        end
        
        subgraph "Form Validation"
            FORM_VAL[Form Validator]
            FIELD_VAL[Field Validation]
            COMBINE[Combine Results]
            
            FORM_VAL --> FIELD_VAL
            FIELD_VAL --> COMBINE
        end
    end
    
    %% 配置关系
    C_MIN --> NAME_VAL : "minLength(2)"
    C_MAX --> NAME_VAL : "maxLength(50)"
    C_PATTERN --> EMAIL_VAL : "emailPattern"
    C_REQUIRED --> PASSWORD_VAL : "required"
    
    %% 使用关系
    NAME_VAL --> FIELD_VAL
    EMAIL_VAL --> FIELD_VAL
    PHONE_VAL --> FIELD_VAL
    PASSWORD_VAL --> FIELD_VAL
```

## API调用中的柯里化

```mermaid
graph TB
    subgraph "API Calls with Currying"
        subgraph "HTTP Client"
            HTTP[HTTP Client]
            REQUEST[request(method, url, options)]
            GET[get(url, options)]
            POST[post(url, data, options)]
        end
        
        subgraph "Curried HTTP"
            C_REQUEST[curriedRequest(method)(url)(options)]
            C_GET[curriedGet(url)(options)]
            C_POST[curriedPost(url)(data)(options)]
        end
        
        subgraph "API Endpoints"
            API_BASE[API Base URL]
            USERS_API[usersAPI = curriedGet('/api/users')]
            POSTS_API[postsAPI = curriedGet('/api/posts')]
            AUTH_API[authAPI = curriedPost('/api/auth')]
        end
        
        subgraph "Configured Clients"
            USER_CLIENT[User Client]
            POST_CLIENT[Post Client]
            AUTH_CLIENT[Auth Client]
            
            USER_GET[getUsers = usersAPI(options)]
            POST_GET[getPosts = postsAPI(options)]
            AUTH_LOGIN[login = authAPI(credentials)]
        end
        
        subgraph "Service Layer"
            USER_SERVICE[User Service]
            POST_SERVICE[Post Service]
            AUTH_SERVICE[Auth Service]
        end
    end
    
    %% 转换关系
    REQUEST -.-> C_REQUEST : "Curry"
    GET -.-> C_GET : "Curry"
    POST -.-> C_POST : "Curry"
    
    %% API配置
    C_GET --> USERS_API : "Bind URL"
    C_GET --> POSTS_API : "Bind URL"
    C_POST --> AUTH_API : "Bind URL"
    
    %% 客户端配置
    USERS_API --> USER_GET : "Bind Options"
    POSTS_API --> POST_GET : "Bind Options"
    AUTH_API --> AUTH_LOGIN : "Bind Data"
    
    %% 服务使用
    USER_GET --> USER_SERVICE
    POST_GET --> POST_SERVICE
    AUTH_LOGIN --> AUTH_SERVICE
```

## 数据处理管道

```mermaid
graph TB
    subgraph "Data Processing Pipeline"
        subgraph "Raw Data"
            RAW[Raw Data]
            CSV[CSV Data]
            JSON[JSON Data]
            XML[XML Data]
        end
        
        subgraph "Processing Functions"
            PARSE[parse(format, data)]
            FILTER[filter(predicate, data)]
            TRANSFORM[transform(mapper, data)]
            AGGREGATE[aggregate(reducer, data)]
        end
        
        subgraph "Curried Processors"
            C_PARSE[curriedParse(format)(data)]
            C_FILTER[curriedFilter(predicate)(data)]
            C_TRANSFORM[curriedTransform(mapper)(data)]
            C_AGGREGATE[curriedAggregate(reducer)(data)]
        end
        
        subgraph "Specialized Processors"
            PARSE_CSV[parseCSV = curriedParse('csv')]
            FILTER_VALID[filterValid = curriedFilter(isValid)]
            TO_OBJECT[toObject = curriedTransform(objectMapper)]
            SUM_VALUES[sumValues = curriedAggregate(sum)]
        end
        
        subgraph "Processing Pipeline"
            PIPELINE[Data Pipeline]
            STEP1[Parse CSV]
            STEP2[Filter Valid]
            STEP3[Transform to Objects]
            STEP4[Aggregate Results]
            
            PIPELINE --> STEP1
            STEP1 --> STEP2
            STEP2 --> STEP3
            STEP3 --> STEP4
        end
    end
    
    %% 数据流
    CSV --> PARSE_CSV
    PARSE_CSV --> STEP1
    FILTER_VALID --> STEP2
    TO_OBJECT --> STEP3
    SUM_VALUES --> STEP4
```

## 优势与应用场景

### 优势
1. **函数复用**: 通过部分应用创建专用函数
2. **组合性**: 易于组合和链式调用
3. **配置化**: 通过参数预设创建配置化函数
4. **延迟执行**: 支持延迟计算和惰性求值
5. **函数式编程**: 支持函数式编程范式

### 应用场景
- 事件处理和回调函数
- 数据处理和转换管道
- API客户端配置
- 表单验证和配置
- 函数式编程库
- 中间件和插件系统

### 最佳实践
1. **适度使用**: 不要过度柯里化简单函数
2. **类型安全**: 使用TypeScript确保类型安全
3. **性能考虑**: 注意闭包和内存使用
4. **文档说明**: 清楚说明柯里化函数的用法
5. **工具支持**: 使用函数式编程库如Ramda、Lodash/FP
