<template>
  <div class="flyweight-demo">
    <div class="pattern-info">
      <p class="description">
        享元模式通过共享技术有效地支持大量细粒度对象。它将对象的内部状态和外部状态分离，内部状态存储在享元对象中并可以共享，外部状态由客户端保存。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>文本编辑器字符渲染</li>
          <li>游戏中的粒子系统</li>
          <li>网页中的图标系统</li>
          <li>数据库连接池</li>
          <li>缓存系统</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 文本编辑器字符渲染：</h4>
      <div class="demo-controls">
        <div class="text-input-group">
          <textarea 
            v-model="inputText" 
            placeholder="输入文本内容..."
            class="text-input"
            rows="3"
          ></textarea>
          <div class="format-controls">
            <select v-model="selectedFontSize" class="font-size-select">
              <option value="12">12px</option>
              <option value="14">14px</option>
              <option value="16">16px</option>
              <option value="18">18px</option>
              <option value="20">20px</option>
            </select>
            <select v-model="selectedColor" class="color-select">
              <option value="black">黑色</option>
              <option value="red">红色</option>
              <option value="blue">蓝色</option>
              <option value="green">绿色</option>
            </select>
            <button @click="addText" class="demo-btn">添加文本</button>
          </div>
        </div>
        <div class="action-buttons">
          <button @click="addRandomText" class="demo-btn">添加随机文本</button>
          <button @click="clearDocument" class="demo-btn clear">清空文档</button>
          <button @click="showStatistics" class="demo-btn">刷新统计</button>
        </div>
      </div>

      <div class="demo-output">
        <div class="document-preview">
          <h5>文档预览</h5>
          <div class="preview-container">
            <div 
              v-for="(char, index) in documentCharacters" 
              :key="index"
              class="character-display"
              :style="{
                fontSize: char.fontSize + 'px',
                color: char.color,
                left: char.x + 'px',
                top: char.y + 'px'
              }"
            >
              {{ char.character === ' ' ? '·' : char.character }}
            </div>
          </div>
        </div>

        <div class="flyweight-statistics">
          <h5>享元模式统计</h5>
          <div class="stats-grid">
            <div class="stat-card total-chars">
              <div class="stat-number">{{ documentCharacters.length }}</div>
              <div class="stat-label">总字符数</div>
            </div>
            <div class="stat-card unique-chars">
              <div class="stat-number">{{ uniqueCharacterCount }}</div>
              <div class="stat-label">唯一字符数</div>
            </div>
            <div class="stat-card flyweight-objects">
              <div class="stat-number">{{ flyweightCount }}</div>
              <div class="stat-label">享元对象数</div>
            </div>
            <div class="stat-card memory-saved">
              <div class="stat-number">{{ memorySavedPercentage }}%</div>
              <div class="stat-label">内存节省率</div>
            </div>
          </div>
          
          <div class="memory-comparison">
            <h6>内存使用对比</h6>
            <div class="comparison-bars">
              <div class="comparison-item">
                <span class="comparison-label">传统方式:</span>
                <div class="comparison-bar">
                  <div class="bar traditional" :style="{ width: '100%' }"></div>
                  <span class="bar-value">{{ documentCharacters.length }} 对象</span>
                </div>
              </div>
              <div class="comparison-item">
                <span class="comparison-label">享元模式:</span>
                <div class="comparison-bar">
                  <div class="bar flyweight" :style="{ width: flyweightUsagePercentage + '%' }"></div>
                  <span class="bar-value">{{ flyweightCount }} 对象</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flyweight-pool">
          <h5>享元对象池</h5>
          <div class="pool-container">
            <div 
              v-for="(flyweight, char) in flyweightPool" 
              :key="char"
              class="flyweight-item"
            >
              <div class="flyweight-char">{{ char === ' ' ? 'Space' : char }}</div>
              <div class="flyweight-usage">使用次数: {{ getCharacterUsageCount(char) }}</div>
            </div>
          </div>
        </div>

        <div class="pattern-explanation">
          <h5>享元模式原理</h5>
          <div class="explanation-content">
            <div class="state-separation">
              <div class="intrinsic-state">
                <h6>🔒 内部状态 (Intrinsic State)</h6>
                <p>存储在享元对象中，可以共享</p>
                <ul>
                  <li>字符内容 (如 'A', 'B', 'C')</li>
                  <li>字体类型</li>
                  <li>字符编码</li>
                </ul>
              </div>
              <div class="extrinsic-state">
                <h6>🔓 外部状态 (Extrinsic State)</h6>
                <p>由客户端维护，不可共享</p>
                <ul>
                  <li>字符位置 (x, y)</li>
                  <li>字体大小</li>
                  <li>颜色</li>
                </ul>
              </div>
            </div>
            
            <div class="flyweight-benefit">
              <strong>核心优势:</strong> 通过分离内部状态和外部状态，大量相同字符只需要一个享元对象，显著减少内存使用。
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 大大减少对象的创建，降低内存占用</li>
        <li>✅ 减少运行时对象的数量，提高效率</li>
        <li>✅ 将状态外部化，增加了灵活性</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 使得系统更加复杂</li>
        <li>❌ 为了节省内存，可能造成时间上的损失</li>
        <li>❌ 外部状态的维护增加了复杂性</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from "vue";

// 享元接口
interface CharacterFlyweight {
  render(x: number, y: number, fontSize: number, color: string): string;
  getCharacter(): string;
}

// 具体享元 - 字符
class Character implements CharacterFlyweight {
  private char: string; // 内部状态

  constructor(char: string) {
    this.char = char;
  }

  render(x: number, y: number, fontSize: number, color: string): string {
    return `渲染字符 '${this.char}' 在位置(${x}, ${y}), 字体大小:${fontSize}px, 颜色:${color}`;
  }

  getCharacter(): string {
    return this.char;
  }
}

// 享元工厂
class CharacterFactory {
  private static flyweights: Map<string, Character> = new Map();
  private static createdCount: number = 0;

  static getCharacter(char: string): Character {
    if (!this.flyweights.has(char)) {
      this.flyweights.set(char, new Character(char));
      this.createdCount++;
    }
    return this.flyweights.get(char)!;
  }

  static getFlyweights(): Map<string, Character> {
    return new Map(this.flyweights);
  }

  static getFlyweightCount(): number {
    return this.flyweights.size;
  }

  static reset(): void {
    this.flyweights.clear();
    this.createdCount = 0;
  }
}

// 上下文类 - 文档中的字符实例
class CharacterContext {
  private character: Character;
  private x: number;
  private y: number;
  private fontSize: number;
  private color: string;

  constructor(char: string, x: number, y: number, fontSize: number, color: string) {
    this.character = CharacterFactory.getCharacter(char);
    this.x = x;
    this.y = y;
    this.fontSize = fontSize;
    this.color = color;
  }

  render(): string {
    return this.character.render(this.x, this.y, this.fontSize, this.color);
  }

  getCharacter(): string {
    return this.character.getCharacter();
  }

  getX(): number { return this.x; }
  getY(): number { return this.y; }
  getFontSize(): number { return this.fontSize; }
  getColor(): string { return this.color; }
}

// 文档类
class Document {
  private characters: CharacterContext[] = [];

  addText(text: string, startX: number, startY: number, fontSize: number, color: string): void {
    let x = startX;
    const y = startY;

    for (const char of text) {
      const context = new CharacterContext(char, x, y, fontSize, color);
      this.characters.push(context);
      
      if (char === ' ') {
        x += fontSize * 0.3;
      } else {
        x += fontSize * 0.6;
      }
    }
  }

  getCharacters(): CharacterContext[] {
    return [...this.characters];
  }

  clear(): void {
    this.characters = [];
  }

  getCharacterCount(): number {
    return this.characters.length;
  }
}

// 响应式数据
const inputText = ref<string>('');
const selectedFontSize = ref<number>(16);
const selectedColor = ref<string>('black');
const document = reactive(new Document());
const documentCharacters = ref<Array<{
  character: string;
  x: number;
  y: number;
  fontSize: number;
  color: string;
}>>([]);

// 计算属性
const flyweightPool = computed(() => {
  return CharacterFactory.getFlyweights();
});

const flyweightCount = computed(() => {
  return CharacterFactory.getFlyweightCount();
});

const uniqueCharacterCount = computed(() => {
  const uniqueChars = new Set(documentCharacters.value.map(char => char.character));
  return uniqueChars.size;
});

const memorySavedPercentage = computed(() => {
  if (documentCharacters.value.length === 0) return 0;
  const totalChars = documentCharacters.value.length;
  const flyweights = flyweightCount.value;
  return Math.round((totalChars - flyweights) / totalChars * 100);
});

const flyweightUsagePercentage = computed(() => {
  if (documentCharacters.value.length === 0) return 0;
  return Math.round(flyweightCount.value / documentCharacters.value.length * 100);
});

// 方法
const addText = () => {
  if (!inputText.value.trim()) return;
  
  const startX = Math.random() * 300;
  const startY = Math.random() * 200 + 50;
  
  document.addText(inputText.value, startX, startY, selectedFontSize.value, selectedColor.value);
  updateDocumentCharacters();
  inputText.value = '';
};

const addRandomText = () => {
  const randomTexts = [
    'Hello World!',
    'Design Patterns',
    'Flyweight Pattern',
    'JavaScript TypeScript',
    'Vue.js React Angular',
    'Frontend Development',
    'Software Engineering'
  ];
  
  const randomText = randomTexts[Math.floor(Math.random() * randomTexts.length)];
  const randomFontSize = [12, 14, 16, 18, 20][Math.floor(Math.random() * 5)];
  const randomColor = ['black', 'red', 'blue', 'green'][Math.floor(Math.random() * 4)];
  const startX = Math.random() * 300;
  const startY = Math.random() * 200 + 50;
  
  document.addText(randomText, startX, startY, randomFontSize, randomColor);
  updateDocumentCharacters();
};

const clearDocument = () => {
  document.clear();
  CharacterFactory.reset();
  documentCharacters.value = [];
};

const showStatistics = () => {
  // 触发响应式更新
  updateDocumentCharacters();
};

const updateDocumentCharacters = () => {
  const chars = document.getCharacters();
  documentCharacters.value = chars.map(char => ({
    character: char.getCharacter(),
    x: char.getX(),
    y: char.getY(),
    fontSize: char.getFontSize(),
    color: char.getColor()
  }));
};

const getCharacterUsageCount = (char: string): number => {
  return documentCharacters.value.filter(c => c.character === char).length;
};

// 初始化示例数据
const initializeDemo = () => {
  document.addText('Hello Flyweight Pattern!', 10, 30, 16, 'black');
  document.addText('享元模式演示', 10, 60, 18, 'blue');
  document.addText('Memory Optimization', 10, 90, 14, 'green');
  updateDocumentCharacters();
};

// 初始化
initializeDemo();
</script>

<style lang="scss" scoped>
.flyweight-demo {
  .pattern-info {
    margin-bottom: 2rem;
    
    .description {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }
    
    .use-cases {
      h4 {
        color: #333;
        margin-bottom: 0.5rem;
      }
      
      ul {
        list-style-type: disc;
        padding-left: 1.5rem;
        
        li {
          margin-bottom: 0.3rem;
          color: #666;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
    }
    
    .demo-controls {
      margin-bottom: 1.5rem;
      
      .text-input-group {
        margin-bottom: 1rem;
        
        .text-input {
          width: 100%;
          padding: 0.8rem;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;
          resize: vertical;
          margin-bottom: 0.5rem;
        }
        
        .format-controls {
          display: flex;
          gap: 0.5rem;
          align-items: center;
          flex-wrap: wrap;
          
          .font-size-select, .color-select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
          }
          
          .demo-btn {
            padding: 0.6rem 1.2rem;
            border: none;
            border-radius: 6px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            }
          }
        }
      }
      
      .action-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        
        .demo-btn {
          padding: 0.6rem 1.2rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          }
          
          &.clear {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
          }
        }
      }
    }
    
    .demo-output {
      .document-preview {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 0.5rem;
        }
        
        .preview-container {
          position: relative;
          height: 250px;
          border: 2px dashed #ddd;
          border-radius: 8px;
          background: #fafafa;
          overflow: hidden;
          
          .character-display {
            position: absolute;
            font-family: 'Courier New', monospace;
            font-weight: 500;
            user-select: none;
            transition: all 0.2s ease;
            
            &:hover {
              transform: scale(1.2);
              z-index: 10;
            }
          }
        }
      }
      
      .flyweight-statistics {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 1rem;
          margin-bottom: 1rem;
          
          .stat-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #e0e0e0;
            
            &.total-chars {
              border-left-color: #1890ff;
            }
            
            &.unique-chars {
              border-left-color: #52c41a;
            }
            
            &.flyweight-objects {
              border-left-color: #faad14;
            }
            
            &.memory-saved {
              border-left-color: #722ed1;
            }
            
            .stat-number {
              font-size: 2rem;
              font-weight: bold;
              color: #333;
              margin-bottom: 0.5rem;
            }
            
            .stat-label {
              font-size: 0.9rem;
              color: #666;
            }
          }
        }
        
        .memory-comparison {
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 8px;
          
          h6 {
            color: #333;
            margin: 0 0 1rem 0;
          }
          
          .comparison-bars {
            .comparison-item {
              display: flex;
              align-items: center;
              margin-bottom: 0.8rem;
              
              .comparison-label {
                min-width: 80px;
                font-size: 0.9rem;
                color: #666;
                font-weight: 500;
              }
              
              .comparison-bar {
                flex: 1;
                height: 30px;
                background: #e0e0e0;
                border-radius: 15px;
                position: relative;
                margin: 0 1rem;
                overflow: hidden;
                
                .bar {
                  height: 100%;
                  border-radius: 15px;
                  transition: width 0.5s ease;
                  
                  &.traditional {
                    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                  }
                  
                  &.flyweight {
                    background: linear-gradient(135deg, #52c41a, #389e0d);
                  }
                }
                
                .bar-value {
                  position: absolute;
                  right: 10px;
                  top: 50%;
                  transform: translateY(-50%);
                  font-size: 0.8rem;
                  color: #333;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
      
      .flyweight-pool {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .pool-container {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
          gap: 0.8rem;
          
          .flyweight-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 0.8rem;
            text-align: center;
            
            .flyweight-char {
              font-size: 1.2rem;
              font-weight: bold;
              color: #333;
              margin-bottom: 0.3rem;
              font-family: 'Courier New', monospace;
            }
            
            .flyweight-usage {
              font-size: 0.8rem;
              color: #666;
            }
          }
        }
      }
      
      .pattern-explanation {
        background: linear-gradient(135deg, #f0f8ff, #ffffff);
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e0f0ff;
        
        h5 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .explanation-content {
          .state-separation {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
            
            @media (max-width: 768px) {
              grid-template-columns: 1fr;
            }
            
            .intrinsic-state, .extrinsic-state {
              background: white;
              padding: 1rem;
              border-radius: 6px;
              border: 1px solid #e0e0e0;
              
              h6 {
                color: #333;
                margin: 0 0 0.5rem 0;
                font-size: 1rem;
              }
              
              p {
                color: #666;
                margin: 0 0 0.5rem 0;
                font-size: 0.9rem;
              }
              
              ul {
                list-style-type: disc;
                padding-left: 1rem;
                margin: 0;
                
                li {
                  color: #666;
                  font-size: 0.85rem;
                  margin-bottom: 0.2rem;
                }
              }
            }
            
            .intrinsic-state {
              border-left: 4px solid #52c41a;
            }
            
            .extrinsic-state {
              border-left: 4px solid #1890ff;
            }
          }
          
          .flyweight-benefit {
            background: #667eea;
            color: white;
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
            font-size: 0.9rem;
          }
        }
      }
    }
  }

  .advantages {
    h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 0.3rem;
        padding-left: 1rem;
        color: #666;
      }
    }
  }
}
</style>
