<template>
  <div class="template-method-demo">
    <div class="pattern-info">
      <p class="description">
        模板方法模式定义一个操作中算法的骨架，而将一些步骤延迟到子类中。模板方法使得子类可以不改变算法的结构即可重定义算法的某些特定步骤。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>数据处理流程</li>
          <li>测试框架</li>
          <li>生命周期管理</li>
          <li>报表生成系统</li>
          <li>编译器设计</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 数据处理流程：</h4>
      <div class="demo-controls">
        <div class="processor-selection">
          <h6>选择数据处理器</h6>
          <div class="processor-buttons">
            <button 
              @click="selectProcessor('user')" 
              :class="{ active: selectedProcessor === 'user' }"
              class="processor-btn user"
            >
              👤 用户数据处理器
            </button>
            <button 
              @click="selectProcessor('order')" 
              :class="{ active: selectedProcessor === 'order' }"
              class="processor-btn order"
            >
              📦 订单数据处理器
            </button>
            <button 
              @click="selectProcessor('log')" 
              :class="{ active: selectedProcessor === 'log' }"
              class="processor-btn log"
            >
              📋 日志数据处理器
            </button>
          </div>
        </div>

        <div class="data-input">
          <h6>输入测试数据</h6>
          <div class="input-controls">
            <textarea 
              v-model="inputData" 
              placeholder="输入JSON格式的测试数据..."
              class="data-textarea"
              rows="6"
            ></textarea>
            <div class="action-buttons">
              <button @click="processData" class="demo-btn process">处理数据</button>
              <button @click="loadSampleData" class="demo-btn sample">加载示例数据</button>
              <button @click="clearData" class="demo-btn clear">清空</button>
            </div>
          </div>
        </div>
      </div>

      <div class="demo-output">
        <div class="processing-flow" v-if="processingSteps.length > 0">
          <h5>处理流程</h5>
          <div class="flow-container">
            <div 
              v-for="(step, index) in processingSteps" 
              :key="index"
              class="flow-step"
              :class="step.status"
            >
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <div class="step-title">{{ step.title }}</div>
                <div class="step-description">{{ step.description }}</div>
                <div v-if="step.details" class="step-details">{{ step.details }}</div>
              </div>
              <div class="step-status">
                <span v-if="step.status === 'success'" class="status-icon">✅</span>
                <span v-else-if="step.status === 'error'" class="status-icon">❌</span>
                <span v-else class="status-icon">⏳</span>
              </div>
            </div>
          </div>
        </div>

        <div class="processing-result" v-if="processResult">
          <h5>处理结果</h5>
          <div class="result-summary">
            <div class="summary-stats">
              <div class="stat-item">
                <span class="stat-label">处理器类型:</span>
                <span class="stat-value">{{ processResult.processorType }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">原始数据量:</span>
                <span class="stat-value">{{ processResult.originalCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">处理后数量:</span>
                <span class="stat-value">{{ processResult.processedCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">处理时间:</span>
                <span class="stat-value">{{ processResult.processingTime }}ms</span>
              </div>
              <div class="stat-item" v-if="processResult.errors.length > 0">
                <span class="stat-label">错误数量:</span>
                <span class="stat-value error">{{ processResult.errors.length }}</span>
              </div>
            </div>
          </div>

          <div v-if="processResult.errors.length > 0" class="error-section">
            <h6>处理错误</h6>
            <div class="error-list">
              <div v-for="(error, index) in processResult.errors" :key="index" class="error-item">
                {{ error }}
              </div>
            </div>
          </div>

          <div v-if="processResult.processedData.length > 0" class="processed-data">
            <h6>处理后的数据 (前5条)</h6>
            <div class="data-preview">
              <pre>{{ JSON.stringify(processResult.processedData.slice(0, 5), null, 2) }}</pre>
            </div>
          </div>
        </div>

        <div class="template-explanation">
          <h5>模板方法模式说明</h5>
          <div class="explanation-content">
            <div class="algorithm-skeleton">
              <h6>🏗️ 算法骨架 (模板方法)</h6>
              <div class="skeleton-steps">
                <div class="skeleton-step">1. 验证数据 (validateData)</div>
                <div class="skeleton-step">2. 预处理数据 (preprocessData)</div>
                <div class="skeleton-step abstract">3. 核心处理 (processCore) - 抽象方法</div>
                <div class="skeleton-step">4. 后处理数据 (postprocessData)</div>
                <div class="skeleton-step hook">5. 保存结果 (saveResult) - 钩子方法</div>
              </div>
            </div>

            <div class="processor-implementations">
              <h6>🔧 具体实现</h6>
              <div class="implementations-grid">
                <div class="implementation user">
                  <h7>用户数据处理器</h7>
                  <ul>
                    <li>验证: 检查必要字段 (id, name)</li>
                    <li>预处理: 去重处理</li>
                    <li>核心处理: 格式化用户信息</li>
                    <li>后处理: 默认处理</li>
                    <li>保存: 保存到数据库</li>
                  </ul>
                </div>
                <div class="implementation order">
                  <h7>订单数据处理器</h7>
                  <ul>
                    <li>验证: 检查订单字段</li>
                    <li>预处理: 默认处理</li>
                    <li>核心处理: 计算税费和总额</li>
                    <li>后处理: 按金额排序</li>
                    <li>保存: 保存订单数据</li>
                  </ul>
                </div>
                <div class="implementation log">
                  <h7>日志数据处理器</h7>
                  <ul>
                    <li>验证: 检查日志格式</li>
                    <li>预处理: 默认处理</li>
                    <li>核心处理: 按级别过滤</li>
                    <li>后处理: 按时间排序</li>
                    <li>保存: 不保存 (钩子返回false)</li>
                  </ul>
                </div>
              </div>
            </div>
            
            <div class="pattern-benefit">
              <strong>核心优势:</strong> 算法结构固定在父类中，具体步骤由子类实现，既保证了算法的一致性，又允许灵活的定制化。
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 封装不变部分，扩展可变部分</li>
        <li>✅ 提取公共代码，便于维护</li>
        <li>✅ 行为由父类控制，子类实现</li>
        <li>✅ 符合开闭原则</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 每一个不同的实现都需要一个子类</li>
        <li>❌ 会增加类的数量</li>
        <li>❌ 间接地增加了系统复杂度</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { DataProcessorFactory, type ProcessResult } from "../../patterns/TemplateMethod";

// 响应式数据
const selectedProcessor = ref<'user' | 'order' | 'log'>('user');
const inputData = ref<string>('');
const processResult = ref<ProcessResult | null>(null);
const processingSteps = ref<Array<{
  title: string;
  description: string;
  details?: string;
  status: 'pending' | 'success' | 'error';
}>>([]);

// 示例数据
const sampleData = {
  user: [
    { id: '1', name: 'Alice Johnson', email: '<EMAIL>', age: '28' },
    { id: '2', name: '  Bob Smith  ', email: '<EMAIL>', age: '35' },
    { id: '3', name: 'Charlie Brown', email: '<EMAIL>', age: 'invalid' },
    { id: '1', name: 'Alice Johnson', email: '<EMAIL>', age: '28' }, // 重复
    { id: '4', name: 'Diana Prince', email: '<EMAIL>', age: '30' }
  ],
  order: [
    { id: 'ORD001', customerId: 'CUST001', amount: '150.00' },
    { id: 'ORD002', customerId: 'CUST002', amount: '75.50' },
    { id: 'ORD003', customerId: 'CUST001', amount: '200.00' },
    { id: 'ORD004', customerId: 'CUST003', amount: '99.99' },
    { id: 'ORD005', customerId: 'CUST002', amount: '300.00' }
  ],
  log: [
    { timestamp: '2024-01-15T10:30:00Z', level: 'INFO', message: '应用启动成功', source: 'app' },
    { timestamp: '2024-01-15T10:31:00Z', level: 'DEBUG', message: '数据库连接建立', source: 'db' },
    { timestamp: '2024-01-15T10:32:00Z', level: 'WARN', message: '内存使用率较高', source: 'monitor' },
    { timestamp: '2024-01-15T10:33:00Z', level: 'ERROR', message: '用户认证失败', source: 'auth' },
    { timestamp: '2024-01-15T10:34:00Z', level: 'INFO', message: '定时任务执行', source: 'scheduler' }
  ]
};

// 方法
const selectProcessor = (type: 'user' | 'order' | 'log') => {
  selectedProcessor.value = type;
  processResult.value = null;
  processingSteps.value = [];
};

const loadSampleData = () => {
  inputData.value = JSON.stringify(sampleData[selectedProcessor.value], null, 2);
};

const clearData = () => {
  inputData.value = '';
  processResult.value = null;
  processingSteps.value = [];
};

const processData = async () => {
  if (!inputData.value.trim()) {
    alert('请输入测试数据');
    return;
  }

  try {
    const data = JSON.parse(inputData.value);
    if (!Array.isArray(data)) {
      throw new Error('数据必须是数组格式');
    }

    // 重置状态
    processingSteps.value = [];
    processResult.value = null;

    // 模拟处理步骤
    await simulateProcessingSteps();

    // 创建处理器并处理数据
    const options = selectedProcessor.value === 'order' ? { taxRate: 0.1 } : 
                   selectedProcessor.value === 'log' ? { logLevel: 'INFO' } : undefined;
    
    const processor = DataProcessorFactory.createProcessor(selectedProcessor.value, options);
    const result = processor.processData(data);
    
    processResult.value = result;

    // 更新最后一步状态
    if (processingSteps.value.length > 0) {
      processingSteps.value[processingSteps.value.length - 1].status = 'success';
    }

  } catch (error) {
    console.error('处理数据时出错:', error);
    alert(`处理数据时出错: ${error instanceof Error ? error.message : String(error)}`);
    
    // 标记错误状态
    if (processingSteps.value.length > 0) {
      processingSteps.value[processingSteps.value.length - 1].status = 'error';
    }
  }
};

const simulateProcessingSteps = async () => {
  const steps = [
    { title: '数据验证', description: '检查数据格式和必要字段', delay: 300 },
    { title: '数据预处理', description: '清理和标准化数据', delay: 500 },
    { title: '核心处理', description: '执行特定的业务逻辑', delay: 800 },
    { title: '数据后处理', description: '排序和格式化结果', delay: 400 },
    { title: '保存结果', description: '持久化处理结果', delay: 300 }
  ];

  for (let i = 0; i < steps.length; i++) {
    const step = steps[i];
    processingSteps.value.push({
      title: step.title,
      description: step.description,
      status: 'pending'
    });

    await new Promise(resolve => setTimeout(resolve, step.delay));
    
    if (i < steps.length - 1) {
      processingSteps.value[i].status = 'success';
    }
  }
};
</script>

<style lang="scss" scoped>
.template-method-demo {
  .pattern-info {
    margin-bottom: 2rem;
    
    .description {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }
    
    .use-cases {
      h4 {
        color: #333;
        margin-bottom: 0.5rem;
      }
      
      ul {
        list-style-type: disc;
        padding-left: 1.5rem;
        
        li {
          margin-bottom: 0.3rem;
          color: #666;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
    }
    
    .demo-controls {
      display: grid;
      grid-template-columns: 300px 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
      
      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
      }
      
      .processor-selection {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        
        h6 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .processor-buttons {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          
          .processor-btn {
            padding: 0.8rem;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: left;
            
            &:hover {
              border-color: #667eea;
            }
            
            &.active {
              border-color: #667eea;
              background: #667eea;
              color: white;
            }
            
            &.user.active { background: #52c41a; border-color: #52c41a; }
            &.order.active { background: #faad14; border-color: #faad14; }
            &.log.active { background: #722ed1; border-color: #722ed1; }
          }
        }
      }
      
      .data-input {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        
        h6 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .input-controls {
          .data-textarea {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            resize: vertical;
            margin-bottom: 1rem;
          }
          
          .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            
            .demo-btn {
              padding: 0.6rem 1.2rem;
              border: none;
              border-radius: 6px;
              cursor: pointer;
              font-weight: 500;
              transition: all 0.3s ease;
              
              &.process {
                background: linear-gradient(135deg, #52c41a, #389e0d);
                color: white;
              }
              
              &.sample {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
              }
              
              &.clear {
                background: #ff6b6b;
                color: white;
              }
              
              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
              }
            }
          }
        }
      }
    }
    
    .demo-output {
      .processing-flow {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .flow-container {
          .flow-step {
            display: flex;
            align-items: center;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            transition: all 0.3s ease;
            
            &.success {
              border-left: 4px solid #52c41a;
              background: #f6ffed;
            }
            
            &.error {
              border-left: 4px solid #ff4d4f;
              background: #fff2f0;
            }
            
            &.pending {
              border-left: 4px solid #1890ff;
              background: #e6f7ff;
            }
            
            .step-number {
              width: 30px;
              height: 30px;
              background: #667eea;
              color: white;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
              margin-right: 1rem;
              flex-shrink: 0;
            }
            
            .step-content {
              flex: 1;
              
              .step-title {
                font-weight: 600;
                color: #333;
                margin-bottom: 0.3rem;
              }
              
              .step-description {
                color: #666;
                font-size: 0.9rem;
                margin-bottom: 0.3rem;
              }
              
              .step-details {
                color: #999;
                font-size: 0.8rem;
              }
            }
            
            .step-status {
              .status-icon {
                font-size: 1.2rem;
              }
            }
          }
        }
      }
      
      .processing-result {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .result-summary {
          background: white;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 1rem;
          margin-bottom: 1rem;
          
          .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            
            .stat-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              
              .stat-label {
                color: #666;
                font-size: 0.9rem;
              }
              
              .stat-value {
                color: #333;
                font-weight: 600;
                
                &.error {
                  color: #ff4d4f;
                }
              }
            }
          }
        }
        
        .error-section {
          background: #fff2f0;
          border: 1px solid #ffccc7;
          border-radius: 8px;
          padding: 1rem;
          margin-bottom: 1rem;
          
          h6 {
            color: #ff4d4f;
            margin: 0 0 0.5rem 0;
          }
          
          .error-list {
            .error-item {
              color: #a8071a;
              font-size: 0.9rem;
              margin-bottom: 0.3rem;
            }
          }
        }
        
        .processed-data {
          background: #f6ffed;
          border: 1px solid #b7eb8f;
          border-radius: 8px;
          padding: 1rem;
          
          h6 {
            color: #52c41a;
            margin: 0 0 0.5rem 0;
          }
          
          .data-preview {
            pre {
              background: white;
              padding: 1rem;
              border-radius: 4px;
              font-size: 0.8rem;
              overflow-x: auto;
              margin: 0;
            }
          }
        }
      }
      
      .template-explanation {
        background: linear-gradient(135deg, #f0f8ff, #ffffff);
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e0f0ff;
        
        h5 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .explanation-content {
          .algorithm-skeleton {
            margin-bottom: 1.5rem;
            
            h6 {
              color: #333;
              margin: 0 0 1rem 0;
            }
            
            .skeleton-steps {
              .skeleton-step {
                background: white;
                padding: 0.8rem;
                margin-bottom: 0.5rem;
                border-radius: 6px;
                border-left: 4px solid #e0e0e0;
                
                &.abstract {
                  border-left-color: #ff6b6b;
                  background: #fff2f0;
                  font-weight: 600;
                }
                
                &.hook {
                  border-left-color: #faad14;
                  background: #fffbe6;
                  font-style: italic;
                }
              }
            }
          }
          
          .processor-implementations {
            margin-bottom: 1rem;
            
            h6 {
              color: #333;
              margin: 0 0 1rem 0;
            }
            
            .implementations-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
              gap: 1rem;
              
              .implementation {
                background: white;
                padding: 1rem;
                border-radius: 6px;
                border: 1px solid #e0e0e0;
                
                &.user { border-left: 4px solid #52c41a; }
                &.order { border-left: 4px solid #faad14; }
                &.log { border-left: 4px solid #722ed1; }
                
                h7 {
                  font-weight: 600;
                  color: #333;
                  display: block;
                  margin-bottom: 0.5rem;
                }
                
                ul {
                  list-style-type: disc;
                  padding-left: 1rem;
                  margin: 0;
                  
                  li {
                    color: #666;
                    font-size: 0.85rem;
                    margin-bottom: 0.2rem;
                  }
                }
              }
            }
          }
          
          .pattern-benefit {
            background: #667eea;
            color: white;
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
            font-size: 0.9rem;
          }
        }
      }
    }
  }

  .advantages {
    h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 0.3rem;
        padding-left: 1rem;
        color: #666;
      }
    }
  }
}
</style>
