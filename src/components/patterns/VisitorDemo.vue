<template>
  <div class="visitor-demo">
    <div class="pattern-info">
      <p class="description">
        访问者模式表示一个作用于某对象结构中的各元素的操作。它使你可以在不改变各元素的类的前提下定义作用于这些元素的新操作。
      </p>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 文档处理系统：</h4>
      <div class="demo-controls">
        <div class="document-builder">
          <h6>构建文档</h6>
          <div class="element-buttons">
            <button @click="addTextElement" class="demo-btn text">添加文本</button>
            <button @click="addImageElement" class="demo-btn image">添加图片</button>
            <button @click="addTableElement" class="demo-btn table">添加表格</button>
            <button @click="addListElement" class="demo-btn list">添加列表</button>
          </div>
          <button @click="clearDocument" class="demo-btn clear">清空文档</button>
        </div>

        <div class="visitor-actions">
          <h6>访问者操作</h6>
          <div class="action-buttons">
            <button @click="exportToHTML" class="demo-btn export">导出HTML</button>
            <button @click="exportToMarkdown" class="demo-btn export">导出Markdown</button>
            <button @click="getStatistics" class="demo-btn stats">获取统计</button>
          </div>
        </div>
      </div>

      <div class="demo-output">
        <div class="document-structure">
          <h5>文档结构 ({{ elements.length }}个元素)</h5>
          <div class="elements-list">
            <div v-for="(element, index) in elements" :key="index" class="element-item" :class="element.type">
              <div class="element-info">
                <span class="element-type">{{ getElementTypeName(element.type) }}</span>
                <span class="element-id">ID: {{ element.id }}</span>
              </div>
              <button @click="removeElement(index)" class="remove-btn">×</button>
            </div>
          </div>
        </div>

        <div class="output-display">
          <h5>输出结果</h5>
          <div v-if="outputResult" class="output-content">
            <div class="output-type">{{ outputType }}</div>
            <pre v-if="outputType !== '统计信息'" class="output-text">{{ outputResult }}</pre>
            <div v-else class="stats-display">
              <div v-for="(value, key) in outputResult" :key="key" class="stat-item">
                <span class="stat-label">{{ getStatLabel(key) }}:</span>
                <span class="stat-value">{{ value }}</span>
              </div>
            </div>
          </div>
          <div v-else class="no-output">
            请选择一个访问者操作来查看结果
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 符合单一职责原则</li>
        <li>✅ 优秀的扩展性</li>
        <li>✅ 灵活性</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 具体元素对访问者公布细节，违反了迪米特法则</li>
        <li>❌ 具体元素变更比较困难</li>
        <li>❌ 违反了依赖倒置原则</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { 
  Document, 
  TextElement, 
  ImageElement, 
  TableElement, 
  ListElement,
  DocumentProcessor 
} from "../../patterns/Visitor";

// 响应式数据
const elements = ref<Array<{ type: string; id: string }>>([]);
const outputResult = ref<any>(null);
const outputType = ref<string>('');

// 创建文档实例
const document = new Document('演示文档');
const processor = new DocumentProcessor(document);

// 方法
const addTextElement = () => {
  const id = `text-${Date.now()}`;
  const element = new TextElement(id, '这是一段示例文本内容。', 14, '#000000');
  document.addElement(element);
  elements.value.push({ type: 'text', id });
};

const addImageElement = () => {
  const id = `image-${Date.now()}`;
  const element = new ImageElement(id, '/placeholder-image.jpg', 300, 200, '示例图片');
  document.addElement(element);
  elements.value.push({ type: 'image', id });
};

const addTableElement = () => {
  const id = `table-${Date.now()}`;
  const headers = ['姓名', '年龄', '职业'];
  const rows = [
    ['张三', '25', '工程师'],
    ['李四', '30', '设计师'],
    ['王五', '28', '产品经理']
  ];
  const element = new TableElement(id, headers, rows);
  document.addElement(element);
  elements.value.push({ type: 'table', id });
};

const addListElement = () => {
  const id = `list-${Date.now()}`;
  const items = ['第一项内容', '第二项内容', '第三项内容'];
  const element = new ListElement(id, items, false);
  document.addElement(element);
  elements.value.push({ type: 'list', id });
};

const removeElement = (index: number) => {
  const element = elements.value[index];
  document.removeElement(element.id);
  elements.value.splice(index, 1);
};

const clearDocument = () => {
  // 重新创建文档
  const newDocument = new Document('演示文档');
  processor.setDocument(newDocument);
  elements.value = [];
  outputResult.value = null;
  outputType.value = '';
};

const exportToHTML = () => {
  outputResult.value = processor.exportToHTML();
  outputType.value = 'HTML';
};

const exportToMarkdown = () => {
  outputResult.value = processor.exportToMarkdown();
  outputType.value = 'Markdown';
};

const getStatistics = () => {
  outputResult.value = processor.getStatistics();
  outputType.value = '统计信息';
};

const getElementTypeName = (type: string): string => {
  const typeNames = {
    text: '文本',
    image: '图片',
    table: '表格',
    list: '列表'
  };
  return typeNames[type as keyof typeof typeNames] || type;
};

const getStatLabel = (key: string): string => {
  const labels = {
    textElements: '文本元素',
    imageElements: '图片元素',
    tableElements: '表格元素',
    listElements: '列表元素',
    totalWords: '总词数',
    totalCharacters: '总字符数',
    totalImages: '总图片数',
    totalTableRows: '总表格行数',
    totalListItems: '总列表项数'
  };
  return labels[key as keyof typeof labels] || key;
};
</script>

<style lang="scss" scoped>
.visitor-demo {
  .pattern-info {
    margin-bottom: 2rem;
    
    .description {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }
  }

  .demo-section {
    margin-bottom: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
    }
    
    .demo-controls {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
      
      .document-builder, .visitor-actions {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        
        h6 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .element-buttons, .action-buttons {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
          margin-bottom: 1rem;
          
          .demo-btn {
            padding: 0.6rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            
            &.text { background: #1890ff; color: white; }
            &.image { background: #52c41a; color: white; }
            &.table { background: #fa8c16; color: white; }
            &.list { background: #722ed1; color: white; }
            &.export { background: #667eea; color: white; }
            &.stats { background: #13c2c2; color: white; }
            &.clear { background: #ff6b6b; color: white; }
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
          }
        }
      }
    }
    
    .demo-output {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      
      .document-structure, .output-display {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        
        h5 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .elements-list {
          max-height: 300px;
          overflow-y: auto;
          
          .element-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            border-radius: 6px;
            
            &.text { background: #e6f7ff; border-left: 4px solid #1890ff; }
            &.image { background: #f6ffed; border-left: 4px solid #52c41a; }
            &.table { background: #fff7e6; border-left: 4px solid #fa8c16; }
            &.list { background: #f9f0ff; border-left: 4px solid #722ed1; }
            
            .element-info {
              display: flex;
              flex-direction: column;
              
              .element-type {
                font-weight: 500;
                color: #333;
              }
              
              .element-id {
                font-size: 0.8rem;
                color: #666;
              }
            }
            
            .remove-btn {
              background: #ff4d4f;
              color: white;
              border: none;
              border-radius: 50%;
              width: 24px;
              height: 24px;
              cursor: pointer;
              font-size: 0.9rem;
            }
          }
        }
        
        .output-content {
          .output-type {
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
            padding: 0.5rem;
            background: #f0f8ff;
            border-radius: 4px;
            text-align: center;
          }
          
          .output-text {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            font-size: 0.8rem;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
          }
          
          .stats-display {
            .stat-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0.5rem;
              margin-bottom: 0.5rem;
              background: #f8f9fa;
              border-radius: 4px;
              
              .stat-label {
                color: #666;
                font-size: 0.9rem;
              }
              
              .stat-value {
                color: #333;
                font-weight: 600;
              }
            }
          }
        }
        
        .no-output {
          text-align: center;
          color: #999;
          font-style: italic;
          padding: 2rem;
        }
      }
    }
  }

  .advantages {
    h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 0.3rem;
        padding-left: 1rem;
        color: #666;
      }
    }
  }
}
</style>
