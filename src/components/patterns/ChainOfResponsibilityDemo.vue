<template>
  <div class="chain-demo">
    <div class="pattern-info">
      <p class="description">
        责任链模式沿着处理者链传递请求，直到某个处理者处理它为止。这种模式将请求的发送者和接收者解耦，让多个对象都有机会处理请求。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>请假审批系统</li>
          <li>异常处理机制</li>
          <li>事件处理系统</li>
          <li>中间件处理</li>
          <li>权限验证链</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 请假审批系统：</h4>
      <div class="demo-controls">
        <div class="request-form">
          <div class="form-row">
            <label>员工姓名:</label>
            <input v-model="requestForm.employeeName" type="text" class="form-input" />
          </div>
          <div class="form-row">
            <label>请假天数:</label>
            <input v-model.number="requestForm.days" type="number" min="1" max="30" class="form-input" />
          </div>
          <div class="form-row">
            <label>请假类型:</label>
            <select v-model="requestForm.type" class="form-select">
              <option value="sick">病假</option>
              <option value="personal">事假</option>
              <option value="annual">年假</option>
              <option value="emergency">紧急假</option>
            </select>
          </div>
          <div class="form-row">
            <label>请假原因:</label>
            <textarea v-model="requestForm.reason" class="form-textarea" rows="2"></textarea>
          </div>
          <button @click="submitRequest" class="demo-btn submit">提交申请</button>
        </div>
        
        <div class="quick-actions">
          <h6>快速测试:</h6>
          <button @click="addSampleRequest('short')" class="demo-btn sample">短期请假 (2天)</button>
          <button @click="addSampleRequest('medium')" class="demo-btn sample">中期请假 (7天)</button>
          <button @click="addSampleRequest('long')" class="demo-btn sample">长期请假 (15天)</button>
          <button @click="addSampleRequest('extended')" class="demo-btn sample">超长请假 (25天)</button>
          <button @click="clearRequests" class="demo-btn clear">清空记录</button>
        </div>
      </div>

      <div class="demo-output">
        <div class="approval-chain">
          <h5>审批链结构</h5>
          <div class="chain-visualization">
            <div 
              v-for="(handler, index) in approvalHandlers" 
              :key="index"
              class="handler-node"
              :class="{ active: currentHandler === index }"
            >
              <div class="handler-info">
                <div class="handler-title">{{ handler.name }}</div>
                <div class="handler-authority">{{ handler.authority }}</div>
              </div>
              <div v-if="index < approvalHandlers.length - 1" class="chain-arrow">→</div>
            </div>
          </div>
        </div>

        <div class="request-processing" v-if="currentRequest">
          <h5>当前处理请求</h5>
          <div class="current-request-card">
            <div class="request-details">
              <h6>{{ currentRequest.employeeName }} 的请假申请</h6>
              <p><strong>天数:</strong> {{ currentRequest.days }}天</p>
              <p><strong>类型:</strong> {{ getTypeLabel(currentRequest.type) }}</p>
              <p><strong>原因:</strong> {{ currentRequest.reason }}</p>
            </div>
            <div class="processing-status">
              <div class="status-indicator" :class="processingStatus.toLowerCase()">
                {{ processingStatus }}
              </div>
            </div>
          </div>
        </div>

        <div class="approval-history">
          <h5>审批历史</h5>
          <div class="history-container">
            <div 
              v-for="(record, index) in approvalHistory" 
              :key="index"
              class="history-item"
              :class="record.result.toLowerCase()"
            >
              <div class="history-header">
                <span class="employee-name">{{ record.request.employeeName }}</span>
                <span class="request-summary">{{ record.request.days }}天{{ getTypeLabel(record.request.type) }}</span>
                <span class="result-badge" :class="record.result.toLowerCase()">
                  {{ record.result }}
                </span>
              </div>
              <div class="history-details">
                <div class="request-info">
                  <strong>原因:</strong> {{ record.request.reason }}
                </div>
                <div class="approval-info">
                  <strong>处理者:</strong> {{ record.handler }}
                </div>
                <div class="approval-message">
                  {{ record.message }}
                </div>
              </div>
              <div class="history-time">
                {{ record.timestamp }}
              </div>
            </div>
          </div>
        </div>

        <div class="chain-explanation">
          <h5>责任链模式说明</h5>
          <div class="explanation-content">
            <div class="chain-flow">
              <h6>处理流程</h6>
              <div class="flow-steps">
                <div class="flow-step">
                  <div class="step-number">1</div>
                  <div class="step-content">
                    <strong>请求提交</strong>
                    <p>员工提交请假申请</p>
                  </div>
                </div>
                <div class="flow-arrow">↓</div>
                <div class="flow-step">
                  <div class="step-number">2</div>
                  <div class="step-content">
                    <strong>链式处理</strong>
                    <p>沿着处理者链传递请求</p>
                  </div>
                </div>
                <div class="flow-arrow">↓</div>
                <div class="flow-step">
                  <div class="step-number">3</div>
                  <div class="step-content">
                    <strong>权限判断</strong>
                    <p>每个处理者检查是否有权限处理</p>
                  </div>
                </div>
                <div class="flow-arrow">↓</div>
                <div class="flow-step">
                  <div class="step-number">4</div>
                  <div class="step-content">
                    <strong>处理或传递</strong>
                    <p>处理请求或传递给下一个处理者</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="authority-levels">
              <h6>权限等级</h6>
              <div class="authority-grid">
                <div class="authority-item supervisor">
                  <div class="authority-title">直接主管</div>
                  <div class="authority-scope">≤ 3天 (病假/事假)</div>
                </div>
                <div class="authority-item manager">
                  <div class="authority-title">部门经理</div>
                  <div class="authority-scope">≤ 7天 (所有类型)</div>
                </div>
                <div class="authority-item director">
                  <div class="authority-title">人事总监</div>
                  <div class="authority-scope">≤ 15天 (所有类型)</div>
                </div>
                <div class="authority-item general">
                  <div class="authority-title">总经理</div>
                  <div class="authority-scope">≤ 30天 (所有类型)</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="pattern-benefit">
            <strong>核心优势:</strong> 请求发送者无需知道具体哪个处理者会处理请求，处理者也无需知道链的结构，实现了发送者和接收者的解耦。
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 降低了请求发送者和接收者的耦合度</li>
        <li>✅ 简化了对象，无需知道链的结构</li>
        <li>✅ 增强了给对象指派职责的灵活性</li>
        <li>✅ 增加新的请求处理类很方便</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 不能保证请求一定被接收</li>
        <li>❌ 系统性能受到一定影响</li>
        <li>❌ 可能不容易观察运行时的特征</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";

// 请假请求类
class LeaveRequest {
  constructor(
    public employeeName: string,
    public days: number,
    public reason: string,
    public type: 'sick' | 'personal' | 'annual' | 'emergency'
  ) {}

  getInfo(): string {
    return `员工: ${this.employeeName}, 请假天数: ${this.days}天, 类型: ${this.type}, 原因: ${this.reason}`;
  }
}

// 抽象处理者
abstract class ApprovalHandler {
  protected nextHandler: ApprovalHandler | null = null;

  setNext(handler: ApprovalHandler): ApprovalHandler {
    this.nextHandler = handler;
    return handler;
  }

  handle(request: LeaveRequest): { result: string; handler: string; message: string } {
    if (this.canHandle(request)) {
      return this.processRequest(request);
    } else if (this.nextHandler) {
      return this.nextHandler.handle(request);
    } else {
      return {
        result: '拒绝',
        handler: '系统',
        message: `请假申请被拒绝: ${request.getInfo()}`
      };
    }
  }

  protected abstract canHandle(request: LeaveRequest): boolean;
  protected abstract processRequest(request: LeaveRequest): { result: string; handler: string; message: string };
  protected abstract getHandlerName(): string;
}

// 具体处理者实现
class DirectSupervisor extends ApprovalHandler {
  protected canHandle(request: LeaveRequest): boolean {
    return request.days <= 3 && (request.type === 'sick' || request.type === 'personal');
  }

  protected processRequest(request: LeaveRequest): { result: string; handler: string; message: string } {
    return {
      result: '批准',
      handler: this.getHandlerName(),
      message: `${this.getHandlerName()}批准了${request.employeeName}的${request.days}天${request.type}假申请`
    };
  }

  protected getHandlerName(): string {
    return '直接主管';
  }
}

class DepartmentManager extends ApprovalHandler {
  protected canHandle(request: LeaveRequest): boolean {
    return request.days <= 7;
  }

  protected processRequest(request: LeaveRequest): { result: string; handler: string; message: string } {
    return {
      result: '批准',
      handler: this.getHandlerName(),
      message: `${this.getHandlerName()}批准了${request.employeeName}的${request.days}天${request.type}假申请`
    };
  }

  protected getHandlerName(): string {
    return '部门经理';
  }
}

class HRDirector extends ApprovalHandler {
  protected canHandle(request: LeaveRequest): boolean {
    return request.days <= 15;
  }

  protected processRequest(request: LeaveRequest): { result: string; handler: string; message: string } {
    return {
      result: '批准',
      handler: this.getHandlerName(),
      message: `${this.getHandlerName()}批准了${request.employeeName}的${request.days}天${request.type}假申请`
    };
  }

  protected getHandlerName(): string {
    return '人事总监';
  }
}

class GeneralManager extends ApprovalHandler {
  protected canHandle(request: LeaveRequest): boolean {
    return request.days <= 30;
  }

  protected processRequest(request: LeaveRequest): { result: string; handler: string; message: string } {
    const message = request.days > 15 
      ? `${this.getHandlerName()}批准了${request.employeeName}的${request.days}天${request.type}假申请（需要特别关注）`
      : `${this.getHandlerName()}批准了${request.employeeName}的${request.days}天${request.type}假申请`;
    
    return {
      result: '批准',
      handler: this.getHandlerName(),
      message
    };
  }

  protected getHandlerName(): string {
    return '总经理';
  }
}

// 审批链构建器
class ApprovalChainBuilder {
  static buildChain(): ApprovalHandler {
    const supervisor = new DirectSupervisor();
    const manager = new DepartmentManager();
    const hrDirector = new HRDirector();
    const generalManager = new GeneralManager();

    supervisor.setNext(manager).setNext(hrDirector).setNext(generalManager);
    return supervisor;
  }
}

// 响应式数据
const requestForm = reactive({
  employeeName: '',
  days: 1,
  type: 'sick' as 'sick' | 'personal' | 'annual' | 'emergency',
  reason: ''
});

const currentRequest = ref<LeaveRequest | null>(null);
const processingStatus = ref<string>('待处理');
const currentHandler = ref<number>(-1);

const approvalHistory = ref<Array<{
  request: LeaveRequest;
  result: string;
  handler: string;
  message: string;
  timestamp: string;
}>>([]);

const approvalHandlers = [
  { name: '直接主管', authority: '≤3天 (病假/事假)' },
  { name: '部门经理', authority: '≤7天 (所有类型)' },
  { name: '人事总监', authority: '≤15天 (所有类型)' },
  { name: '总经理', authority: '≤30天 (所有类型)' }
];

// 方法
const submitRequest = () => {
  if (!requestForm.employeeName.trim() || !requestForm.reason.trim()) {
    alert('请填写完整信息');
    return;
  }

  const request = new LeaveRequest(
    requestForm.employeeName,
    requestForm.days,
    requestForm.reason,
    requestForm.type
  );

  processRequest(request);
  
  // 重置表单
  requestForm.employeeName = '';
  requestForm.days = 1;
  requestForm.reason = '';
};

const processRequest = async (request: LeaveRequest) => {
  currentRequest.value = request;
  processingStatus.value = '处理中';
  
  // 模拟处理过程
  const approvalChain = ApprovalChainBuilder.buildChain();
  
  // 模拟链式处理的可视化效果
  for (let i = 0; i < approvalHandlers.length; i++) {
    currentHandler.value = i;
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // 检查当前处理者是否能处理
    if (canHandlerProcess(request, i)) {
      break;
    }
  }
  
  const result = approvalChain.handle(request);
  
  // 添加到历史记录
  approvalHistory.value.unshift({
    request: { ...request },
    result: result.result,
    handler: result.handler,
    message: result.message,
    timestamp: new Date().toLocaleString()
  });
  
  processingStatus.value = result.result;
  
  // 重置状态
  setTimeout(() => {
    currentRequest.value = null;
    currentHandler.value = -1;
    processingStatus.value = '待处理';
  }, 2000);
};

const canHandlerProcess = (request: LeaveRequest, handlerIndex: number): boolean => {
  switch (handlerIndex) {
    case 0: // 直接主管
      return request.days <= 3 && (request.type === 'sick' || request.type === 'personal');
    case 1: // 部门经理
      return request.days <= 7;
    case 2: // 人事总监
      return request.days <= 15;
    case 3: // 总经理
      return request.days <= 30;
    default:
      return false;
  }
};

const addSampleRequest = (type: string) => {
  const samples = {
    short: { name: '张三', days: 2, type: 'sick' as const, reason: '感冒发烧需要休息' },
    medium: { name: '李四', days: 7, type: 'annual' as const, reason: '年假旅游' },
    long: { name: '王五', days: 15, type: 'emergency' as const, reason: '家人住院需要照顾' },
    extended: { name: '赵六', days: 25, type: 'personal' as const, reason: '个人事务处理' }
  };
  
  const sample = samples[type as keyof typeof samples];
  if (sample) {
    const request = new LeaveRequest(sample.name, sample.days, sample.reason, sample.type);
    processRequest(request);
  }
};

const clearRequests = () => {
  approvalHistory.value = [];
  currentRequest.value = null;
  currentHandler.value = -1;
  processingStatus.value = '待处理';
};

const getTypeLabel = (type: string): string => {
  const labels = {
    sick: '病假',
    personal: '事假',
    annual: '年假',
    emergency: '紧急假'
  };
  return labels[type as keyof typeof labels] || type;
};
</script>

<style lang="scss" scoped>
.chain-demo {
  .pattern-info {
    margin-bottom: 2rem;
    
    .description {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }
    
    .use-cases {
      h4 {
        color: #333;
        margin-bottom: 0.5rem;
      }
      
      ul {
        list-style-type: disc;
        padding-left: 1.5rem;
        
        li {
          margin-bottom: 0.3rem;
          color: #666;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
    }
    
    .demo-controls {
      display: grid;
      grid-template-columns: 1fr 300px;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
      
      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
      }
      
      .request-form {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        
        .form-row {
          display: flex;
          align-items: center;
          margin-bottom: 1rem;
          
          label {
            min-width: 80px;
            font-weight: 500;
            color: #555;
            margin-right: 0.5rem;
          }
          
          .form-input, .form-select {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
          }
          
          .form-textarea {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
            resize: vertical;
          }
        }
        
        .demo-btn.submit {
          width: 100%;
          padding: 0.8rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #52c41a, #389e0d);
          color: white;
          cursor: pointer;
          font-weight: 500;
          font-size: 1rem;
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
          }
        }
      }
      
      .quick-actions {
        background: #f0f0f0;
        padding: 1rem;
        border-radius: 8px;
        
        h6 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .demo-btn {
          width: 100%;
          margin-bottom: 0.5rem;
          padding: 0.6rem;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.9rem;
          transition: all 0.2s ease;
          
          &.sample {
            background: #667eea;
            color: white;
            
            &:hover {
              background: #5a67d8;
            }
          }
          
          &.clear {
            background: #ff6b6b;
            color: white;
            
            &:hover {
              background: #ff5252;
            }
          }
        }
      }
    }
    
    .demo-output {
      .approval-chain {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .chain-visualization {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
          gap: 1rem;
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 8px;
          
          .handler-node {
            display: flex;
            align-items: center;
            
            &.active .handler-info {
              background: linear-gradient(135deg, #667eea, #764ba2);
              color: white;
              transform: scale(1.05);
            }
            
            .handler-info {
              background: white;
              border: 2px solid #e0e0e0;
              border-radius: 8px;
              padding: 1rem;
              text-align: center;
              transition: all 0.3s ease;
              min-width: 120px;
              
              .handler-title {
                font-weight: 600;
                margin-bottom: 0.3rem;
              }
              
              .handler-authority {
                font-size: 0.8rem;
                opacity: 0.8;
              }
            }
            
            .chain-arrow {
              font-size: 1.5rem;
              color: #667eea;
              margin: 0 0.5rem;
              font-weight: bold;
            }
          }
        }
      }
      
      .request-processing {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .current-request-card {
          background: white;
          border: 2px solid #e0e0e0;
          border-radius: 8px;
          padding: 1.5rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .request-details {
            flex: 1;
            
            h6 {
              color: #333;
              margin: 0 0 0.5rem 0;
              font-size: 1.1rem;
            }
            
            p {
              margin: 0.3rem 0;
              color: #666;
              font-size: 0.9rem;
            }
          }
          
          .processing-status {
            .status-indicator {
              padding: 0.5rem 1rem;
              border-radius: 20px;
              font-weight: 500;
              text-align: center;
              
              &.待处理 {
                background: #f0f0f0;
                color: #666;
              }
              
              &.处理中 {
                background: #1890ff;
                color: white;
                animation: pulse 1.5s infinite;
              }
              
              &.批准 {
                background: #52c41a;
                color: white;
              }
              
              &.拒绝 {
                background: #ff4d4f;
                color: white;
              }
            }
          }
        }
      }
      
      .approval-history {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .history-container {
          max-height: 400px;
          overflow-y: auto;
          
          .history-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.8rem;
            
            &.批准 {
              border-left: 4px solid #52c41a;
            }
            
            &.拒绝 {
              border-left: 4px solid #ff4d4f;
            }
            
            .history-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 0.5rem;
              
              .employee-name {
                font-weight: 600;
                color: #333;
              }
              
              .request-summary {
                color: #666;
                font-size: 0.9rem;
              }
              
              .result-badge {
                padding: 0.2rem 0.6rem;
                border-radius: 12px;
                font-size: 0.8rem;
                font-weight: 500;
                
                &.批准 {
                  background: #52c41a;
                  color: white;
                }
                
                &.拒绝 {
                  background: #ff4d4f;
                  color: white;
                }
              }
            }
            
            .history-details {
              font-size: 0.9rem;
              color: #666;
              margin-bottom: 0.5rem;
              
              div {
                margin-bottom: 0.3rem;
              }
              
              .approval-message {
                color: #333;
                font-style: italic;
              }
            }
            
            .history-time {
              font-size: 0.8rem;
              color: #999;
              text-align: right;
            }
          }
        }
      }
      
      .chain-explanation {
        background: linear-gradient(135deg, #f0f8ff, #ffffff);
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e0f0ff;
        
        h5 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .explanation-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1.5rem;
          margin-bottom: 1rem;
          
          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }
          
          .chain-flow {
            h6 {
              color: #333;
              margin: 0 0 1rem 0;
            }
            
            .flow-steps {
              .flow-step {
                display: flex;
                align-items: center;
                margin-bottom: 0.5rem;
                
                .step-number {
                  width: 30px;
                  height: 30px;
                  background: #667eea;
                  color: white;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-weight: bold;
                  margin-right: 1rem;
                  flex-shrink: 0;
                }
                
                .step-content {
                  strong {
                    color: #333;
                    display: block;
                    margin-bottom: 0.2rem;
                  }
                  
                  p {
                    color: #666;
                    font-size: 0.9rem;
                    margin: 0;
                  }
                }
              }
              
              .flow-arrow {
                text-align: center;
                font-size: 1.2rem;
                color: #667eea;
                margin: 0.5rem 0;
              }
            }
          }
          
          .authority-levels {
            h6 {
              color: #333;
              margin: 0 0 1rem 0;
            }
            
            .authority-grid {
              .authority-item {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 0.8rem;
                margin-bottom: 0.5rem;
                
                &.supervisor { border-left: 4px solid #faad14; }
                &.manager { border-left: 4px solid #1890ff; }
                &.director { border-left: 4px solid #722ed1; }
                &.general { border-left: 4px solid #52c41a; }
                
                .authority-title {
                  font-weight: 600;
                  color: #333;
                  margin-bottom: 0.3rem;
                }
                
                .authority-scope {
                  font-size: 0.85rem;
                  color: #666;
                }
              }
            }
          }
        }
        
        .pattern-benefit {
          background: #667eea;
          color: white;
          padding: 1rem;
          border-radius: 6px;
          text-align: center;
          font-size: 0.9rem;
        }
      }
    }
  }

  .advantages {
    h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 0.3rem;
        padding-left: 1rem;
        color: #666;
      }
    }
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}
</style>
