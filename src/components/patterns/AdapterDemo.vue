<template>
  <div class="adapter-demo">
    <div class="pattern-info">
      <p class="description">
        适配器模式允许接口不兼容的类一起工作。它将一个类的接口转换成客户希望的另一个接口。
        这个演示展示了如何使用适配器模式统一不同第三方支付接口。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>第三方API接口适配</li>
          <li>数据格式转换</li>
          <li>遗留系统集成</li>
          <li>不同数据库驱动适配</li>
          <li>多平台兼容性处理</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示：第三方支付接口适配</h4>
      
      <!-- 支付平台选择 -->
      <div class="platform-selection">
        <h5>🏦 选择支付平台</h5>
        <div class="platform-grid">
          <div
            v-for="platform in supportedPlatforms"
            :key="platform.id"
            class="platform-card"
            :class="{ active: selectedPlatform === platform.id }"
            @click="selectPlatform(platform.id)"
          >
            <div class="platform-icon">{{ getPlatformIcon(platform.id) }}</div>
            <div class="platform-name">{{ platform.name }}</div>
            <div class="platform-desc">{{ platform.description }}</div>
          </div>
        </div>
      </div>

      <!-- 支付操作 -->
      <div class="payment-operations" v-if="selectedPlatform">
        <h5>💳 支付操作</h5>
        
        <div class="operation-form">
          <div class="form-row">
            <div class="form-group">
              <label>支付金额：</label>
              <input 
                v-model.number="paymentAmount" 
                type="number" 
                placeholder="请输入金额" 
                min="0.01"
                step="0.01"
              />
            </div>
            <div class="form-group">
              <label>货币类型：</label>
              <select v-model="currency">
                <option value="CNY">人民币 (CNY)</option>
                <option value="USD">美元 (USD)</option>
                <option value="EUR">欧元 (EUR)</option>
              </select>
            </div>
          </div>
          
          <div class="operation-buttons">
            <button 
              @click="processPayment" 
              class="demo-btn primary"
              :disabled="!canProcessPayment"
            >
              💳 发起支付
            </button>
            <button 
              @click="queryStatus" 
              class="demo-btn secondary"
              :disabled="!lastTransactionId"
            >
              🔍 查询状态
            </button>
            <button 
              @click="processRefund" 
              class="demo-btn warning"
              :disabled="!lastTransactionId"
            >
              💰 申请退款
            </button>
          </div>
        </div>
      </div>

      <!-- 操作结果 -->
      <div class="operation-results" v-if="operationHistory.length > 0">
        <h5>📋 操作记录</h5>
        <div class="results-list">
          <div
            v-for="(record, index) in operationHistory"
            :key="index"
            class="result-item"
            :class="record.type"
          >
            <div class="result-header">
              <span class="result-icon">{{ getOperationIcon(record.type) }}</span>
              <span class="result-title">{{ record.title }}</span>
              <span class="result-time">{{ formatTime(record.timestamp) }}</span>
            </div>
            <div class="result-content">
              <div class="result-details">
                <div v-if="record.transactionId" class="detail-item">
                  <span class="label">交易ID：</span>
                  <span class="value">{{ record.transactionId }}</span>
                </div>
                <div v-if="record.amount" class="detail-item">
                  <span class="label">金额：</span>
                  <span class="value">{{ record.currency }} {{ record.amount.toFixed(2) }}</span>
                </div>
                <div v-if="record.fee" class="detail-item">
                  <span class="label">手续费：</span>
                  <span class="value">{{ record.currency }} {{ record.fee.toFixed(2) }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">状态：</span>
                  <span class="value" :class="record.success ? 'success' : 'error'">
                    {{ record.message }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 接口对比 -->
      <div class="interface-comparison">
        <h5>🔄 接口适配对比</h5>
        <div class="comparison-grid">
          <div class="comparison-item">
            <h6>原始接口 (支付宝)</h6>
            <pre class="code-block">
pay(money: number, coin: string): {
  code: number;
  data: { 
    trade_no: string; 
    msg: string; 
    fee_amount: number 
  };
  timestamp: number;
}</pre>
          </div>
          
          <div class="comparison-item">
            <h6>原始接口 (微信)</h6>
            <pre class="code-block">
createOrder(orderAmount: number, currencyType: string): {
  result_code: string;
  transaction_id: string;
  return_msg: string;
  total_fee: number;
  create_time: string;
}</pre>
          </div>
          
          <div class="comparison-item unified">
            <h6>统一接口 (适配后)</h6>
            <pre class="code-block">
processPayment(amount: number, currency: string): {
  success: boolean;
  transactionId: string;
  message: string;
  timestamp: Date;
  fee: number;
}</pre>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 使不兼容的接口能够协同工作</li>
        <li>✅ 提高代码的复用性</li>
        <li>✅ 分离接口转换逻辑</li>
        <li>✅ 符合开闭原则</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 增加系统复杂度</li>
        <li>❌ 可能影响性能</li>
        <li>❌ 需要维护适配器代码</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { 
  PaymentAdapterFactory, 
  PaymentStatus,
  type PaymentTargetType,
  type PaymentResultType,
  type RefundResultType
} from "@/patterns/Adapter";

// 响应式数据
const selectedPlatform = ref<string>("");
const paymentAmount = ref<number>(0);
const currency = ref<string>("CNY");
const lastTransactionId = ref<string>("");
const operationHistory = ref<Array<{
  type: 'payment' | 'query' | 'refund';
  title: string;
  platform: string;
  success: boolean;
  message: string;
  transactionId?: string;
  amount?: number;
  currency?: string;
  fee?: number;
  timestamp: Date;
}>>([]);

// 支持的平台
const supportedPlatforms = PaymentAdapterFactory.getSupportedTypes();

// 计算属性
const canProcessPayment = computed(() => {
  return selectedPlatform.value && paymentAmount.value > 0;
});

// 方法
const selectPlatform = (platformId: string) => {
  selectedPlatform.value = platformId;
};

const processPayment = () => {
  if (!canProcessPayment.value) return;

  try {
    const adapter = PaymentAdapterFactory.createAdapter(
      selectedPlatform.value as 'alipay' | 'wechat' | 'unionpay'
    );
    
    const result: PaymentResultType = adapter.processPayment(
      paymentAmount.value, 
      currency.value
    );

    if (result.success) {
      lastTransactionId.value = result.transactionId;
    }

    operationHistory.value.unshift({
      type: 'payment',
      title: '支付处理',
      platform: selectedPlatform.value,
      success: result.success,
      message: result.message,
      transactionId: result.transactionId,
      amount: paymentAmount.value,
      currency: currency.value,
      fee: result.fee,
      timestamp: result.timestamp
    });

  } catch (error) {
    operationHistory.value.unshift({
      type: 'payment',
      title: '支付处理',
      platform: selectedPlatform.value,
      success: false,
      message: `支付失败: ${error}`,
      timestamp: new Date()
    });
  }
};

const queryStatus = () => {
  if (!lastTransactionId.value) return;

  try {
    const adapter = PaymentAdapterFactory.createAdapter(
      selectedPlatform.value as 'alipay' | 'wechat' | 'unionpay'
    );
    
    const status: PaymentStatus = adapter.getPaymentStatus(lastTransactionId.value);
    
    operationHistory.value.unshift({
      type: 'query',
      title: '状态查询',
      platform: selectedPlatform.value,
      success: true,
      message: `支付状态: ${getStatusText(status)}`,
      transactionId: lastTransactionId.value,
      timestamp: new Date()
    });

  } catch (error) {
    operationHistory.value.unshift({
      type: 'query',
      title: '状态查询',
      platform: selectedPlatform.value,
      success: false,
      message: `查询失败: ${error}`,
      timestamp: new Date()
    });
  }
};

const processRefund = () => {
  if (!lastTransactionId.value) return;

  try {
    const adapter = PaymentAdapterFactory.createAdapter(
      selectedPlatform.value as 'alipay' | 'wechat' | 'unionpay'
    );
    
    const result: RefundResultType = adapter.refund(
      lastTransactionId.value, 
      paymentAmount.value
    );

    operationHistory.value.unshift({
      type: 'refund',
      title: '退款处理',
      platform: selectedPlatform.value,
      success: result.success,
      message: result.message,
      transactionId: result.refundId,
      amount: paymentAmount.value,
      currency: currency.value,
      timestamp: result.timestamp
    });

  } catch (error) {
    operationHistory.value.unshift({
      type: 'refund',
      title: '退款处理',
      platform: selectedPlatform.value,
      success: false,
      message: `退款失败: ${error}`,
      timestamp: new Date()
    });
  }
};

const getPlatformIcon = (platformId: string): string => {
  const iconMap: Record<string, string> = {
    'alipay': '🟦',
    'wechat': '🟢',
    'unionpay': '🔴'
  };
  return iconMap[platformId] || '💳';
};

const getOperationIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    'payment': '💳',
    'query': '🔍',
    'refund': '💰'
  };
  return iconMap[type] || '📋';
};

const getStatusText = (status: PaymentStatus): string => {
  const statusMap: Record<PaymentStatus, string> = {
    [PaymentStatus.PENDING]: '处理中',
    [PaymentStatus.SUCCESS]: '成功',
    [PaymentStatus.FAILED]: '失败',
    [PaymentStatus.CANCELLED]: '已取消'
  };
  return statusMap[status] || '未知';
};

const formatTime = (timestamp: Date): string => {
  return timestamp.toLocaleTimeString();
};
</script>

<style lang="scss" scoped>
.adapter-demo {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .pattern-info {
    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #667eea;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .demo-section {
    h4, h5 {
      margin-bottom: 1rem;
      color: #333;
    }

    .platform-selection {
      margin-bottom: 2rem;

      .platform-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;

        .platform-card {
          background: #f8f9fa;
          border: 2px solid #e9ecef;
          border-radius: 8px;
          padding: 1.5rem;
          text-align: center;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #667eea;
            transform: translateY(-2px);
          }

          &.active {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
          }

          .platform-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
          }

          .platform-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
          }

          .platform-desc {
            font-size: 0.9rem;
            opacity: 0.8;
          }
        }
      }
    }

    .payment-operations {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 2rem;

      .operation-form {
        .form-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1rem;
          margin-bottom: 1.5rem;

          .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;

            label {
              font-weight: 500;
              color: #555;
            }

            input, select {
              padding: 0.75rem;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 0.9rem;

              &:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
              }
            }
          }
        }

        .operation-buttons {
          display: flex;
          gap: 1rem;
          flex-wrap: wrap;
        }
      }
    }

    .operation-results {
      margin-bottom: 2rem;

      .results-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .result-item {
          background: white;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 1rem;
          border-left: 4px solid #6c757d;

          &.payment {
            border-left-color: #667eea;
          }

          &.query {
            border-left-color: #17a2b8;
          }

          &.refund {
            border-left-color: #ffc107;
          }

          .result-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.8rem;

            .result-icon {
              font-size: 1.2rem;
            }

            .result-title {
              font-weight: 600;
              color: #333;
            }

            .result-time {
              margin-left: auto;
              font-size: 0.8rem;
              color: #666;
            }
          }

          .result-content {
            .result-details {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 0.5rem;

              .detail-item {
                display: flex;
                gap: 0.5rem;

                .label {
                  font-weight: 500;
                  color: #666;
                  min-width: 60px;
                }

                .value {
                  color: #333;

                  &.success {
                    color: #28a745;
                  }

                  &.error {
                    color: #dc3545;
                  }
                }
              }
            }
          }
        }
      }
    }

    .interface-comparison {
      .comparison-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;

        .comparison-item {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 1rem;

          &.unified {
            border-color: #667eea;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
          }

          h6 {
            margin: 0 0 1rem 0;
            color: #333;
          }

          .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 4px;
            font-size: 0.8rem;
            line-height: 1.4;
            overflow-x: auto;
            margin: 0;
          }
        }
      }
    }
  }

  .demo-btn {
    padding: 0.8rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.9rem;

    &.primary {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
    }

    &.secondary {
      background: linear-gradient(135deg, #17a2b8, #138496);
      color: white;
    }

    &.warning {
      background: linear-gradient(135deg, #ffc107, #e0a800);
      color: #212529;
    }

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}
</style>
