<template>
  <div class="composite-demo">
    <div class="pattern-info">
      <p class="description">
        组合模式将对象组合成树形结构以表示"部分-整体"的层次结构。它使得用户对单个对象和组合对象的使用具有一致性。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>文件系统（文件夹和文件）</li>
          <li>组织架构（部门和员工）</li>
          <li>UI组件树（容器和控件）</li>
          <li>菜单系统（菜单和菜单项）</li>
          <li>表达式解析（操作符和操作数）</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 文件系统：</h4>
      <div class="demo-controls">
        <div class="action-group">
          <input 
            v-model="newItemName" 
            placeholder="输入名称" 
            class="name-input"
            @keyup.enter="addItem"
          />
          <select v-model="newItemType" class="type-select">
            <option value="file">文件</option>
            <option value="directory">文件夹</option>
          </select>
          <button @click="addItem" class="demo-btn">添加</button>
        </div>
        <div class="action-group">
          <button @click="calculateTotalSize" class="demo-btn">计算总大小</button>
          <button @click="expandAll" class="demo-btn">展开全部</button>
          <button @click="collapseAll" class="demo-btn">折叠全部</button>
          <button @click="resetFileSystem" class="demo-btn clear">重置</button>
        </div>
      </div>

      <div class="demo-output">
        <div class="file-system-container">
          <div class="file-system-header">
            <h5>文件系统结构</h5>
            <div class="system-stats">
              <span>总大小: {{ totalSize }}KB</span>
              <span>文件数: {{ fileCount }}</span>
              <span>文件夹数: {{ directoryCount }}</span>
            </div>
          </div>
          
          <div class="file-tree">
            <FileSystemNode 
              :node="rootDirectory"
              :level="0"
              :expanded-nodes="expandedNodes"
              :selected-node="selectedNode"
              @toggle-expand="toggleExpand"
              @select-node="selectNode"
              @delete-node="deleteNode"
            />
          </div>
        </div>

        <div class="node-details" v-if="selectedNode">
          <h5>节点详情</h5>
          <div class="details-content">
            <div class="detail-item">
              <strong>名称:</strong> {{ selectedNode.getName() }}
            </div>
            <div class="detail-item">
              <strong>类型:</strong> {{ selectedNode.getType() }}
            </div>
            <div class="detail-item">
              <strong>大小:</strong> {{ selectedNode.getSize() }}KB
            </div>
            <div class="detail-item" v-if="selectedNode.getType() === 'directory'">
              <strong>子项数量:</strong> {{ selectedNode.getChildren().length }}
            </div>
            <div class="detail-item" v-if="selectedNode.getType() === 'directory'">
              <strong>路径:</strong> {{ getNodePath(selectedNode) }}
            </div>
          </div>
        </div>

        <div class="composite-explanation">
          <h5>组合模式说明</h5>
          <div class="explanation-grid">
            <div class="explanation-item">
              <h6>🗂️ 组合对象 (Composite)</h6>
              <p>文件夹 - 可以包含其他文件夹和文件</p>
              <ul>
                <li>可以添加/删除子节点</li>
                <li>大小 = 所有子节点大小之和</li>
                <li>支持递归操作</li>
              </ul>
            </div>
            <div class="explanation-item">
              <h6>📄 叶子对象 (Leaf)</h6>
              <p>文件 - 不能包含其他对象</p>
              <ul>
                <li>具有固定大小</li>
                <li>不支持添加子节点</li>
                <li>是树结构的终端节点</li>
              </ul>
            </div>
          </div>
          <div class="pattern-benefit">
            <strong>统一接口:</strong> 无论是文件还是文件夹，都可以通过相同的方式获取名称、大小等信息，客户端无需区分对待。
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 简化客户端代码，统一处理单个对象和组合对象</li>
        <li>✅ 更容易增加新类型的组件</li>
        <li>✅ 提供了灵活的树形结构</li>
        <li>✅ 符合开闭原则</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 设计过于宽泛，难以限制组合中的组件类型</li>
        <li>❌ 使设计变得更加抽象</li>
        <li>❌ 可能会增加系统复杂性</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";

// 文件系统组件接口
interface FileSystemComponent {
  getName(): string;
  getSize(): number;
  getType(): string;
  display(indent?: string): string;
  getChildren?(): FileSystemComponent[];
  add?(component: FileSystemComponent): void;
  remove?(component: FileSystemComponent): void;
}

// 文件类（叶子节点）
class File implements FileSystemComponent {
  private name: string;
  private size: number;
  private extension: string;

  constructor(name: string, size?: number) {
    this.name = name;
    this.extension = name.split('.').pop() || '';
    this.size = size || Math.floor(Math.random() * 100) + 1;
  }

  getName(): string {
    return this.name;
  }

  getSize(): number {
    return this.size;
  }

  getType(): string {
    return 'file';
  }

  display(indent: string = ''): string {
    return `${indent}📄 ${this.name} (${this.size}KB)`;
  }

  getExtension(): string {
    return this.extension;
  }
}

// 目录类（组合节点）
class Directory implements FileSystemComponent {
  private name: string;
  private children: FileSystemComponent[] = [];

  constructor(name: string) {
    this.name = name;
  }

  getName(): string {
    return this.name;
  }

  getSize(): number {
    return this.children.reduce((total, child) => total + child.getSize(), 0);
  }

  getType(): string {
    return 'directory';
  }

  add(component: FileSystemComponent): void {
    this.children.push(component);
  }

  remove(component: FileSystemComponent): void {
    const index = this.children.indexOf(component);
    if (index > -1) {
      this.children.splice(index, 1);
    }
  }

  getChildren(): FileSystemComponent[] {
    return [...this.children];
  }

  display(indent: string = ''): string {
    let result = `${indent}📁 ${this.name}/ (${this.getSize()}KB)\n`;
    
    this.children.forEach((child, index) => {
      const isLast = index === this.children.length - 1;
      const childIndent = indent + (isLast ? '  ' : '│ ');
      const prefix = isLast ? '└─' : '├─';
      result += `${indent}${prefix} ${child.display().replace(/^\s*/, '')}\n`;
      
      if (child.getType() === 'directory' && child.getChildren) {
        const subResult = child.display(childIndent);
        const lines = subResult.split('\n').slice(1, -1);
        result += lines.join('\n') + (lines.length > 0 ? '\n' : '');
      }
    });
    
    return result.trimEnd();
  }
}

// 响应式数据
const newItemName = ref<string>('');
const newItemType = ref<string>('file');
const selectedNode = ref<FileSystemComponent | null>(null);
const expandedNodes = ref<Set<FileSystemComponent>>(new Set());
const totalSize = ref<number>(0);

// 创建根目录
const rootDirectory = reactive(new Directory('根目录'));

// 计算属性
const fileCount = computed(() => {
  const countFiles = (node: FileSystemComponent): number => {
    if (node.getType() === 'file') return 1;
    if (node.getChildren) {
      return node.getChildren().reduce((count, child) => count + countFiles(child), 0);
    }
    return 0;
  };
  return countFiles(rootDirectory);
});

const directoryCount = computed(() => {
  const countDirectories = (node: FileSystemComponent): number => {
    if (node.getType() === 'directory') {
      let count = 1;
      if (node.getChildren) {
        count += node.getChildren().reduce((total, child) => total + countDirectories(child), 0);
      }
      return count;
    }
    return 0;
  };
  return countDirectories(rootDirectory) - 1; // 不计算根目录
});

// 方法
const addItem = () => {
  if (!newItemName.value.trim()) return;
  
  const targetDirectory = selectedNode.value?.getType() === 'directory' 
    ? selectedNode.value as Directory 
    : rootDirectory;
  
  if (newItemType.value === 'file') {
    const file = new File(newItemName.value);
    targetDirectory.add(file);
  } else {
    const directory = new Directory(newItemName.value);
    targetDirectory.add(directory);
  }
  
  newItemName.value = '';
  calculateTotalSize();
};

const calculateTotalSize = () => {
  totalSize.value = rootDirectory.getSize();
};

const toggleExpand = (node: FileSystemComponent) => {
  if (expandedNodes.value.has(node)) {
    expandedNodes.value.delete(node);
  } else {
    expandedNodes.value.add(node);
  }
};

const selectNode = (node: FileSystemComponent) => {
  selectedNode.value = node;
};

const deleteNode = (node: FileSystemComponent) => {
  const deleteFromDirectory = (dir: Directory, target: FileSystemComponent): boolean => {
    const children = dir.getChildren();
    const index = children.indexOf(target);
    if (index > -1) {
      dir.remove(target);
      return true;
    }
    
    for (const child of children) {
      if (child.getType() === 'directory') {
        if (deleteFromDirectory(child as Directory, target)) {
          return true;
        }
      }
    }
    return false;
  };
  
  if (node !== rootDirectory) {
    deleteFromDirectory(rootDirectory, node);
    if (selectedNode.value === node) {
      selectedNode.value = null;
    }
    expandedNodes.value.delete(node);
    calculateTotalSize();
  }
};

const expandAll = () => {
  const expandRecursively = (node: FileSystemComponent) => {
    if (node.getType() === 'directory') {
      expandedNodes.value.add(node);
      if (node.getChildren) {
        node.getChildren().forEach(child => expandRecursively(child));
      }
    }
  };
  expandRecursively(rootDirectory);
};

const collapseAll = () => {
  expandedNodes.value.clear();
};

const resetFileSystem = () => {
  // 清空根目录
  rootDirectory.getChildren().forEach(child => {
    rootDirectory.remove(child);
  });
  
  // 重新初始化示例数据
  initializeFileSystem();
  selectedNode.value = null;
  expandedNodes.value.clear();
  expandedNodes.value.add(rootDirectory);
  calculateTotalSize();
};

const getNodePath = (node: FileSystemComponent): string => {
  const findPath = (current: FileSystemComponent, target: FileSystemComponent, path: string = ''): string | null => {
    if (current === target) {
      return path + current.getName();
    }
    
    if (current.getChildren) {
      for (const child of current.getChildren()) {
        const result = findPath(child, target, path + current.getName() + '/');
        if (result) return result;
      }
    }
    
    return null;
  };
  
  return findPath(rootDirectory, node) || '';
};

const initializeFileSystem = () => {
  // 创建示例文件系统结构
  const src = new Directory('src');
  src.add(new File('index.ts', 15));
  src.add(new File('app.ts', 25));
  
  const components = new Directory('components');
  components.add(new File('Header.vue', 8));
  components.add(new File('Footer.vue', 6));
  src.add(components);
  
  const config = new Directory('config');
  config.add(new File('webpack.config.js', 12));
  config.add(new File('tsconfig.json', 3));
  
  rootDirectory.add(src);
  rootDirectory.add(config);
  rootDirectory.add(new File('package.json', 5));
  rootDirectory.add(new File('README.md', 2));
};

// 初始化
onMounted(() => {
  initializeFileSystem();
  expandedNodes.value.add(rootDirectory);
  calculateTotalSize();
});
</script>

<script lang="ts">
// FileSystemNode 组件
import { defineComponent, PropType } from 'vue';

const FileSystemNode = defineComponent({
  name: 'FileSystemNode',
  props: {
    node: {
      type: Object as PropType<FileSystemComponent>,
      required: true
    },
    level: {
      type: Number,
      default: 0
    },
    expandedNodes: {
      type: Object as PropType<Set<FileSystemComponent>>,
      required: true
    },
    selectedNode: {
      type: Object as PropType<FileSystemComponent | null>,
      default: null
    }
  },
  emits: ['toggle-expand', 'select-node', 'delete-node'],
  template: `
    <div class="file-node">
      <div 
        class="node-content"
        :class="{ 
          selected: selectedNode === node,
          directory: node.getType() === 'directory',
          file: node.getType() === 'file'
        }"
        :style="{ paddingLeft: (level * 20) + 'px' }"
        @click="$emit('select-node', node)"
      >
        <span 
          v-if="node.getType() === 'directory'"
          class="expand-icon"
          @click.stop="$emit('toggle-expand', node)"
        >
          {{ expandedNodes.has(node) ? '📂' : '📁' }}
        </span>
        <span v-else class="file-icon">📄</span>
        
        <span class="node-name">{{ node.getName() }}</span>
        <span class="node-size">({{ node.getSize() }}KB)</span>
        
        <button 
          v-if="level > 0"
          class="delete-btn"
          @click.stop="$emit('delete-node', node)"
          title="删除"
        >
          ×
        </button>
      </div>
      
      <div v-if="node.getType() === 'directory' && expandedNodes.has(node) && node.getChildren">
        <FileSystemNode
          v-for="child in node.getChildren()"
          :key="child.getName()"
          :node="child"
          :level="level + 1"
          :expanded-nodes="expandedNodes"
          :selected-node="selectedNode"
          @toggle-expand="$emit('toggle-expand', $event)"
          @select-node="$emit('select-node', $event)"
          @delete-node="$emit('delete-node', $event)"
        />
      </div>
    </div>
  `
});

export default {
  components: {
    FileSystemNode
  }
};
</script>

<style lang="scss" scoped>
.composite-demo {
  .pattern-info {
    margin-bottom: 2rem;
    
    .description {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }
    
    .use-cases {
      h4 {
        color: #333;
        margin-bottom: 0.5rem;
      }
      
      ul {
        list-style-type: disc;
        padding-left: 1.5rem;
        
        li {
          margin-bottom: 0.3rem;
          color: #666;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
    }
    
    .demo-controls {
      display: flex;
      gap: 1rem;
      margin-bottom: 1.5rem;
      flex-wrap: wrap;
      
      .action-group {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        
        .name-input {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
          width: 150px;
        }
        
        .type-select {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
        }
        
        .demo-btn {
          padding: 0.6rem 1rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          font-size: 0.9rem;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          }
          
          &.clear {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
          }
        }
      }
    }
    
    .demo-output {
      display: grid;
      grid-template-columns: 1fr 300px;
      gap: 1.5rem;
      
      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
      }
      
      .file-system-container {
        .file-system-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
          padding-bottom: 0.5rem;
          border-bottom: 2px solid #e0e0e0;
          
          h5 {
            color: #333;
            margin: 0;
          }
          
          .system-stats {
            display: flex;
            gap: 1rem;
            font-size: 0.9rem;
            color: #666;
            
            span {
              background: #f0f0f0;
              padding: 0.3rem 0.6rem;
              border-radius: 12px;
            }
          }
        }
        
        .file-tree {
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          background: white;
          max-height: 400px;
          overflow-y: auto;
          
          :deep(.file-node) {
            .node-content {
              display: flex;
              align-items: center;
              padding: 0.5rem;
              cursor: pointer;
              transition: background-color 0.2s ease;
              border-bottom: 1px solid #f0f0f0;
              
              &:hover {
                background: #f8f9fa;
              }
              
              &.selected {
                background: #e6f7ff;
                border-left: 3px solid #1890ff;
              }
              
              .expand-icon, .file-icon {
                margin-right: 0.5rem;
                font-size: 1rem;
                cursor: pointer;
              }
              
              .node-name {
                flex: 1;
                font-weight: 500;
                color: #333;
              }
              
              .node-size {
                color: #666;
                font-size: 0.9rem;
                margin-left: 0.5rem;
              }
              
              .delete-btn {
                margin-left: 0.5rem;
                background: #ff4d4f;
                color: white;
                border: none;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                cursor: pointer;
                font-size: 0.8rem;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.2s ease;
                
                &:hover {
                  background: #d32f2f;
                }
              }
              
              &:hover .delete-btn {
                opacity: 1;
              }
            }
          }
        }
      }
      
      .node-details {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        height: fit-content;
        
        h5 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .details-content {
          .detail-item {
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            
            strong {
              color: #333;
            }
            
            &:not(strong) {
              color: #666;
            }
          }
        }
      }
      
      .composite-explanation {
        grid-column: 1 / -1;
        background: linear-gradient(135deg, #f0f8ff, #ffffff);
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e0f0ff;
        margin-top: 1rem;
        
        h5 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .explanation-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1rem;
          margin-bottom: 1rem;
          
          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }
          
          .explanation-item {
            background: white;
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            
            h6 {
              color: #333;
              margin: 0 0 0.5rem 0;
              font-size: 1rem;
            }
            
            p {
              color: #666;
              margin: 0 0 0.5rem 0;
              font-size: 0.9rem;
            }
            
            ul {
              list-style-type: disc;
              padding-left: 1rem;
              margin: 0;
              
              li {
                color: #666;
                font-size: 0.85rem;
                margin-bottom: 0.2rem;
              }
            }
          }
        }
        
        .pattern-benefit {
          background: #667eea;
          color: white;
          padding: 1rem;
          border-radius: 6px;
          text-align: center;
          font-size: 0.9rem;
        }
      }
    }
  }

  .advantages {
    h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 0.3rem;
        padding-left: 1rem;
        color: #666;
      }
    }
  }
}
</style>
