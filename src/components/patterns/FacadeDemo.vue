<template>
  <div class="facade-demo">
    <div class="pattern-info">
      <p class="description">
        外观模式为复杂子系统提供一个简单的接口。它定义了一个高层接口，让子系统更容易使用，隐藏了系统的复杂性。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>智能家居控制系统</li>
          <li>编译器前端接口</li>
          <li>数据库访问层</li>
          <li>API网关</li>
          <li>第三方库封装</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 智能家居控制系统：</h4>
      <div class="demo-controls">
        <div class="mode-buttons">
          <button @click="executeMode('arrive')" class="demo-btn mode-btn arrive">
            🏠 回家模式
          </button>
          <button @click="executeMode('leave')" class="demo-btn mode-btn leave">
            🚪 离家模式
          </button>
          <button @click="executeMode('sleep')" class="demo-btn mode-btn sleep">
            😴 睡眠模式
          </button>
          <button @click="executeMode('party')" class="demo-btn mode-btn party">
            🎉 聚会模式
          </button>
        </div>
        <div class="manual-controls">
          <h6>手动控制子系统：</h6>
          <div class="subsystem-controls">
            <div class="control-group">
              <label>灯光控制：</label>
              <select v-model="selectedRoom" class="room-select">
                <option value="客厅">客厅</option>
                <option value="卧室">卧室</option>
                <option value="厨房">厨房</option>
              </select>
              <button @click="toggleLight" class="mini-btn">开/关灯</button>
              <button @click="dimLight" class="mini-btn">调光</button>
            </div>
            <div class="control-group">
              <label>空调控制：</label>
              <input v-model.number="targetTemp" type="number" min="16" max="30" class="temp-input" />
              <button @click="setTemperature" class="mini-btn">设置温度</button>
              <button @click="toggleAC" class="mini-btn">开/关空调</button>
            </div>
            <div class="control-group">
              <label>音响控制：</label>
              <input v-model="musicTrack" placeholder="音乐名称" class="track-input" />
              <button @click="playMusic" class="mini-btn">播放</button>
              <button @click="stopMusic" class="mini-btn">停止</button>
            </div>
          </div>
        </div>
      </div>

      <div class="demo-output">
        <div class="system-status">
          <h5>系统状态</h5>
          <div class="status-grid">
            <div class="status-card lighting">
              <h6>💡 灯光系统</h6>
              <div class="status-content">
                <div v-for="(status, room) in lightingStatus" :key="room" class="room-status">
                  <span class="room-name">{{ room }}:</span>
                  <span class="status-value" :class="{ on: status.isOn }">
                    {{ status.isOn ? '开启' : '关闭' }}
                    <span v-if="status.brightness < 100 && status.isOn" class="brightness">
                      ({{ status.brightness }}%)
                    </span>
                  </span>
                </div>
              </div>
            </div>

            <div class="status-card air-conditioning">
              <h6>❄️ 空调系统</h6>
              <div class="status-content">
                <div class="status-item">
                  <span>状态:</span>
                  <span class="status-value" :class="{ on: acStatus.isOn }">
                    {{ acStatus.isOn ? '开启' : '关闭' }}
                  </span>
                </div>
                <div class="status-item">
                  <span>温度:</span>
                  <span class="status-value">{{ acStatus.temperature }}°C</span>
                </div>
              </div>
            </div>

            <div class="status-card audio">
              <h6>🎵 音响系统</h6>
              <div class="status-content">
                <div class="status-item">
                  <span>状态:</span>
                  <span class="status-value" :class="{ on: audioStatus.isPlaying }">
                    {{ audioStatus.isPlaying ? '播放中' : '停止' }}
                  </span>
                </div>
                <div class="status-item" v-if="audioStatus.currentTrack">
                  <span>曲目:</span>
                  <span class="status-value">{{ audioStatus.currentTrack }}</span>
                </div>
                <div class="status-item">
                  <span>音量:</span>
                  <span class="status-value">{{ audioStatus.volume }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="action-log">
          <h5>操作日志</h5>
          <div class="log-container">
            <div 
              v-for="(log, index) in actionLogs" 
              :key="index"
              class="log-entry"
              :class="log.type"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>

        <div class="facade-explanation">
          <h5>外观模式架构</h5>
          <div class="architecture-diagram">
            <div class="client-side">
              <h6>👤 客户端</h6>
              <div class="client-actions">
                <div class="action-item">回家模式</div>
                <div class="action-item">离家模式</div>
                <div class="action-item">睡眠模式</div>
                <div class="action-item">聚会模式</div>
              </div>
            </div>
            
            <div class="facade-layer">
              <h6>🏠 智能家居外观</h6>
              <div class="facade-description">
                统一接口，简化操作
              </div>
            </div>
            
            <div class="subsystems">
              <h6>⚙️ 子系统</h6>
              <div class="subsystem-list">
                <div class="subsystem-item">💡 灯光系统</div>
                <div class="subsystem-item">❄️ 空调系统</div>
                <div class="subsystem-item">🎵 音响系统</div>
              </div>
            </div>
          </div>
          
          <div class="pattern-benefit">
            <strong>简化复杂性:</strong> 客户端只需调用一个方法就能控制多个子系统，无需了解各子系统的具体实现细节。
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 简化了客户端与子系统的交互</li>
        <li>✅ 降低了客户端与子系统的耦合度</li>
        <li>✅ 更好地划分了访问层次</li>
        <li>✅ 符合迪米特法则</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 不符合开闭原则，修改很麻烦</li>
        <li>❌ 某些情况下可能违背单一职责原则</li>
        <li>❌ 过度使用会导致系统过于复杂</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";

// 子系统 - 灯光控制
class LightingSystem {
  private lights: Map<string, { isOn: boolean; brightness: number }> = new Map();

  constructor() {
    this.lights.set('客厅', { isOn: false, brightness: 100 });
    this.lights.set('卧室', { isOn: false, brightness: 100 });
    this.lights.set('厨房', { isOn: false, brightness: 100 });
  }

  turnOn(room: string): string {
    const light = this.lights.get(room);
    if (light) {
      light.isOn = true;
      return `${room}灯光已开启`;
    }
    return `${room}不存在`;
  }

  turnOff(room: string): string {
    const light = this.lights.get(room);
    if (light) {
      light.isOn = false;
      return `${room}灯光已关闭`;
    }
    return `${room}不存在`;
  }

  dimLights(room: string, level: number): string {
    const light = this.lights.get(room);
    if (light) {
      light.brightness = Math.max(0, Math.min(100, level));
      return `${room}灯光调节至${light.brightness}%亮度`;
    }
    return `${room}不存在`;
  }

  getStatus(): Map<string, { isOn: boolean; brightness: number }> {
    return new Map(this.lights);
  }

  toggle(room: string): string {
    const light = this.lights.get(room);
    if (light) {
      light.isOn = !light.isOn;
      return `${room}灯光已${light.isOn ? '开启' : '关闭'}`;
    }
    return `${room}不存在`;
  }
}

// 子系统 - 空调控制
class AirConditioningSystem {
  private temperature: number = 25;
  private isOn: boolean = false;

  turnOn(): string {
    this.isOn = true;
    return `空调已开启，当前温度${this.temperature}°C`;
  }

  turnOff(): string {
    this.isOn = false;
    return '空调已关闭';
  }

  setTemperature(temp: number): string {
    this.temperature = Math.max(16, Math.min(30, temp));
    if (this.isOn) {
      return `空调温度设置为${this.temperature}°C`;
    } else {
      this.isOn = true;
      return `空调已开启并设置温度为${this.temperature}°C`;
    }
  }

  getStatus(): { isOn: boolean; temperature: number } {
    return { isOn: this.isOn, temperature: this.temperature };
  }

  toggle(): string {
    this.isOn = !this.isOn;
    return this.isOn ? this.turnOn() : this.turnOff();
  }
}

// 子系统 - 音响控制
class AudioSystem {
  private volume: number = 50;
  private isPlaying: boolean = false;
  private currentTrack: string = '';

  play(track: string): string {
    this.isPlaying = true;
    this.currentTrack = track;
    return `正在播放: ${track}`;
  }

  stop(): string {
    this.isPlaying = false;
    const prevTrack = this.currentTrack;
    this.currentTrack = '';
    return `已停止播放${prevTrack ? ': ' + prevTrack : ''}`;
  }

  setVolume(volume: number): string {
    this.volume = Math.max(0, Math.min(100, volume));
    return `音量设置为${this.volume}%`;
  }

  getStatus(): { isPlaying: boolean; currentTrack: string; volume: number } {
    return { 
      isPlaying: this.isPlaying, 
      currentTrack: this.currentTrack, 
      volume: this.volume 
    };
  }
}

// 外观类 - 智能家居控制器
class SmartHomeFacade {
  private lighting: LightingSystem;
  private airConditioning: AirConditioningSystem;
  private audio: AudioSystem;

  constructor() {
    this.lighting = new LightingSystem();
    this.airConditioning = new AirConditioningSystem();
    this.audio = new AudioSystem();
  }

  // 回家模式
  arriveHome(): string[] {
    return [
      '=== 回家模式启动 ===',
      this.lighting.turnOn('客厅'),
      this.lighting.turnOn('卧室'),
      this.airConditioning.turnOn(),
      this.airConditioning.setTemperature(24),
      this.audio.play('轻松音乐'),
      this.audio.setVolume(30),
      '回家模式设置完成'
    ];
  }

  // 离家模式
  leaveHome(): string[] {
    return [
      '=== 离家模式启动 ===',
      this.lighting.turnOff('客厅'),
      this.lighting.turnOff('卧室'),
      this.lighting.turnOff('厨房'),
      this.airConditioning.turnOff(),
      this.audio.stop(),
      '离家模式设置完成'
    ];
  }

  // 睡眠模式
  sleepMode(): string[] {
    return [
      '=== 睡眠模式启动 ===',
      this.lighting.turnOff('客厅'),
      this.lighting.turnOff('厨房'),
      this.lighting.dimLights('卧室', 10),
      this.airConditioning.setTemperature(22),
      this.audio.play('白噪音'),
      this.audio.setVolume(15),
      '睡眠模式设置完成'
    ];
  }

  // 聚会模式
  partyMode(): string[] {
    return [
      '=== 聚会模式启动 ===',
      this.lighting.turnOn('客厅'),
      this.lighting.turnOn('厨房'),
      this.lighting.dimLights('客厅', 80),
      this.airConditioning.setTemperature(20),
      this.audio.play('派对音乐'),
      this.audio.setVolume(70),
      '聚会模式设置完成'
    ];
  }

  // 获取子系统引用（用于手动控制）
  getLightingSystem(): LightingSystem { return this.lighting; }
  getAirConditioningSystem(): AirConditioningSystem { return this.airConditioning; }
  getAudioSystem(): AudioSystem { return this.audio; }
}

// 响应式数据
const smartHome = new SmartHomeFacade();
const selectedRoom = ref<string>('客厅');
const targetTemp = ref<number>(24);
const musicTrack = ref<string>('');

const lightingStatus = ref(smartHome.getLightingSystem().getStatus());
const acStatus = ref(smartHome.getAirConditioningSystem().getStatus());
const audioStatus = ref(smartHome.getAudioSystem().getStatus());

const actionLogs = ref<Array<{
  time: string;
  message: string;
  type: string;
}>>([]);

// 方法
const executeMode = (mode: string) => {
  let actions: string[] = [];
  let logType = 'mode';
  
  switch (mode) {
    case 'arrive':
      actions = smartHome.arriveHome();
      break;
    case 'leave':
      actions = smartHome.leaveHome();
      break;
    case 'sleep':
      actions = smartHome.sleepMode();
      break;
    case 'party':
      actions = smartHome.partyMode();
      break;
  }
  
  actions.forEach(action => {
    addLog(action, logType);
  });
  
  updateStatus();
};

const toggleLight = () => {
  const result = smartHome.getLightingSystem().toggle(selectedRoom.value);
  addLog(result, 'manual');
  updateStatus();
};

const dimLight = () => {
  const level = Math.floor(Math.random() * 50) + 30; // 30-80%
  const result = smartHome.getLightingSystem().dimLights(selectedRoom.value, level);
  addLog(result, 'manual');
  updateStatus();
};

const toggleAC = () => {
  const result = smartHome.getAirConditioningSystem().toggle();
  addLog(result, 'manual');
  updateStatus();
};

const setTemperature = () => {
  const result = smartHome.getAirConditioningSystem().setTemperature(targetTemp.value);
  addLog(result, 'manual');
  updateStatus();
};

const playMusic = () => {
  const track = musicTrack.value || '默认音乐';
  const result = smartHome.getAudioSystem().play(track);
  addLog(result, 'manual');
  musicTrack.value = '';
  updateStatus();
};

const stopMusic = () => {
  const result = smartHome.getAudioSystem().stop();
  addLog(result, 'manual');
  updateStatus();
};

const addLog = (message: string, type: string) => {
  const now = new Date();
  const time = now.toLocaleTimeString();
  
  actionLogs.value.unshift({
    time,
    message,
    type
  });
  
  // 保持日志数量在合理范围内
  if (actionLogs.value.length > 20) {
    actionLogs.value = actionLogs.value.slice(0, 20);
  }
};

const updateStatus = () => {
  lightingStatus.value = smartHome.getLightingSystem().getStatus();
  acStatus.value = smartHome.getAirConditioningSystem().getStatus();
  audioStatus.value = smartHome.getAudioSystem().getStatus();
};

// 初始化
addLog('智能家居系统已启动', 'system');
</script>

<style lang="scss" scoped>
.facade-demo {
  .pattern-info {
    margin-bottom: 2rem;
    
    .description {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }
    
    .use-cases {
      h4 {
        color: #333;
        margin-bottom: 0.5rem;
      }
      
      ul {
        list-style-type: disc;
        padding-left: 1.5rem;
        
        li {
          margin-bottom: 0.3rem;
          color: #666;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
    }
    
    .demo-controls {
      margin-bottom: 1.5rem;
      
      .mode-buttons {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
        flex-wrap: wrap;
        
        .mode-btn {
          padding: 0.8rem 1.5rem;
          border: none;
          border-radius: 8px;
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          font-size: 1rem;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }
          
          &.arrive {
            background: linear-gradient(135deg, #52c41a, #389e0d);
          }
          
          &.leave {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
          }
          
          &.sleep {
            background: linear-gradient(135deg, #722ed1, #531dab);
          }
          
          &.party {
            background: linear-gradient(135deg, #fa8c16, #d46b08);
          }
        }
      }
      
      .manual-controls {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        
        h6 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .subsystem-controls {
          display: flex;
          flex-direction: column;
          gap: 0.8rem;
          
          .control-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
            
            label {
              font-weight: 500;
              color: #555;
              min-width: 80px;
            }
            
            .room-select, .temp-input, .track-input {
              padding: 0.4rem;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 0.9rem;
            }
            
            .temp-input {
              width: 60px;
            }
            
            .track-input {
              width: 120px;
            }
            
            .mini-btn {
              padding: 0.4rem 0.8rem;
              border: none;
              border-radius: 4px;
              background: #667eea;
              color: white;
              cursor: pointer;
              font-size: 0.85rem;
              transition: all 0.2s ease;
              
              &:hover {
                background: #5a67d8;
              }
            }
          }
        }
      }
    }
    
    .demo-output {
      .system-status {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .status-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 1rem;
          
          .status-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 2px solid #e0e0e0;
            
            &.lighting {
              border-left-color: #faad14;
            }
            
            &.air-conditioning {
              border-left-color: #1890ff;
            }
            
            &.audio {
              border-left-color: #722ed1;
            }
            
            h6 {
              color: #333;
              margin: 0 0 0.8rem 0;
              font-size: 1rem;
            }
            
            .status-content {
              .room-status, .status-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
                
                .room-name, span:first-child {
                  color: #666;
                  font-weight: 500;
                }
                
                .status-value {
                  color: #999;
                  
                  &.on {
                    color: #52c41a;
                    font-weight: 500;
                  }
                  
                  .brightness {
                    color: #666;
                    font-size: 0.8rem;
                  }
                }
              }
            }
          }
        }
      }
      
      .action-log {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .log-container {
          background: #1f1f1f;
          border-radius: 8px;
          padding: 1rem;
          max-height: 300px;
          overflow-y: auto;
          font-family: 'Courier New', monospace;
          
          .log-entry {
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            
            &.mode {
              color: #52c41a;
            }
            
            &.manual {
              color: #1890ff;
            }
            
            &.system {
              color: #faad14;
            }
            
            .log-time {
              color: #666;
              margin-right: 0.5rem;
            }
            
            .log-message {
              color: inherit;
            }
          }
        }
      }
      
      .facade-explanation {
        background: linear-gradient(135deg, #f0f8ff, #ffffff);
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e0f0ff;
        
        h5 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .architecture-diagram {
          display: grid;
          grid-template-columns: 1fr auto 1fr;
          gap: 1rem;
          align-items: center;
          margin-bottom: 1rem;
          
          @media (max-width: 768px) {
            grid-template-columns: 1fr;
            text-align: center;
          }
          
          .client-side, .facade-layer, .subsystems {
            background: white;
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            
            h6 {
              color: #333;
              margin: 0 0 0.5rem 0;
              font-size: 1rem;
            }
          }
          
          .client-side {
            .client-actions {
              .action-item {
                background: #f0f0f0;
                padding: 0.3rem 0.6rem;
                margin: 0.3rem 0;
                border-radius: 4px;
                font-size: 0.85rem;
                color: #666;
              }
            }
          }
          
          .facade-layer {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            
            h6 {
              color: white;
            }
            
            .facade-description {
              font-size: 0.9rem;
              text-align: center;
            }
          }
          
          .subsystems {
            .subsystem-list {
              .subsystem-item {
                background: #f0f0f0;
                padding: 0.3rem 0.6rem;
                margin: 0.3rem 0;
                border-radius: 4px;
                font-size: 0.85rem;
                color: #666;
              }
            }
          }
        }
        
        .pattern-benefit {
          background: #667eea;
          color: white;
          padding: 1rem;
          border-radius: 6px;
          text-align: center;
          font-size: 0.9rem;
        }
      }
    }
  }

  .advantages {
    h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 0.3rem;
        padding-left: 1rem;
        color: #666;
      }
    }
  }
}
</style>
