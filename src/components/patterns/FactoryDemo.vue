<template>
  <div class="factory-demo">
    <div class="pattern-info">
      <p class="description">
        工厂模式提供了一种创建对象的最佳方式。在工厂模式中，我们在创建对象时不会对客户端暴露创建逻辑，并且是通过使用一个共同的接口来指向新创建的对象。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>数据库连接器（MySQL、PostgreSQL、MongoDB）</li>
          <li>UI组件库（Button、Input、Modal）</li>
          <li>支付处理器（支付宝、微信、银联）</li>
          <li>文件解析器（JSON、XML、CSV）</li>
          <li>通知服务（邮件、短信、推送）</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 支付处理器工厂：</h4>
      <div class="demo-controls">
        <div class="payment-selector">
          <label>选择支付方式：</label>
          <select v-model="selectedPaymentType" class="payment-select">
            <option value="alipay">支付宝</option>
            <option value="wechat">微信支付</option>
            <option value="unionpay">银联支付</option>
          </select>
        </div>

        <div class="amount-input">
          <label>支付金额：</label>
          <input
            v-model.number="paymentAmount"
            type="number"
            min="0.01"
            step="0.01"
            placeholder="请输入金额"
            class="amount-field"
          />
        </div>

        <button
          @click="processPayment"
          class="demo-btn"
          :disabled="!paymentAmount"
        >
          创建支付处理器并处理支付
        </button>
        <button @click="clearResults" class="demo-btn clear">清空结果</button>
      </div>

      <div class="demo-output">
        <div class="processor-info" v-if="currentProcessor">
          <h5>当前支付处理器信息：</h5>
          <div class="processor-details">
            <p><strong>类型:</strong> {{ currentProcessor.getType() }}</p>
            <p><strong>提供商:</strong> {{ currentProcessor.getProvider() }}</p>
            <p>
              <strong>手续费率:</strong> {{ currentProcessor.getFeeRate() }}%
            </p>
          </div>
        </div>

        <div class="payment-results">
          <h5>支付处理结果：</h5>
          <div class="results-list">
            <div
              v-for="(result, index) in paymentResults"
              :key="index"
              class="result-entry"
            >
              <div class="result-header">
                <span class="result-type">{{ result.type }}</span>
                <span class="result-amount">¥{{ result.amount }}</span>
                <span class="result-status" :class="result.status">{{
                  result.status
                }}</span>
              </div>
              <div class="result-message">{{ result.message }}</div>
              <div class="result-time">{{ result.timestamp }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 解耦对象的创建和使用</li>
        <li>✅ 符合开闭原则，易于扩展</li>
        <li>✅ 集中管理对象创建逻辑</li>
        <li>✅ 隐藏复杂的创建过程</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 增加了系统复杂性</li>
        <li>❌ 每增加一个产品需要增加对应的工厂类</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { paymentFactory, type PaymentProcessorType } from "@/patterns/Factory";

// 响应式数据
const selectedPaymentType = ref<string>("alipay");
const paymentAmount = ref<number>(0);
const currentProcessor = ref<PaymentProcessorType | null>(null);
const paymentResults = ref<
  Array<{
    type: string;
    amount: number;
    status: string;
    message: string;
    timestamp: string;
  }>
>([]);

// 方法
const processPayment = () => {
  if (!paymentAmount.value || paymentAmount.value <= 0) {
    return;
  }

  try {
    // 使用工厂创建支付处理器
    currentProcessor.value = paymentFactory.createProcessor(
      selectedPaymentType.value
    );

    // 处理支付
    const message = currentProcessor.value.process(paymentAmount.value);

    // 添加结果
    paymentResults.value.unshift({
      type: currentProcessor.value.getType(),
      amount: paymentAmount.value,
      status: "success",
      message,
      timestamp: new Date().toLocaleString(),
    });
  } catch (error) {
    paymentResults.value.unshift({
      type: selectedPaymentType.value,
      amount: paymentAmount.value,
      status: "error",
      message: `支付失败: ${error}`,
      timestamp: new Date().toLocaleString(),
    });
  }
};

const clearResults = () => {
  paymentResults.value = [];
  currentProcessor.value = null;
};
</script>

<style lang="scss" scoped>
.factory-demo {
  .pattern-info {
    margin-bottom: 2rem;

    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #28a745;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;

    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .demo-controls {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;

      .payment-selector,
      .amount-input {
        margin-bottom: 1rem;

        label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 500;
          color: #333;
        }

        .payment-select,
        .amount-field {
          padding: 0.8rem;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 1rem;
          width: 200px;

          &:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      }

      .demo-btn {
        padding: 0.8rem 1.5rem;
        border: none;
        border-radius: 6px;
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-right: 1rem;

        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        &.clear {
          background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }
      }
    }

    .demo-output {
      .processor-info {
        background: #e3f2fd;
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
        border-left: 4px solid #2196f3;

        h5 {
          margin: 0 0 1rem 0;
          color: #333;
        }

        .processor-details p {
          margin: 0.5rem 0;
          color: #555;
        }
      }

      .payment-results {
        h5 {
          margin: 0 0 1rem 0;
          color: #333;
        }

        .results-list {
          max-height: 300px;
          overflow-y: auto;

          .result-entry {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;

            .result-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 0.5rem;

              .result-type {
                font-weight: 600;
                color: #333;
              }

              .result-amount {
                font-weight: 600;
                color: #28a745;
              }

              .result-status {
                padding: 0.25rem 0.8rem;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 500;

                &.success {
                  background: #d4edda;
                  color: #155724;
                }

                &.error {
                  background: #f8d7da;
                  color: #721c24;
                }
              }
            }

            .result-message {
              color: #666;
              margin-bottom: 0.5rem;
            }

            .result-time {
              font-size: 0.8rem;
              color: #999;
            }
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
      }
    }
  }
}
</style>
