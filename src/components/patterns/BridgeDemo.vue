<template>
  <div class="bridge-demo">
    <div class="pattern-info">
      <p class="description">
        桥接模式将抽象与实现分离，使它们可以独立变化。它通过组合而不是继承来连接抽象和实现，提供了更大的灵活性。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>图形绘制系统（不同API）</li>
          <li>数据库驱动程序</li>
          <li>跨平台应用开发</li>
          <li>消息发送系统</li>
          <li>设备驱动程序</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 图形绘制系统：</h4>
      <div class="demo-controls">
        <div class="api-selector">
          <label>选择绘制API：</label>
          <select v-model="selectedAPI" @change="switchAPI" class="api-select">
            <option value="canvas">Canvas API</option>
            <option value="svg">SVG API</option>
            <option value="webgl">WebGL API</option>
          </select>
        </div>
        <div class="shape-selector">
          <label>选择图形：</label>
          <select v-model="selectedShape" class="shape-select">
            <option value="circle">圆形</option>
            <option value="rectangle">矩形</option>
            <option value="triangle">三角形</option>
          </select>
        </div>
        <button @click="drawShape" class="demo-btn">绘制图形</button>
        <button @click="clearCanvas" class="demo-btn clear">清空画布</button>
      </div>

      <div class="demo-output">
        <div class="api-info">
          <h5>当前绘制API：</h5>
          <div class="api-card">
            <h6>{{ currentAPI?.getAPIName() || "未选择" }}</h6>
            <p>{{ getAPIDescription() }}</p>
          </div>
        </div>

        <div class="canvas-area">
          <h5>绘制结果：</h5>
          <div class="drawing-canvas">
            <div 
              v-for="(drawing, index) in drawings" 
              :key="index"
              class="drawing-item"
              :class="drawing.apiType"
            >
              <div class="drawing-header">
                <span class="shape-type">{{ drawing.shapeInfo }}</span>
                <span class="api-badge">{{ drawing.apiName }}</span>
              </div>
              <div class="drawing-content">
                {{ drawing.result }}
              </div>
            </div>
          </div>
        </div>

        <div class="shape-gallery" v-if="drawings.length > 0">
          <h5>图形库：</h5>
          <div class="gallery-grid">
            <div 
              v-for="(shape, index) in uniqueShapes" 
              :key="index"
              class="gallery-item"
            >
              <h6>{{ shape.name }}</h6>
              <div class="api-variants">
                <div 
                  v-for="variant in shape.variants" 
                  :key="variant.api"
                  class="variant-item"
                  :class="variant.api"
                >
                  <span class="variant-label">{{ variant.apiName }}</span>
                  <div class="variant-preview">{{ variant.preview }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="bridge-explanation">
          <h5>桥接模式说明：</h5>
          <div class="explanation-content">
            <div class="abstraction-side">
              <h6>抽象层 (图形)</h6>
              <ul>
                <li>Circle (圆形)</li>
                <li>Rectangle (矩形)</li>
                <li>Triangle (三角形)</li>
              </ul>
            </div>
            <div class="bridge-arrow">
              <span>🌉 桥接</span>
            </div>
            <div class="implementation-side">
              <h6>实现层 (绘制API)</h6>
              <ul>
                <li>Canvas API</li>
                <li>SVG API</li>
                <li>WebGL API</li>
              </ul>
            </div>
          </div>
          <p class="bridge-benefit">
            通过桥接模式，图形类型和绘制API可以独立变化，新增图形或API都不会影响对方。
          </p>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 分离抽象和实现，降低耦合</li>
        <li>✅ 抽象和实现可以独立扩展</li>
        <li>✅ 对客户端隐藏实现细节</li>
        <li>✅ 运行时可以切换实现</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 增加了系统的复杂性</li>
        <li>❌ 需要正确识别系统中的变化维度</li>
        <li>❌ 理解和设计难度较高</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";

// 绘制API接口
interface DrawingAPI {
  drawCircle(x: number, y: number, radius: number): string;
  drawRectangle(x: number, y: number, width: number, height: number): string;
  drawTriangle(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number): string;
  getAPIName(): string;
}

// Canvas API实现
class CanvasAPI implements DrawingAPI {
  drawCircle(x: number, y: number, radius: number): string {
    return `Canvas: ctx.arc(${x}, ${y}, ${radius}, 0, 2*Math.PI)`;
  }

  drawRectangle(x: number, y: number, width: number, height: number): string {
    return `Canvas: ctx.fillRect(${x}, ${y}, ${width}, ${height})`;
  }

  drawTriangle(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number): string {
    return `Canvas: ctx.moveTo(${x1}, ${y1}); ctx.lineTo(${x2}, ${y2}); ctx.lineTo(${x3}, ${y3})`;
  }

  getAPIName(): string {
    return "Canvas API";
  }
}

// SVG API实现
class SVGAPI implements DrawingAPI {
  drawCircle(x: number, y: number, radius: number): string {
    return `SVG: <circle cx="${x}" cy="${y}" r="${radius}" />`;
  }

  drawRectangle(x: number, y: number, width: number, height: number): string {
    return `SVG: <rect x="${x}" y="${y}" width="${width}" height="${height}" />`;
  }

  drawTriangle(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number): string {
    return `SVG: <polygon points="${x1},${y1} ${x2},${y2} ${x3},${y3}" />`;
  }

  getAPIName(): string {
    return "SVG API";
  }
}

// WebGL API实现
class WebGLAPI implements DrawingAPI {
  drawCircle(x: number, y: number, radius: number): string {
    return `WebGL: drawCircle(${x}, ${y}, ${radius}) with vertex shader`;
  }

  drawRectangle(x: number, y: number, width: number, height: number): string {
    return `WebGL: drawQuad(${x}, ${y}, ${width}, ${height}) with buffers`;
  }

  drawTriangle(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number): string {
    return `WebGL: drawTriangle([${x1},${y1}], [${x2},${y2}], [${x3},${y3}]) with vertices`;
  }

  getAPIName(): string {
    return "WebGL API";
  }
}

// 抽象图形类
abstract class Shape {
  protected drawingAPI: DrawingAPI;

  constructor(drawingAPI: DrawingAPI) {
    this.drawingAPI = drawingAPI;
  }

  abstract draw(): string;
  abstract getShapeInfo(): string;

  changeAPI(newAPI: DrawingAPI): void {
    this.drawingAPI = newAPI;
  }
}

// 具体图形实现
class Circle extends Shape {
  private x: number;
  private y: number;
  private radius: number;

  constructor(x: number, y: number, radius: number, drawingAPI: DrawingAPI) {
    super(drawingAPI);
    this.x = x;
    this.y = y;
    this.radius = radius;
  }

  draw(): string {
    return this.drawingAPI.drawCircle(this.x, this.y, this.radius);
  }

  getShapeInfo(): string {
    return `圆形 - 中心(${this.x}, ${this.y}), 半径${this.radius}`;
  }
}

class Rectangle extends Shape {
  private x: number;
  private y: number;
  private width: number;
  private height: number;

  constructor(x: number, y: number, width: number, height: number, drawingAPI: DrawingAPI) {
    super(drawingAPI);
    this.x = x;
    this.y = y;
    this.width = width;
    this.height = height;
  }

  draw(): string {
    return this.drawingAPI.drawRectangle(this.x, this.y, this.width, this.height);
  }

  getShapeInfo(): string {
    return `矩形 - 位置(${this.x}, ${this.y}), 尺寸${this.width}x${this.height}`;
  }
}

class Triangle extends Shape {
  private x1: number; private y1: number;
  private x2: number; private y2: number;
  private x3: number; private y3: number;

  constructor(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, drawingAPI: DrawingAPI) {
    super(drawingAPI);
    this.x1 = x1; this.y1 = y1;
    this.x2 = x2; this.y2 = y2;
    this.x3 = x3; this.y3 = y3;
  }

  draw(): string {
    return this.drawingAPI.drawTriangle(this.x1, this.y1, this.x2, this.y2, this.x3, this.y3);
  }

  getShapeInfo(): string {
    return `三角形 - 顶点(${this.x1},${this.y1}), (${this.x2},${this.y2}), (${this.x3},${this.y3})`;
  }
}

// 响应式数据
const selectedAPI = ref<string>("canvas");
const selectedShape = ref<string>("circle");
const currentAPI = ref<DrawingAPI | null>(null);
const drawings = ref<Array<{
  shapeInfo: string;
  result: string;
  apiName: string;
  apiType: string;
}>>([]);

// API实例
const apis = {
  canvas: new CanvasAPI(),
  svg: new SVGAPI(),
  webgl: new WebGLAPI()
};

// 方法
const switchAPI = () => {
  currentAPI.value = apis[selectedAPI.value as keyof typeof apis];
};

const getAPIDescription = (): string => {
  const descriptions = {
    canvas: "HTML5 Canvas 2D 绘图API，适用于动态图形和游戏",
    svg: "可缩放矢量图形，适用于静态图形和图标",
    webgl: "基于OpenGL ES的3D图形API，适用于高性能图形渲染"
  };
  return descriptions[selectedAPI.value as keyof typeof descriptions] || "";
};

const drawShape = () => {
  if (!currentAPI.value) {
    switchAPI();
  }

  let shape: Shape;
  const api = currentAPI.value!;

  switch (selectedShape.value) {
    case "circle":
      shape = new Circle(50, 50, 25, api);
      break;
    case "rectangle":
      shape = new Rectangle(20, 30, 60, 40, api);
      break;
    case "triangle":
      shape = new Triangle(50, 20, 30, 70, 70, 70, api);
      break;
    default:
      return;
  }

  drawings.value.push({
    shapeInfo: shape.getShapeInfo(),
    result: shape.draw(),
    apiName: api.getAPIName(),
    apiType: selectedAPI.value
  });
};

const clearCanvas = () => {
  drawings.value = [];
};

// 计算属性
const uniqueShapes = computed(() => {
  const shapeMap = new Map();
  
  drawings.value.forEach(drawing => {
    const shapeName = drawing.shapeInfo.split(' - ')[0];
    if (!shapeMap.has(shapeName)) {
      shapeMap.set(shapeName, {
        name: shapeName,
        variants: []
      });
    }
    
    const shape = shapeMap.get(shapeName);
    const existingVariant = shape.variants.find((v: any) => v.api === drawing.apiType);
    if (!existingVariant) {
      shape.variants.push({
        api: drawing.apiType,
        apiName: drawing.apiName,
        preview: drawing.result.substring(0, 30) + "..."
      });
    }
  });
  
  return Array.from(shapeMap.values());
});

// 初始化
switchAPI();
</script>

<style lang="scss" scoped>
.bridge-demo {
  .pattern-info {
    margin-bottom: 2rem;
    
    .description {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }
    
    .use-cases {
      h4 {
        color: #333;
        margin-bottom: 0.5rem;
      }
      
      ul {
        list-style-type: disc;
        padding-left: 1.5rem;
        
        li {
          margin-bottom: 0.3rem;
          color: #666;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
    }
    
    .demo-controls {
      display: flex;
      gap: 1rem;
      align-items: center;
      margin-bottom: 1.5rem;
      flex-wrap: wrap;
      
      .api-selector, .shape-selector {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        
        label {
          font-weight: 500;
          color: #555;
        }
        
        select {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
        }
      }
      
      .demo-btn {
        padding: 0.6rem 1.2rem;
        border: none;
        border-radius: 6px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        &.clear {
          background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }
      }
    }
    
    .demo-output {
      .api-info {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 0.5rem;
        }
        
        .api-card {
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 8px;
          border-left: 4px solid #667eea;
          
          h6 {
            margin: 0 0 0.5rem 0;
            color: #333;
          }
          
          p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
          }
        }
      }
      
      .canvas-area {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 0.5rem;
        }
        
        .drawing-canvas {
          border: 2px dashed #ddd;
          border-radius: 8px;
          padding: 1rem;
          min-height: 200px;
          background: #fafafa;
          
          .drawing-item {
            margin-bottom: 1rem;
            padding: 0.8rem;
            border-radius: 6px;
            background: white;
            border-left: 4px solid #ccc;
            
            &.canvas { border-left-color: #ff6b6b; }
            &.svg { border-left-color: #52c41a; }
            &.webgl { border-left-color: #1890ff; }
            
            .drawing-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 0.5rem;
              
              .shape-type {
                font-weight: 500;
                color: #333;
              }
              
              .api-badge {
                padding: 0.2rem 0.5rem;
                border-radius: 12px;
                font-size: 0.8rem;
                color: white;
                
                .canvas & { background: #ff6b6b; }
                .svg & { background: #52c41a; }
                .webgl & { background: #1890ff; }
              }
            }
            
            .drawing-content {
              font-family: 'Courier New', monospace;
              font-size: 0.9rem;
              color: #666;
              background: #f5f5f5;
              padding: 0.5rem;
              border-radius: 4px;
            }
          }
        }
      }
      
      .shape-gallery {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 0.5rem;
        }
        
        .gallery-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 1rem;
          
          .gallery-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1rem;
            background: white;
            
            h6 {
              margin: 0 0 0.5rem 0;
              color: #333;
            }
            
            .api-variants {
              .variant-item {
                margin-bottom: 0.5rem;
                padding: 0.5rem;
                border-radius: 4px;
                background: #f8f9fa;
                
                &.canvas { border-left: 3px solid #ff6b6b; }
                &.svg { border-left: 3px solid #52c41a; }
                &.webgl { border-left: 3px solid #1890ff; }
                
                .variant-label {
                  font-size: 0.8rem;
                  font-weight: 500;
                  color: #333;
                  display: block;
                  margin-bottom: 0.3rem;
                }
                
                .variant-preview {
                  font-family: 'Courier New', monospace;
                  font-size: 0.8rem;
                  color: #666;
                }
              }
            }
          }
        }
      }
      
      .bridge-explanation {
        background: linear-gradient(135deg, #f0f2ff, #ffffff);
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e0e7ff;
        
        h5 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .explanation-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 1rem;
          
          .abstraction-side, .implementation-side {
            flex: 1;
            
            h6 {
              color: #333;
              margin: 0 0 0.5rem 0;
              font-size: 1rem;
            }
            
            ul {
              list-style-type: disc;
              padding-left: 1rem;
              margin: 0;
              
              li {
                color: #666;
                margin-bottom: 0.3rem;
              }
            }
          }
          
          .bridge-arrow {
            flex: 0 0 auto;
            text-align: center;
            margin: 0 1rem;
            
            span {
              display: inline-block;
              padding: 0.5rem 1rem;
              background: #667eea;
              color: white;
              border-radius: 20px;
              font-weight: 500;
              font-size: 0.9rem;
            }
          }
        }
        
        .bridge-benefit {
          color: #555;
          font-style: italic;
          margin: 0;
          text-align: center;
        }
      }
    }
  }

  .advantages {
    h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 0.3rem;
        padding-left: 1rem;
        color: #666;
      }
    }
  }
}
</style>
