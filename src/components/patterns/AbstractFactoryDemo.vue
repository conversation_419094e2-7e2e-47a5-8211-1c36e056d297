<template>
  <div class="abstract-factory-demo">
    <div class="pattern-info">
      <p class="description">
        抽象工厂模式提供一个创建一系列相关或相互依赖对象的接口，而无需指定它们具体的类。它是工厂方法模式的升级版本，可以创建多个产品族。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>UI组件库的主题系统</li>
          <li>跨平台应用开发</li>
          <li>数据库驱动程序</li>
          <li>操作系统相关的API封装</li>
          <li>游戏中的不同风格资源</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - UI组件主题系统：</h4>

      <div class="theme-selector">
        <h5>🎨 选择主题</h5>
        <div class="theme-options">
          <div
            v-for="theme in availableThemes"
            :key="theme.id"
            class="theme-option"
            :class="{ active: selectedTheme === theme.id }"
            @click="selectTheme(theme.id)"
          >
            <div class="theme-preview" :style="theme.previewStyle"></div>
            <span class="theme-name">{{ theme.name }}</span>
          </div>
        </div>
      </div>

      <div class="components-showcase">
        <h5>🧩 组件展示</h5>
        <div class="component-grid">
          <div class="component-item">
            <h6>按钮组件</h6>
            <div class="component-preview">
              <div
                class="preview-element"
                :style="currentComponents.button?.getStyle()"
                v-html="currentComponents.button?.render()"
              ></div>
            </div>
            <div class="component-info">
              <span class="component-type">{{ currentComponents.button?.getType() }}</span>
            </div>
          </div>

          <div class="component-item">
            <h6>输入框组件</h6>
            <div class="component-preview">
              <div
                class="preview-element"
                :style="currentComponents.input?.getStyle()"
                v-html="currentComponents.input?.render()"
              ></div>
            </div>
            <div class="component-info">
              <span class="component-type">{{ currentComponents.input?.getType() }}</span>
            </div>
          </div>

          <div class="component-item">
            <h6>卡片组件</h6>
            <div class="component-preview">
              <div
                class="preview-element"
                :style="currentComponents.card?.getStyle()"
                v-html="currentComponents.card?.render()"
              ></div>
            </div>
            <div class="component-info">
              <span class="component-type">{{ currentComponents.card?.getType() }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="factory-info">
        <h5>🏭 工厂信息</h5>
        <div class="factory-details">
          <p><strong>当前工厂:</strong> {{ currentFactory?.getThemeName() }}</p>
          <p><strong>创建的组件族:</strong> 按钮、输入框、卡片</p>
          <p><strong>主题一致性:</strong> <span class="success">✅ 所有组件风格统一</span></p>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 确保产品族的一致性</li>
        <li>✅ 分离具体类的创建和使用</li>
        <li>✅ 易于交换产品系列</li>
        <li>✅ 有利于产品的一致性</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 难以支持新种类的产品</li>
        <li>❌ 增加了系统的抽象性和理解难度</li>
        <li>❌ 类的个数容易过多</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { 
  themeFactoryCreator, 
  type UIComponentFactoryType,
  type ButtonType,
  type InputType,
  type CardType
} from "@/patterns/AbstractFactory";

// 响应式数据
const selectedTheme = ref<string>("modern");
const currentFactory = ref<UIComponentFactoryType | null>(null);

// 可用主题配置
const availableThemes = [
  {
    id: "modern",
    name: "现代主题",
    previewStyle: "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px;"
  },
  {
    id: "classic", 
    name: "经典主题",
    previewStyle: "background: #f9f9f9; border: 2px solid #333; border-radius: 4px;"
  },
  {
    id: "dark",
    name: "暗黑主题", 
    previewStyle: "background: #2d3748; border: 1px solid #4a5568; border-radius: 6px;"
  }
];

// 计算属性
const currentComponents = computed(() => {
  if (!currentFactory.value) return {};
  
  return {
    button: currentFactory.value.createButton(),
    input: currentFactory.value.createInput(),
    card: currentFactory.value.createCard()
  };
});

// 方法
const selectTheme = (themeId: string) => {
  selectedTheme.value = themeId;
  currentFactory.value = themeFactoryCreator.createFactory(themeId);
};

// 初始化
onMounted(() => {
  selectTheme("modern");
});
</script>

<style lang="scss" scoped>
.abstract-factory-demo {
  .pattern-info {
    margin-bottom: 2rem;

    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #28a745;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;

    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .theme-selector {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      border-left: 4px solid #17a2b8;

      h5 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 1.2rem;
      }

      .theme-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .theme-option {
          display: flex;
          flex-direction: column;
          align-items: center;
          background: white;
          padding: 1rem;
          border-radius: 8px;
          border: 2px solid #e9ecef;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #17a2b8;
            box-shadow: 0 2px 8px rgba(23, 162, 184, 0.2);
          }

          &.active {
            border-color: #17a2b8;
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
          }

          .theme-preview {
            width: 60px;
            height: 40px;
            margin-bottom: 0.5rem;
          }

          .theme-name {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
          }
        }
      }
    }

    .components-showcase {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      border-left: 4px solid #6f42c1;

      h5 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 1.2rem;
      }

      .component-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;

        .component-item {
          background: white;
          padding: 1rem;
          border-radius: 8px;
          border: 1px solid #e9ecef;

          h6 {
            margin: 0 0 1rem 0;
            color: #333;
            font-size: 1rem;
          }

          .component-preview {
            margin-bottom: 1rem;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fafafa;
            border-radius: 6px;
            padding: 1rem;

            .preview-element {
              :deep(button), :deep(input), :deep(div) {
                margin: 0;
                display: block;
              }
            }
          }

          .component-info {
            .component-type {
              font-size: 0.85rem;
              color: #6f42c1;
              font-weight: 500;
            }
          }
        }
      }
    }

    .factory-info {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 1.5rem;
      border-radius: 8px;

      h5 {
        margin: 0 0 1rem 0;
        font-size: 1.2rem;
      }

      .factory-details {
        p {
          margin: 0.5rem 0;
          font-size: 0.95rem;

          .success {
            color: #d4edda;
            font-weight: 600;
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
      }
    }
  }
}
</style>
