<template>
  <div class="decorator-demo">
    <div class="pattern-info">
      <p class="description">
        装饰器模式允许向一个现有的对象添加新的功能，同时又不改变其结构。这种类型的设计模式属于结构型模式，它是作为现有的类的一个包装。装饰器模式创建了一个装饰类，用来包装原有的类，并在保持类方法签名完整性的前提下，提供了额外的功能。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>咖啡店点单系统（基础咖啡 + 各种配料）</li>
          <li>文本编辑器（基础文本 + 加粗、斜体、下划线）</li>
          <li>图形界面组件（基础组件 + 边框、滚动条、阴影）</li>
          <li>数据流处理（基础流 + 压缩、加密、缓存）</li>
          <li>Web请求处理（基础请求 + 认证、日志、缓存）</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 咖啡定制系统：</h4>

      <div class="coffee-builder">
        <div class="base-coffee">
          <h5>☕ 选择基础咖啡</h5>
          <div class="coffee-options">
            <div
              v-for="coffee in baseCoffees"
              :key="coffee.id"
              class="coffee-option"
              :class="{ active: selectedBaseCoffee === coffee.id }"
              @click="selectBaseCoffee(coffee.id)"
            >
              <span class="coffee-icon">{{ coffee.icon }}</span>
              <div class="coffee-info">
                <div class="coffee-name">{{ coffee.name }}</div>
                <div class="coffee-price">¥{{ coffee.price.toFixed(2) }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="decorators-section">
          <h5>🧂 添加配料装饰器</h5>
          <div class="decorators-grid">
            <div
              v-for="decorator in availableDecorators"
              :key="decorator.id"
              class="decorator-item"
              :class="{ selected: isDecoratorSelected(decorator.id) }"
              @click="toggleDecorator(decorator.id)"
            >
              <span class="decorator-icon">{{ decorator.icon }}</span>
              <div class="decorator-info">
                <div class="decorator-name">{{ decorator.name }}</div>
                <div class="decorator-price">
                  +¥{{ decorator.price.toFixed(2) }}
                </div>
              </div>
              <div
                class="decorator-count"
                v-if="getDecoratorCount(decorator.id) > 0"
              >
                {{ getDecoratorCount(decorator.id) }}
              </div>
            </div>
          </div>
          <button @click="clearDecorators" class="demo-btn clear">
            清除所有配料
          </button>
        </div>
      </div>

      <div class="order-summary">
        <div class="summary-card">
          <h5>📋 订单详情</h5>
          <div class="order-details">
            <div class="base-item">
              <span class="item-name">{{
                getCurrentBaseCoffee()?.name || "未选择"
              }}</span>
              <span class="item-price"
                >¥{{ getCurrentBaseCoffee()?.price.toFixed(2) || "0.00" }}</span
              >
            </div>

            <div
              v-for="decorator in selectedDecorators"
              :key="decorator.id"
              class="decorator-item-summary"
            >
              <span class="item-name"
                >{{ decorator.name }} × {{ decorator.count }}</span
              >
              <span class="item-price"
                >¥{{ (decorator.price * decorator.count).toFixed(2) }}</span
              >
            </div>

            <div class="total-line">
              <span class="total-label">总计:</span>
              <span class="total-price">¥{{ totalPrice.toFixed(2) }}</span>
            </div>
          </div>

          <div class="coffee-description">
            <h6>☕ 您的咖啡:</h6>
            <div class="description-text">{{ coffeeDescription }}</div>
          </div>

          <button
            @click="createCoffee"
            class="demo-btn order"
            :disabled="!selectedBaseCoffee"
          >
            制作这杯咖啡
          </button>
        </div>
      </div>

      <div class="creation-process" v-if="creationSteps.length > 0">
        <h5>🔄 制作过程</h5>
        <div class="process-steps">
          <div
            v-for="(step, index) in creationSteps"
            :key="index"
            class="process-step"
            :class="{ active: index === currentStep }"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">{{ step }}</div>
          </div>
        </div>
        <button @click="resetProcess" class="demo-btn reset">重新开始</button>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 比继承更加灵活</li>
        <li>✅ 可以动态添加或删除功能</li>
        <li>✅ 符合开闭原则</li>
        <li>✅ 可以用多个装饰器装饰同一个对象</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 会产生很多小对象</li>
        <li>❌ 装饰链过长时难以调试</li>
        <li>❌ 动态装饰时，多层装饰比较复杂</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { CoffeeFactory, type CoffeeType } from "@/patterns/Decorator";

// 响应式数据
const selectedBaseCoffee = ref<string>("");
const selectedDecorators = ref<
  Array<{ id: string; name: string; price: number; count: number }>
>([]);
const creationSteps = ref<string[]>([]);
const currentStep = ref<number>(-1);

// 基础咖啡配置
const baseCoffees = CoffeeFactory.getBaseCoffeeTypes();

// 装饰器配置
const availableDecorators = CoffeeFactory.getDecoratorTypes();

// 计算属性
const totalPrice = computed(() => {
  const basePrice = getCurrentBaseCoffee()?.price || 0;
  const decoratorPrice = selectedDecorators.value.reduce((sum, decorator) => {
    return sum + decorator.price * decorator.count;
  }, 0);
  return basePrice + decoratorPrice;
});

const coffeeDescription = computed(() => {
  const baseName = getCurrentBaseCoffee()?.name || "未选择咖啡";
  if (selectedDecorators.value.length === 0) {
    return baseName;
  }

  const decoratorNames = selectedDecorators.value
    .map((d) => (d.count > 1 ? `${d.name} × ${d.count}` : d.name))
    .join(" + ");

  return `${baseName} + ${decoratorNames}`;
});

// 方法
const selectBaseCoffee = (coffeeId: string) => {
  selectedBaseCoffee.value = coffeeId;
};

const getCurrentBaseCoffee = () => {
  return baseCoffees.find((coffee) => coffee.id === selectedBaseCoffee.value);
};

const toggleDecorator = (decoratorId: string) => {
  const existingIndex = selectedDecorators.value.findIndex(
    (d) => d.id === decoratorId
  );

  if (existingIndex >= 0) {
    selectedDecorators.value[existingIndex].count++;
  } else {
    const decorator = availableDecorators.find((d) => d.id === decoratorId);
    if (decorator) {
      selectedDecorators.value.push({
        id: decorator.id,
        name: decorator.name,
        price: decorator.price,
        count: 1,
      });
    }
  }
};

const isDecoratorSelected = (decoratorId: string) => {
  return selectedDecorators.value.some((d) => d.id === decoratorId);
};

const getDecoratorCount = (decoratorId: string) => {
  const decorator = selectedDecorators.value.find((d) => d.id === decoratorId);
  return decorator ? decorator.count : 0;
};

const clearDecorators = () => {
  selectedDecorators.value = [];
};

const createCoffee = () => {
  if (!selectedBaseCoffee.value) return;

  const baseCoffeeConfig = baseCoffees.find(
    (c) => c.id === selectedBaseCoffee.value
  );
  if (!baseCoffeeConfig) return;

  // 创建基础咖啡
  let coffee: CoffeeType = CoffeeFactory.createBaseCoffee(
    selectedBaseCoffee.value
  );
  const steps = [`制作基础${baseCoffeeConfig.name}`];

  // 应用装饰器
  selectedDecorators.value.forEach((decorator) => {
    const decoratorConfig = availableDecorators.find(
      (d) => d.id === decorator.id
    );
    if (decoratorConfig) {
      for (let i = 0; i < decorator.count; i++) {
        coffee = CoffeeFactory.createDecorator(decorator.id, coffee);
        steps.push(`添加${decoratorConfig.name}`);
      }
    }
  });

  steps.push(
    `完成制作: ${coffee.getDescription()}，总价: ¥${coffee
      .getCost()
      .toFixed(2)}`
  );

  creationSteps.value = steps;
  currentStep.value = 0;

  // 模拟制作过程
  const interval = setInterval(() => {
    currentStep.value++;
    if (currentStep.value >= steps.length) {
      clearInterval(interval);
      currentStep.value = steps.length - 1;
    }
  }, 1000);
};

const resetProcess = () => {
  creationSteps.value = [];
  currentStep.value = -1;
};
</script>

<style lang="scss" scoped>
.decorator-demo {
  .pattern-info {
    margin-bottom: 2rem;

    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #6f42c1;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;

    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .coffee-builder {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }

      .base-coffee {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        border-left: 4px solid #8b4513;

        h5 {
          margin: 0 0 1rem 0;
          color: #333;
          font-size: 1.2rem;
        }

        .coffee-options {
          display: grid;
          gap: 0.8rem;

          .coffee-option {
            display: flex;
            align-items: center;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              border-color: #8b4513;
              box-shadow: 0 2px 8px rgba(139, 69, 19, 0.2);
            }

            &.active {
              border-color: #8b4513;
              background: linear-gradient(135deg, #f4e4bc, #deb887);
              box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
            }

            .coffee-icon {
              font-size: 2rem;
              margin-right: 1rem;
            }

            .coffee-info {
              flex: 1;

              .coffee-name {
                font-weight: 600;
                color: #333;
                margin-bottom: 0.3rem;
              }

              .coffee-price {
                color: #8b4513;
                font-weight: 500;
              }
            }
          }
        }
      }

      .decorators-section {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        border-left: 4px solid #6f42c1;

        h5 {
          margin: 0 0 1rem 0;
          color: #333;
          font-size: 1.2rem;
        }

        .decorators-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 0.8rem;
          margin-bottom: 1rem;

          .decorator-item {
            position: relative;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;

            &:hover {
              border-color: #6f42c1;
              box-shadow: 0 2px 8px rgba(111, 66, 193, 0.2);
            }

            &.selected {
              border-color: #6f42c1;
              background: linear-gradient(135deg, #e8d5ff, #d1c4e9);
              box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
            }

            .decorator-icon {
              font-size: 1.5rem;
              display: block;
              margin-bottom: 0.5rem;
            }

            .decorator-info {
              .decorator-name {
                font-weight: 600;
                color: #333;
                font-size: 0.9rem;
                margin-bottom: 0.3rem;
              }

              .decorator-price {
                color: #6f42c1;
                font-weight: 500;
                font-size: 0.8rem;
              }
            }

            .decorator-count {
              position: absolute;
              top: -8px;
              right: -8px;
              background: #dc3545;
              color: white;
              border-radius: 50%;
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 0.8rem;
              font-weight: 600;
            }
          }
        }

        .demo-btn.clear {
          padding: 0.8rem 1.5rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #dc3545, #c82333);
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
          }
        }
      }
    }

    .order-summary {
      .summary-card {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;

        h5 {
          margin: 0 0 1rem 0;
          font-size: 1.2rem;
        }

        .order-details {
          margin-bottom: 1rem;

          .base-item,
          .decorator-item-summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);

            .item-name {
              flex: 1;
            }

            .item-price {
              font-weight: 600;
            }
          }

          .total-line {
            display: flex;
            justify-content: space-between;
            font-size: 1.2rem;
            font-weight: 700;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 2px solid rgba(255, 255, 255, 0.3);
          }
        }

        .coffee-description {
          background: rgba(255, 255, 255, 0.1);
          padding: 1rem;
          border-radius: 6px;
          margin-bottom: 1rem;

          h6 {
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
          }

          .description-text {
            font-family: "Monaco", "Consolas", monospace;
            font-size: 0.9rem;
            line-height: 1.4;
          }
        }

        .demo-btn.order {
          width: 100%;
          padding: 1rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #28a745, #20c997);
          color: white;
          cursor: pointer;
          font-weight: 600;
          font-size: 1.1rem;
          transition: all 0.3s ease;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }

    .creation-process {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #28a745;

      h5 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 1.2rem;
      }

      .process-steps {
        margin-bottom: 1rem;

        .process-step {
          display: flex;
          align-items: center;
          margin-bottom: 0.8rem;
          padding: 0.8rem;
          background: white;
          border-radius: 6px;
          border: 2px solid #e9ecef;
          transition: all 0.3s ease;

          &.active {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
          }

          .step-number {
            background: #6c757d;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 1rem;

            .active & {
              background: #28a745;
            }
          }

          .step-content {
            flex: 1;
            color: #333;
            font-weight: 500;
          }
        }
      }

      .demo-btn.reset {
        padding: 0.8rem 1.5rem;
        border: none;
        border-radius: 6px;
        background: linear-gradient(135deg, #6c757d, #5a6268);
        color: white;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
      }
    }
  }
}
</style>
