<template>
  <div class="proxy-demo">
    <div class="pattern-info">
      <p class="description">
        代理模式是一种结构型设计模式，它允许一个对象代表另一个对象，并控制对它的访问。
        代理对象可以在访问原对象前后执行额外操作，比如缓存、延迟加载、访问控制等，同时对客户端保持透明。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>虚拟代理：延迟加载大型对象，如图片懒加载</li>
          <li>缓存代理：存储结果并在后续请求中重用</li>
          <li>保护代理：控制对敏感对象的访问</li>
          <li>远程代理：表示远程对象</li>
          <li>日志代理：记录对象的访问</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 图片加载代理：</h4>

      <div class="demo-controls">
        <div class="toggle-container">
          <h5>选择模式：</h5>
          <div class="toggle-buttons">
            <button
              @click="useProxy = true"
              :class="{ active: useProxy }"
              class="toggle-btn"
            >
              使用代理模式
            </button>
            <button
              @click="useProxy = false"
              :class="{ active: !useProxy }"
              class="toggle-btn"
            >
              直接加载
            </button>
          </div>
          <p class="toggle-description">
            {{
              useProxy
                ? "代理模式：懒加载、缓存机制、优化性能"
                : "直接模式：立即创建所有实例，无缓存"
            }}
          </p>
        </div>

        <button @click="resetDemo" class="demo-btn reset">重置演示</button>
      </div>

      <div class="gallery-container">
        <h5>图片画廊：</h5>
        <div class="gallery-grid">
          <div
            v-for="(image, index) in displayedImages"
            :key="index"
            class="image-card"
            :class="{
              loaded: image.getStatus() === 'loaded',
              error: image.getStatus() === 'error',
            }"
            @click="loadImage(image)"
          >
            <div class="image-placeholder">
              <div
                v-if="image.getStatus() === 'unloaded'"
                class="status-icon unloaded"
              >
                ⏱️
              </div>
              <div
                v-else-if="image.getStatus() === 'loading'"
                class="status-icon loading"
              >
                ⌛
              </div>
              <div
                v-else-if="image.getStatus() === 'loaded'"
                class="status-icon loaded"
              >
                ✅
              </div>
              <div v-else class="status-icon error">❌</div>
            </div>
            <div class="image-info">
              <h6>{{ image.getTitle() }}</h6>
              <p class="image-details">
                {{ formatSize(image.getSize()) }} | {{ image.getStatus() }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div class="log-panel">
        <h5>操作日志：</h5>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-entry">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message" :class="log.type">{{ log.message }}</span>
          </div>
        </div>
      </div>

      <div class="performance-metrics" v-if="metrics.totalLoaded > 0">
        <h5>性能指标：</h5>
        <div class="metrics-grid">
          <div class="metric-card">
            <div class="metric-title">加载图片总数</div>
            <div class="metric-value">{{ metrics.totalLoaded }}</div>
          </div>
          <div class="metric-card">
            <div class="metric-title">从缓存加载次数</div>
            <div class="metric-value">{{ metrics.cachedLoads }}</div>
          </div>
          <div class="metric-card">
            <div class="metric-title">网络请求次数</div>
            <div class="metric-value">{{ metrics.networkRequests }}</div>
          </div>
          <div class="metric-card">
            <div class="metric-title">平均加载时间</div>
            <div class="metric-value">
              {{ metrics.avgLoadTime.toFixed(2) }} ms
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 控制对原始对象的访问</li>
        <li>✅ 延迟初始化（虚拟代理）</li>
        <li>✅ 缓存结果（缓存代理）</li>
        <li>✅ 对客户端透明</li>
        <li>✅ 分离关注点（增加新功能而不修改原对象）</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 增加系统复杂度</li>
        <li>❌ 可能引入额外的延迟</li>
        <li>❌ 有些模式（如远程代理）实现较为复杂</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from "vue";
import {
  createProxyImageGallery,
  createDirectImageGallery,
  type ImageInterface,
  ImageProxy,
  RealImage,
} from "@/patterns/Proxy";

// 响应式数据
const useProxy = ref<boolean>(true);
const proxyGallery = ref(createProxyImageGallery());
const directImages = ref<RealImage[]>([]);
const logs = ref<Array<{ time: string; message: string; type: string }>>([]);

// 性能指标
const metrics = reactive({
  totalLoaded: 0,
  cachedLoads: 0,
  networkRequests: 0,
  totalLoadTime: 0,
  avgLoadTime: 0,
});

// 计算属性
const displayedImages = computed<ImageInterface[]>(() => {
  if (useProxy.value) {
    return proxyGallery.value.getImages();
  } else {
    return directImages.value;
  }
});

// 初始化
onMounted(() => {
  directImages.value = createDirectImageGallery();
  addLog("系统初始化完成", "info");
  addLog(`当前模式: ${useProxy.value ? "代理模式" : "直接加载模式"}`, "info");
});

// 方法
const loadImage = async (image: ImageInterface) => {
  if (image.getStatus() === "loading") {
    addLog(`图片 ${image.getTitle()} 正在加载中...`, "warning");
    return;
  }

  if (image.getStatus() === "loaded") {
    addLog(`图片 ${image.getTitle()} 已经加载完成，无需重新加载`, "info");
    if (useProxy.value) {
      metrics.cachedLoads++;
      updateMetrics();
    }
    return;
  }

  const startTime = performance.now();
  addLog(`开始加载图片: ${image.getTitle()}`, "info");

  try {
    await image.display();
    const endTime = performance.now();
    const loadTime = endTime - startTime;

    addLog(
      `图片 ${image.getTitle()} 加载成功! 耗时: ${loadTime.toFixed(2)}ms`,
      "success"
    );

    // 更新性能指标
    metrics.totalLoaded++;
    if (!useProxy.value || !metrics.cachedLoads) {
      // 非代理或首次加载
      metrics.networkRequests++;
      metrics.totalLoadTime += loadTime;
    }
    updateMetrics();
  } catch (error) {
    addLog(`图片 ${image.getTitle()} 加载失败: ${error}`, "error");
  }
};

const resetDemo = () => {
  // 重置图片状态
  proxyGallery.value = createProxyImageGallery();
  directImages.value = createDirectImageGallery();

  // 清空日志
  logs.value = [];

  // 重置性能指标
  Object.assign(metrics, {
    totalLoaded: 0,
    cachedLoads: 0,
    networkRequests: 0,
    totalLoadTime: 0,
    avgLoadTime: 0,
  });

  addLog("演示已重置", "info");
  addLog(`当前模式: ${useProxy.value ? "代理模式" : "直接加载模式"}`, "info");
};

const addLog = (message: string, type: string = "info") => {
  const now = new Date();
  const timeStr = `${now.getHours().toString().padStart(2, "0")}:${now
    .getMinutes()
    .toString()
    .padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;

  logs.value.unshift({
    time: timeStr,
    message,
    type,
  });

  // 限制日志条数
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20);
  }
};

const formatSize = (sizeInKB: number): string => {
  if (sizeInKB < 1000) {
    return `${sizeInKB} KB`;
  } else {
    return `${(sizeInKB / 1000).toFixed(2)} MB`;
  }
};

const updateMetrics = () => {
  if (metrics.networkRequests > 0) {
    metrics.avgLoadTime = metrics.totalLoadTime / metrics.networkRequests;
  }
};
</script>

<style lang="scss" scoped>
.proxy-demo {
  .pattern-info {
    margin-bottom: 2rem;

    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #6c5ce7;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;

    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .demo-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;

      .toggle-container {
        h5 {
          margin: 0 0 0.5rem 0;
          color: #333;
        }

        .toggle-buttons {
          display: flex;
          gap: 0.5rem;

          .toggle-btn {
            padding: 0.6rem 1rem;
            border: 2px solid #ddd;
            border-radius: 6px;
            background: white;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;

            &.active {
              border-color: #6c5ce7;
              background: #6c5ce7;
              color: white;
            }
          }
        }

        .toggle-description {
          margin: 0.5rem 0 0 0;
          font-size: 0.85rem;
          color: #666;
        }
      }

      .demo-btn {
        padding: 0.8rem 1.5rem;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;

        &.reset {
          background: #e74c3c;
          color: white;

          &:hover {
            background: #c0392b;
            transform: translateY(-2px);
          }
        }
      }
    }

    .gallery-container {
      margin-bottom: 1.5rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      .gallery-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;

        .image-card {
          background: white;
          border: 2px solid #e9ecef;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
          }

          &.loaded {
            border-color: #2ecc71;
          }

          &.error {
            border-color: #e74c3c;
          }

          .image-placeholder {
            height: 150px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;

            .status-icon {
              font-size: 2rem;

              &.loading {
                animation: spin 1.5s linear infinite;
              }
            }
          }

          .image-info {
            padding: 1rem;

            h6 {
              margin: 0 0 0.5rem 0;
              color: #333;
              font-size: 1rem;
            }

            .image-details {
              margin: 0;
              color: #666;
              font-size: 0.85rem;
            }
          }
        }
      }
    }

    .log-panel {
      margin-bottom: 1.5rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      .log-container {
        background: #2d3436;
        border-radius: 8px;
        padding: 1rem;
        max-height: 200px;
        overflow-y: auto;

        .log-entry {
          margin-bottom: 0.5rem;
          font-family: "Monaco", "Consolas", monospace;
          font-size: 0.9rem;

          .log-time {
            color: #74b9ff;
            margin-right: 0.5rem;
          }

          .log-message {
            &.info {
              color: #dfe6e9;
            }

            &.success {
              color: #55efc4;
            }

            &.warning {
              color: #ffeaa7;
            }

            &.error {
              color: #ff7675;
            }
          }
        }
      }
    }

    .performance-metrics {
      h5 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .metric-card {
          background: linear-gradient(135deg, #6c5ce7, #a29bfe);
          padding: 1rem;
          border-radius: 8px;
          color: white;
          text-align: center;

          .metric-title {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 0.5rem;
          }

          .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
