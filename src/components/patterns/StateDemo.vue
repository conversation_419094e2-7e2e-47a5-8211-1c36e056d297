<template>
  <div class="state-demo">
    <div class="pattern-info">
      <p class="description">
        状态模式允许对象在内部状态改变时改变它的行为，对象看起来好像修改了它的类。
        这个演示展示了订单状态管理系统，每个状态都有不同的可执行操作。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>订单状态管理</li>
          <li>游戏角色状态</li>
          <li>工作流引擎</li>
          <li>状态机实现</li>
          <li>UI组件状态</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示：订单状态管理</h4>
      
      <!-- 创建订单 -->
      <div class="order-creation" v-if="!currentOrder">
        <h5>📝 创建新订单</h5>
        <div class="creation-form">
          <div class="form-group">
            <label>客户姓名：</label>
            <input v-model="newOrder.customerName" placeholder="请输入客户姓名" />
          </div>
          <div class="form-group">
            <label>订单金额：</label>
            <input 
              v-model.number="newOrder.amount" 
              type="number" 
              placeholder="请输入金额" 
              min="1"
            />
          </div>
          <div class="form-group">
            <label>商品列表：</label>
            <input 
              v-model="newOrder.itemsText" 
              placeholder="请输入商品，用逗号分隔" 
            />
          </div>
          <button @click="createOrder" class="demo-btn create" :disabled="!canCreateOrder">
            创建订单
          </button>
        </div>
      </div>

      <!-- 订单详情 -->
      <div class="order-details" v-if="currentOrder">
        <div class="order-header">
          <h5>📋 订单详情</h5>
          <button @click="resetDemo" class="demo-btn reset">创建新订单</button>
        </div>
        
        <div class="order-info">
          <div class="info-item">
            <span class="label">订单号：</span>
            <span class="value">{{ orderInfo.id }}</span>
          </div>
          <div class="info-item">
            <span class="label">客户：</span>
            <span class="value">{{ orderInfo.customerName }}</span>
          </div>
          <div class="info-item">
            <span class="label">金额：</span>
            <span class="value">¥{{ orderInfo.amount.toFixed(2) }}</span>
          </div>
          <div class="info-item">
            <span class="label">商品：</span>
            <span class="value">{{ orderInfo.items.join(', ') }}</span>
          </div>
        </div>

        <!-- 当前状态 -->
        <div class="current-state">
          <div class="state-display">
            <span class="state-icon">{{ currentState.getStateIcon() }}</span>
            <span 
              class="state-name" 
              :style="{ color: currentState.getStateColor() }"
            >
              {{ currentState.getStateName() }}
            </span>
          </div>
          <p class="state-description">{{ currentState.getDescription() }}</p>
        </div>

        <!-- 可执行操作 -->
        <div class="available-actions">
          <h6>可执行操作：</h6>
          <div class="actions-grid">
            <button
              v-for="action in availableActions"
              :key="action"
              @click="executeAction(action)"
              class="action-btn"
              :class="getActionClass(action)"
            >
              {{ getActionIcon(action) }} {{ action }}
            </button>
          </div>
          <p v-if="availableActions.length === 0" class="no-actions">
            当前状态无可执行操作
          </p>
        </div>

        <!-- 状态历史 -->
        <div class="state-history">
          <h6>📈 状态历史：</h6>
          <div class="history-timeline">
            <div
              v-for="(record, index) in orderHistory"
              :key="index"
              class="history-item"
            >
              <div class="history-time">
                {{ formatTime(record.timestamp) }}
              </div>
              <div class="history-content">
                <span class="history-state">{{ record.state }}</span>
                <span v-if="record.action" class="history-action">
                  - {{ record.action }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 封装状态相关的行为</li>
        <li>✅ 状态转换逻辑清晰</li>
        <li>✅ 易于添加新状态</li>
        <li>✅ 符合开闭原则</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 增加类的数量</li>
        <li>❌ 状态过多时复杂度增加</li>
        <li>❌ 状态转换关系需要明确定义</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { OrderFactory, type OrderContextType, type OrderStateType } from "@/patterns/State";

// 响应式数据
const currentOrder = ref<OrderContextType | null>(null);
const newOrder = ref({
  customerName: "",
  amount: 0,
  itemsText: ""
});

// 计算属性
const currentState = computed((): OrderStateType => {
  return currentOrder.value?.getState() || {} as OrderStateType;
});

const orderInfo = computed(() => {
  return currentOrder.value?.getOrderInfo() || {};
});

const availableActions = computed((): string[] => {
  return currentState.value?.getAvailableActions() || [];
});

const orderHistory = computed(() => {
  return orderInfo.value.history || [];
});

const canCreateOrder = computed(() => {
  return newOrder.value.customerName.trim() && 
         newOrder.value.amount > 0 && 
         newOrder.value.itemsText.trim();
});

// 方法
const createOrder = () => {
  if (!canCreateOrder.value) return;

  const orderId = `ORD-${Date.now()}`;
  const items = newOrder.value.itemsText.split(',').map(item => item.trim()).filter(Boolean);
  
  currentOrder.value = OrderFactory.createOrder(
    orderId,
    newOrder.value.amount,
    items,
    newOrder.value.customerName
  );
};

const executeAction = (action: string) => {
  if (currentOrder.value) {
    const success = currentOrder.value.executeAction(action);
    if (!success) {
      alert(`无法执行操作: ${action}`);
    }
  }
};

const resetDemo = () => {
  currentOrder.value = null;
  newOrder.value = {
    customerName: "",
    amount: 0,
    itemsText: ""
  };
};

const formatTime = (timestamp: Date): string => {
  return new Date(timestamp).toLocaleTimeString();
};

const getActionIcon = (action: string): string => {
  const iconMap: Record<string, string> = {
    '支付': '💳',
    '取消订单': '❌',
    '发货': '📦',
    '申请退款': '💰',
    '确认收货': '✅',
    '申请退货': '↩️',
    '同意退款': '✅',
    '拒绝退款': '❌',
    '同意退货': '✅',
    '拒绝退货': '❌',
    '评价商品': '⭐'
  };
  return iconMap[action] || '🔄';
};

const getActionClass = (action: string): string => {
  if (action.includes('取消') || action.includes('拒绝')) return 'danger';
  if (action.includes('同意') || action.includes('确认') || action.includes('支付')) return 'success';
  if (action.includes('申请')) return 'warning';
  return 'primary';
};
</script>

<style lang="scss" scoped>
.state-demo {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .pattern-info {
    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #667eea;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .demo-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .order-creation {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      .creation-form {
        display: grid;
        gap: 1rem;

        .form-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          label {
            font-weight: 500;
            color: #555;
          }

          input {
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;

            &:focus {
              outline: none;
              border-color: #667eea;
              box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
            }
          }
        }
      }
    }

    .order-details {
      .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;

        h5 {
          margin: 0;
          color: #333;
        }
      }

      .order-info {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 6px;
        margin-bottom: 1.5rem;

        .info-item {
          display: flex;
          margin-bottom: 0.5rem;

          .label {
            font-weight: 500;
            color: #666;
            min-width: 80px;
          }

          .value {
            color: #333;
          }
        }
      }

      .current-state {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;

        .state-display {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          margin-bottom: 0.5rem;

          .state-icon {
            font-size: 1.5rem;
          }

          .state-name {
            font-size: 1.2rem;
            font-weight: 600;
          }
        }

        .state-description {
          margin: 0;
          opacity: 0.9;
        }
      }

      .available-actions {
        margin-bottom: 1.5rem;

        h6 {
          margin: 0 0 1rem 0;
          color: #333;
        }

        .actions-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 0.8rem;
        }

        .no-actions {
          color: #666;
          font-style: italic;
          margin: 0;
        }
      }

      .state-history {
        h6 {
          margin: 0 0 1rem 0;
          color: #333;
        }

        .history-timeline {
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 6px;
          max-height: 200px;
          overflow-y: auto;

          .history-item {
            display: flex;
            gap: 1rem;
            margin-bottom: 0.8rem;
            padding-bottom: 0.8rem;
            border-bottom: 1px solid #e9ecef;

            &:last-child {
              border-bottom: none;
              margin-bottom: 0;
              padding-bottom: 0;
            }

            .history-time {
              font-size: 0.8rem;
              color: #666;
              min-width: 80px;
            }

            .history-content {
              .history-state {
                font-weight: 500;
                color: #333;
              }

              .history-action {
                color: #666;
                font-size: 0.9rem;
              }
            }
          }
        }
      }
    }
  }

  .demo-btn {
    padding: 0.8rem 1rem;
    border: none;
    border-radius: 6px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.9rem;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    &.create {
      background: linear-gradient(135deg, #10b981, #059669);
    }

    &.reset {
      background: linear-gradient(135deg, #6b7280, #4b5563);
      padding: 0.5rem 1rem;
      font-size: 0.8rem;
    }
  }

  .action-btn {
    padding: 0.6rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.85rem;

    &.primary {
      background: #667eea;
      color: white;
    }

    &.success {
      background: #10b981;
      color: white;
    }

    &.warning {
      background: #f59e0b;
      color: white;
    }

    &.danger {
      background: #ef4444;
      color: white;
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}
</style>
