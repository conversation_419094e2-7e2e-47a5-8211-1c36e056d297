<template>
  <div class="interpreter-demo">
    <div class="pattern-info">
      <p class="description">
        解释器模式给定一个语言，定义它的文法的一种表示，并定义一个解释器，这个解释器使用该表示来解释语言中的句子。
      </p>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 数学表达式计算器：</h4>
      <div class="demo-controls">
        <div class="expression-input">
          <h6>表达式输入</h6>
          <input 
            v-model="expression" 
            placeholder="输入数学表达式，如: 2 + 3 * (x - 1)"
            class="expression-field"
            @keyup.enter="calculateExpression"
          />
          <button @click="calculateExpression" class="demo-btn calculate">计算</button>
        </div>

        <div class="variables-panel">
          <h6>变量设置</h6>
          <div class="variable-form">
            <input v-model="variableName" placeholder="变量名" class="variable-input" />
            <input v-model.number="variableValue" type="number" placeholder="值" class="variable-input" />
            <button @click="setVariable" class="demo-btn">设置变量</button>
          </div>
          
          <div class="variables-list">
            <div v-for="[name, value] in variables" :key="name" class="variable-item">
              <span class="var-name">{{ name }}</span>
              <span class="var-value">= {{ value }}</span>
              <button @click="removeVariable(name)" class="remove-btn">×</button>
            </div>
          </div>
        </div>
      </div>

      <div class="demo-output">
        <div class="result-display">
          <h5>计算结果</h5>
          <div v-if="result !== null" class="result-value">
            {{ expression }} = {{ result }}
          </div>
          <div v-if="error" class="error-message">
            错误: {{ error }}
          </div>
        </div>

        <div class="history-display">
          <h5>计算历史</h5>
          <div class="history-list">
            <div v-for="(record, index) in history" :key="index" class="history-item">
              {{ record }}
            </div>
          </div>
          <button @click="clearHistory" class="demo-btn clear">清空历史</button>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 可扩展性比较好，灵活</li>
        <li>✅ 增加了新的解释表达式的方式</li>
        <li>✅ 易于实现简单文法</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 可利用场景比较少</li>
        <li>❌ 对于复杂的文法比较难维护</li>
        <li>❌ 解释器模式会引起类膨胀</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ExpressionCalculator } from "../../patterns/Interpreter";

// 响应式数据
const expression = ref<string>('');
const variableName = ref<string>('');
const variableValue = ref<number>(0);
const result = ref<number | null>(null);
const error = ref<string>('');

// 创建计算器实例
const calculator = new ExpressionCalculator();

// 计算属性
const variables = computed(() => {
  return calculator.getVariables();
});

const history = computed(() => {
  return calculator.getHistory();
});

// 方法
const calculateExpression = () => {
  if (!expression.value.trim()) {
    error.value = '请输入表达式';
    return;
  }

  try {
    result.value = calculator.calculate(expression.value);
    error.value = '';
  } catch (err) {
    error.value = err instanceof Error ? err.message : String(err);
    result.value = null;
  }
};

const setVariable = () => {
  if (!variableName.value.trim()) {
    alert('请输入变量名');
    return;
  }

  calculator.setVariable(variableName.value, variableValue.value);
  variableName.value = '';
  variableValue.value = 0;
};

const removeVariable = (name: string) => {
  // 注意：这里需要重新创建计算器实例来移除变量
  // 因为当前的实现没有提供移除变量的方法
  const newCalculator = new ExpressionCalculator();
  for (const [varName, value] of variables.value) {
    if (varName !== name) {
      newCalculator.setVariable(varName, value);
    }
  }
  // 这里需要替换计算器实例，但为了简化演示，我们只是提示
  alert(`变量 ${name} 已移除（演示版本）`);
};

const clearHistory = () => {
  calculator.clearHistory();
};
</script>

<style lang="scss" scoped>
.interpreter-demo {
  .pattern-info {
    margin-bottom: 2rem;
    
    .description {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }
  }

  .demo-section {
    margin-bottom: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
    }
    
    .demo-controls {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
      
      .expression-input, .variables-panel {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        
        h6 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .expression-field, .variable-input {
          width: 100%;
          padding: 0.8rem;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;
          margin-bottom: 1rem;
        }
        
        .variable-form {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          margin-bottom: 1rem;
        }
        
        .variables-list {
          .variable-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            
            .var-name {
              font-weight: 500;
              color: #333;
            }
            
            .var-value {
              color: #666;
            }
            
            .remove-btn {
              background: #ff4d4f;
              color: white;
              border: none;
              border-radius: 50%;
              width: 20px;
              height: 20px;
              cursor: pointer;
              font-size: 0.8rem;
            }
          }
        }
        
        .demo-btn {
          padding: 0.6rem 1rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.calculate {
            background: linear-gradient(135deg, #52c41a, #389e0d);
          }
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
    
    .demo-output {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      
      .result-display, .history-display {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        
        h5 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .result-value {
          font-size: 1.2rem;
          font-weight: 600;
          color: #52c41a;
          padding: 1rem;
          background: #f6ffed;
          border-radius: 6px;
          text-align: center;
        }
        
        .error-message {
          color: #ff4d4f;
          background: #fff2f0;
          padding: 1rem;
          border-radius: 6px;
          text-align: center;
        }
        
        .history-list {
          max-height: 200px;
          overflow-y: auto;
          margin-bottom: 1rem;
          
          .history-item {
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
          }
        }
        
        .clear {
          background: #ff6b6b;
          
          &:hover {
            background: #ff5252;
          }
        }
      }
    }
  }

  .advantages {
    h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 0.3rem;
        padding-left: 1rem;
        color: #666;
      }
    }
  }
}
</style>
