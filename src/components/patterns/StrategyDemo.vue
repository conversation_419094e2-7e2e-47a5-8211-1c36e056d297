<template>
  <div class="strategy-demo">
    <div class="pattern-info">
      <p class="description">
        策略模式定义了算法族，分别封装起来，让它们之间可以互相替换。此模式让算法的变化独立于使用算法的客户。它将行为和环境分离，使得算法可以在运行时动态切换。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>支付方式选择（信用卡、支付宝、微信）</li>
          <li>排序算法（快排、归并、冒泡）</li>
          <li>压缩算法（ZIP、RAR、7Z）</li>
          <li>折扣计算（会员折扣、节日折扣、新用户折扣）</li>
          <li>路径规划（最短路径、最快路径、最省钱路径）</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 电商折扣计算系统：</h4>

      <div class="shopping-cart">
        <h5>🛒 购物车</h5>
        <div class="cart-controls">
          <div class="add-item">
            <input
              v-model="newItemName"
              placeholder="商品名称"
              class="item-input"
              @keyup.enter="addItem"
            />
            <input
              v-model.number="newItemPrice"
              type="number"
              min="0.01"
              step="0.01"
              placeholder="价格"
              class="price-input"
              @keyup.enter="addItem"
            />
            <button @click="addItem" class="demo-btn add">添加商品</button>
          </div>
          <button @click="clearCart" class="demo-btn clear">清空购物车</button>
        </div>

        <div class="cart-items">
          <div
            v-for="(item, index) in cartItems"
            :key="index"
            class="cart-item"
          >
            <span class="item-name">{{ item.name }}</span>
            <span class="item-price">¥{{ item.price.toFixed(2) }}</span>
            <button @click="removeItem(index)" class="remove-btn">×</button>
          </div>
          <div v-if="cartItems.length === 0" class="empty-cart">
            购物车为空，请添加商品
          </div>
        </div>

        <div class="cart-total">
          <strong>原价总计: ¥{{ originalTotal.toFixed(2) }}</strong>
        </div>
      </div>

      <div class="discount-strategies">
        <h5>💰 选择折扣策略</h5>
        <div class="strategy-selector">
          <div
            v-for="strategy in discountStrategies"
            :key="strategy.id"
            class="strategy-option"
            :class="{ active: selectedStrategy === strategy.id }"
            @click="selectStrategy(strategy.id)"
          >
            <div class="strategy-header">
              <span class="strategy-icon">{{ strategy.icon }}</span>
              <span class="strategy-name">{{ strategy.name }}</span>
            </div>
            <div class="strategy-desc">{{ strategy.description }}</div>
            <div class="strategy-condition">{{ strategy.condition }}</div>
          </div>
        </div>
      </div>

      <div class="calculation-result">
        <div class="result-card">
          <h5>💳 结算结果</h5>
          <div class="result-details">
            <div class="result-row">
              <span>原价总计:</span>
              <span>¥{{ originalTotal.toFixed(2) }}</span>
            </div>
            <div class="result-row discount">
              <span>折扣策略:</span>
              <span>{{ getCurrentStrategy()?.name || "无" }}</span>
            </div>
            <div class="result-row discount">
              <span>优惠金额:</span>
              <span>-¥{{ discountAmount.toFixed(2) }}</span>
            </div>
            <div class="result-row total">
              <span>最终价格:</span>
              <span>¥{{ finalTotal.toFixed(2) }}</span>
            </div>
            <div class="result-row savings" v-if="discountAmount > 0">
              <span>节省:</span>
              <span
                >{{
                  ((discountAmount / originalTotal) * 100).toFixed(1)
                }}%</span
              >
            </div>
          </div>

          <div class="calculation-process" v-if="calculationDetails">
            <h6>计算过程:</h6>
            <div class="process-text">{{ calculationDetails }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 算法可以自由切换</li>
        <li>✅ 避免使用多重条件判断</li>
        <li>✅ 扩展性良好，符合开闭原则</li>
        <li>✅ 算法的使用和实现分离</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 客户端必须知道所有的策略类</li>
        <li>❌ 策略类数量增多</li>
        <li>❌ 所有策略类都需要对外暴露</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import {
  StrategyFactory,
  DiscountCalculator,
  type CartItemType,
} from "@/patterns/Strategy";

// 响应式数据
const cartItems = ref<CartItemType[]>([]);
const newItemName = ref<string>("");
const newItemPrice = ref<number>(0);
const selectedStrategy = ref<string>("none");

// 折扣策略配置
const discountStrategies = [
  {
    id: "none",
    name: "无折扣",
    description: "原价购买",
    condition: "无条件限制",
    icon: "💸",
    strategy: StrategyFactory.createStrategy("none"),
  },
  {
    id: "percentage",
    name: "全场9折",
    description: "所有商品享受9折优惠",
    condition: "无最低消费限制",
    icon: "🏷️",
    strategy: StrategyFactory.createStrategy("percentage"),
  },
  {
    id: "fixed",
    name: "满200减50",
    description: "满200元立减50元",
    condition: "最低消费200元",
    icon: "🎫",
    strategy: StrategyFactory.createStrategy("fixed"),
  },
  {
    id: "vip",
    name: "VIP会员",
    description: "VIP专享85折，3件以上额外5%折扣",
    condition: "VIP会员专享",
    icon: "👑",
    strategy: StrategyFactory.createStrategy("vip"),
  },
];

// 计算器实例
const calculator = new DiscountCalculator(discountStrategies[0].strategy);

// 计算属性
const originalTotal = computed(() => {
  return cartItems.value.reduce((sum, item) => sum + item.price, 0);
});

const discountResult = computed(() => {
  return calculator.calculateDiscount(originalTotal.value, cartItems.value);
});

const discountAmount = computed(() => discountResult.value.discount);
const calculationDetails = computed(() => discountResult.value.details);
const finalTotal = computed(() =>
  Math.max(0, originalTotal.value - discountAmount.value)
);

// 方法
const addItem = () => {
  if (newItemName.value.trim() && newItemPrice.value > 0) {
    cartItems.value.push({
      name: newItemName.value.trim(),
      price: newItemPrice.value,
    });
    newItemName.value = "";
    newItemPrice.value = 0;
  }
};

const removeItem = (index: number) => {
  cartItems.value.splice(index, 1);
};

const clearCart = () => {
  cartItems.value = [];
};

const selectStrategy = (strategyId: string) => {
  selectedStrategy.value = strategyId;
  const strategy = discountStrategies.find((s) => s.id === strategyId);
  if (strategy) {
    calculator.setStrategy(strategy.strategy);
  }
};

const getCurrentStrategy = () => {
  return discountStrategies.find((s) => s.id === selectedStrategy.value);
};

// 初始化一些示例商品
cartItems.value = [
  { name: "iPhone 15", price: 5999 },
  { name: "AirPods Pro", price: 1999 },
  { name: "保护壳", price: 99 },
];
</script>

<style lang="scss" scoped>
.strategy-demo {
  .pattern-info {
    margin-bottom: 2rem;

    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #fd7e14;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;

    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .shopping-cart {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      border-left: 4px solid #28a745;

      h5 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 1.2rem;
      }

      .cart-controls {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
        flex-wrap: wrap;
        align-items: center;

        .add-item {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;

          .item-input,
          .price-input {
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;

            &:focus {
              outline: none;
              border-color: #28a745;
              box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
            }
          }

          .item-input {
            min-width: 150px;
          }

          .price-input {
            width: 100px;
          }
        }

        .demo-btn {
          padding: 0.8rem 1.5rem;
          border: none;
          border-radius: 6px;
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;

          &.add {
            background: linear-gradient(135deg, #28a745, #20c997);
          }

          &.clear {
            background: linear-gradient(135deg, #dc3545, #c82333);
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }
        }
      }

      .cart-items {
        margin-bottom: 1rem;

        .cart-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: white;
          padding: 0.8rem;
          border-radius: 6px;
          margin-bottom: 0.5rem;
          border: 1px solid #e9ecef;

          .item-name {
            flex: 1;
            font-weight: 500;
            color: #333;
          }

          .item-price {
            font-weight: 600;
            color: #28a745;
            margin-right: 1rem;
          }

          .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 1rem;
            line-height: 1;

            &:hover {
              background: #c82333;
            }
          }
        }

        .empty-cart {
          text-align: center;
          color: #999;
          padding: 2rem;
          font-style: italic;
        }
      }

      .cart-total {
        text-align: right;
        font-size: 1.1rem;
        color: #333;
        padding-top: 1rem;
        border-top: 2px solid #dee2e6;
      }
    }

    .discount-strategies {
      margin-bottom: 1.5rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 1.2rem;
      }

      .strategy-selector {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;

        .strategy-option {
          background: white;
          border: 2px solid #e9ecef;
          border-radius: 8px;
          padding: 1rem;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #fd7e14;
            box-shadow: 0 4px 12px rgba(253, 126, 20, 0.2);
          }

          &.active {
            border-color: #fd7e14;
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            box-shadow: 0 4px 12px rgba(253, 126, 20, 0.3);
          }

          .strategy-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;

            .strategy-icon {
              font-size: 1.5rem;
              margin-right: 0.5rem;
            }

            .strategy-name {
              font-weight: 600;
              color: #333;
            }
          }

          .strategy-desc {
            color: #666;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
          }

          .strategy-condition {
            color: #999;
            font-size: 0.8rem;
            font-style: italic;
          }
        }
      }
    }

    .calculation-result {
      .result-card {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1.5rem;
        border-radius: 8px;

        h5 {
          margin: 0 0 1rem 0;
          font-size: 1.2rem;
        }

        .result-details {
          margin-bottom: 1rem;

          .result-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;

            &.discount {
              color: #ffd700;
            }

            &.total {
              font-size: 1.2rem;
              font-weight: 700;
              border-top: 1px solid rgba(255, 255, 255, 0.3);
              padding-top: 0.5rem;
              margin-top: 0.5rem;
            }

            &.savings {
              color: #90ee90;
              font-weight: 600;
            }
          }
        }

        .calculation-process {
          background: rgba(255, 255, 255, 0.1);
          padding: 1rem;
          border-radius: 6px;

          h6 {
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
          }

          .process-text {
            font-family: "Monaco", "Consolas", monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            white-space: pre-line;
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
      }
    }
  }
}
</style>
