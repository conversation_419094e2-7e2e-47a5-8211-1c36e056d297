<template>
  <div class="iterator-demo">
    <div class="pattern-info">
      <p class="description">
        迭代器模式提供一种方法顺序访问聚合对象中的各个元素，而又不暴露该对象的内部表示。它将遍历逻辑从聚合对象中分离出来。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>音乐播放器播放列表</li>
          <li>数据库结果集遍历</li>
          <li>文件系统目录遍历</li>
          <li>集合类的统一遍历</li>
          <li>分页数据处理</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 音乐播放器：</h4>
      <div class="demo-controls">
        <div class="playlist-management">
          <h6>播放列表管理</h6>
          <div class="add-song-form">
            <input v-model="newSong.title" placeholder="歌曲名称" class="song-input" />
            <input v-model="newSong.artist" placeholder="艺术家" class="song-input" />
            <input v-model.number="newSong.duration" type="number" placeholder="时长(秒)" class="song-input" />
            <select v-model="newSong.genre" class="genre-select">
              <option value="Pop">流行</option>
              <option value="Rock">摇滚</option>
              <option value="Jazz">爵士</option>
              <option value="Classical">古典</option>
              <option value="Electronic">电子</option>
            </select>
            <button @click="addSong" class="demo-btn">添加歌曲</button>
          </div>
        </div>

        <div class="player-controls">
          <h6>播放控制</h6>
          <div class="play-mode-selector">
            <label>播放模式:</label>
            <select v-model="playMode" @change="changePlayMode" class="mode-select">
              <option value="sequential">顺序播放</option>
              <option value="random">随机播放</option>
              <option value="genre">按流派播放</option>
            </select>
            <select v-if="playMode === 'genre'" v-model="selectedGenre" @change="changePlayMode" class="genre-filter">
              <option v-for="genre in availableGenres" :key="genre" :value="genre">{{ genre }}</option>
            </select>
          </div>
          <div class="player-buttons">
            <button @click="playNext" :disabled="!canPlayNext" class="demo-btn play">下一首</button>
            <button @click="resetPlayer" class="demo-btn">重置</button>
            <button @click="addRandomSongs" class="demo-btn">添加随机歌曲</button>
          </div>
        </div>
      </div>

      <div class="demo-output">
        <div class="current-playing">
          <h5>当前播放</h5>
          <div class="now-playing-card">
            <div v-if="currentSong" class="song-info">
              <h6>{{ currentSong.title }}</h6>
              <p>{{ currentSong.artist }} - {{ currentSong.getDurationString() }}</p>
              <span class="genre-tag" :class="currentSong.genre.toLowerCase()">{{ currentSong.genre }}</span>
            </div>
            <div v-else class="no-song">
              <p>没有正在播放的歌曲</p>
            </div>
            <div class="play-status">
              <span class="mode-indicator">{{ getModeDisplayName() }}</span>
              <span class="progress">{{ playedCount }}/{{ totalSongs }}</span>
            </div>
          </div>
        </div>

        <div class="playlist-display">
          <h5>播放列表 ({{ playlist.getCount() }}首歌曲)</h5>
          <div class="playlist-container">
            <div 
              v-for="(song, index) in playlistSongs" 
              :key="song.id"
              class="song-item"
              :class="{ current: currentSong && currentSong.id === song.id }"
            >
              <div class="song-index">{{ index + 1 }}</div>
              <div class="song-details">
                <div class="song-title">{{ song.title }}</div>
                <div class="song-meta">{{ song.artist }} - {{ song.getDurationString() }}</div>
              </div>
              <div class="song-genre">
                <span class="genre-tag" :class="song.genre.toLowerCase()">{{ song.genre }}</span>
              </div>
              <button @click="removeSong(song.id)" class="remove-btn">×</button>
            </div>
          </div>
        </div>

        <div class="iterator-explanation">
          <h5>迭代器模式说明</h5>
          <div class="explanation-content">
            <div class="iterator-types">
              <div class="iterator-type">
                <h6>🔄 顺序迭代器</h6>
                <p>按添加顺序依次播放歌曲</p>
                <ul>
                  <li>维护当前位置索引</li>
                  <li>支持前进和重置操作</li>
                  <li>到达末尾时停止</li>
                </ul>
              </div>
              <div class="iterator-type">
                <h6>🎲 随机迭代器</h6>
                <p>随机选择未播放的歌曲</p>
                <ul>
                  <li>记录已访问的歌曲</li>
                  <li>避免重复播放</li>
                  <li>全部播放完后结束</li>
                </ul>
              </div>
              <div class="iterator-type">
                <h6>🎵 过滤迭代器</h6>
                <p>只播放指定流派的歌曲</p>
                <ul>
                  <li>预先过滤符合条件的歌曲</li>
                  <li>在过滤结果中顺序遍历</li>
                  <li>支持动态切换过滤条件</li>
                </ul>
              </div>
            </div>
            
            <div class="pattern-benefit">
              <strong>核心优势:</strong> 统一的遍历接口让客户端无需了解集合的内部结构，不同的迭代策略可以独立变化和扩展。
            </div>
          </div>
        </div>

        <div class="statistics">
          <h5>播放统计</h5>
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">总歌曲数:</span>
              <span class="stat-value">{{ totalSongs }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已播放:</span>
              <span class="stat-value">{{ playedCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总时长:</span>
              <span class="stat-value">{{ formatTotalDuration() }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">流派数:</span>
              <span class="stat-value">{{ availableGenres.length }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 支持以不同方式遍历聚合对象</li>
        <li>✅ 简化了聚合类的接口</li>
        <li>✅ 在同一个聚合上可以有多个遍历</li>
        <li>✅ 增加新的聚合类和迭代器类都很方便</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 对于比较简单的遍历，使用迭代器方式较为繁琐</li>
        <li>❌ 增加了系统的复杂性</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { Song, Playlist, MusicPlayer } from "../../patterns/Iterator";

// 响应式数据
const newSong = reactive({
  title: '',
  artist: '',
  duration: 180,
  genre: 'Pop'
});

const playMode = ref<'sequential' | 'random' | 'genre'>('sequential');
const selectedGenre = ref<string>('Pop');
const currentSong = ref<Song | null>(null);
const playedCount = ref<number>(0);

// 创建播放列表和播放器
const playlist = reactive(new Playlist('我的播放列表'));
const player = reactive(new MusicPlayer());

// 计算属性
const playlistSongs = computed(() => playlist.getItems());
const totalSongs = computed(() => playlist.getCount());
const availableGenres = computed(() => playlist.getGenres());
const canPlayNext = computed(() => player.hasNext());

// 方法
const addSong = () => {
  if (!newSong.title.trim() || !newSong.artist.trim()) {
    alert('请填写歌曲名称和艺术家');
    return;
  }

  const song = new Song(
    Date.now().toString(),
    newSong.title,
    newSong.artist,
    newSong.duration,
    newSong.genre
  );

  playlist.addSong(song);
  
  // 重新设置播放列表到播放器
  player.setPlaylist(playlist);
  
  // 重置表单
  newSong.title = '';
  newSong.artist = '';
  newSong.duration = 180;
};

const removeSong = (songId: string) => {
  playlist.removeSong(songId);
  player.setPlaylist(playlist);
  
  // 如果删除的是当前播放的歌曲，清空当前歌曲
  if (currentSong.value && currentSong.value.id === songId) {
    currentSong.value = null;
  }
};

const changePlayMode = () => {
  if (playMode.value === 'genre' && selectedGenre.value) {
    player.setPlayMode('genre', selectedGenre.value);
  } else {
    player.setPlayMode(playMode.value);
  }
  resetPlayback();
};

const playNext = () => {
  const nextSong = player.next();
  if (nextSong) {
    currentSong.value = nextSong;
    playedCount.value++;
  }
};

const resetPlayer = () => {
  player.reset();
  resetPlayback();
};

const resetPlayback = () => {
  currentSong.value = null;
  playedCount.value = 0;
};

const addRandomSongs = () => {
  const sampleSongs = [
    { title: 'Shape of You', artist: 'Ed Sheeran', duration: 233, genre: 'Pop' },
    { title: 'Bohemian Rhapsody', artist: 'Queen', duration: 355, genre: 'Rock' },
    { title: 'Take Five', artist: 'Dave Brubeck', duration: 324, genre: 'Jazz' },
    { title: 'Für Elise', artist: 'Beethoven', duration: 196, genre: 'Classical' },
    { title: 'Strobe', artist: 'Deadmau5', duration: 645, genre: 'Electronic' },
    { title: 'Imagine', artist: 'John Lennon', duration: 183, genre: 'Rock' },
    { title: 'Billie Jean', artist: 'Michael Jackson', duration: 294, genre: 'Pop' },
    { title: 'Blue in Green', artist: 'Miles Davis', duration: 337, genre: 'Jazz' }
  ];

  // 随机选择3-5首歌曲
  const count = Math.floor(Math.random() * 3) + 3;
  const shuffled = [...sampleSongs].sort(() => Math.random() - 0.5);
  
  for (let i = 0; i < count && i < shuffled.length; i++) {
    const songData = shuffled[i];
    const song = new Song(
      Date.now().toString() + i,
      songData.title,
      songData.artist,
      songData.duration,
      songData.genre
    );
    playlist.addSong(song);
  }
  
  player.setPlaylist(playlist);
};

const getModeDisplayName = (): string => {
  switch (playMode.value) {
    case 'sequential': return '顺序播放';
    case 'random': return '随机播放';
    case 'genre': return `${selectedGenre.value}流派`;
    default: return '未知模式';
  }
};

const formatTotalDuration = (): string => {
  const totalSeconds = playlist.getTotalDuration();
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
};

// 初始化
onMounted(() => {
  player.setPlaylist(playlist);
  addRandomSongs();
});
</script>

<style lang="scss" scoped>
.iterator-demo {
  .pattern-info {
    margin-bottom: 2rem;
    
    .description {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }
    
    .use-cases {
      h4 {
        color: #333;
        margin-bottom: 0.5rem;
      }
      
      ul {
        list-style-type: disc;
        padding-left: 1.5rem;
        
        li {
          margin-bottom: 0.3rem;
          color: #666;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
    }
    
    .demo-controls {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
      
      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
      }
      
      .playlist-management, .player-controls {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        
        h6 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .add-song-form {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          
          .song-input, .genre-select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
          }
          
          .demo-btn {
            padding: 0.6rem;
            border: none;
            border-radius: 6px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            }
          }
        }
        
        .play-mode-selector {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          margin-bottom: 1rem;
          
          label {
            font-weight: 500;
            color: #555;
          }
          
          .mode-select, .genre-filter {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
          }
        }
        
        .player-buttons {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;
          
          .demo-btn {
            flex: 1;
            padding: 0.6rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            
            &.play {
              background: linear-gradient(135deg, #52c41a, #389e0d);
              color: white;
              
              &:disabled {
                background: #ccc;
                cursor: not-allowed;
              }
            }
            
            &:not(.play) {
              background: #667eea;
              color: white;
            }
            
            &:hover:not(:disabled) {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
          }
        }
      }
    }
    
    .demo-output {
      .current-playing {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .now-playing-card {
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: white;
          padding: 1.5rem;
          border-radius: 12px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .song-info {
            flex: 1;
            
            h6 {
              margin: 0 0 0.5rem 0;
              font-size: 1.2rem;
            }
            
            p {
              margin: 0 0 0.5rem 0;
              opacity: 0.9;
            }
            
            .genre-tag {
              padding: 0.2rem 0.6rem;
              border-radius: 12px;
              font-size: 0.8rem;
              background: rgba(255, 255, 255, 0.2);
            }
          }
          
          .no-song {
            flex: 1;
            text-align: center;
            opacity: 0.8;
          }
          
          .play-status {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.5rem;
            
            .mode-indicator {
              background: rgba(255, 255, 255, 0.2);
              padding: 0.3rem 0.6rem;
              border-radius: 12px;
              font-size: 0.8rem;
            }
            
            .progress {
              font-size: 1.1rem;
              font-weight: bold;
            }
          }
        }
      }
      
      .playlist-display {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .playlist-container {
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          max-height: 300px;
          overflow-y: auto;
          
          .song-item {
            display: flex;
            align-items: center;
            padding: 0.8rem;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
            
            &:hover {
              background: #f8f9fa;
            }
            
            &.current {
              background: #e6f7ff;
              border-left: 4px solid #1890ff;
            }
            
            .song-index {
              width: 30px;
              text-align: center;
              color: #666;
              font-weight: 500;
            }
            
            .song-details {
              flex: 1;
              margin-left: 1rem;
              
              .song-title {
                font-weight: 500;
                color: #333;
                margin-bottom: 0.2rem;
              }
              
              .song-meta {
                font-size: 0.9rem;
                color: #666;
              }
            }
            
            .song-genre {
              margin-right: 1rem;
              
              .genre-tag {
                padding: 0.2rem 0.6rem;
                border-radius: 12px;
                font-size: 0.8rem;
                color: white;
                
                &.pop { background: #ff6b6b; }
                &.rock { background: #4ecdc4; }
                &.jazz { background: #45b7d1; }
                &.classical { background: #96ceb4; }
                &.electronic { background: #feca57; }
              }
            }
            
            .remove-btn {
              background: #ff4d4f;
              color: white;
              border: none;
              border-radius: 50%;
              width: 24px;
              height: 24px;
              cursor: pointer;
              font-size: 0.9rem;
              display: flex;
              align-items: center;
              justify-content: center;
              
              &:hover {
                background: #d32f2f;
              }
            }
          }
        }
      }
      
      .iterator-explanation {
        background: linear-gradient(135deg, #f0f8ff, #ffffff);
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e0f0ff;
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .explanation-content {
          .iterator-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
            
            .iterator-type {
              background: white;
              padding: 1rem;
              border-radius: 6px;
              border: 1px solid #e0e0e0;
              
              h6 {
                color: #333;
                margin: 0 0 0.5rem 0;
                font-size: 1rem;
              }
              
              p {
                color: #666;
                margin: 0 0 0.5rem 0;
                font-size: 0.9rem;
              }
              
              ul {
                list-style-type: disc;
                padding-left: 1rem;
                margin: 0;
                
                li {
                  color: #666;
                  font-size: 0.85rem;
                  margin-bottom: 0.2rem;
                }
              }
            }
          }
          
          .pattern-benefit {
            background: #667eea;
            color: white;
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
            font-size: 0.9rem;
          }
        }
      }
      
      .statistics {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        
        h5 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 1rem;
          
          .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .stat-label {
              color: #666;
              font-size: 0.9rem;
            }
            
            .stat-value {
              color: #333;
              font-weight: 600;
            }
          }
        }
      }
    }
  }

  .advantages {
    h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 0.3rem;
        padding-left: 1rem;
        color: #666;
      }
    }
  }
}
</style>
