<template>
  <div class="command-demo">
    <div class="pattern-info">
      <p class="description">
        命令模式是一种行为型设计模式，它将请求封装为对象，从而允许将请求参数化并且支持请求的排队、记录日志和撤销操作。
        命令模式将调用者(Invoker)与接收者(Receiver)解耦，使系统更加灵活。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>文本编辑器的撤销和重做功能</li>
          <li>事务处理系统</li>
          <li>GUI系统中的菜单项和按钮动作处理</li>
          <li>宏命令（组合多个命令）</li>
          <li>请求的队列处理</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 文本编辑器：</h4>

      <div class="editor-container">
        <div class="editor-toolbar">
          <div class="editor-actions">
            <button
              @click="undo"
              :disabled="!canUndo"
              class="action-btn"
              title="撤销"
            >
              ↩️ 撤销
            </button>
            <button
              @click="redo"
              :disabled="!canRedo"
              class="action-btn"
              title="重做"
            >
              ↪️ 重做
            </button>
            <span class="divider">|</span>
            <button
              @click="copy"
              :disabled="!hasSelection"
              class="action-btn"
              title="复制"
            >
              📋 复制
            </button>
            <button
              @click="cut"
              :disabled="!hasSelection"
              class="action-btn"
              title="剪切"
            >
              ✂️ 剪切
            </button>
            <button
              @click="paste"
              :disabled="!clipboardHasContent"
              class="action-btn"
              title="粘贴"
            >
              📄 粘贴
            </button>
            <span class="divider">|</span>
            <button
              @click="insertSampleText"
              class="action-btn"
              title="插入示例文本"
            >
              📝 插入示例
            </button>
            <button
              @click="clearEditor"
              class="action-btn danger"
              title="清空编辑器"
            >
              🗑️ 清空
            </button>
          </div>
          <div class="editor-status">
            {{ statusText }}
          </div>
        </div>

        <div class="editor-content">
          <textarea
            ref="editorTextarea"
            v-model="editorContent"
            @select="handleSelection"
            @click="handleSelection"
            @keyup="handleKeyup"
            placeholder="在此处输入文本..."
            class="content-area"
          ></textarea>
        </div>

        <div class="command-history">
          <h5>命令历史：</h5>
          <div class="history-list">
            <div v-if="commandHistory.length === 0" class="history-placeholder">
              尚无命令执行记录...
            </div>
            <div v-else class="history-items">
              <div
                v-for="(command, index) in commandHistory"
                :key="index"
                class="history-item"
                :class="{ current: index === commandHistory.length - 1 }"
              >
                <span class="command-num">{{ index + 1 }}</span>
                <span class="command-name">{{ command.getName() }}</span>
                <span class="command-desc">{{ command.getDescription() }}</span>
              </div>
            </div>
          </div>

          <h5>已撤销命令：</h5>
          <div class="history-list">
            <div v-if="undoneCommands.length === 0" class="history-placeholder">
              尚无撤销记录...
            </div>
            <div v-else class="history-items">
              <div
                v-for="(command, index) in undoneCommands"
                :key="index"
                class="history-item undone"
              >
                <span class="command-num">{{ index + 1 }}</span>
                <span class="command-name">{{ command.getName() }}</span>
                <span class="command-desc">{{ command.getDescription() }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="command-pattern-visualization">
        <h5>命令模式结构：</h5>
        <div class="pattern-structure">
          <div class="structure-component client">
            <h6>Client</h6>
            <p>创建命令对象，设置接收者</p>
          </div>

          <div class="structure-component command">
            <h6>Command</h6>
            <p>封装执行和撤销操作的接口</p>
          </div>

          <div class="structure-component invoker">
            <h6>Invoker</h6>
            <p>存储命令，请求命令执行</p>
          </div>

          <div class="structure-component receiver">
            <h6>Receiver</h6>
            <p>知道如何执行相关操作</p>
          </div>

          <svg class="structure-arrows">
            <defs>
              <marker
                id="arrowhead"
                markerWidth="10"
                markerHeight="7"
                refX="9"
                refY="3.5"
                orient="auto"
              >
                <polygon points="0 0, 10 3.5, 0 7" />
              </marker>
            </defs>
            <line
              x1="150"
              y1="50"
              x2="300"
              y2="50"
              stroke="#333"
              stroke-width="2"
              marker-end="url(#arrowhead)"
            />
            <line
              x1="350"
              y1="80"
              x2="350"
              y2="140"
              stroke="#333"
              stroke-width="2"
              marker-end="url(#arrowhead)"
            />
            <line
              x1="300"
              y1="170"
              x2="150"
              y2="170"
              stroke="#333"
              stroke-width="2"
              marker-end="url(#arrowhead)"
            />
            <text x="180" y="40">创建</text>
            <text x="360" y="110">执行</text>
            <text x="180" y="160">操作</text>
          </svg>
        </div>
      </div>
    </div>

    <div class="benefits">
      <h4>命令模式的优势：</h4>
      <ul>
        <li>✅ <b>解耦发送者与接收者</b> - 调用者与实际操作的对象分离</li>
        <li>✅ <b>命令的参数化</b> - 可以将命令作为参数传递</li>
        <li>✅ <b>支持撤销/重做</b> - 命令可以存储操作历史</li>
        <li>✅ <b>支持事务</b> - 可以组合多个命令成为原子操作</li>
        <li>✅ <b>可扩展性</b> - 可以轻松添加新命令而不修改现有代码</li>
      </ul>
    </div>

    <div class="tips">
      <h4>实现要点：</h4>
      <ol>
        <li>定义命令接口（通常包含执行和撤销方法）</li>
        <li>实现具体命令类，封装接收者和相关参数</li>
        <li>创建调用者，用于存储和执行命令</li>
        <li>在执行命令时记录状态变化，以支持撤销操作</li>
        <li>考虑命令的组合，如宏命令（组合多个命令）</li>
      </ol>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import {
  type Command,
  TextEditor,
  CommandHistory,
  createTextEditor,
  createCommandHistory,
  createInsertCommand,
  createDeleteCommand,
  createCopyCommand,
  createCutCommand,
  createPasteCommand,
} from "@/patterns/Command";

// 文本编辑器实例
const editor = ref<TextEditor>(createTextEditor());
// 命令历史实例
const commandManager = ref<CommandHistory>(createCommandHistory(20));
// 界面相关的响应式数据
const editorContent = ref<string>("");
const editorTextarea = ref<HTMLTextAreaElement | null>(null);
const selectionStart = ref<number>(0);
const selectionEnd = ref<number>(0);

// 计算属性
const hasSelection = computed(
  () => selectionStart.value !== selectionEnd.value
);
const clipboardHasContent = computed(
  () => editor.value.getClipboard().length > 0
);
const canUndo = computed(() => commandManager.value.getHistory().length > 0);
const canRedo = computed(
  () => commandManager.value.getUndoneCommands().length > 0
);
const statusText = computed(() => {
  const position = selectionStart.value;
  const hasSelect = hasSelection.value;
  const content = editorContent.value;

  // 计算行号和列号
  let line = 1;
  let col = 1;
  for (let i = 0; i < position; i++) {
    if (content[i] === "\n") {
      line++;
      col = 1;
    } else {
      col++;
    }
  }

  return hasSelect
    ? `已选中 ${
        selectionEnd.value - selectionStart.value
      } 个字符 | 行 ${line}, 列 ${col}`
    : `行 ${line}, 列 ${col}`;
});

const commandHistory = computed(() => {
  return commandManager.value.getHistory();
});

const undoneCommands = computed(() => {
  return commandManager.value.getUndoneCommands();
});

// 同步文本编辑器内容与视图
watch(editorContent, (newContent) => {
  // 视图->模型的单向数据流
  // 这个简单的示例，直接用 v-model 是可以的
  // 但在真实应用中，可能需要更复杂的同步机制
});

// 编辑器操作方法
function handleSelection() {
  if (!editorTextarea.value) return;
  selectionStart.value = editorTextarea.value.selectionStart;
  selectionEnd.value = editorTextarea.value.selectionEnd;
}

function handleKeyup(e: KeyboardEvent) {
  if (e.key === "z" && (e.ctrlKey || e.metaKey)) {
    // Ctrl+Z / Cmd+Z 撤销
    undo();
    e.preventDefault();
  } else if (e.key === "y" && (e.ctrlKey || e.metaKey)) {
    // Ctrl+Y / Cmd+Y 重做
    redo();
    e.preventDefault();
  } else {
    handleSelection();
  }
}

function executeCommand(command: Command) {
  commandManager.value.execute(command);
  updateEditorContent();
}

function undo() {
  if (!canUndo.value) return;

  commandManager.value.undo();
  updateEditorContent();
}

function redo() {
  if (!canRedo.value) return;

  commandManager.value.redo();
  updateEditorContent();
}

function copy() {
  if (!hasSelection.value) return;
  syncModelFromView();

  const command = createCopyCommand(editor.value);
  executeCommand(command);
}

function cut() {
  if (!hasSelection.value) return;
  syncModelFromView();

  const command = createCutCommand(editor.value);
  executeCommand(command);
}

function paste() {
  if (!clipboardHasContent.value) return;
  syncModelFromView();

  const command = createPasteCommand(editor.value);
  executeCommand(command);
}

function insertSampleText() {
  syncModelFromView();

  const sampleTexts = [
    "这是一段示例文本，用于展示命令模式。",
    "命令模式将请求封装为对象。",
    "通过命令模式，我们可以实现撤销和重做功能。",
    "这是命令模式的实际应用。",
  ];

  const randomText =
    sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
  const command = createInsertCommand(editor.value, randomText);
  executeCommand(command);
}

function clearEditor() {
  syncModelFromView();

  const length = editorContent.value.length;
  if (length > 0) {
    const command = createDeleteCommand(editor.value, 0, length);
    executeCommand(command);
  }
}

// 同步视图状态到模型
function syncModelFromView() {
  // 在真实应用中，这里可能需要更复杂的同步逻辑
  if (editorTextarea.value) {
    editor.value.select(selectionStart.value, selectionEnd.value);
  }
}

// 同步模型状态到视图
function syncViewFromModel() {
  // 更新文本内容
  editorContent.value = editor.value.getContent();

  // 更新选择范围
  if (editorTextarea.value) {
    selectionStart.value = editor.value.getSelectionStart();
    selectionEnd.value = editor.value.getSelectionEnd();

    // 设置DOM元素的选择范围
    setTimeout(() => {
      if (editorTextarea.value) {
        editorTextarea.value.selectionStart = selectionStart.value;
        editorTextarea.value.selectionEnd = selectionEnd.value;
      }
    });
  }
}

function updateEditorContent() {
  syncViewFromModel();
  focusEditor();
}

function focusEditor() {
  setTimeout(() => {
    if (editorTextarea.value) {
      editorTextarea.value.focus();
    }
  });
}

// 初始化
onMounted(() => {
  focusEditor();
  insertSampleText();
});
</script>

<style lang="scss" scoped>
.command-demo {
  .pattern-info {
    margin-bottom: 2rem;

    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #6c5ce7;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;

    h4 {
      margin-bottom: 1rem;
      color: #333;
    }
  }

  .editor-container {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 2rem;
    background-color: #fff;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  }

  .editor-toolbar {
    padding: 0.8rem;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .editor-actions {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
      align-items: center;
    }

    .editor-status {
      font-size: 0.8rem;
      color: #666;
      white-space: nowrap;
    }
  }

  .action-btn {
    background-color: #f0f0f0;
    border: none;
    border-radius: 4px;
    padding: 0.4rem 0.8rem;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;

    &:hover:not(:disabled) {
      background-color: #e0e0e0;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &.danger:hover {
      background-color: #ff5252;
      color: white;
    }
  }

  .divider {
    color: #ddd;
    margin: 0 0.3rem;
  }

  .editor-content {
    padding: 1rem;

    .content-area {
      width: 100%;
      min-height: 200px;
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 1rem;
      font-family: "Courier New", Courier, monospace;
      font-size: 1rem;
      line-height: 1.5;
      resize: vertical;

      &:focus {
        outline: none;
        border-color: #6c5ce7;
        box-shadow: 0 0 0 2px rgba(108, 92, 231, 0.2);
      }
    }
  }

  .command-history {
    padding: 1rem;
    background-color: #f8f9fa;
    border-top: 1px solid #eee;

    h5 {
      margin: 0 0 0.8rem 0;
      font-size: 1rem;
      color: #333;
    }

    .history-list {
      background: white;
      border: 1px solid #eee;
      border-radius: 4px;
      min-height: 80px;
      max-height: 200px;
      overflow-y: auto;
      margin-bottom: 1.5rem;
    }

    .history-placeholder {
      padding: 1rem;
      color: #999;
      font-style: italic;
      text-align: center;
    }

    .history-items {
      .history-item {
        padding: 0.6rem 1rem;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &.current {
          background-color: #eff6ff;
          border-left: 3px solid #3b82f6;
        }

        &.undone {
          background-color: #fef9c3;
          border-left: 3px solid #eab308;
        }

        .command-num {
          font-weight: bold;
          color: #666;
          margin-right: 0.8rem;
          flex-shrink: 0;
          width: 20px;
        }

        .command-name {
          font-weight: 500;
          margin-right: 1rem;
          flex-shrink: 0;
          width: 80px;
        }

        .command-desc {
          color: #666;
          font-size: 0.9rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .command-pattern-visualization {
    margin-top: 2rem;

    .pattern-structure {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 2rem;
      height: 250px;
      position: relative;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }

    .structure-component {
      background: white;
      border: 2px solid #333;
      border-radius: 6px;
      width: 150px;
      padding: 0.8rem;
      text-align: center;
      position: relative;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      h6 {
        margin: 0 0 0.5rem;
        font-size: 1rem;
        color: #333;
      }

      p {
        margin: 0;
        font-size: 0.8rem;
        color: #666;
      }

      &.client {
        order: 1;
        margin-top: 20px;
        border-color: #3b82f6;
      }

      &.command {
        order: 2;
        margin-top: 20px;
        border-color: #6c5ce7;
      }

      &.invoker {
        order: 3;
        margin-top: 20px;
        border-color: #10b981;
      }

      &.receiver {
        order: 4;
        margin-top: 120px;
        border-color: #ef4444;
      }
    }

    .structure-arrows {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;

      text {
        fill: #666;
        font-size: 12px;
      }

      marker {
        fill: #333;
      }
    }
  }

  .benefits,
  .tips {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;
    }

    ul,
    ol {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.8rem;
        color: #444;
        line-height: 1.5;
      }
    }
  }

  .tips {
    background: #fff8e1;
    border-left: 4px solid #ffc107;
  }
}

@media (max-width: 768px) {
  .command-demo {
    .pattern-structure {
      flex-direction: column;
      height: auto;
      gap: 1rem;

      .structure-component {
        width: 100%;
      }

      .structure-arrows {
        display: none;
      }
    }
  }
}
</style>
