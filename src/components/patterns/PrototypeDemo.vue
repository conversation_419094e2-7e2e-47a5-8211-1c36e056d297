<template>
  <div class="prototype-demo">
    <div class="pattern-info">
      <p class="description">
        原型模式通过复制现有实例来创建新对象，而不是通过实例化类。这在创建对象的成本较高时特别有用，可以避免重复的初始化工作。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>游戏角色克隆系统</li>
          <li>图形编辑器中的对象复制</li>
          <li>数据库记录复制</li>
          <li>配置对象的复制</li>
          <li>复杂对象的快速创建</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 游戏角色克隆系统：</h4>
      <div class="demo-controls">
        <div class="character-selector">
          <label>选择角色原型：</label>
          <select v-model="selectedPrototype" class="prototype-select">
            <option value="warrior">战士</option>
            <option value="mage">法师</option>
            <option value="archer">弓箭手</option>
          </select>
        </div>
        <button @click="cloneCharacter" class="demo-btn">克隆角色</button>
        <button @click="levelUpOriginal" class="demo-btn">原型升级</button>
        <button @click="clearClones" class="demo-btn clear">清空克隆</button>
      </div>

      <div class="demo-output">
        <div class="prototypes-section">
          <h5>原型角色：</h5>
          <div class="character-grid">
            <div 
              v-for="(prototype, key) in prototypes" 
              :key="key"
              class="character-card prototype"
            >
              <div class="character-header">
                <h6>{{ prototype.getName() }} (原型)</h6>
                <span class="character-level">等级 {{ prototype.getLevel() }}</span>
              </div>
              <div class="character-stats">
                <p>生命值: {{ prototype.getHealth() }}</p>
                <p>魔法值: {{ prototype.getMana() }}</p>
                <div class="equipment-list">
                  <strong>装备:</strong>
                  <ul>
                    <li v-for="(equipment, index) in prototype.getEquipment()" :key="index">
                      {{ equipment.getInfo() }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="clones-section" v-if="clonedCharacters.length > 0">
          <h5>克隆角色：</h5>
          <div class="character-grid">
            <div 
              v-for="(clone, index) in clonedCharacters" 
              :key="index"
              class="character-card clone"
            >
              <div class="character-header">
                <h6>{{ clone.getName() }} (克隆 #{{ index + 1 }})</h6>
                <span class="character-level">等级 {{ clone.getLevel() }}</span>
              </div>
              <div class="character-stats">
                <p>生命值: {{ clone.getHealth() }}</p>
                <p>魔法值: {{ clone.getMana() }}</p>
                <div class="equipment-list">
                  <strong>装备:</strong>
                  <ul>
                    <li v-for="(equipment, index) in clone.getEquipment()" :key="index">
                      {{ equipment.getInfo() }}
                    </li>
                  </ul>
                </div>
              </div>
              <div class="clone-actions">
                <button @click="levelUpClone(index)" class="mini-btn">升级</button>
                <button @click="addEquipmentToClone(index)" class="mini-btn">添加装备</button>
              </div>
            </div>
          </div>
        </div>

        <div class="statistics" v-if="clonedCharacters.length > 0">
          <h5>统计信息：</h5>
          <p><strong>原型数量：</strong> {{ Object.keys(prototypes).length }}</p>
          <p><strong>克隆数量：</strong> {{ clonedCharacters.length }}</p>
          <p><strong>内存节省：</strong> 通过共享原型结构，节省了 {{ calculateMemorySaving() }}% 的内存</p>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 避免重复的初始化工作</li>
        <li>✅ 可以在运行时动态配置对象</li>
        <li>✅ 减少子类的构造</li>
        <li>✅ 比直接创建对象更高效</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 深度克隆复杂对象可能很困难</li>
        <li>❌ 每个类都需要实现克隆方法</li>
        <li>❌ 克隆包含循环引用的对象很复杂</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";

// 装备类
class Equipment {
  constructor(
    public name: string,
    public type: string,
    public attack: number,
    public defense: number
  ) {}

  clone(): Equipment {
    return new Equipment(this.name, this.type, this.attack, this.defense);
  }

  getInfo(): string {
    return `${this.name} (${this.type}) - 攻击:${this.attack} 防御:${this.defense}`;
  }
}

// 游戏角色类
class GameCharacter {
  private name: string;
  private level: number;
  private health: number;
  private mana: number;
  private equipment: Equipment[] = [];

  constructor(name: string, level: number = 1) {
    this.name = name;
    this.level = level;
    this.health = level * 100;
    this.mana = level * 50;
  }

  clone(): GameCharacter {
    const cloned = new GameCharacter(this.name, this.level);
    cloned.health = this.health;
    cloned.mana = this.mana;
    cloned.equipment = this.equipment.map(item => item.clone());
    return cloned;
  }

  addEquipment(equipment: Equipment): void {
    this.equipment.push(equipment);
  }

  levelUp(): void {
    this.level++;
    this.health += 100;
    this.mana += 50;
  }

  getName(): string { return this.name; }
  getLevel(): number { return this.level; }
  getHealth(): number { return this.health; }
  getMana(): number { return this.mana; }
  getEquipment(): Equipment[] { return this.equipment; }
}

// 响应式数据
const selectedPrototype = ref<string>("warrior");
const clonedCharacters = ref<GameCharacter[]>([]);

// 创建原型角色
const prototypes = reactive({
  warrior: (() => {
    const warrior = new GameCharacter("战士", 10);
    warrior.addEquipment(new Equipment("钢铁剑", "武器", 50, 0));
    warrior.addEquipment(new Equipment("钢铁盾", "防具", 0, 30));
    return warrior;
  })(),
  mage: (() => {
    const mage = new GameCharacter("法师", 8);
    mage.addEquipment(new Equipment("魔法杖", "武器", 30, 0));
    mage.addEquipment(new Equipment("法师袍", "防具", 0, 15));
    return mage;
  })(),
  archer: (() => {
    const archer = new GameCharacter("弓箭手", 9);
    archer.addEquipment(new Equipment("精灵弓", "武器", 40, 0));
    archer.addEquipment(new Equipment("皮甲", "防具", 0, 20));
    return archer;
  })()
});

// 方法
const cloneCharacter = () => {
  const prototype = prototypes[selectedPrototype.value as keyof typeof prototypes];
  if (prototype) {
    const clone = prototype.clone();
    clonedCharacters.value.push(clone);
  }
};

const levelUpOriginal = () => {
  const prototype = prototypes[selectedPrototype.value as keyof typeof prototypes];
  if (prototype) {
    prototype.levelUp();
  }
};

const levelUpClone = (index: number) => {
  if (clonedCharacters.value[index]) {
    clonedCharacters.value[index].levelUp();
  }
};

const addEquipmentToClone = (index: number) => {
  if (clonedCharacters.value[index]) {
    const randomEquipment = [
      new Equipment("强化剑", "武器", 60, 0),
      new Equipment("龙鳞甲", "防具", 0, 40),
      new Equipment("敏捷靴", "鞋子", 10, 10),
      new Equipment("力量戒指", "饰品", 20, 5)
    ];
    const equipment = randomEquipment[Math.floor(Math.random() * randomEquipment.length)];
    clonedCharacters.value[index].addEquipment(equipment);
  }
};

const clearClones = () => {
  clonedCharacters.value = [];
};

const calculateMemorySaving = (): number => {
  if (clonedCharacters.value.length === 0) return 0;
  // 假设每个完整对象需要100%内存，克隆只需要额外的30%
  const totalObjects = clonedCharacters.value.length + Object.keys(prototypes).length;
  const actualMemory = Object.keys(prototypes).length * 100 + clonedCharacters.value.length * 30;
  const fullMemory = totalObjects * 100;
  return Math.round((fullMemory - actualMemory) / fullMemory * 100);
};
</script>

<style lang="scss" scoped>
.prototype-demo {
  .pattern-info {
    margin-bottom: 2rem;
    
    .description {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }
    
    .use-cases {
      h4 {
        color: #333;
        margin-bottom: 0.5rem;
      }
      
      ul {
        list-style-type: disc;
        padding-left: 1.5rem;
        
        li {
          margin-bottom: 0.3rem;
          color: #666;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
    }
    
    .demo-controls {
      display: flex;
      gap: 1rem;
      align-items: center;
      margin-bottom: 1.5rem;
      flex-wrap: wrap;
      
      .character-selector {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        
        label {
          font-weight: 500;
          color: #555;
        }
        
        .prototype-select {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
        }
      }
      
      .demo-btn {
        padding: 0.6rem 1.2rem;
        border: none;
        border-radius: 6px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        &.clear {
          background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }
      }
    }
    
    .demo-output {
      .prototypes-section, .clones-section {
        margin-bottom: 2rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
      }
      
      .character-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
      }
      
      .character-card {
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        padding: 1rem;
        background: white;
        
        &.prototype {
          border-color: #667eea;
          background: linear-gradient(135deg, #f8f9ff, #ffffff);
        }
        
        &.clone {
          border-color: #52c41a;
          background: linear-gradient(135deg, #f6ffed, #ffffff);
        }
        
        .character-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.5rem;
          
          h6 {
            margin: 0;
            color: #333;
            font-size: 1rem;
          }
          
          .character-level {
            background: #667eea;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
          }
        }
        
        .character-stats {
          font-size: 0.9rem;
          color: #666;
          
          p {
            margin: 0.3rem 0;
          }
          
          .equipment-list {
            margin-top: 0.5rem;
            
            ul {
              margin: 0.3rem 0 0 0;
              padding-left: 1rem;
              list-style-type: disc;
              
              li {
                margin: 0.2rem 0;
                font-size: 0.8rem;
              }
            }
          }
        }
        
        .clone-actions {
          margin-top: 0.8rem;
          display: flex;
          gap: 0.5rem;
          
          .mini-btn {
            padding: 0.3rem 0.6rem;
            border: none;
            border-radius: 4px;
            background: #52c41a;
            color: white;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
            
            &:hover {
              background: #389e0d;
            }
          }
        }
      }
      
      .statistics {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        
        h5 {
          color: #333;
          margin: 0 0 0.5rem 0;
        }
        
        p {
          margin: 0.3rem 0;
          color: #555;
        }
      }
    }
  }

  .advantages {
    h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 0.3rem;
        padding-left: 1rem;
        color: #666;
      }
    }
  }
}
</style>
