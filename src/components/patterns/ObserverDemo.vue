<template>
  <div class="observer-demo">
    <div class="pattern-info">
      <p class="description">
        观察者模式定义了对象间的一对多依赖关系，当一个对象的状态发生改变时，所有依赖于它的对象都会得到通知并自动更新。这种模式也被称为发布-订阅模式。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>事件处理系统</li>
          <li>模型-视图架构（MVC、MVVM）</li>
          <li>消息队列和通知系统</li>
          <li>股票价格监控</li>
          <li>用户界面组件状态同步</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 股票价格监控系统：</h4>

      <div class="stock-controls">
        <div class="stock-info">
          <h5>📈 {{ stock.symbol }} - {{ stock.name }}</h5>
          <div class="current-price">
            当前价格: <span class="price">¥{{ stock.price.toFixed(2) }}</span>
          </div>
        </div>

        <div class="price-controls">
          <button @click="increasePrice" class="demo-btn increase">
            📈 涨价
          </button>
          <button @click="decreasePrice" class="demo-btn decrease">
            📉 跌价
          </button>
          <button @click="randomPrice" class="demo-btn random">
            🎲 随机变动
          </button>
        </div>
      </div>

      <div class="observers-section">
        <div class="add-observer">
          <h5>添加观察者：</h5>
          <div class="observer-controls">
            <select v-model="newObserverType" class="observer-select">
              <option value="investor">投资者</option>
              <option value="trader">交易员</option>
              <option value="analyst">分析师</option>
            </select>
            <input
              v-model="newObserverName"
              placeholder="输入名称"
              class="observer-name"
              @keyup.enter="addObserver"
            />
            <button @click="addObserver" class="demo-btn add">
              添加观察者
            </button>
          </div>
        </div>

        <div class="observers-list">
          <h5>当前观察者 ({{ observers.length }})：</h5>
          <div class="observer-cards">
            <div
              v-for="observer in observers"
              :key="observer.id"
              class="observer-card"
              :class="observer.type"
            >
              <div class="observer-header">
                <span class="observer-icon">{{
                  getObserverIcon(observer.type)
                }}</span>
                <span class="observer-name">{{ observer.name }}</span>
                <button @click="removeObserver(observer.id)" class="remove-btn">
                  ×
                </button>
              </div>
              <div class="observer-notifications">
                <div
                  v-for="(notification, index) in observer.notifications.slice(
                    -3
                  )"
                  :key="index"
                  class="notification"
                >
                  {{ notification }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 松耦合：主题和观察者之间松耦合</li>
        <li>✅ 动态关系：可以在运行时建立对象间的关系</li>
        <li>✅ 广播通信：支持广播式的通信</li>
        <li>✅ 符合开闭原则</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 如果观察者过多，通知会花费较多时间</li>
        <li>❌ 可能导致循环依赖</li>
        <li>❌ 观察者只知道主题发生了变化，不知道如何变化</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import {
  createObserver,
  Stock,
  type ObserverType,
  type StockType,
} from "@/patterns/Observer";

// 响应式数据
const stock = reactive(new Stock("AAPL", "苹果公司", 150.0));
const observers = ref<ObserverType[]>([]);
const newObserverType = ref<string>("investor");
const newObserverName = ref<string>("");

// 方法
const addObserver = () => {
  if (!newObserverName.value.trim()) return;

  const observer = createObserver(newObserverName.value, newObserverType.value);
  stock.addObserver(observer);
  observers.value = stock.getObservers();
  newObserverName.value = "";
};

const removeObserver = (observerId: string) => {
  stock.removeObserver(observerId);
  observers.value = stock.getObservers();
};

const increasePrice = () => {
  const increase = Math.random() * 5 + 1;
  stock.setPrice(stock.price + increase);
};

const decreasePrice = () => {
  const decrease = Math.random() * 5 + 1;
  stock.setPrice(Math.max(0.01, stock.price - decrease));
};

const randomPrice = () => {
  const change = (Math.random() - 0.5) * 10;
  stock.setPrice(Math.max(0.01, stock.price + change));
};

const getObserverIcon = (type: string): string => {
  switch (type) {
    case "investor":
      return "💼";
    case "trader":
      return "📊";
    case "analyst":
      return "🔍";
    default:
      return "👤";
  }
};
</script>

<style lang="scss" scoped>
.observer-demo {
  .pattern-info {
    margin-bottom: 2rem;

    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #17a2b8;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;

    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .stock-controls {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;

      .stock-info {
        margin-bottom: 1rem;

        h5 {
          margin: 0 0 0.5rem 0;
          font-size: 1.3rem;
        }

        .current-price {
          font-size: 1.1rem;

          .price {
            font-weight: 700;
            font-size: 1.3rem;
          }
        }
      }

      .price-controls {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;

        .demo-btn {
          padding: 0.8rem 1.5rem;
          border: none;
          border-radius: 6px;
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;

          &.increase {
            background: linear-gradient(135deg, #28a745, #20c997);
          }

          &.decrease {
            background: linear-gradient(135deg, #dc3545, #c82333);
          }

          &.random {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
          }
        }
      }
    }

    .observers-section {
      .add-observer {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;

        h5 {
          margin: 0 0 1rem 0;
          color: #333;
        }

        .observer-controls {
          display: flex;
          gap: 1rem;
          flex-wrap: wrap;
          align-items: center;

          .observer-select,
          .observer-name {
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;

            &:focus {
              outline: none;
              border-color: #667eea;
              box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }
          }

          .observer-name {
            min-width: 150px;
          }

          .demo-btn.add {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 6px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }

      .observers-list {
        h5 {
          margin: 0 0 1rem 0;
          color: #333;
        }

        .observer-cards {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 1rem;

          .observer-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            transition: all 0.3s ease;

            &.investor {
              border-left-color: #28a745;
            }

            &.trader {
              border-left-color: #17a2b8;
            }

            &.analyst {
              border-left-color: #ffc107;
            }

            .observer-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 1rem;

              .observer-icon {
                font-size: 1.2rem;
                margin-right: 0.5rem;
              }

              .observer-name {
                font-weight: 600;
                color: #333;
                flex: 1;
              }

              .remove-btn {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 50%;
                width: 24px;
                height: 24px;
                cursor: pointer;
                font-size: 1rem;
                line-height: 1;

                &:hover {
                  background: #c82333;
                }
              }
            }

            .observer-notifications {
              .notification {
                background: #f8f9fa;
                padding: 0.5rem;
                border-radius: 4px;
                margin-bottom: 0.5rem;
                font-size: 0.85rem;
                color: #666;
                border-left: 3px solid #dee2e6;

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
      }
    }
  }
}
</style>
