<template>
  <div class="builder-demo">
    <div class="pattern-info">
      <p class="description">
        建造者模式将一个复杂对象的构建与它的表示分离，使得同样的构建过程可以创建不同的表示。它适用于创建复杂对象，这些对象的创建过程稳定，但各个部分经常变化。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>电脑配置构建器</li>
          <li>SQL查询构建器</li>
          <li>HTTP请求构建器</li>
          <li>UI组件构建器</li>
          <li>配置文件生成器</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 电脑配置构建器：</h4>

      <div class="builder-selector">
        <h5>🖥️ 选择电脑类型</h5>
        <div class="builder-options">
          <div
            v-for="type in availableTypes"
            :key="type.id"
            class="builder-option"
            :class="{ active: selectedType === type.id }"
            @click="selectType(type.id)"
          >
            <span class="type-icon">{{ type.icon }}</span>
            <div class="type-info">
              <div class="type-name">{{ type.name }}</div>
              <div class="type-desc">{{ type.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="build-controls">
        <h5>🔧 构建选项</h5>
        <div class="control-buttons">
          <button @click="buildFullComputer" class="demo-btn full">
            构建完整配置
          </button>
          <button @click="buildBasicComputer" class="demo-btn basic">
            构建基础配置
          </button>
          <button @click="buildStepByStep" class="demo-btn step">
            分步构建
          </button>
          <button @click="resetBuild" class="demo-btn reset">
            重置构建
          </button>
        </div>
      </div>

      <div class="build-process" v-if="buildSteps.length > 0">
        <h5>🔄 构建过程</h5>
        <div class="process-steps">
          <div
            v-for="(step, index) in buildSteps"
            :key="index"
            class="process-step"
            :class="{ 
              completed: index < currentStep,
              active: index === currentStep,
              pending: index > currentStep
            }"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <div class="step-title">{{ step.title }}</div>
              <div class="step-detail">{{ step.detail }}</div>
            </div>
            <div class="step-status">
              <span v-if="index < currentStep">✅</span>
              <span v-else-if="index === currentStep">⏳</span>
              <span v-else>⏸️</span>
            </div>
          </div>
        </div>
      </div>

      <div class="computer-result" v-if="builtComputer">
        <h5>💻 构建结果</h5>
        <div class="result-card">
          <div class="computer-specs">
            <div class="spec-header">
              <h6>{{ getTypeDisplayName(selectedType) }}</h6>
              <span class="performance-level">{{ builtComputer.getPerformanceLevel() }}</span>
            </div>
            
            <div class="spec-details">
              <div class="spec-grid">
                <div class="spec-item" v-for="(value, key) in computerConfig" :key="key">
                  <span class="spec-label">{{ getSpecLabel(key) }}:</span>
                  <span class="spec-value">{{ value }}</span>
                </div>
              </div>
            </div>

            <div class="spec-footer">
              <div class="total-price">
                总价: <span class="price">¥{{ computerConfig.totalPrice?.toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 将复杂对象的创建过程和表示分离</li>
        <li>✅ 可以更精细地控制构建过程</li>
        <li>✅ 可以构建不同表示的对象</li>
        <li>✅ 代码复用性好</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 增加了系统的复杂性</li>
        <li>❌ 如果产品差异很大，不适合使用建造者模式</li>
        <li>❌ 需要创建多个具体建造者类</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { 
  computerBuilderFactory,
  ComputerDirector,
  type ComputerType,
  type ComputerBuilderType
} from "@/patterns/Builder";

// 响应式数据
const selectedType = ref<string>("gaming");
const builtComputer = ref<ComputerType | null>(null);
const buildSteps = ref<Array<{ title: string; detail: string }>>([]);
const currentStep = ref<number>(-1);
const isBuilding = ref<boolean>(false);

// 可用类型配置
const availableTypes = [
  {
    id: "gaming",
    name: "游戏电脑",
    description: "高性能游戏配置",
    icon: "🎮"
  },
  {
    id: "office", 
    name: "办公电脑",
    description: "日常办公使用",
    icon: "💼"
  },
  {
    id: "workstation",
    name: "工作站",
    description: "专业工作站配置", 
    icon: "🖥️"
  }
];

// 计算属性
const computerConfig = computed(() => {
  return builtComputer.value?.getConfiguration() || {};
});

// 方法
const selectType = (typeId: string) => {
  selectedType.value = typeId;
  resetBuild();
};

const getTypeDisplayName = (typeId: string) => {
  return computerBuilderFactory.getTypeDisplayNames()[typeId] || typeId;
};

const getSpecLabel = (key: string): string => {
  const labels: Record<string, string> = {
    cpu: 'CPU',
    memory: '内存',
    storage: '存储',
    graphics: '显卡',
    motherboard: '主板',
    powerSupply: '电源',
    coolingSystem: '散热',
    caseType: '机箱',
    totalPrice: '总价'
  };
  return labels[key] || key;
};

const buildFullComputer = () => {
  const builder = computerBuilderFactory.createBuilder(selectedType.value);
  const director = new ComputerDirector(builder);
  builtComputer.value = director.buildFullComputer();
  
  buildSteps.value = [
    { title: "构建CPU", detail: "安装处理器" },
    { title: "构建内存", detail: "安装内存条" },
    { title: "构建存储", detail: "安装硬盘" },
    { title: "构建显卡", detail: "安装显卡" },
    { title: "构建主板", detail: "安装主板" },
    { title: "构建电源", detail: "安装电源" },
    { title: "构建散热", detail: "安装散热系统" },
    { title: "构建机箱", detail: "安装机箱" }
  ];
  currentStep.value = buildSteps.value.length;
};

const buildBasicComputer = () => {
  const builder = computerBuilderFactory.createBuilder(selectedType.value);
  const director = new ComputerDirector(builder);
  builtComputer.value = director.buildBasicComputer();
  
  buildSteps.value = [
    { title: "构建CPU", detail: "安装处理器" },
    { title: "构建内存", detail: "安装内存条" },
    { title: "构建存储", detail: "安装硬盘" },
    { title: "构建主板", detail: "安装主板" },
    { title: "构建电源", detail: "安装电源" }
  ];
  currentStep.value = buildSteps.value.length;
};

const buildStepByStep = async () => {
  if (isBuilding.value) return;
  
  isBuilding.value = true;
  const builder = computerBuilderFactory.createBuilder(selectedType.value);
  
  buildSteps.value = [
    { title: "重置构建器", detail: "准备构建环境" },
    { title: "构建CPU", detail: "安装处理器" },
    { title: "构建内存", detail: "安装内存条" },
    { title: "构建存储", detail: "安装硬盘" },
    { title: "构建显卡", detail: "安装显卡" },
    { title: "构建主板", detail: "安装主板" },
    { title: "构建电源", detail: "安装电源" },
    { title: "构建散热", detail: "安装散热系统" },
    { title: "构建机箱", detail: "安装机箱" }
  ];
  
  currentStep.value = 0;
  
  // 模拟分步构建过程
  const steps = [
    () => builder.reset(),
    () => builder.buildCPU(),
    () => builder.buildMemory(),
    () => builder.buildStorage(),
    () => builder.buildGraphics(),
    () => builder.buildMotherboard(),
    () => builder.buildPowerSupply(),
    () => builder.buildCoolingSystem(),
    () => builder.buildCase()
  ];
  
  for (let i = 0; i < steps.length; i++) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    steps[i]();
    currentStep.value = i + 1;
  }
  
  builtComputer.value = builder.getResult();
  isBuilding.value = false;
};

const resetBuild = () => {
  builtComputer.value = null;
  buildSteps.value = [];
  currentStep.value = -1;
  isBuilding.value = false;
};
</script>

<style lang="scss" scoped>
.builder-demo {
  .pattern-info {
    margin-bottom: 2rem;

    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #fd7e14;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;

    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .builder-selector {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      border-left: 4px solid #fd7e14;

      h5 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 1.2rem;
      }

      .builder-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;

        .builder-option {
          display: flex;
          align-items: center;
          background: white;
          padding: 1rem;
          border-radius: 8px;
          border: 2px solid #e9ecef;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #fd7e14;
            box-shadow: 0 2px 8px rgba(253, 126, 20, 0.2);
          }

          &.active {
            border-color: #fd7e14;
            background: linear-gradient(135deg, #fff3e0, #ffe0b3);
            box-shadow: 0 4px 12px rgba(253, 126, 20, 0.3);
          }

          .type-icon {
            font-size: 2rem;
            margin-right: 1rem;
          }

          .type-info {
            flex: 1;

            .type-name {
              font-weight: 600;
              color: #333;
              margin-bottom: 0.3rem;
            }

            .type-desc {
              color: #666;
              font-size: 0.9rem;
            }
          }
        }
      }
    }

    .build-controls {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      border-left: 4px solid #20c997;

      h5 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 1.2rem;
      }

      .control-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 0.8rem;

        .demo-btn {
          padding: 0.8rem 1rem;
          border: none;
          border-radius: 6px;
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          font-size: 0.9rem;

          &:hover {
            transform: translateY(-2px);
          }

          &.full {
            background: linear-gradient(135deg, #28a745, #20c997);
            &:hover { box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4); }
          }

          &.basic {
            background: linear-gradient(135deg, #17a2b8, #138496);
            &:hover { box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4); }
          }

          &.step {
            background: linear-gradient(135deg, #6f42c1, #5a32a3);
            &:hover { box-shadow: 0 4px 12px rgba(111, 66, 193, 0.4); }
          }

          &.reset {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            &:hover { box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4); }
          }
        }
      }
    }

    .build-process {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      border-left: 4px solid #6f42c1;

      h5 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 1.2rem;
      }

      .process-steps {
        .process-step {
          display: flex;
          align-items: center;
          margin-bottom: 0.8rem;
          padding: 0.8rem;
          background: white;
          border-radius: 6px;
          border: 2px solid #e9ecef;
          transition: all 0.3s ease;

          &.completed {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
          }

          &.active {
            border-color: #ffc107;
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
          }

          &.pending {
            opacity: 0.6;
          }

          .step-number {
            background: #6c757d;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 1rem;
            font-size: 0.9rem;
          }

          .step-content {
            flex: 1;

            .step-title {
              font-weight: 600;
              color: #333;
              margin-bottom: 0.2rem;
            }

            .step-detail {
              color: #666;
              font-size: 0.9rem;
            }
          }

          .step-status {
            font-size: 1.2rem;
          }
        }
      }
    }

    .computer-result {
      .result-card {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1.5rem;
        border-radius: 8px;

        .computer-specs {
          .spec-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;

            h6 {
              margin: 0;
              font-size: 1.3rem;
            }

            .performance-level {
              background: rgba(255, 255, 255, 0.2);
              padding: 0.3rem 0.8rem;
              border-radius: 20px;
              font-size: 0.85rem;
              font-weight: 500;
            }
          }

          .spec-details {
            margin-bottom: 1rem;

            .spec-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
              gap: 0.8rem;

              .spec-item {
                display: flex;
                justify-content: space-between;
                padding: 0.5rem 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);

                .spec-label {
                  font-weight: 500;
                }

                .spec-value {
                  text-align: right;
                  opacity: 0.9;
                }
              }
            }
          }

          .spec-footer {
            border-top: 2px solid rgba(255, 255, 255, 0.3);
            padding-top: 1rem;

            .total-price {
              text-align: center;
              font-size: 1.2rem;
              font-weight: 600;

              .price {
                color: #ffd700;
                font-size: 1.4rem;
              }
            }
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
      }
    }
  }
}
</style>
