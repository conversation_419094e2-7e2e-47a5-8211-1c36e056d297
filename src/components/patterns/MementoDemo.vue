<template>
  <div class="memento-demo">
    <div class="pattern-info">
      <p class="description">
        备忘录模式在不破坏封装性的前提下，捕获一个对象的内部状态，并在该对象之外保存这个状态。这样以后就可将该对象恢复到原先保存的状态。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>文档编辑器的撤销/重做功能</li>
          <li>游戏存档系统</li>
          <li>数据库事务回滚</li>
          <li>浏览器历史记录</li>
          <li>配置管理系统</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 文档编辑器：</h4>
      <div class="demo-controls">
        <div class="editor-panel">
          <h6>文档编辑</h6>
          <div class="editor-controls">
            <textarea 
              v-model="documentContent" 
              @input="onContentChange"
              placeholder="在这里输入文档内容..."
              class="document-editor"
              rows="8"
            ></textarea>
            
            <div class="formatting-controls">
              <div class="control-group">
                <label>字体大小:</label>
                <input 
                  v-model.number="fontSize" 
                  @change="onFontSizeChange"
                  type="range" 
                  min="8" 
                  max="72" 
                  class="font-size-slider"
                />
                <span class="font-size-display">{{ fontSize }}px</span>
              </div>
              
              <div class="control-group">
                <label>字体:</label>
                <select v-model="fontFamily" @change="onFontFamilyChange" class="font-family-select">
                  <option value="Arial">Arial</option>
                  <option value="Times New Roman">Times New Roman</option>
                  <option value="Courier New">Courier New</option>
                  <option value="Helvetica">Helvetica</option>
                </select>
              </div>
              
              <div class="control-group">
                <label>文字颜色:</label>
                <input 
                  v-model="textColor" 
                  @change="onTextColorChange"
                  type="color" 
                  class="color-picker"
                />
              </div>
              
              <div class="control-group">
                <label>背景颜色:</label>
                <input 
                  v-model="backgroundColor" 
                  @change="onBackgroundColorChange"
                  type="color" 
                  class="color-picker"
                />
              </div>
            </div>
            
            <div class="action-buttons">
              <button @click="createSnapshot" class="demo-btn snapshot">📸 创建快照</button>
              <button @click="undo" :disabled="!canUndo" class="demo-btn undo">↶ 撤销</button>
              <button @click="redo" :disabled="!canRedo" class="demo-btn redo">↷ 重做</button>
              <button @click="clearDocument" class="demo-btn clear">🗑️ 清空</button>
            </div>
          </div>
        </div>

        <div class="preview-panel">
          <h6>文档预览</h6>
          <div 
            class="document-preview"
            :style="{
              fontSize: fontSize + 'px',
              fontFamily: fontFamily,
              color: textColor,
              backgroundColor: backgroundColor
            }"
          >
            <div v-if="documentContent.trim()" class="preview-content">
              {{ documentContent }}
            </div>
            <div v-else class="preview-placeholder">
              文档内容将在这里显示...
            </div>
            
            <div class="document-stats">
              <div class="stat">字数: {{ wordCount }}</div>
              <div class="stat">字符数: {{ documentContent.length }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="demo-output">
        <div class="history-panel">
          <h5>历史记录 ({{ historyStats.totalRecords }}条)</h5>
          <div class="history-controls">
            <div class="history-stats">
              <span class="stat-item">当前位置: {{ historyStats.currentIndex + 1 }}</span>
              <span class="stat-item">内存使用: {{ historyStats.memoryUsage }}</span>
              <span class="stat-item">可撤销: {{ historyStats.canUndo ? '是' : '否' }}</span>
              <span class="stat-item">可重做: {{ historyStats.canRedo ? '是' : '否' }}</span>
            </div>
            <button @click="clearHistory" class="demo-btn clear-history">清空历史</button>
          </div>
          
          <div class="history-list">
            <div 
              v-for="(record, index) in historyRecords" 
              :key="index"
              class="history-item"
              :class="{ current: record.isCurrent }"
              @click="jumpToHistory(index)"
            >
              <div class="history-index">{{ index + 1 }}</div>
              <div class="history-content">
                <div class="history-description">{{ record.description }}</div>
                <div class="history-timestamp">{{ formatTime(record.timestamp) }}</div>
              </div>
              <div class="history-indicator">
                <span v-if="record.isCurrent" class="current-indicator">📍</span>
              </div>
            </div>
          </div>
        </div>

        <div class="memento-explanation">
          <h5>备忘录模式说明</h5>
          <div class="explanation-content">
            <div class="pattern-roles">
              <div class="role-item">
                <h6>🏗️ 原发器 (Originator)</h6>
                <p>DocumentEditor - 创建备忘录和恢复状态</p>
                <ul>
                  <li>createMemento(): 创建当前状态的备忘录</li>
                  <li>restoreFromMemento(): 从备忘录恢复状态</li>
                  <li>维护内部状态 (内容、格式等)</li>
                </ul>
              </div>
              
              <div class="role-item">
                <h6>💾 备忘录 (Memento)</h6>
                <p>DocumentMemento - 存储原发器的内部状态</p>
                <ul>
                  <li>getState(): 返回存储的状态</li>
                  <li>getTimestamp(): 返回创建时间</li>
                  <li>getDescription(): 返回操作描述</li>
                </ul>
              </div>
              
              <div class="role-item">
                <h6>📚 管理者 (Caretaker)</h6>
                <p>HistoryManager - 管理备忘录的存储和检索</p>
                <ul>
                  <li>save(): 保存备忘录</li>
                  <li>undo()/redo(): 撤销/重做操作</li>
                  <li>管理历史记录列表</li>
                </ul>
              </div>
            </div>
            
            <div class="pattern-benefit">
              <strong>核心优势:</strong> 在不破坏对象封装性的前提下，实现状态的保存和恢复，支持撤销操作和历史记录管理。
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 给用户提供了一种可以恢复状态的机制</li>
        <li>✅ 可以实现撤销操作</li>
        <li>✅ 实现了信息的封装</li>
        <li>✅ 简化了原发器</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 消耗资源，如果类的成员变量过多，势必会占用比较大的资源</li>
        <li>❌ 适用范围有限，如果状态需要完整存储到外部，会暴露内部实现</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { DocumentApp } from "../../patterns/Memento";

// 响应式数据
const documentContent = ref<string>('');
const fontSize = ref<number>(14);
const fontFamily = ref<string>('Arial');
const textColor = ref<string>('#000000');
const backgroundColor = ref<string>('#ffffff');

// 创建文档应用实例
const documentApp = new DocumentApp(20); // 最多保存20条历史记录

// 计算属性
const wordCount = computed(() => {
  return documentContent.value.split(/\s+/).filter(word => word.length > 0).length;
});

const historyStats = computed(() => {
  return documentApp.getHistoryManager().getStats();
});

const historyRecords = computed(() => {
  return documentApp.getHistoryManager().getHistory();
});

const canUndo = computed(() => {
  return documentApp.getHistoryManager().canUndo();
});

const canRedo = computed(() => {
  return documentApp.getHistoryManager().canRedo();
});

// 方法
const syncFromEditor = () => {
  const editor = documentApp.getEditor();
  documentContent.value = editor.getContent();
  fontSize.value = editor.getFontSize();
  fontFamily.value = editor.getFontFamily();
  textColor.value = editor.getTextColor();
  backgroundColor.value = editor.getBackgroundColor();
};

const onContentChange = () => {
  documentApp.setContent(documentContent.value);
};

const onFontSizeChange = () => {
  documentApp.setFontSize(fontSize.value);
};

const onFontFamilyChange = () => {
  documentApp.setFontFamily(fontFamily.value);
};

const onTextColorChange = () => {
  documentApp.setTextColor(textColor.value);
};

const onBackgroundColorChange = () => {
  documentApp.setBackgroundColor(backgroundColor.value);
};

const createSnapshot = () => {
  const timestamp = new Date().toLocaleTimeString();
  documentApp.createSnapshot(`手动快照 - ${timestamp}`);
};

const undo = () => {
  if (documentApp.undo()) {
    syncFromEditor();
  }
};

const redo = () => {
  if (documentApp.redo()) {
    syncFromEditor();
  }
};

const jumpToHistory = (index: number) => {
  if (documentApp.jumpToHistory(index)) {
    syncFromEditor();
  }
};

const clearDocument = () => {
  documentApp.setContent('');
  syncFromEditor();
};

const clearHistory = () => {
  documentApp.getHistoryManager().clear();
  // 重新保存当前状态
  documentApp.createSnapshot('清空历史后的初始状态');
};

const formatTime = (timestamp: Date): string => {
  return timestamp.toLocaleTimeString();
};

// 初始化
onMounted(() => {
  syncFromEditor();
});
</script>

<style lang="scss" scoped>
.memento-demo {
  .pattern-info {
    margin-bottom: 2rem;
    
    .description {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }
    
    .use-cases {
      h4 {
        color: #333;
        margin-bottom: 0.5rem;
      }
      
      ul {
        list-style-type: disc;
        padding-left: 1.5rem;
        
        li {
          margin-bottom: 0.3rem;
          color: #666;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
    }
    
    .demo-controls {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
      
      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
      }
      
      .editor-panel, .preview-panel {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        
        h6 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .document-editor {
          width: 100%;
          padding: 0.8rem;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-family: 'Courier New', monospace;
          font-size: 0.9rem;
          resize: vertical;
          margin-bottom: 1rem;
        }
        
        .formatting-controls {
          display: flex;
          flex-direction: column;
          gap: 0.8rem;
          margin-bottom: 1rem;
          
          .control-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            
            label {
              min-width: 80px;
              font-size: 0.9rem;
              color: #555;
            }
            
            .font-size-slider {
              flex: 1;
            }
            
            .font-size-display {
              min-width: 40px;
              font-size: 0.9rem;
              color: #666;
            }
            
            .font-family-select {
              flex: 1;
              padding: 0.4rem;
              border: 1px solid #ddd;
              border-radius: 4px;
            }
            
            .color-picker {
              width: 40px;
              height: 30px;
              border: 1px solid #ddd;
              border-radius: 4px;
              cursor: pointer;
            }
          }
        }
        
        .action-buttons {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;
          
          .demo-btn {
            padding: 0.6rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            
            &.snapshot {
              background: linear-gradient(135deg, #52c41a, #389e0d);
              color: white;
            }
            
            &.undo {
              background: linear-gradient(135deg, #1890ff, #096dd9);
              color: white;
              
              &:disabled {
                background: #ccc;
                cursor: not-allowed;
              }
            }
            
            &.redo {
              background: linear-gradient(135deg, #722ed1, #531dab);
              color: white;
              
              &:disabled {
                background: #ccc;
                cursor: not-allowed;
              }
            }
            
            &.clear {
              background: #ff6b6b;
              color: white;
            }
            
            &:hover:not(:disabled) {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
          }
        }
      }
      
      .preview-panel {
        .document-preview {
          min-height: 200px;
          padding: 1rem;
          border: 1px solid #ddd;
          border-radius: 6px;
          background: white;
          position: relative;
          
          .preview-content {
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.6;
          }
          
          .preview-placeholder {
            color: #999;
            font-style: italic;
          }
          
          .document-stats {
            position: absolute;
            bottom: 0.5rem;
            right: 0.5rem;
            display: flex;
            gap: 1rem;
            
            .stat {
              font-size: 0.8rem;
              color: #666;
              background: rgba(255, 255, 255, 0.8);
              padding: 0.2rem 0.5rem;
              border-radius: 4px;
            }
          }
        }
      }
    }
    
    .demo-output {
      .history-panel {
        margin-bottom: 1.5rem;
        
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .history-controls {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 6px;
          
          .history-stats {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            
            .stat-item {
              font-size: 0.9rem;
              color: #666;
            }
          }
          
          .clear-history {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            background: #ff6b6b;
            color: white;
            cursor: pointer;
            font-size: 0.9rem;
            
            &:hover {
              background: #ff5252;
            }
          }
        }
        
        .history-list {
          max-height: 300px;
          overflow-y: auto;
          border: 1px solid #e0e0e0;
          border-radius: 6px;
          
          .history-item {
            display: flex;
            align-items: center;
            padding: 0.8rem;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s ease;
            
            &:hover {
              background: #f8f9fa;
            }
            
            &.current {
              background: #e6f7ff;
              border-left: 4px solid #1890ff;
            }
            
            .history-index {
              width: 30px;
              text-align: center;
              color: #666;
              font-weight: 500;
              font-size: 0.9rem;
            }
            
            .history-content {
              flex: 1;
              margin-left: 1rem;
              
              .history-description {
                font-weight: 500;
                color: #333;
                margin-bottom: 0.2rem;
              }
              
              .history-timestamp {
                font-size: 0.8rem;
                color: #999;
              }
            }
            
            .history-indicator {
              .current-indicator {
                font-size: 1.2rem;
              }
            }
          }
        }
      }
      
      .memento-explanation {
        background: linear-gradient(135deg, #f0f8ff, #ffffff);
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e0f0ff;
        
        h5 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .explanation-content {
          .pattern-roles {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
            
            .role-item {
              background: white;
              padding: 1rem;
              border-radius: 6px;
              border: 1px solid #e0e0e0;
              
              h6 {
                color: #333;
                margin: 0 0 0.5rem 0;
                font-size: 1rem;
              }
              
              p {
                color: #666;
                margin: 0 0 0.5rem 0;
                font-size: 0.9rem;
              }
              
              ul {
                list-style-type: disc;
                padding-left: 1rem;
                margin: 0;
                
                li {
                  color: #666;
                  font-size: 0.85rem;
                  margin-bottom: 0.2rem;
                }
              }
            }
          }
          
          .pattern-benefit {
            background: #667eea;
            color: white;
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
            font-size: 0.9rem;
          }
        }
      }
    }
  }

  .advantages {
    h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 0.3rem;
        padding-left: 1rem;
        color: #666;
      }
    }
  }
}
</style>
