<template>
  <div class="singleton-demo">
    <div class="pattern-info">
      <p class="description">
        单例模式确保一个类只有一个实例，并提供一个全局访问点。这在需要控制资源访问、配置管理、日志记录等场景中非常有用。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>数据库连接池</li>
          <li>配置管理器</li>
          <li>日志记录器</li>
          <li>缓存管理</li>
          <li>线程池</li>
        </ul>
      </div>
    </div>

    <div class="demo-section">
      <h4>交互式演示：</h4>
      <div class="demo-controls">
        <button @click="createLogger" class="demo-btn">
          创建日志记录器实例
        </button>
        <button @click="createAnotherLogger" class="demo-btn">
          创建另一个日志记录器实例
        </button>
        <button @click="logMessage" class="demo-btn">记录日志</button>
        <button @click="clearLogs" class="demo-btn clear">清空日志</button>
      </div>

      <div class="demo-output">
        <div class="instance-info">
          <p><strong>实例1 ID:</strong> {{ logger1?.getId() || "未创建" }}</p>
          <p><strong>实例2 ID:</strong> {{ logger2?.getId() || "未创建" }}</p>
          <p>
            <strong>是否为同一实例:</strong>
            <span
              :class="{
                same: areSameInstance,
                different: !areSameInstance,
              }"
            >
              {{ areSameInstance ? "是 ✅" : "否 ❌" }}
            </span>
          </p>
        </div>

        <div class="logs-container">
          <h5>日志输出：</h5>
          <div class="logs">
            <div v-for="(log, index) in logs" :key="index" class="log-entry">
              {{ log }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 控制实例数量，节省内存</li>
        <li>✅ 提供全局访问点</li>
        <li>✅ 延迟初始化</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 违反单一职责原则</li>
        <li>❌ 难以进行单元测试</li>
        <li>❌ 在多线程环境下需要特殊处理</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { logger, type LoggerType } from "@/patterns/Singleton";

// 响应式数据
const logger1 = ref<LoggerType | null>(null);
const logger2 = ref<LoggerType | null>(null);
const logs = ref<string[]>([]);

// 计算属性
const areSameInstance = computed(() => {
  return logger1.value && logger2.value && logger1.value === logger2.value;
});

// 方法
const createLogger = () => {
  logger1.value = logger;
  updateLogs();
};

const createAnotherLogger = () => {
  logger2.value = logger;
  updateLogs();
};

const logMessage = () => {
  if (logger1.value) {
    const messages = [
      "用户登录成功",
      "数据库连接建立",
      "缓存更新完成",
      "文件上传成功",
      "邮件发送完成",
    ];
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    logger1.value.log(randomMessage);
    updateLogs();
  }
};

const clearLogs = () => {
  if (logger1.value) {
    logger1.value.clearLogs();
    updateLogs();
  }
};

const updateLogs = () => {
  if (logger1.value) {
    logs.value = logger1.value.getLogs();
  }
};
</script>

<style lang="scss" scoped>
.singleton-demo {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .pattern-info {
    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #667eea;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .demo-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .demo-controls {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 0.8rem;
      margin-bottom: 1.5rem;

      .demo-btn {
        padding: 0.8rem 1rem;
        border: none;
        border-radius: 6px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;
        font-size: 0.9rem;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        &.clear {
          background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }
      }
    }

    .demo-output {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .instance-info {
        margin-bottom: 1.5rem;

        p {
          margin: 0.5rem 0;
          font-size: 0.95rem;

          .same {
            color: #28a745;
            font-weight: 600;
          }

          .different {
            color: #dc3545;
            font-weight: 600;
          }
        }
      }

      .logs-container {
        h5 {
          margin: 0 0 1rem 0;
          color: #333;
        }

        .logs {
          background: #2d3748;
          color: #e2e8f0;
          padding: 1rem;
          border-radius: 6px;
          max-height: 200px;
          overflow-y: auto;
          font-family: "Monaco", "Consolas", monospace;
          font-size: 0.85rem;

          .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
            border-bottom: 1px solid #4a5568;

            &:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}
</style>
