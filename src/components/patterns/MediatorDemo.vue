<template>
  <div class="mediator-demo">
    <div class="pattern-info">
      <p class="description">
        中介者模式定义一个中介对象来封装一系列对象之间的交互。中介者使各对象不需要显式地相互引用，从而使其耦合松散，而且可以独立地改变它们之间的交互。
      </p>
    </div>

    <div class="demo-section">
      <h4>交互式演示 - 聊天室系统：</h4>
      <div class="demo-controls">
        <div class="user-management">
          <h6>用户管理</h6>
          <div class="add-user-form">
            <input v-model="newUserName" placeholder="用户名" class="user-input" />
            <select v-model="newUserType" class="user-type-select">
              <option value="regular">普通用户</option>
              <option value="admin">管理员</option>
            </select>
            <button @click="addUser" class="demo-btn">添加用户</button>
          </div>
        </div>

        <div class="message-controls">
          <h6>发送消息</h6>
          <div class="message-form">
            <select v-model="selectedSender" class="sender-select">
              <option value="">选择发送者</option>
              <option v-for="user in users" :key="user.name" :value="user.name">
                {{ user.name }} ({{ user.type }})
              </option>
            </select>
            <input v-model="messageText" placeholder="输入消息..." class="message-input" />
            <button @click="sendMessage" :disabled="!selectedSender || !messageText" class="demo-btn">发送</button>
          </div>
        </div>
      </div>

      <div class="demo-output">
        <div class="chat-display">
          <h5>聊天记录</h5>
          <div class="messages-container">
            <div v-for="(message, index) in messages" :key="index" class="message-item" :class="message.type">
              <div class="message-header">
                <span class="sender">{{ message.sender }}</span>
                <span class="timestamp">{{ formatTime(message.timestamp) }}</span>
              </div>
              <div class="message-content">{{ message.content }}</div>
            </div>
          </div>
        </div>

        <div class="users-display">
          <h5>在线用户 ({{ users.length }})</h5>
          <div class="users-list">
            <div v-for="user in users" :key="user.name" class="user-item" :class="user.type">
              <span class="user-name">{{ user.name }}</span>
              <span class="user-type">{{ user.type === 'admin' ? '管理员' : '普通用户' }}</span>
              <button @click="removeUser(user.name)" class="remove-btn">×</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 减少类间的依赖，降低了耦合</li>
        <li>✅ 将多对多转化为一对多，易于理解和维护</li>
        <li>✅ 符合迪米特法则</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 中介者会庞大，变得复杂难以维护</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { ChatRoom, RegularUser, AdminUser } from "../../patterns/Mediator";

// 响应式数据
const newUserName = ref<string>('');
const newUserType = ref<'regular' | 'admin'>('regular');
const selectedSender = ref<string>('');
const messageText = ref<string>('');
const messages = ref<Array<{
  sender: string;
  content: string;
  timestamp: Date;
  type: 'regular' | 'admin' | 'system';
}>>([]);
const users = ref<Array<{ name: string; type: 'regular' | 'admin' }>>([]);

// 创建聊天室
const chatRoom = new ChatRoom('演示聊天室');
const userInstances = new Map<string, RegularUser | AdminUser>();

// 方法
const addUser = () => {
  if (!newUserName.value.trim()) {
    alert('请输入用户名');
    return;
  }

  if (users.value.some(u => u.name === newUserName.value)) {
    alert('用户名已存在');
    return;
  }

  const user = newUserType.value === 'admin' 
    ? new AdminUser(newUserName.value, chatRoom)
    : new RegularUser(newUserName.value, chatRoom);

  userInstances.set(newUserName.value, user);
  chatRoom.addUser(user);
  
  users.value.push({
    name: newUserName.value,
    type: newUserType.value
  });

  // 添加系统消息
  messages.value.push({
    sender: 'System',
    content: `${newUserName.value} 加入了聊天室`,
    timestamp: new Date(),
    type: 'system'
  });

  newUserName.value = '';
};

const removeUser = (userName: string) => {
  const user = userInstances.get(userName);
  if (user) {
    chatRoom.removeUser(user);
    userInstances.delete(userName);
    users.value = users.value.filter(u => u.name !== userName);
    
    // 添加系统消息
    messages.value.push({
      sender: 'System',
      content: `${userName} 离开了聊天室`,
      timestamp: new Date(),
      type: 'system'
    });

    // 如果删除的是当前选中的发送者，清空选择
    if (selectedSender.value === userName) {
      selectedSender.value = '';
    }
  }
};

const sendMessage = () => {
  const sender = userInstances.get(selectedSender.value);
  if (sender && messageText.value.trim()) {
    // 发送消息前先添加到显示列表
    const senderType = users.value.find(u => u.name === selectedSender.value)?.type || 'regular';
    messages.value.push({
      sender: selectedSender.value,
      content: messageText.value,
      timestamp: new Date(),
      type: senderType
    });

    // 通过中介者发送消息
    sender.send(messageText.value);
    
    messageText.value = '';
  }
};

const formatTime = (timestamp: Date): string => {
  return timestamp.toLocaleTimeString();
};
</script>

<style lang="scss" scoped>
.mediator-demo {
  .pattern-info {
    margin-bottom: 2rem;
    
    .description {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }
  }

  .demo-section {
    margin-bottom: 2rem;
    
    h4 {
      color: #333;
      margin-bottom: 1rem;
    }
    
    .demo-controls {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
      
      .user-management, .message-controls {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        
        h6 {
          color: #333;
          margin: 0 0 1rem 0;
        }
        
        .add-user-form, .message-form {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          
          .user-input, .message-input, .sender-select, .user-type-select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
          }
          
          .demo-btn {
            padding: 0.6rem;
            border: none;
            border-radius: 6px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &:disabled {
              background: #ccc;
              cursor: not-allowed;
            }
            
            &:hover:not(:disabled) {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            }
          }
        }
      }
    }
    
    .demo-output {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 1.5rem;
      
      .chat-display {
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .messages-container {
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          max-height: 400px;
          overflow-y: auto;
          padding: 1rem;
          background: white;
          
          .message-item {
            margin-bottom: 1rem;
            padding: 0.8rem;
            border-radius: 8px;
            
            &.regular {
              background: #f0f8ff;
              border-left: 4px solid #1890ff;
            }
            
            &.admin {
              background: #fff2e6;
              border-left: 4px solid #fa8c16;
            }
            
            &.system {
              background: #f6ffed;
              border-left: 4px solid #52c41a;
              text-align: center;
              font-style: italic;
            }
            
            .message-header {
              display: flex;
              justify-content: space-between;
              margin-bottom: 0.5rem;
              
              .sender {
                font-weight: 600;
                color: #333;
              }
              
              .timestamp {
                font-size: 0.8rem;
                color: #999;
              }
            }
            
            .message-content {
              color: #555;
            }
          }
        }
      }
      
      .users-display {
        h5 {
          color: #333;
          margin-bottom: 1rem;
        }
        
        .users-list {
          .user-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            
            &.admin {
              border-left: 4px solid #fa8c16;
            }
            
            &.regular {
              border-left: 4px solid #1890ff;
            }
            
            .user-name {
              font-weight: 500;
              color: #333;
            }
            
            .user-type {
              font-size: 0.8rem;
              color: #666;
            }
            
            .remove-btn {
              background: #ff4d4f;
              color: white;
              border: none;
              border-radius: 50%;
              width: 24px;
              height: 24px;
              cursor: pointer;
              font-size: 0.9rem;
              
              &:hover {
                background: #d32f2f;
              }
            }
          }
        }
      }
    }
  }

  .advantages {
    h4 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    ul {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 0.3rem;
        padding-left: 1rem;
        color: #666;
      }
    }
  }
}
</style>
