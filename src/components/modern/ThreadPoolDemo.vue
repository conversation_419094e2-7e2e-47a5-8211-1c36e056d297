<template>
  <div class="thread-pool-demo">
    <!-- 模式信息说明 -->
    <div class="pattern-info">
      <p class="description">
        线程池模式（Thread Pool
        Pattern）是一种并发设计模式，通过预先创建固定数量的线程来重复使用，
        避免频繁创建和销毁线程的开销。线程池维护一个任务队列，工作线程从队列中获取任务执行，
        这种模式在高并发场景下能显著提升性能和资源利用率。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>Web服务器请求处理</li>
          <li>数据库连接池</li>
          <li>批量数据处理</li>
          <li>异步任务执行</li>
          <li>I/O密集型操作</li>
          <li>定时任务调度</li>
        </ul>
      </div>
    </div>

    <!-- 线程池架构图 -->
    <div class="architecture-section">
      <h4>🏗️ 线程池架构图</h4>
      <div class="architecture-diagram">
        <div class="thread-pool-container">
          <!-- 任务队列 -->
          <div class="task-queue">
            <h5>任务队列</h5>
            <div class="queue-items">
              <div
                v-for="(task, index) in taskQueue.slice(0, 5)"
                :key="index"
                class="task-item"
                :class="{ executing: task.status === 'executing' }"
              >
                {{ task.name }}
              </div>
              <div v-if="taskQueue.length > 5" class="more-tasks">
                +{{ taskQueue.length - 5 }} 更多
              </div>
            </div>
          </div>

          <!-- 线程池 -->
          <div class="thread-pool">
            <h5>线程池 ({{ poolSize }}个线程)</h5>
            <div class="threads">
              <div
                v-for="thread in threads"
                :key="thread.id"
                class="thread"
                :class="{
                  busy: thread.status === 'busy',
                  idle: thread.status === 'idle',
                }"
              >
                <div class="thread-id">T{{ thread.id }}</div>
                <div class="thread-status">
                  {{ thread.status === "busy" ? "忙碌" : "空闲" }}
                </div>
                <div v-if="thread.currentTask" class="current-task">
                  {{ thread.currentTask }}
                </div>
              </div>
            </div>
          </div>

          <!-- 执行结果 -->
          <div class="results">
            <h5>执行结果</h5>
            <div class="result-items">
              <div
                v-for="result in completedTasks.slice(-5)"
                :key="result.id"
                class="result-item"
              >
                <span class="task-name">{{ result.name }}</span>
                <span class="execution-time">{{ result.executionTime }}ms</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 交互式演示 -->
    <div class="demo-section">
      <h4>交互式演示：</h4>

      <!-- 线程池配置 -->
      <div class="demo-subsection">
        <h5>1. 线程池配置</h5>
        <div class="demo-controls">
          <div class="input-group">
            <label>线程池大小：</label>
            <input
              v-model.number="poolSize"
              type="number"
              min="1"
              max="8"
              class="demo-input"
              @change="updatePoolSize"
            />
          </div>
          <div class="input-group">
            <label>队列容量：</label>
            <input
              v-model.number="queueCapacity"
              type="number"
              min="5"
              max="50"
              class="demo-input"
            />
          </div>
          <button @click="resetThreadPool" class="demo-btn">重置线程池</button>
        </div>
      </div>

      <!-- 任务提交 -->
      <div class="demo-subsection">
        <h5>2. 任务提交</h5>
        <div class="demo-controls">
          <div class="input-group">
            <label>任务类型：</label>
            <select v-model="selectedTaskType" class="demo-select">
              <option value="cpu">CPU密集型 (1-3秒)</option>
              <option value="io">I/O密集型 (2-5秒)</option>
              <option value="quick">快速任务 (0.5-1秒)</option>
              <option value="slow">慢速任务 (3-8秒)</option>
            </select>
          </div>
          <div class="input-group">
            <label>批量数量：</label>
            <input
              v-model.number="batchSize"
              type="number"
              min="1"
              max="20"
              class="demo-input"
            />
          </div>
          <button @click="submitSingleTask" class="demo-btn">
            提交单个任务
          </button>
          <button @click="submitBatchTasks" class="demo-btn">批量提交</button>
        </div>

        <div class="demo-output">
          <div class="stats">
            <div class="stat-item">
              <strong>队列中任务:</strong> {{ taskQueue.length }}
            </div>
            <div class="stat-item">
              <strong>执行中任务:</strong> {{ executingTasks }}
            </div>
            <div class="stat-item">
              <strong>已完成任务:</strong> {{ completedTasks.length }}
            </div>
            <div class="stat-item">
              <strong>平均执行时间:</strong> {{ averageExecutionTime }}ms
            </div>
          </div>
        </div>
      </div>

      <!-- 性能对比 -->
      <div class="demo-subsection">
        <h5>3. 性能对比演示</h5>
        <div class="demo-controls">
          <button
            @click="runPerformanceTest"
            class="demo-btn"
            :disabled="isRunningTest"
          >
            {{ isRunningTest ? "测试中..." : "运行性能测试" }}
          </button>
          <button @click="clearResults" class="demo-btn">清空结果</button>
        </div>

        <div class="demo-output">
          <div v-if="performanceResults.length > 0" class="performance-results">
            <h6>性能测试结果：</h6>
            <div
              class="test-result"
              v-for="result in performanceResults"
              :key="result.id"
            >
              <div class="test-name">{{ result.name }}</div>
              <div class="test-metrics">
                <span>总时间: {{ result.totalTime }}ms</span>
                <span>吞吐量: {{ result.throughput }} 任务/秒</span>
                <span>平均延迟: {{ result.averageLatency }}ms</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优缺点分析 -->
    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 减少线程创建和销毁的开销</li>
        <li>✅ 控制并发线程数量，避免资源耗尽</li>
        <li>✅ 提高系统响应速度和吞吐量</li>
        <li>✅ 更好的资源管理和控制</li>
        <li>✅ 支持任务队列，处理突发请求</li>
        <li>✅ 可配置的拒绝策略</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 增加了系统复杂性</li>
        <li>❌ 需要合理配置线程数量</li>
        <li>❌ 可能出现任务积压</li>
        <li>❌ 调试和监控相对困难</li>
        <li>❌ 内存占用相对较高</li>
      </ul>

      <h4>关键参数：</h4>
      <ul>
        <li>🔧 <strong>核心线程数:</strong> 始终保持活跃的线程数量</li>
        <li>🔧 <strong>最大线程数:</strong> 线程池允许的最大线程数</li>
        <li>🔧 <strong>队列容量:</strong> 任务队列的最大容量</li>
        <li>🔧 <strong>拒绝策略:</strong> 队列满时的处理策略</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { ThreadPool, Task, ThreadPoolConfig } from "@/patterns/ThreadPool";

// 响应式数据
const poolSize = ref<number>(4);
const queueCapacity = ref<number>(10);
const selectedTaskType = ref<string>("cpu");
const batchSize = ref<number>(5);
const isRunningTest = ref<boolean>(false);

// 线程池实例
const threadPool = ref<ThreadPool | null>(null);

// 状态数据
const taskQueue = ref<Task[]>([]);
const threads = ref<
  Array<{
    id: number;
    status: "idle" | "busy";
    currentTask?: string;
  }>
>([]);
const completedTasks = ref<
  Array<{
    id: string;
    name: string;
    executionTime: number;
  }>
>([]);
const performanceResults = ref<
  Array<{
    id: string;
    name: string;
    totalTime: number;
    throughput: number;
    averageLatency: number;
  }>
>([]);

// 计算属性
const executingTasks = computed(() => {
  return threads.value.filter((t) => t.status === "busy").length;
});

const averageExecutionTime = computed(() => {
  if (completedTasks.value.length === 0) return 0;
  const total = completedTasks.value.reduce(
    (sum, task) => sum + task.executionTime,
    0
  );
  return Math.round(total / completedTasks.value.length);
});

// 任务类型配置
const getTaskConfig = (type: string) => {
  const configs = {
    cpu: { name: "CPU密集型", minTime: 1000, maxTime: 3000 },
    io: { name: "I/O密集型", minTime: 2000, maxTime: 5000 },
    quick: { name: "快速任务", minTime: 500, maxTime: 1000 },
    slow: { name: "慢速任务", minTime: 3000, maxTime: 8000 },
  };
  return configs[type as keyof typeof configs] || configs.cpu;
};

// 方法
const updatePoolSize = () => {
  if (threadPool.value) {
    threadPool.value.resize(poolSize.value);
    updateThreadsDisplay();
  }
};

const resetThreadPool = () => {
  if (threadPool.value) {
    threadPool.value.shutdown();
  }

  const config: ThreadPoolConfig = {
    corePoolSize: poolSize.value,
    maximumPoolSize: poolSize.value,
    queueCapacity: queueCapacity.value,
  };

  threadPool.value = new ThreadPool(config);
  setupThreadPoolListeners();
  updateThreadsDisplay();
  taskQueue.value = [];
  completedTasks.value = [];
};

const setupThreadPoolListeners = () => {
  if (!threadPool.value) return;

  // 监听任务状态变化
  threadPool.value.onTaskStart = (task: Task) => {
    updateThreadsDisplay();
    updateTaskQueue();
  };

  threadPool.value.onTaskComplete = (task: Task, executionTime: number) => {
    completedTasks.value.push({
      id: task.id,
      name: task.name,
      executionTime,
    });
    updateThreadsDisplay();
    updateTaskQueue();
  };
};

const updateThreadsDisplay = () => {
  if (!threadPool.value) return;

  const poolThreads = threadPool.value.getThreads();
  threads.value = poolThreads.map((thread) => ({
    id: thread.id,
    status: thread.isBusy() ? "busy" : "idle",
    currentTask: thread.getCurrentTask()?.name,
  }));
};

const updateTaskQueue = () => {
  if (!threadPool.value) return;
  taskQueue.value = threadPool.value.getQueuedTasks();
};

const submitSingleTask = () => {
  if (!threadPool.value) return;

  const config = getTaskConfig(selectedTaskType.value);
  const taskName = `${config.name}-${Date.now()}`;
  const executionTime =
    Math.random() * (config.maxTime - config.minTime) + config.minTime;

  const task = new Task(taskName, () => {
    return new Promise((resolve) => {
      setTimeout(resolve, executionTime);
    });
  });

  threadPool.value.submit(task);
  updateTaskQueue();
};

const submitBatchTasks = () => {
  for (let i = 0; i < batchSize.value; i++) {
    submitSingleTask();
  }
};

const runPerformanceTest = async () => {
  if (!threadPool.value || isRunningTest.value) return;

  isRunningTest.value = true;

  // 测试不同配置的性能
  const testConfigs = [
    { poolSize: 2, taskCount: 20, name: "小线程池 (2线程, 20任务)" },
    { poolSize: 4, taskCount: 20, name: "中线程池 (4线程, 20任务)" },
    { poolSize: 8, taskCount: 20, name: "大线程池 (8线程, 20任务)" },
  ];

  for (const config of testConfigs) {
    const startTime = Date.now();

    // 重置线程池
    poolSize.value = config.poolSize;
    resetThreadPool();

    // 提交任务
    const tasks: Promise<void>[] = [];
    for (let i = 0; i < config.taskCount; i++) {
      const task = new Task(`测试任务-${i}`, () => {
        return new Promise((resolve) => {
          setTimeout(resolve, Math.random() * 1000 + 500);
        });
      });
      tasks.push(threadPool.value!.submit(task));
    }

    // 等待所有任务完成
    await Promise.all(tasks);

    const totalTime = Date.now() - startTime;
    const throughput = Math.round((config.taskCount / totalTime) * 1000);
    const averageLatency = Math.round(totalTime / config.taskCount);

    performanceResults.value.push({
      id: `test-${Date.now()}`,
      name: config.name,
      totalTime,
      throughput,
      averageLatency,
    });
  }

  isRunningTest.value = false;
};

const clearResults = () => {
  completedTasks.value = [];
  performanceResults.value = [];
};

// 生命周期
onMounted(() => {
  resetThreadPool();

  // 定期更新显示
  const interval = setInterval(() => {
    updateThreadsDisplay();
    updateTaskQueue();
  }, 500);

  onUnmounted(() => {
    clearInterval(interval);
    if (threadPool.value) {
      threadPool.value.shutdown();
    }
  });
});
</script>

<style lang="scss" scoped>
.thread-pool-demo {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .pattern-info {
    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #1890ff;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .architecture-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .architecture-diagram {
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .thread-pool-container {
        display: grid;
        grid-template-columns: 1fr 2fr 1fr;
        gap: 2rem;
        align-items: start;

        .task-queue,
        .thread-pool,
        .results {
          background: white;
          padding: 1.5rem;
          border-radius: 8px;
          border: 2px solid #1890ff;

          h5 {
            margin: 0 0 1rem 0;
            color: #1890ff;
            font-weight: 600;
            text-align: center;
          }
        }

        .task-queue {
          .queue-items {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;

            .task-item {
              background: #e3f2fd;
              padding: 0.5rem;
              border-radius: 4px;
              font-size: 0.8rem;
              text-align: center;
              color: #1565c0;

              &.executing {
                background: #ffecb3;
                color: #f57c00;
                animation: pulse 1s infinite;
              }
            }

            .more-tasks {
              color: #666;
              font-size: 0.8rem;
              text-align: center;
              font-style: italic;
            }
          }
        }

        .thread-pool {
          .threads {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.8rem;

            .thread {
              background: #f5f5f5;
              padding: 0.8rem;
              border-radius: 6px;
              text-align: center;
              border: 2px solid transparent;
              transition: all 0.3s ease;

              &.idle {
                border-color: #4caf50;
                background: #e8f5e9;
              }

              &.busy {
                border-color: #ff9800;
                background: #fff3e0;
                animation: working 2s infinite;
              }

              .thread-id {
                font-weight: 600;
                color: #333;
                margin-bottom: 0.3rem;
              }

              .thread-status {
                font-size: 0.8rem;
                color: #666;
                margin-bottom: 0.3rem;
              }

              .current-task {
                font-size: 0.7rem;
                color: #f57c00;
                background: #ffecb3;
                padding: 0.2rem 0.4rem;
                border-radius: 3px;
              }
            }
          }
        }

        .results {
          .result-items {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;

            .result-item {
              background: #e8f5e9;
              padding: 0.5rem;
              border-radius: 4px;
              font-size: 0.8rem;
              display: flex;
              justify-content: space-between;
              align-items: center;

              .task-name {
                color: #2e7d32;
                font-weight: 500;
              }

              .execution-time {
                color: #666;
                font-size: 0.7rem;
              }
            }
          }
        }
      }
    }
  }

  .demo-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .demo-subsection {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      margin-bottom: 1.5rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #1890ff;
        font-weight: 600;
      }

      h6 {
        margin: 0 0 0.8rem 0;
        color: #333;
        font-weight: 600;
      }

      .demo-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1.5rem;
        align-items: end;

        .input-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          label {
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
          }
        }

        .demo-input,
        .demo-select {
          padding: 0.6rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
          min-width: 120px;

          &:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .demo-btn {
          padding: 0.6rem 1.2rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #1890ff, #722ed1);
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          font-size: 0.9rem;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }

      .demo-output {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .stats {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 1rem;

          .stat-item {
            text-align: center;
            padding: 0.8rem;
            background: #f8f9fa;
            border-radius: 6px;
            font-size: 0.9rem;

            strong {
              display: block;
              color: #333;
              margin-bottom: 0.3rem;
            }
          }
        }

        .performance-results {
          .test-result {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            border-left: 4px solid #1890ff;

            .test-name {
              font-weight: 600;
              color: #333;
              margin-bottom: 0.5rem;
            }

            .test-metrics {
              display: flex;
              gap: 1rem;
              flex-wrap: wrap;

              span {
                background: white;
                padding: 0.3rem 0.6rem;
                border-radius: 4px;
                font-size: 0.8rem;
                color: #666;
              }
            }
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes working {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@media (max-width: 768px) {
  .thread-pool-demo {
    .architecture-section {
      .architecture-diagram {
        .thread-pool-container {
          grid-template-columns: 1fr;
          gap: 1rem;
        }
      }
    }

    .demo-section {
      .demo-subsection {
        .demo-controls {
          flex-direction: column;
          align-items: stretch;

          .demo-input,
          .demo-select {
            min-width: auto;
          }
        }

        .demo-output {
          .stats {
            grid-template-columns: 1fr;
          }

          .performance-results {
            .test-result {
              .test-metrics {
                flex-direction: column;
                gap: 0.5rem;
              }
            }
          }
        }
      }
    }
  }
}
</style>
