<template>
  <div class="mvp-demo">
    <!-- 模式信息说明 -->
    <div class="pattern-info">
      <p class="description">
        MVP模式（Model-View-Presenter
        Pattern）是MVC模式的变体，通过Presenter作为View和Model之间的中介，
        实现了View的完全被动化。在MVP中，View不直接与Model交互，所有的UI逻辑都由Presenter处理，
        这使得View更容易进行单元测试，并提高了代码的可维护性和可测试性。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>Android应用开发（Activity/Fragment + Presenter）</li>
          <li>WinForms/WPF桌面应用</li>
          <li>需要高度可测试性的UI应用</li>
          <li>复杂的用户交互逻辑</li>
          <li>团队协作开发（UI和逻辑分离）</li>
          <li>遗留系统重构</li>
        </ul>
      </div>
    </div>

    <!-- MVP架构图 -->
    <div class="architecture-section">
      <h4>🏗️ MVP架构图</h4>
      <div class="architecture-diagram">
        <div class="mvp-container">
          <!-- Model层 -->
          <div class="model-layer">
            <h5>Model 层</h5>
            <div class="model-content">
              <div class="data-section">
                <h6>用户数据</h6>
                <div
                  class="data-item"
                  v-for="user in modelData.users"
                  :key="user.id"
                >
                  <span class="user-info"
                    >{{ user.name }} ({{ user.email }})</span
                  >
                  <span
                    class="user-status"
                    :class="user.active ? 'active' : 'inactive'"
                  >
                    {{ user.active ? "活跃" : "非活跃" }}
                  </span>
                </div>
              </div>
              <div class="business-logic">
                <h6>业务逻辑</h6>
                <div class="logic-item">数据验证</div>
                <div class="logic-item">业务规则</div>
                <div class="logic-item">数据持久化</div>
              </div>
            </div>
          </div>

          <!-- Presenter层 -->
          <div class="presenter-layer">
            <h5>Presenter 层</h5>
            <div class="presenter-content">
              <div class="presenter-state">
                <h6>当前状态</h6>
                <div class="state-item">
                  <strong>加载状态:</strong>
                  {{ presenterState.loading ? "加载中" : "空闲" }}
                </div>
                <div class="state-item">
                  <strong>选中用户:</strong>
                  {{ presenterState.selectedUser?.name || "无" }}
                </div>
                <div class="state-item">
                  <strong>操作历史:</strong>
                  {{ presenterState.operationHistory.length }} 条
                </div>
              </div>
              <div class="presenter-methods">
                <h6>Presenter方法</h6>
                <div class="method-item">loadUsers()</div>
                <div class="method-item">selectUser(id)</div>
                <div class="method-item">updateUser(user)</div>
                <div class="method-item">deleteUser(id)</div>
              </div>
            </div>
          </div>

          <!-- View层 -->
          <div class="view-layer">
            <h5>View 层</h5>
            <div class="view-content">
              <div class="view-state">
                <h6>UI状态</h6>
                <div
                  class="ui-element"
                  :class="{ active: viewState.showUserList }"
                >
                  用户列表: {{ viewState.showUserList ? "显示" : "隐藏" }}
                </div>
                <div
                  class="ui-element"
                  :class="{ active: viewState.showUserDetail }"
                >
                  用户详情: {{ viewState.showUserDetail ? "显示" : "隐藏" }}
                </div>
                <div
                  class="ui-element"
                  :class="{ active: viewState.showLoading }"
                >
                  加载指示器: {{ viewState.showLoading ? "显示" : "隐藏" }}
                </div>
              </div>
              <div class="view-events">
                <h6>View事件</h6>
                <div class="event-item">onUserClick</div>
                <div class="event-item">onAddUser</div>
                <div class="event-item">onEditUser</div>
                <div class="event-item">onDeleteUser</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 交互箭头 -->
        <div class="interaction-arrows">
          <div class="arrow model-to-presenter">
            <span class="arrow-label">数据通知</span>
          </div>
          <div class="arrow presenter-to-model">
            <span class="arrow-label">业务调用</span>
          </div>
          <div class="arrow view-to-presenter">
            <span class="arrow-label">用户事件</span>
          </div>
          <div class="arrow presenter-to-view">
            <span class="arrow-label">UI更新</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 交互式演示 -->
    <div class="demo-section">
      <h4>交互式演示：</h4>

      <!-- 用户管理演示 -->
      <div class="demo-subsection">
        <h5>1. 用户管理系统 (MVP实现)</h5>
        <div class="demo-controls">
          <button @click="loadUsers" class="demo-btn" :disabled="isLoading">
            {{ isLoading ? "加载中..." : "加载用户" }}
          </button>
          <button @click="addUser" class="demo-btn">添加用户</button>
          <button @click="clearUsers" class="demo-btn">清空用户</button>
          <button @click="showArchitecture" class="demo-btn">
            显示架构调用
          </button>
        </div>

        <div class="demo-output">
          <div class="user-management">
            <div class="user-list">
              <h6>用户列表</h6>
              <div v-if="displayUsers.length === 0" class="empty-state">
                暂无用户数据
              </div>
              <div
                v-for="user in displayUsers"
                :key="user.id"
                class="user-item"
                :class="{ selected: selectedUserId === user.id }"
                @click="selectUser(user.id)"
              >
                <div class="user-avatar">{{ user.name.charAt(0) }}</div>
                <div class="user-info">
                  <div class="user-name">{{ user.name }}</div>
                  <div class="user-email">{{ user.email }}</div>
                </div>
                <div class="user-actions">
                  <button
                    @click.stop="editUser(user.id)"
                    class="action-btn edit"
                  >
                    编辑
                  </button>
                  <button
                    @click.stop="deleteUser(user.id)"
                    class="action-btn delete"
                  >
                    删除
                  </button>
                </div>
              </div>
            </div>

            <div class="user-detail" v-if="selectedUser">
              <h6>用户详情</h6>
              <div class="detail-content">
                <div class="detail-item">
                  <strong>ID:</strong> {{ selectedUser.id }}
                </div>
                <div class="detail-item">
                  <strong>姓名:</strong> {{ selectedUser.name }}
                </div>
                <div class="detail-item">
                  <strong>邮箱:</strong> {{ selectedUser.email }}
                </div>
                <div class="detail-item">
                  <strong>状态:</strong>
                  <span
                    :class="
                      selectedUser.active ? 'status-active' : 'status-inactive'
                    "
                  >
                    {{ selectedUser.active ? "活跃" : "非活跃" }}
                  </span>
                </div>
                <div class="detail-item">
                  <strong>创建时间:</strong>
                  {{ formatDate(selectedUser.createdAt) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 架构对比演示 -->
      <div class="demo-subsection">
        <h5>2. 架构模式对比</h5>
        <div class="demo-controls">
          <div class="pattern-selector">
            <label>选择架构模式：</label>
            <select v-model="selectedPattern" class="demo-select">
              <option value="mvc">MVC模式</option>
              <option value="mvp">MVP模式</option>
              <option value="mvvm">MVVM模式</option>
            </select>
          </div>
          <button @click="demonstratePattern" class="demo-btn">
            演示架构调用
          </button>
        </div>

        <div class="demo-output">
          <div class="pattern-comparison">
            <div class="comparison-item">
              <h6>{{ getPatternName(selectedPattern) }} 特点</h6>
              <ul>
                <li
                  v-for="feature in getPatternFeatures(selectedPattern)"
                  :key="feature"
                >
                  {{ feature }}
                </li>
              </ul>
            </div>
            <div class="comparison-item">
              <h6>调用流程</h6>
              <div class="call-flow">
                <div
                  v-for="(step, index) in callFlow"
                  :key="index"
                  class="flow-step"
                  :class="{ active: step.active }"
                >
                  <span class="step-number">{{ index + 1 }}</span>
                  <span class="step-description">{{ step.description }}</span>
                  <span class="step-component">{{ step.component }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 测试友好性演示 -->
      <div class="demo-subsection">
        <h5>3. 单元测试演示</h5>
        <div class="demo-controls">
          <button @click="runPresenterTests" class="demo-btn">
            运行Presenter测试
          </button>
          <button @click="runViewTests" class="demo-btn">运行View测试</button>
          <button @click="runModelTests" class="demo-btn">运行Model测试</button>
        </div>

        <div class="demo-output">
          <div class="test-results">
            <div
              v-for="(result, index) in testResults"
              :key="index"
              class="test-result"
            >
              <div class="test-name">{{ result.name }}</div>
              <div class="test-status" :class="result.status">
                {{ result.status === "passed" ? "✅ 通过" : "❌ 失败" }}
              </div>
              <div class="test-description">{{ result.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作日志 -->
      <div class="demo-subsection">
        <h5>4. 操作日志</h5>
        <div class="operation-log">
          <div
            v-for="log in operationLogs.slice(-8)"
            :key="log.id"
            class="log-entry"
            :class="log.type"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-component">{{ log.component }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 优缺点分析 -->
    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ View完全被动，易于单元测试</li>
        <li>✅ Presenter可以独立测试，不依赖UI框架</li>
        <li>✅ View和Model完全解耦，降低依赖</li>
        <li>✅ 业务逻辑集中在Presenter，便于维护</li>
        <li>✅ 支持多个View共享同一个Presenter</li>
        <li>✅ 便于团队协作开发（UI和逻辑分离）</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ Presenter可能变得臃肿，承担过多职责</li>
        <li>❌ View和Presenter之间需要定义大量接口</li>
        <li>❌ 相比MVC增加了代码复杂度</li>
        <li>❌ 学习成本相对较高</li>
        <li>❌ 在简单应用中可能过度设计</li>
      </ul>

      <h4>与其他模式对比：</h4>
      <ul>
        <li>
          🔍 <strong>vs MVC:</strong> View不直接访问Model，通过Presenter中介
        </li>
        <li>🔍 <strong>vs MVVM:</strong> 没有数据绑定，需要手动更新View</li>
        <li>🔍 <strong>测试性:</strong> MVP > MVC，但可能不如MVVM便利</li>
        <li>🔍 <strong>复杂度:</strong> MVP介于MVC和MVVM之间</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  UserModel,
  UserPresenter,
  IUserView,
  User,
  MVPTestSuite,
} from "@/patterns/MVP";

// 响应式数据
const isLoading = ref<boolean>(false);
const selectedUserId = ref<number | null>(null);
const selectedPattern = ref<string>("mvp");

// MVP组件实例
const userModel = ref<UserModel | null>(null);
const userPresenter = ref<UserPresenter | null>(null);

// 状态数据
const modelData = ref<{
  users: User[];
}>({
  users: [],
});

const presenterState = ref<{
  loading: boolean;
  selectedUser: User | null;
  operationHistory: string[];
}>({
  loading: false,
  selectedUser: null,
  operationHistory: [],
});

const viewState = ref<{
  showUserList: boolean;
  showUserDetail: boolean;
  showLoading: boolean;
}>({
  showUserList: true,
  showUserDetail: false,
  showLoading: false,
});

const displayUsers = ref<User[]>([]);
const callFlow = ref<
  Array<{
    description: string;
    component: string;
    active: boolean;
  }>
>([]);

const testResults = ref<
  Array<{
    name: string;
    status: "passed" | "failed";
    description: string;
  }>
>([]);

const operationLogs = ref<
  Array<{
    id: string;
    timestamp: Date;
    component: string;
    message: string;
    type: "model" | "view" | "presenter";
  }>
>([]);

// 计算属性
const selectedUser = computed(() => {
  return (
    displayUsers.value.find((user) => user.id === selectedUserId.value) || null
  );
});

// View接口实现
class VueUserView implements IUserView {
  showLoading(): void {
    viewState.value.showLoading = true;
    addLog("view", "showLoading() - 显示加载指示器");
  }

  hideLoading(): void {
    viewState.value.showLoading = false;
    addLog("view", "hideLoading() - 隐藏加载指示器");
  }

  showUsers(users: User[]): void {
    displayUsers.value = users;
    viewState.value.showUserList = true;
    addLog("view", `showUsers() - 显示${users.length}个用户`);
  }

  showUserDetail(user: User): void {
    selectedUserId.value = user.id;
    viewState.value.showUserDetail = true;
    addLog("view", `showUserDetail() - 显示用户${user.name}的详情`);
  }

  showError(message: string): void {
    alert(`错误: ${message}`);
    addLog("view", `showError() - ${message}`);
  }

  showSuccess(message: string): void {
    // 可以用toast或其他方式显示
    console.log(`成功: ${message}`);
    addLog("view", `showSuccess() - ${message}`);
  }

  clearSelection(): void {
    selectedUserId.value = null;
    viewState.value.showUserDetail = false;
    addLog("view", "clearSelection() - 清除选择");
  }
}

// 方法
const loadUsers = async () => {
  if (userPresenter.value) {
    isLoading.value = true;
    presenterState.value.loading = true;
    await userPresenter.value.loadUsers();
    isLoading.value = false;
    presenterState.value.loading = false;

    // 更新模型数据显示
    if (userModel.value) {
      modelData.value.users = userModel.value.getAllUsers();
    }
  }
};

const selectUser = (userId: number) => {
  if (userPresenter.value) {
    userPresenter.value.selectUser(userId);
    presenterState.value.selectedUser = selectedUser.value;
    presenterState.value.operationHistory.push(`选择用户 ${userId}`);
  }
};

const addUser = () => {
  if (userPresenter.value) {
    const newUser: Omit<User, "id" | "createdAt"> = {
      name: `用户${Date.now()}`,
      email: `user${Date.now()}@example.com`,
      active: true,
    };
    userPresenter.value.addUser(newUser);
    presenterState.value.operationHistory.push("添加新用户");

    // 更新模型数据显示
    if (userModel.value) {
      modelData.value.users = userModel.value.getAllUsers();
    }
  }
};

const editUser = (userId: number) => {
  if (userPresenter.value) {
    const user = displayUsers.value.find((u) => u.id === userId);
    if (user) {
      const updatedUser = {
        ...user,
        name: `${user.name} (已编辑)`,
        active: !user.active,
      };
      userPresenter.value.updateUser(updatedUser);
      presenterState.value.operationHistory.push(`编辑用户 ${userId}`);

      // 更新模型数据显示
      if (userModel.value) {
        modelData.value.users = userModel.value.getAllUsers();
      }
    }
  }
};

const deleteUser = (userId: number) => {
  if (userPresenter.value) {
    userPresenter.value.deleteUser(userId);
    presenterState.value.operationHistory.push(`删除用户 ${userId}`);

    // 更新模型数据显示
    if (userModel.value) {
      modelData.value.users = userModel.value.getAllUsers();
    }
  }
};

const clearUsers = () => {
  if (userModel.value) {
    userModel.value.clearUsers();
    displayUsers.value = [];
    selectedUserId.value = null;
    modelData.value.users = [];
    presenterState.value.operationHistory.push("清空所有用户");
    addLog("model", "clearUsers() - 清空用户数据");
  }
};

const showArchitecture = () => {
  // 演示架构调用流程
  callFlow.value = [
    { description: "用户点击按钮", component: "View", active: false },
    { description: "触发事件处理", component: "Presenter", active: false },
    { description: "调用业务逻辑", component: "Model", active: false },
    {
      description: "返回数据结果",
      component: "Model → Presenter",
      active: false,
    },
    { description: "更新UI显示", component: "Presenter → View", active: false },
  ];

  // 逐步激活显示
  callFlow.value.forEach((step, index) => {
    setTimeout(() => {
      step.active = true;
    }, index * 500);
  });
};

const demonstratePattern = () => {
  const patterns = {
    mvc: [
      { description: "用户操作View", component: "View", active: false },
      {
        description: "View直接调用Controller",
        component: "Controller",
        active: false,
      },
      { description: "Controller操作Model", component: "Model", active: false },
      {
        description: "Model通知View更新",
        component: "Model → View",
        active: false,
      },
    ],
    mvp: [
      { description: "用户操作View", component: "View", active: false },
      {
        description: "View通知Presenter",
        component: "Presenter",
        active: false,
      },
      { description: "Presenter操作Model", component: "Model", active: false },
      {
        description: "Presenter更新View",
        component: "Presenter → View",
        active: false,
      },
    ],
    mvvm: [
      { description: "用户操作View", component: "View", active: false },
      {
        description: "View绑定ViewModel",
        component: "ViewModel",
        active: false,
      },
      { description: "ViewModel操作Model", component: "Model", active: false },
      {
        description: "数据绑定自动更新",
        component: "ViewModel → View",
        active: false,
      },
    ],
  };

  callFlow.value = patterns[selectedPattern.value as keyof typeof patterns];

  // 逐步激活显示
  callFlow.value.forEach((step, index) => {
    setTimeout(() => {
      step.active = true;
    }, index * 600);
  });
};

const getPatternName = (pattern: string): string => {
  const names = {
    mvc: "MVC模式",
    mvp: "MVP模式",
    mvvm: "MVVM模式",
  };
  return names[pattern as keyof typeof names] || pattern;
};

const getPatternFeatures = (pattern: string): string[] => {
  const features = {
    mvc: [
      "View可以直接访问Model",
      "Controller处理用户输入",
      "Model通知View更新",
      "紧耦合，难以测试",
    ],
    mvp: [
      "View完全被动，不访问Model",
      "Presenter处理所有UI逻辑",
      "View和Model完全解耦",
      "易于单元测试",
    ],
    mvvm: [
      "View通过数据绑定连接ViewModel",
      "ViewModel暴露数据和命令",
      "自动同步View和ViewModel",
      "声明式UI更新",
    ],
  };
  return features[pattern as keyof typeof features] || [];
};

const runPresenterTests = () => {
  const testSuite = new MVPTestSuite();
  const results = testSuite.runPresenterTests();
  testResults.value = results;
  addLog("presenter", `运行Presenter测试 - ${results.length}个测试用例`);
};

const runViewTests = () => {
  const testSuite = new MVPTestSuite();
  const results = testSuite.runViewTests();
  testResults.value = results;
  addLog("view", `运行View测试 - ${results.length}个测试用例`);
};

const runModelTests = () => {
  const testSuite = new MVPTestSuite();
  const results = testSuite.runModelTests();
  testResults.value = results;
  addLog("model", `运行Model测试 - ${results.length}个测试用例`);
};

const addLog = (component: "model" | "view" | "presenter", message: string) => {
  operationLogs.value.push({
    id: `log-${Date.now()}-${Math.random()}`,
    timestamp: new Date(),
    component: component.toUpperCase(),
    message,
    type: component,
  });
};

const formatDate = (date: Date): string => {
  return date.toLocaleString();
};

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString();
};

// 生命周期
onMounted(() => {
  // 初始化MVP组件
  const view = new VueUserView();
  userModel.value = new UserModel();
  userPresenter.value = new UserPresenter(view, userModel.value);

  addLog("presenter", "MVP架构初始化完成");

  // 初始化一些示例数据
  setTimeout(() => {
    loadUsers();
  }, 500);
});
</script>

<style lang="scss" scoped>
.mvp-demo {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .pattern-info {
    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #1890ff;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .architecture-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .architecture-diagram {
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      position: relative;

      .mvp-container {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;

        .model-layer,
        .presenter-layer,
        .view-layer {
          background: white;
          padding: 1.5rem;
          border-radius: 8px;
          border: 2px solid;
          min-height: 300px;

          h5 {
            margin: 0 0 1rem 0;
            font-weight: 600;
            text-align: center;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
          }
        }

        .model-layer {
          border-color: #52c41a;

          h5 {
            color: #52c41a;
          }

          .data-section {
            margin-bottom: 1rem;

            h6 {
              margin: 0 0 0.5rem 0;
              color: #333;
              font-size: 0.9rem;
            }

            .data-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0.5rem;
              margin-bottom: 0.3rem;
              background: #f6ffed;
              border-radius: 4px;
              font-size: 0.8rem;

              .user-status {
                padding: 0.2rem 0.4rem;
                border-radius: 3px;
                font-size: 0.7rem;

                &.active {
                  background: #52c41a;
                  color: white;
                }

                &.inactive {
                  background: #d9d9d9;
                  color: #666;
                }
              }
            }
          }

          .business-logic {
            h6 {
              margin: 0 0 0.5rem 0;
              color: #333;
              font-size: 0.9rem;
            }

            .logic-item {
              padding: 0.3rem 0.5rem;
              margin-bottom: 0.3rem;
              background: #f6ffed;
              border-radius: 4px;
              font-size: 0.8rem;
              color: #52c41a;
            }
          }
        }

        .presenter-layer {
          border-color: #1890ff;

          h5 {
            color: #1890ff;
          }

          .presenter-state {
            margin-bottom: 1rem;

            h6 {
              margin: 0 0 0.5rem 0;
              color: #333;
              font-size: 0.9rem;
            }

            .state-item {
              padding: 0.3rem 0.5rem;
              margin-bottom: 0.3rem;
              background: #e6f7ff;
              border-radius: 4px;
              font-size: 0.8rem;
              color: #1890ff;
            }
          }

          .presenter-methods {
            h6 {
              margin: 0 0 0.5rem 0;
              color: #333;
              font-size: 0.9rem;
            }

            .method-item {
              padding: 0.3rem 0.5rem;
              margin-bottom: 0.3rem;
              background: #e6f7ff;
              border-radius: 4px;
              font-size: 0.8rem;
              color: #1890ff;
              font-family: "Monaco", "Consolas", monospace;
            }
          }
        }

        .view-layer {
          border-color: #722ed1;

          h5 {
            color: #722ed1;
          }

          .view-state {
            margin-bottom: 1rem;

            h6 {
              margin: 0 0 0.5rem 0;
              color: #333;
              font-size: 0.9rem;
            }

            .ui-element {
              padding: 0.3rem 0.5rem;
              margin-bottom: 0.3rem;
              background: #f9f0ff;
              border-radius: 4px;
              font-size: 0.8rem;
              color: #722ed1;

              &.active {
                background: #722ed1;
                color: white;
              }
            }
          }

          .view-events {
            h6 {
              margin: 0 0 0.5rem 0;
              color: #333;
              font-size: 0.9rem;
            }

            .event-item {
              padding: 0.3rem 0.5rem;
              margin-bottom: 0.3rem;
              background: #f9f0ff;
              border-radius: 4px;
              font-size: 0.8rem;
              color: #722ed1;
              font-family: "Monaco", "Consolas", monospace;
            }
          }
        }
      }

      .interaction-arrows {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;

        .arrow {
          position: absolute;

          &::after {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
          }

          .arrow-label {
            position: absolute;
            background: white;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-size: 0.7rem;
            color: #666;
            border: 1px solid #ddd;
          }
        }

        .model-to-presenter {
          top: 50%;
          left: 30%;
          width: 15%;
          height: 2px;
          background: #52c41a;

          &::after {
            right: -5px;
            top: -3px;
            border-left: 5px solid #52c41a;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
          }

          .arrow-label {
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
          }
        }

        .presenter-to-model {
          top: 60%;
          left: 30%;
          width: 15%;
          height: 2px;
          background: #1890ff;

          &::before {
            content: "";
            position: absolute;
            left: -5px;
            top: -3px;
            width: 0;
            height: 0;
            border-right: 5px solid #1890ff;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
          }

          .arrow-label {
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
          }
        }

        .view-to-presenter {
          top: 50%;
          right: 30%;
          width: 15%;
          height: 2px;
          background: #722ed1;

          &::before {
            content: "";
            position: absolute;
            left: -5px;
            top: -3px;
            width: 0;
            height: 0;
            border-right: 5px solid #722ed1;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
          }

          .arrow-label {
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
          }
        }

        .presenter-to-view {
          top: 60%;
          right: 30%;
          width: 15%;
          height: 2px;
          background: #1890ff;

          &::after {
            right: -5px;
            top: -3px;
            border-left: 5px solid #1890ff;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
          }

          .arrow-label {
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
          }
        }
      }
    }
  }

  .demo-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .demo-subsection {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      margin-bottom: 1.5rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #1890ff;
        font-weight: 600;
      }

      h6 {
        margin: 0 0 0.8rem 0;
        color: #333;
        font-weight: 600;
      }

      .demo-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1.5rem;
        align-items: end;

        .pattern-selector {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          label {
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
          }
        }

        .demo-select {
          padding: 0.6rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
          min-width: 150px;

          &:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .demo-btn {
          padding: 0.6rem 1.2rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #1890ff, #722ed1);
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          font-size: 0.9rem;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }

      .demo-output {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .user-management {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: 2rem;

          .user-list {
            .empty-state {
              text-align: center;
              color: #999;
              padding: 2rem;
              font-style: italic;
            }

            .user-item {
              display: flex;
              align-items: center;
              gap: 1rem;
              padding: 1rem;
              margin-bottom: 0.5rem;
              background: #f8f9fa;
              border-radius: 6px;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background: #e9ecef;
              }

              &.selected {
                background: #e6f7ff;
                border: 2px solid #1890ff;
              }

              .user-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: #1890ff;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
              }

              .user-info {
                flex: 1;

                .user-name {
                  font-weight: 600;
                  color: #333;
                  margin-bottom: 0.2rem;
                }

                .user-email {
                  color: #666;
                  font-size: 0.9rem;
                }
              }

              .user-actions {
                display: flex;
                gap: 0.5rem;

                .action-btn {
                  padding: 0.3rem 0.6rem;
                  border: none;
                  border-radius: 4px;
                  cursor: pointer;
                  font-size: 0.8rem;
                  transition: all 0.3s ease;

                  &.edit {
                    background: #52c41a;
                    color: white;

                    &:hover {
                      background: #389e0d;
                    }
                  }

                  &.delete {
                    background: #ff4d4f;
                    color: white;

                    &:hover {
                      background: #cf1322;
                    }
                  }
                }
              }
            }
          }

          .user-detail {
            .detail-content {
              .detail-item {
                padding: 0.5rem 0;
                border-bottom: 1px solid #f0f0f0;

                &:last-child {
                  border-bottom: none;
                }

                strong {
                  color: #333;
                  margin-right: 0.5rem;
                }

                .status-active {
                  color: #52c41a;
                  font-weight: 600;
                }

                .status-inactive {
                  color: #d9d9d9;
                  font-weight: 600;
                }
              }
            }
          }
        }

        .pattern-comparison {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 2rem;

          .comparison-item {
            ul {
              margin: 0;
              padding-left: 1.5rem;

              li {
                margin-bottom: 0.5rem;
                color: #555;
              }
            }

            .call-flow {
              .flow-step {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 0.8rem;
                margin-bottom: 0.5rem;
                background: #f8f9fa;
                border-radius: 6px;
                transition: all 0.3s ease;

                &.active {
                  background: #e6f7ff;
                  border: 2px solid #1890ff;
                  transform: scale(1.02);
                }

                .step-number {
                  background: #1890ff;
                  color: white;
                  width: 24px;
                  height: 24px;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 0.8rem;
                  font-weight: 600;
                }

                .step-description {
                  flex: 1;
                  color: #333;
                  font-size: 0.9rem;
                }

                .step-component {
                  background: #722ed1;
                  color: white;
                  padding: 0.2rem 0.6rem;
                  border-radius: 4px;
                  font-size: 0.8rem;
                  font-weight: 600;
                }
              }
            }
          }
        }

        .test-results {
          .test-result {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 6px;

            .test-name {
              flex: 1;
              font-weight: 600;
              color: #333;
            }

            .test-status {
              font-weight: 600;

              &.passed {
                color: #52c41a;
              }

              &.failed {
                color: #ff4d4f;
              }
            }

            .test-description {
              color: #666;
              font-size: 0.9rem;
            }
          }
        }
      }

      .operation-log {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        max-height: 300px;
        overflow-y: auto;

        .log-entry {
          display: flex;
          gap: 1rem;
          padding: 0.5rem;
          border-radius: 4px;
          margin-bottom: 0.5rem;
          font-size: 0.85rem;

          &.model {
            background: #f6ffed;
            border-left: 3px solid #52c41a;
          }

          &.presenter {
            background: #e6f7ff;
            border-left: 3px solid #1890ff;
          }

          &.view {
            background: #f9f0ff;
            border-left: 3px solid #722ed1;
          }

          .log-time {
            color: #666;
            font-family: "Monaco", "Consolas", monospace;
            min-width: 80px;
          }

          .log-component {
            color: #333;
            font-weight: 600;
            min-width: 80px;
          }

          .log-message {
            color: #333;
            flex: 1;
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .mvp-demo {
    .architecture-section {
      .architecture-diagram {
        .mvp-container {
          grid-template-columns: 1fr;
        }

        .interaction-arrows {
          display: none;
        }
      }
    }

    .demo-section {
      .demo-subsection {
        .demo-controls {
          flex-direction: column;
          align-items: stretch;
        }

        .demo-output {
          .user-management {
            grid-template-columns: 1fr;
          }

          .pattern-comparison {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
}
</style>
