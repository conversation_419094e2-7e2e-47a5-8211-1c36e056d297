<template>
  <div class="pubsub-demo">
    <div class="demo-header">
      <h3>发布-订阅模式演示</h3>
      <p>模拟新闻发布系统，展示发布者和订阅者之间的松耦合通信</p>
    </div>

    <div class="demo-container">
      <!-- 发布者区域 -->
      <div class="publisher-section">
        <h4>📢 新闻发布中心</h4>
        <div class="publisher-controls">
          <div class="news-form">
            <select v-model="selectedCategory" class="category-select">
              <option value="tech">科技新闻</option>
              <option value="sports">体育新闻</option>
              <option value="finance">财经新闻</option>
              <option value="entertainment">娱乐新闻</option>
            </select>
            <input 
              v-model="newsTitle" 
              placeholder="输入新闻标题..." 
              class="news-input"
              @keyup.enter="publishNews"
            />
            <button @click="publishNews" class="publish-btn" :disabled="!newsTitle.trim()">
              发布新闻
            </button>
          </div>
          <div class="publisher-stats">
            <span class="stat-item">已发布: {{ publishedCount }}</span>
            <span class="stat-item">订阅者: {{ totalSubscribers }}</span>
          </div>
        </div>
      </div>

      <!-- 订阅者区域 -->
      <div class="subscribers-section">
        <h4>📱 订阅者管理</h4>
        <div class="subscriber-controls">
          <div class="add-subscriber">
            <input 
              v-model="newSubscriberName" 
              placeholder="订阅者名称..." 
              class="subscriber-input"
              @keyup.enter="addSubscriber"
            />
            <div class="interest-checkboxes">
              <label v-for="category in categories" :key="category.id" class="checkbox-label">
                <input 
                  type="checkbox" 
                  :value="category.id" 
                  v-model="newSubscriberInterests"
                />
                {{ category.name }}
              </label>
            </div>
            <button @click="addSubscriber" class="add-btn" :disabled="!newSubscriberName.trim() || newSubscriberInterests.length === 0">
              添加订阅者
            </button>
          </div>
        </div>

        <div class="subscribers-list">
          <div 
            v-for="subscriber in subscribers" 
            :key="subscriber.id" 
            class="subscriber-card"
          >
            <div class="subscriber-header">
              <span class="subscriber-name">{{ subscriber.name }}</span>
              <button @click="removeSubscriber(subscriber.id)" class="remove-btn">×</button>
            </div>
            <div class="subscriber-interests">
              <span 
                v-for="interest in subscriber.interests" 
                :key="interest" 
                class="interest-tag"
                :class="interest"
              >
                {{ getCategoryName(interest) }}
              </span>
            </div>
            <div class="subscriber-messages">
              <div class="messages-header">
                <span>收到消息 ({{ subscriber.messages.length }})</span>
                <button @click="clearSubscriberMessages(subscriber.id)" class="clear-btn">清空</button>
              </div>
              <div class="messages-list">
                <div 
                  v-for="message in subscriber.messages.slice(-3)" 
                  :key="message.id" 
                  class="message-item"
                  :class="message.category"
                >
                  <span class="message-time">{{ message.time }}</span>
                  <span class="message-content">{{ message.content }}</span>
                </div>
                <div v-if="subscriber.messages.length === 0" class="no-messages">
                  暂无消息
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息流展示 -->
    <div class="message-flow">
      <h4>📡 实时消息流</h4>
      <div class="flow-container">
        <div 
          v-for="flow in messageFlows.slice(-5)" 
          :key="flow.id" 
          class="flow-item"
          :class="flow.category"
        >
          <div class="flow-time">{{ flow.time }}</div>
          <div class="flow-content">
            <strong>[{{ getCategoryName(flow.category) }}]</strong> {{ flow.content }}
          </div>
          <div class="flow-recipients">
            → 推送给 {{ flow.recipients.length }} 个订阅者: {{ flow.recipients.join(', ') }}
          </div>
        </div>
        <div v-if="messageFlows.length === 0" class="no-flows">
          暂无消息流
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface NewsMessage {
  id: string
  category: string
  content: string
  time: string
  publisherId: string
}

interface Subscriber {
  id: string
  name: string
  interests: string[]
  messages: NewsMessage[]
}

interface MessageFlow {
  id: string
  category: string
  content: string
  time: string
  recipients: string[]
}

// 新闻分类
const categories = [
  { id: 'tech', name: '科技' },
  { id: 'sports', name: '体育' },
  { id: 'finance', name: '财经' },
  { id: 'entertainment', name: '娱乐' }
]

// 发布者状态
const selectedCategory = ref('tech')
const newsTitle = ref('')
const publishedCount = ref(0)

// 订阅者状态
const newSubscriberName = ref('')
const newSubscriberInterests = ref<string[]>([])
const subscribers = ref<Subscriber[]>([])

// 消息流
const messageFlows = ref<MessageFlow[]>([])

// 发布-订阅系统核心类
class EventBus {
  private subscribers: Map<string, Set<(message: NewsMessage) => void>> = new Map()

  // 订阅事件
  subscribe(event: string, callback: (message: NewsMessage) => void): () => void {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, new Set())
    }
    this.subscribers.get(event)!.add(callback)

    // 返回取消订阅函数
    return () => {
      this.subscribers.get(event)?.delete(callback)
    }
  }

  // 发布事件
  publish(event: string, message: NewsMessage): void {
    const eventSubscribers = this.subscribers.get(event)
    if (eventSubscribers) {
      eventSubscribers.forEach(callback => callback(message))
    }
  }

  // 获取订阅者数量
  getSubscriberCount(event: string): number {
    return this.subscribers.get(event)?.size || 0
  }

  // 获取所有订阅者数量
  getTotalSubscriberCount(): number {
    let total = 0
    this.subscribers.forEach(subs => total += subs.size)
    return total
  }
}

// 创建事件总线实例
const eventBus = new EventBus()

// 计算属性
const totalSubscribers = computed(() => {
  return subscribers.value.reduce((total, sub) => total + sub.interests.length, 0)
})

// 获取分类名称
const getCategoryName = (categoryId: string): string => {
  return categories.find(cat => cat.id === categoryId)?.name || categoryId
}

// 发布新闻
const publishNews = () => {
  if (!newsTitle.value.trim()) return

  const message: NewsMessage = {
    id: Date.now().toString(),
    category: selectedCategory.value,
    content: newsTitle.value.trim(),
    time: new Date().toLocaleTimeString(),
    publisherId: 'news-center'
  }

  // 发布到事件总线
  eventBus.publish(selectedCategory.value, message)
  
  publishedCount.value++
  newsTitle.value = ''

  // 记录消息流
  const recipients = subscribers.value
    .filter(sub => sub.interests.includes(selectedCategory.value))
    .map(sub => sub.name)

  messageFlows.value.push({
    id: message.id,
    category: message.category,
    content: message.content,
    time: message.time,
    recipients
  })
}

// 添加订阅者
const addSubscriber = () => {
  if (!newSubscriberName.value.trim() || newSubscriberInterests.value.length === 0) return

  const subscriber: Subscriber = {
    id: Date.now().toString(),
    name: newSubscriberName.value.trim(),
    interests: [...newSubscriberInterests.value],
    messages: []
  }

  // 为每个感兴趣的分类订阅事件
  subscriber.interests.forEach(interest => {
    eventBus.subscribe(interest, (message: NewsMessage) => {
      subscriber.messages.push(message)
    })
  })

  subscribers.value.push(subscriber)
  
  // 重置表单
  newSubscriberName.value = ''
  newSubscriberInterests.value = []
}

// 移除订阅者
const removeSubscriber = (subscriberId: string) => {
  const index = subscribers.value.findIndex(sub => sub.id === subscriberId)
  if (index > -1) {
    subscribers.value.splice(index, 1)
  }
}

// 清空订阅者消息
const clearSubscriberMessages = (subscriberId: string) => {
  const subscriber = subscribers.value.find(sub => sub.id === subscriberId)
  if (subscriber) {
    subscriber.messages = []
  }
}

// 初始化一些示例订阅者
const initializeDemo = () => {
  // 添加示例订阅者
  const demoSubscribers = [
    { name: '科技爱好者小王', interests: ['tech'] },
    { name: '体育迷小李', interests: ['sports'] },
    { name: '投资者小张', interests: ['finance', 'tech'] }
  ]

  demoSubscribers.forEach(demo => {
    const subscriber: Subscriber = {
      id: Date.now().toString() + Math.random(),
      name: demo.name,
      interests: demo.interests,
      messages: []
    }

    subscriber.interests.forEach(interest => {
      eventBus.subscribe(interest, (message: NewsMessage) => {
        subscriber.messages.push(message)
      })
    })

    subscribers.value.push(subscriber)
  })
}

// 组件挂载时初始化演示
initializeDemo()
</script>

<style lang="scss" scoped>
.pubsub-demo {
  padding: 1.5rem;
  
  .demo-header {
    margin-bottom: 1.5rem;
    
    h3 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.3rem;
    }
    
    p {
      margin: 0;
      color: #666;
      line-height: 1.5;
    }
  }
  
  .demo-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
    
    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }
  
  .publisher-section, .subscribers-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;
    
    h4 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  }
  
  .publisher-controls {
    .news-form {
      display: flex;
      flex-direction: column;
      gap: 0.8rem;
      margin-bottom: 1rem;
      
      .category-select, .news-input {
        padding: 0.6rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 0.9rem;
      }
      
      .publish-btn {
        padding: 0.6rem 1.2rem;
        background: #1890ff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        
        &:hover:not(:disabled) {
          background: #096dd9;
        }
        
        &:disabled {
          background: #ccc;
          cursor: not-allowed;
        }
      }
    }
    
    .publisher-stats {
      display: flex;
      gap: 1rem;
      
      .stat-item {
        background: white;
        padding: 0.4rem 0.8rem;
        border-radius: 4px;
        font-size: 0.85rem;
        color: #666;
        border: 1px solid #e0e0e0;
      }
    }
  }
  
  .subscriber-controls {
    .add-subscriber {
      .subscriber-input {
        width: 100%;
        padding: 0.6rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 0.8rem;
        font-size: 0.9rem;
      }
      
      .interest-checkboxes {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 0.8rem;
        
        .checkbox-label {
          display: flex;
          align-items: center;
          gap: 0.3rem;
          font-size: 0.85rem;
          color: #666;
          cursor: pointer;
          
          input[type="checkbox"] {
            margin: 0;
          }
        }
      }
      
      .add-btn {
        padding: 0.5rem 1rem;
        background: #52c41a;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.85rem;
        
        &:hover:not(:disabled) {
          background: #389e0d;
        }
        
        &:disabled {
          background: #ccc;
          cursor: not-allowed;
        }
      }
    }
  }
  
  .subscribers-list {
    margin-top: 1rem;
    max-height: 400px;
    overflow-y: auto;
    
    .subscriber-card {
      background: white;
      border-radius: 6px;
      padding: 1rem;
      margin-bottom: 0.8rem;
      border: 1px solid #e0e0e0;
      
      .subscriber-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
        
        .subscriber-name {
          font-weight: 600;
          color: #333;
        }
        
        .remove-btn {
          background: #ff4d4f;
          color: white;
          border: none;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          cursor: pointer;
          font-size: 0.8rem;
          display: flex;
          align-items: center;
          justify-content: center;
          
          &:hover {
            background: #cf1322;
          }
        }
      }
      
      .subscriber-interests {
        margin-bottom: 0.8rem;
        
        .interest-tag {
          display: inline-block;
          padding: 0.2rem 0.5rem;
          border-radius: 12px;
          font-size: 0.75rem;
          margin-right: 0.3rem;
          
          &.tech { background: #e6f7ff; color: #1890ff; }
          &.sports { background: #f6ffed; color: #52c41a; }
          &.finance { background: #fff7e6; color: #fa8c16; }
          &.entertainment { background: #fff0f6; color: #eb2f96; }
        }
      }
      
      .subscriber-messages {
        .messages-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.5rem;
          font-size: 0.85rem;
          color: #666;
          
          .clear-btn {
            background: none;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 0.2rem 0.4rem;
            cursor: pointer;
            font-size: 0.75rem;
            color: #666;
            
            &:hover {
              background: #f5f5f5;
            }
          }
        }
        
        .messages-list {
          max-height: 120px;
          overflow-y: auto;
          
          .message-item {
            padding: 0.4rem;
            margin-bottom: 0.3rem;
            border-radius: 4px;
            font-size: 0.8rem;
            border-left: 3px solid;
            
            &.tech { 
              background: #e6f7ff; 
              border-left-color: #1890ff;
            }
            &.sports { 
              background: #f6ffed; 
              border-left-color: #52c41a;
            }
            &.finance { 
              background: #fff7e6; 
              border-left-color: #fa8c16;
            }
            &.entertainment { 
              background: #fff0f6; 
              border-left-color: #eb2f96;
            }
            
            .message-time {
              color: #999;
              margin-right: 0.5rem;
            }
            
            .message-content {
              color: #333;
            }
          }
          
          .no-messages {
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 1rem;
          }
        }
      }
    }
  }
  
  .message-flow {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;
    
    h4 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.1rem;
    }
    
    .flow-container {
      max-height: 300px;
      overflow-y: auto;
      
      .flow-item {
        background: white;
        border-radius: 6px;
        padding: 1rem;
        margin-bottom: 0.8rem;
        border-left: 4px solid;
        
        &.tech { border-left-color: #1890ff; }
        &.sports { border-left-color: #52c41a; }
        &.finance { border-left-color: #fa8c16; }
        &.entertainment { border-left-color: #eb2f96; }
        
        .flow-time {
          font-size: 0.8rem;
          color: #999;
          margin-bottom: 0.3rem;
        }
        
        .flow-content {
          font-size: 0.9rem;
          color: #333;
          margin-bottom: 0.3rem;
        }
        
        .flow-recipients {
          font-size: 0.8rem;
          color: #666;
          font-style: italic;
        }
      }
      
      .no-flows {
        text-align: center;
        color: #999;
        font-style: italic;
        padding: 2rem;
      }
    }
  }
}
</style>
