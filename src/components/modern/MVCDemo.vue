<template>
  <div class="mvc-demo">
    <div class="demo-header">
      <h3>MVC模式演示</h3>
      <p>用户管理系统，展示Model-View-Controller的分离架构</p>
    </div>

    <div class="mvc-container">
      <!-- View层 -->
      <div class="view-section">
        <h4>📱 View (视图层)</h4>
        
        <!-- 用户列表视图 -->
        <div v-if="currentView === 'list'" class="user-list-view">
          <div class="view-header">
            <h5>用户列表</h5>
            <button @click="showAddForm" class="add-btn">添加用户</button>
          </div>
          
          <div class="search-bar">
            <input 
              v-model="searchQuery" 
              @input="handleSearch"
              placeholder="搜索用户..."
              class="search-input"
            />
          </div>
          
          <div class="users-grid">
            <div 
              v-for="user in displayUsers" 
              :key="user.id"
              class="user-card"
            >
              <div class="user-info">
                <h6>{{ user.name }}</h6>
                <p>{{ user.email }}</p>
                <span class="user-age">年龄: {{ user.age }}</span>
              </div>
              <div class="user-actions">
                <button @click="editUser(user)" class="edit-btn">编辑</button>
                <button @click="deleteUser(user.id)" class="delete-btn">删除</button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 用户表单视图 -->
        <div v-else-if="currentView === 'form'" class="user-form-view">
          <div class="view-header">
            <h5>{{ editingUser ? '编辑用户' : '添加用户' }}</h5>
            <button @click="showList" class="back-btn">返回列表</button>
          </div>
          
          <form @submit.prevent="handleSubmit" class="user-form">
            <div class="form-group">
              <label>姓名:</label>
              <input v-model="formData.name" required class="form-input" />
            </div>
            <div class="form-group">
              <label>邮箱:</label>
              <input v-model="formData.email" type="email" required class="form-input" />
            </div>
            <div class="form-group">
              <label>年龄:</label>
              <input v-model.number="formData.age" type="number" required class="form-input" />
            </div>
            <div class="form-actions">
              <button type="submit" class="submit-btn">
                {{ editingUser ? '更新' : '添加' }}
              </button>
              <button type="button" @click="resetForm" class="reset-btn">重置</button>
            </div>
          </form>
        </div>
      </div>
      
      <!-- Controller层状态显示 -->
      <div class="controller-section">
        <h4>🎮 Controller (控制器)</h4>
        <div class="controller-status">
          <div class="status-item">
            <span class="label">当前视图:</span>
            <span class="value">{{ currentView === 'list' ? '用户列表' : '用户表单' }}</span>
          </div>
          <div class="status-item">
            <span class="label">操作状态:</span>
            <span class="value">{{ operationStatus }}</span>
          </div>
          <div class="status-item">
            <span class="label">搜索查询:</span>
            <span class="value">{{ searchQuery || '无' }}</span>
          </div>
        </div>
        
        <div class="recent-actions">
          <h5>最近操作</h5>
          <div class="actions-list">
            <div 
              v-for="action in recentActions" 
              :key="action.id"
              class="action-item"
            >
              <span class="action-time">{{ action.time }}</span>
              <span class="action-desc">{{ action.description }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Model层数据显示 -->
      <div class="model-section">
        <h4>💾 Model (数据模型)</h4>
        <div class="model-stats">
          <div class="stat-card">
            <span class="stat-number">{{ users.length }}</span>
            <span class="stat-label">总用户数</span>
          </div>
          <div class="stat-card">
            <span class="stat-number">{{ displayUsers.length }}</span>
            <span class="stat-label">显示用户数</span>
          </div>
        </div>
        
        <div class="model-data">
          <h5>原始数据</h5>
          <div class="data-preview">
            <pre>{{ JSON.stringify(users, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue';

interface User {
  id: number;
  name: string;
  email: string;
  age: number;
}

interface Action {
  id: string;
  time: string;
  description: string;
}

// Model层数据
const users = ref<User[]>([
  { id: 1, name: 'Alice', email: '<EMAIL>', age: 25 },
  { id: 2, name: 'Bob', email: '<EMAIL>', age: 30 },
  { id: 3, name: 'Charlie', email: '<EMAIL>', age: 35 }
]);

// Controller层状态
const currentView = ref<'list' | 'form'>('list');
const operationStatus = ref('就绪');
const searchQuery = ref('');
const editingUser = ref<User | null>(null);
const recentActions = ref<Action[]>([]);

// View层表单数据
const formData = reactive({
  name: '',
  email: '',
  age: 0
});

let nextId = 4;
let actionId = 0;

// 计算属性 - 过滤后的用户列表
const displayUsers = computed(() => {
  if (!searchQuery.value) return users.value;
  
  const query = searchQuery.value.toLowerCase();
  return users.value.filter(user => 
    user.name.toLowerCase().includes(query) ||
    user.email.toLowerCase().includes(query)
  );
});

// 添加操作记录
const addAction = (description: string) => {
  const action: Action = {
    id: (++actionId).toString(),
    time: new Date().toLocaleTimeString(),
    description
  };
  
  recentActions.value.unshift(action);
  
  // 限制记录数量
  if (recentActions.value.length > 5) {
    recentActions.value.pop();
  }
};

// Controller方法
const showList = () => {
  currentView.value = 'list';
  operationStatus.value = '显示用户列表';
  addAction('切换到列表视图');
};

const showAddForm = () => {
  currentView.value = 'form';
  editingUser.value = null;
  resetForm();
  operationStatus.value = '显示添加表单';
  addAction('打开添加用户表单');
};

const editUser = (user: User) => {
  currentView.value = 'form';
  editingUser.value = user;
  formData.name = user.name;
  formData.email = user.email;
  formData.age = user.age;
  operationStatus.value = `编辑用户: ${user.name}`;
  addAction(`开始编辑用户: ${user.name}`);
};

const handleSubmit = () => {
  if (editingUser.value) {
    // 更新用户
    const index = users.value.findIndex(u => u.id === editingUser.value!.id);
    if (index !== -1) {
      users.value[index] = {
        ...editingUser.value,
        name: formData.name,
        email: formData.email,
        age: formData.age
      };
      operationStatus.value = `用户 ${formData.name} 更新成功`;
      addAction(`更新用户: ${formData.name}`);
    }
  } else {
    // 添加新用户
    const newUser: User = {
      id: nextId++,
      name: formData.name,
      email: formData.email,
      age: formData.age
    };
    users.value.push(newUser);
    operationStatus.value = `用户 ${formData.name} 添加成功`;
    addAction(`添加新用户: ${formData.name}`);
  }
  
  showList();
};

const deleteUser = (userId: number) => {
  const user = users.value.find(u => u.id === userId);
  if (user && confirm(`确定要删除用户 ${user.name} 吗？`)) {
    users.value = users.value.filter(u => u.id !== userId);
    operationStatus.value = `用户 ${user.name} 删除成功`;
    addAction(`删除用户: ${user.name}`);
  }
};

const handleSearch = () => {
  operationStatus.value = searchQuery.value ? `搜索: ${searchQuery.value}` : '显示所有用户';
  addAction(searchQuery.value ? `搜索用户: ${searchQuery.value}` : '清空搜索');
};

const resetForm = () => {
  formData.name = '';
  formData.email = '';
  formData.age = 0;
  operationStatus.value = '表单已重置';
  addAction('重置表单');
};

// 初始化
addAction('MVC演示系统初始化完成');
</script>

<style lang="scss" scoped>
.mvc-demo {
  padding: 1.5rem;
  
  .demo-header {
    margin-bottom: 1.5rem;
    
    h3 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.3rem;
    }
    
    p {
      margin: 0;
      color: #666;
      line-height: 1.5;
    }
  }
  
  .mvc-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1.5rem;
    
    @media (max-width: 1200px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }
  
  .view-section, .controller-section, .model-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    
    h4 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  }
  
  // View层样式
  .view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    
    h5 {
      margin: 0;
      color: #333;
    }
    
    button {
      padding: 0.4rem 0.8rem;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.8rem;
      
      &.add-btn, &.back-btn {
        background: #1890ff;
        color: white;
        
        &:hover {
          background: #096dd9;
        }
      }
    }
  }
  
  .search-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    margin-bottom: 1rem;
  }
  
  .users-grid {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    max-height: 300px;
    overflow-y: auto;
  }
  
  .user-card {
    background: white;
    border-radius: 6px;
    padding: 0.8rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .user-info {
      h6 {
        margin: 0 0 0.2rem 0;
        color: #333;
      }
      
      p {
        margin: 0 0 0.2rem 0;
        color: #666;
        font-size: 0.85rem;
      }
      
      .user-age {
        font-size: 0.8rem;
        color: #888;
      }
    }
    
    .user-actions {
      display: flex;
      gap: 0.5rem;
      
      button {
        padding: 0.3rem 0.6rem;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 0.75rem;
        
        &.edit-btn {
          background: #faad14;
          color: white;
          
          &:hover {
            background: #d48806;
          }
        }
        
        &.delete-btn {
          background: #ff4d4f;
          color: white;
          
          &:hover {
            background: #cf1322;
          }
        }
      }
    }
  }
  
  .user-form {
    .form-group {
      margin-bottom: 1rem;
      
      label {
        display: block;
        margin-bottom: 0.3rem;
        color: #333;
        font-size: 0.9rem;
      }
      
      .form-input {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
      }
    }
    
    .form-actions {
      display: flex;
      gap: 0.8rem;
      
      button {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.85rem;
        
        &.submit-btn {
          background: #52c41a;
          color: white;
          
          &:hover {
            background: #389e0d;
          }
        }
        
        &.reset-btn {
          background: #d9d9d9;
          color: #333;
          
          &:hover {
            background: #bfbfbf;
          }
        }
      }
    }
  }
  
  // Controller层样式
  .controller-status {
    margin-bottom: 1rem;
    
    .status-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.5rem;
      font-size: 0.85rem;
      
      .label {
        color: #666;
      }
      
      .value {
        color: #333;
        font-weight: 500;
      }
    }
  }
  
  .recent-actions {
    h5 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 0.9rem;
    }
    
    .actions-list {
      max-height: 150px;
      overflow-y: auto;
      
      .action-item {
        display: flex;
        flex-direction: column;
        padding: 0.4rem;
        background: white;
        border-radius: 4px;
        margin-bottom: 0.3rem;
        font-size: 0.8rem;
        
        .action-time {
          color: #888;
        }
        
        .action-desc {
          color: #333;
        }
      }
    }
  }
  
  // Model层样式
  .model-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    
    .stat-card {
      background: white;
      border-radius: 6px;
      padding: 0.8rem;
      text-align: center;
      flex: 1;
      
      .stat-number {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        color: #1890ff;
      }
      
      .stat-label {
        font-size: 0.8rem;
        color: #666;
      }
    }
  }
  
  .model-data {
    h5 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 0.9rem;
    }
    
    .data-preview {
      background: white;
      border-radius: 4px;
      padding: 0.8rem;
      max-height: 200px;
      overflow-y: auto;
      
      pre {
        margin: 0;
        font-size: 0.75rem;
        color: #555;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
  }
}
</style>
