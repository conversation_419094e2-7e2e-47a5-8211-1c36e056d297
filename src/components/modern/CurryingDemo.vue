<template>
  <div class="currying-demo">
    <div class="demo-header">
      <h3>柯里化模式演示</h3>
      <p>展示如何将多参数函数转换为单参数函数序列，实现函数的部分应用和复用</p>
    </div>

    <div class="demo-container">
      <!-- 基础柯里化演示 -->
      <div class="basic-currying-section">
        <h4>🔧 基础柯里化演示</h4>
        <div class="function-demo">
          <div class="original-function">
            <h5>原始函数：add(a, b, c)</h5>
            <div class="function-call">
              <input
                v-model.number="basicA"
                type="number"
                placeholder="a"
                class="param-input"
              />
              <input
                v-model.number="basicB"
                type="number"
                placeholder="b"
                class="param-input"
              />
              <input
                v-model.number="basicC"
                type="number"
                placeholder="c"
                class="param-input"
              />
              <button @click="callOriginalAdd" class="call-btn">调用</button>
              <span class="result">= {{ basicResult }}</span>
            </div>
          </div>

          <div class="curried-function">
            <h5>柯里化函数：curriedAdd(a)(b)(c)</h5>
            <div class="currying-steps">
              <div class="step">
                <span class="step-label">第1步:</span>
                <input
                  v-model.number="curriedA"
                  type="number"
                  placeholder="a"
                  class="param-input"
                />
                <button @click="applyCurriedA" class="apply-btn">
                  应用参数a
                </button>
                <span v-if="partialA" class="partial-result"
                  >→ 返回函数 f(b)(c)</span
                >
              </div>
              <div class="step" :class="{ disabled: !partialA }">
                <span class="step-label">第2步:</span>
                <input
                  v-model.number="curriedB"
                  type="number"
                  placeholder="b"
                  class="param-input"
                  :disabled="!partialA"
                />
                <button
                  @click="applyCurriedB"
                  class="apply-btn"
                  :disabled="!partialA"
                >
                  应用参数b
                </button>
                <span v-if="partialB" class="partial-result"
                  >→ 返回函数 f(c)</span
                >
              </div>
              <div class="step" :class="{ disabled: !partialB }">
                <span class="step-label">第3步:</span>
                <input
                  v-model.number="curriedC"
                  type="number"
                  placeholder="c"
                  class="param-input"
                  :disabled="!partialB"
                />
                <button
                  @click="applyCurriedC"
                  class="apply-btn"
                  :disabled="!partialB"
                >
                  应用参数c
                </button>
                <span v-if="curriedResult !== null" class="final-result"
                  >→ 最终结果: {{ curriedResult }}</span
                >
              </div>
            </div>
            <button @click="resetCurrying" class="reset-btn">重置</button>
          </div>
        </div>
      </div>

      <!-- 实用场景演示 -->
      <div class="practical-scenarios">
        <h4>💼 实用场景演示</h4>

        <!-- 配置函数场景 -->
        <div class="scenario-section">
          <h5>场景1: 配置函数</h5>
          <p>创建预配置的API请求函数</p>
          <div class="config-demo">
            <div class="config-step">
              <label>基础URL:</label>
              <select v-model="selectedBaseUrl" @change="createApiConfig">
                <option value="https://api.example.com">
                  https://api.example.com
                </option>
                <option value="https://dev-api.example.com">
                  https://dev-api.example.com
                </option>
                <option value="https://test-api.example.com">
                  https://test-api.example.com
                </option>
              </select>
            </div>
            <div class="config-step">
              <label>认证Token:</label>
              <input
                v-model="authToken"
                @input="createApiConfig"
                placeholder="输入token..."
                class="token-input"
              />
            </div>
            <div class="config-step">
              <label>API端点:</label>
              <input
                v-model="apiEndpoint"
                placeholder="例如: /users"
                class="endpoint-input"
              />
              <button
                @click="makeApiCall"
                class="api-call-btn"
                :disabled="!apiConfigured"
              >
                发起请求
              </button>
            </div>
            <div v-if="apiResult" class="api-result">
              <strong>请求结果:</strong> {{ apiResult }}
            </div>
          </div>
        </div>

        <!-- 事件处理场景 -->
        <div class="scenario-section">
          <h5>场景2: 事件处理器</h5>
          <p>创建预配置的事件处理函数</p>
          <div class="event-demo">
            <div class="event-config">
              <label>事件类型:</label>
              <select v-model="selectedEventType" @change="createEventHandler">
                <option value="click">点击事件</option>
                <option value="hover">悬停事件</option>
                <option value="focus">焦点事件</option>
              </select>
            </div>
            <div class="event-config">
              <label>处理方式:</label>
              <select v-model="selectedAction" @change="createEventHandler">
                <option value="log">记录日志</option>
                <option value="alert">显示警告</option>
                <option value="track">数据追踪</option>
              </select>
            </div>
            <div class="event-targets">
              <button
                v-for="(target, index) in eventTargets"
                :key="index"
                @click="triggerEvent(target, 'click')"
                @mouseenter="triggerEvent(target, 'hover')"
                @focus="triggerEvent(target, 'focus')"
                class="event-target-btn"
                :class="target.type"
              >
                {{ target.name }}
              </button>
            </div>
            <div class="event-log">
              <h6>事件日志:</h6>
              <div class="log-entries">
                <div
                  v-for="(entry, index) in eventLog.slice(-5)"
                  :key="index"
                  class="log-entry"
                >
                  <span class="log-time">{{ entry.time }}</span>
                  <span class="log-content">{{ entry.message }}</span>
                </div>
              </div>
              <button @click="clearEventLog" class="clear-log-btn">
                清空日志
              </button>
            </div>
          </div>
        </div>

        <!-- 数据处理管道场景 -->
        <div class="scenario-section">
          <h5>场景3: 数据处理管道</h5>
          <p>使用柯里化创建可复用的数据转换函数</p>
          <div class="pipeline-demo">
            <div class="pipeline-config">
              <label>数据源:</label>
              <textarea
                v-model="sourceData"
                placeholder="输入JSON数组，例如: [1,2,3,4,5]"
                class="data-input"
              ></textarea>
            </div>
            <div class="pipeline-steps">
              <div class="pipeline-step">
                <label>
                  <input
                    type="checkbox"
                    v-model="enableFilter"
                    @change="updatePipeline"
                  />
                  过滤器 (大于):
                </label>
                <input
                  v-model.number="filterValue"
                  type="number"
                  :disabled="!enableFilter"
                  @input="updatePipeline"
                  class="filter-input"
                />
              </div>
              <div class="pipeline-step">
                <label>
                  <input
                    type="checkbox"
                    v-model="enableMap"
                    @change="updatePipeline"
                  />
                  映射函数:
                </label>
                <select
                  v-model="mapFunction"
                  :disabled="!enableMap"
                  @change="updatePipeline"
                >
                  <option value="double">乘以2</option>
                  <option value="square">平方</option>
                  <option value="increment">加1</option>
                </select>
              </div>
              <div class="pipeline-step">
                <label>
                  <input
                    type="checkbox"
                    v-model="enableReduce"
                    @change="updatePipeline"
                  />
                  聚合函数:
                </label>
                <select
                  v-model="reduceFunction"
                  :disabled="!enableReduce"
                  @change="updatePipeline"
                >
                  <option value="sum">求和</option>
                  <option value="product">求积</option>
                  <option value="max">最大值</option>
                  <option value="min">最小值</option>
                </select>
              </div>
            </div>
            <button @click="executePipeline" class="execute-btn">
              执行管道
            </button>
            <div v-if="pipelineResult !== null" class="pipeline-result">
              <h6>处理结果:</h6>
              <div class="result-steps">
                <div class="result-step">
                  <span class="step-name">原始数据:</span>
                  <span class="step-value">{{ parsedSourceData }}</span>
                </div>
                <div v-if="enableFilter" class="result-step">
                  <span class="step-name">过滤后:</span>
                  <span class="step-value">{{ filteredData }}</span>
                </div>
                <div v-if="enableMap" class="result-step">
                  <span class="step-name">映射后:</span>
                  <span class="step-value">{{ mappedData }}</span>
                </div>
                <div v-if="enableReduce" class="result-step">
                  <span class="step-name">聚合结果:</span>
                  <span class="step-value">{{ pipelineResult }}</span>
                </div>
                <div v-else class="result-step">
                  <span class="step-name">最终结果:</span>
                  <span class="step-value">{{
                    mappedData || filteredData || parsedSourceData
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 柯里化原理说明 -->
    <div class="principle-explanation">
      <h4>🧠 柯里化原理</h4>
      <div class="principle-content">
        <div class="principle-item">
          <h5>什么是柯里化？</h5>
          <p>
            柯里化是将接受多个参数的函数转换为接受单个参数的函数序列的技术。每次调用返回一个新函数，直到所有参数都被提供。
          </p>
        </div>
        <div class="principle-item">
          <h5>核心优势</h5>
          <ul>
            <li><strong>函数复用</strong>：创建预配置的函数变体</li>
            <li><strong>参数预设</strong>：部分应用参数，延迟执行</li>
            <li><strong>函数组合</strong>：更容易组合和链式调用</li>
            <li><strong>代码简洁</strong>：减少重复代码，提高可读性</li>
          </ul>
        </div>
        <div class="principle-item">
          <h5>实现原理</h5>
          <p>
            通过闭包保存已应用的参数，返回新函数等待剩余参数，当所有参数收集完毕时执行原始函数。
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";

// 基础柯里化演示
const basicA = ref<number>(0);
const basicB = ref<number>(0);
const basicC = ref<number>(0);
const basicResult = ref<number | null>(null);

const curriedA = ref<number>(0);
const curriedB = ref<number>(0);
const curriedC = ref<number>(0);
const curriedResult = ref<number | null>(null);
const partialA = ref<Function | null>(null);
const partialB = ref<Function | null>(null);

// 原始加法函数
const add = (a: number, b: number, c: number): number => a + b + c;

// 柯里化加法函数
const curriedAdd = (a: number) => (b: number) => (c: number) => a + b + c;

// 调用原始函数
const callOriginalAdd = () => {
  basicResult.value = add(basicA.value, basicB.value, basicC.value);
};

// 柯里化步骤
const applyCurriedA = () => {
  partialA.value = curriedAdd(curriedA.value);
  partialB.value = null;
  curriedResult.value = null;
};

const applyCurriedB = () => {
  if (partialA.value) {
    partialB.value = partialA.value(curriedB.value);
  }
};

const applyCurriedC = () => {
  if (partialB.value) {
    curriedResult.value = partialB.value(curriedC.value);
  }
};

const resetCurrying = () => {
  partialA.value = null;
  partialB.value = null;
  curriedResult.value = null;
  curriedA.value = 0;
  curriedB.value = 0;
  curriedC.value = 0;
};

// 配置函数场景
const selectedBaseUrl = ref("https://api.example.com");
const authToken = ref("");
const apiEndpoint = ref("");
const apiResult = ref("");
const apiConfigured = ref(false);

// 柯里化的API请求函数
const createApiRequest =
  (baseUrl: string) => (token: string) => (endpoint: string) => {
    return `请求: ${baseUrl}${endpoint} (Token: ${token.substring(0, 10)}...)`;
  };

let configuredApiRequest: Function | null = null;

const createApiConfig = () => {
  if (selectedBaseUrl.value && authToken.value) {
    const baseConfigured = createApiRequest(selectedBaseUrl.value);
    configuredApiRequest = baseConfigured(authToken.value);
    apiConfigured.value = true;
  } else {
    apiConfigured.value = false;
  }
};

const makeApiCall = () => {
  if (configuredApiRequest && apiEndpoint.value) {
    apiResult.value = configuredApiRequest(apiEndpoint.value);
  }
};

// 事件处理场景
const selectedEventType = ref("click");
const selectedAction = ref("log");
const eventLog = ref<Array<{ time: string; message: string }>>([]);

const eventTargets = ref([
  { name: "按钮A", type: "primary" },
  { name: "按钮B", type: "secondary" },
  { name: "按钮C", type: "success" },
]);

// 柯里化的事件处理函数
const createEventHandlerCurried =
  (eventType: string) =>
  (action: string) =>
  (target: string, actualEventType: string) => {
    if (actualEventType === eventType) {
      const time = new Date().toLocaleTimeString();
      let message = "";

      switch (action) {
        case "log":
          message = `记录: ${target} 触发了 ${eventType} 事件`;
          break;
        case "alert":
          message = `警告: ${target} 的 ${eventType} 事件需要注意`;
          break;
        case "track":
          message = `追踪: ${target} 的 ${eventType} 事件已记录到分析系统`;
          break;
      }

      eventLog.value.push({ time, message });
    }
  };

let configuredEventHandler: Function | null = null;

const createEventHandler = () => {
  const typeConfigured = createEventHandlerCurried(selectedEventType.value);
  configuredEventHandler = typeConfigured(selectedAction.value);
};

const triggerEvent = (target: any, eventType: string) => {
  if (configuredEventHandler) {
    configuredEventHandler(target.name, eventType);
  }
};

const clearEventLog = () => {
  eventLog.value = [];
};

// 数据处理管道场景
const sourceData = ref("[1, 2, 3, 4, 5]");
const enableFilter = ref(false);
const filterValue = ref(3);
const enableMap = ref(false);
const mapFunction = ref("double");
const enableReduce = ref(false);
const reduceFunction = ref("sum");
const pipelineResult = ref<any>(null);

// 柯里化的数据处理函数
const createFilter = (threshold: number) => (arr: number[]) =>
  arr.filter((x) => x > threshold);
const createMapper = (fn: string) => (arr: number[]) => {
  const mapFunctions = {
    double: (x: number) => x * 2,
    square: (x: number) => x * x,
    increment: (x: number) => x + 1,
  };
  return arr.map(mapFunctions[fn as keyof typeof mapFunctions]);
};
const createReducer = (fn: string) => (arr: number[]) => {
  const reduceFunctions = {
    sum: (acc: number, x: number) => acc + x,
    product: (acc: number, x: number) => acc * x,
    max: (acc: number, x: number) => Math.max(acc, x),
    min: (acc: number, x: number) => Math.min(acc, x),
  };
  const reducer = reduceFunctions[fn as keyof typeof reduceFunctions];
  return arr.reduce(
    reducer,
    fn === "product" ? 1 : fn === "min" ? Infinity : 0
  );
};

const parsedSourceData = computed(() => {
  try {
    return JSON.parse(sourceData.value);
  } catch {
    return [];
  }
});

const filteredData = computed(() => {
  if (!enableFilter.value) return parsedSourceData.value;
  const filter = createFilter(filterValue.value);
  return filter(parsedSourceData.value);
});

const mappedData = computed(() => {
  if (!enableMap.value) return filteredData.value;
  const mapper = createMapper(mapFunction.value);
  return mapper(filteredData.value);
});

const updatePipeline = () => {
  pipelineResult.value = null;
};

const executePipeline = () => {
  let result = mappedData.value;

  if (enableReduce.value) {
    const reducer = createReducer(reduceFunction.value);
    result = reducer(result);
  }

  pipelineResult.value = result;
};

// 初始化
createApiConfig();
createEventHandler();
</script>

<style lang="scss" scoped>
.currying-demo {
  padding: 1.5rem;

  .demo-header {
    margin-bottom: 1.5rem;

    h3 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.3rem;
    }

    p {
      margin: 0;
      color: #666;
      line-height: 1.5;
    }
  }

  .demo-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .basic-currying-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.1rem;
    }

    .function-demo {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;

      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
      }
    }

    .original-function,
    .curried-function {
      background: white;
      border-radius: 6px;
      padding: 1rem;
      border: 1px solid #ddd;

      h5 {
        margin: 0 0 1rem 0;
        color: #444;
        font-size: 1rem;
      }
    }

    .function-call {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      flex-wrap: wrap;

      .param-input {
        width: 60px;
        padding: 0.4rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        text-align: center;
      }

      .call-btn {
        padding: 0.4rem 0.8rem;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          background: #0056b3;
        }
      }

      .result {
        font-weight: bold;
        color: #28a745;
      }
    }

    .currying-steps {
      display: flex;
      flex-direction: column;
      gap: 0.8rem;

      .step {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        border-radius: 4px;
        background: #f8f9fa;

        &.disabled {
          opacity: 0.5;
        }

        .step-label {
          min-width: 50px;
          font-weight: 500;
        }

        .apply-btn {
          padding: 0.3rem 0.6rem;
          background: #28a745;
          color: white;
          border: none;
          border-radius: 3px;
          cursor: pointer;
          font-size: 0.85rem;

          &:hover:not(:disabled) {
            background: #1e7e34;
          }

          &:disabled {
            background: #ccc;
            cursor: not-allowed;
          }
        }

        .partial-result {
          color: #6c757d;
          font-style: italic;
        }

        .final-result {
          color: #28a745;
          font-weight: bold;
        }
      }
    }

    .reset-btn {
      margin-top: 1rem;
      padding: 0.5rem 1rem;
      background: #6c757d;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background: #545b62;
      }
    }
  }

  .practical-scenarios {
    h4 {
      margin: 0 0 1.5rem 0;
      color: #333;
      font-size: 1.1rem;
    }

    .scenario-section {
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      border: 1px solid #e0e0e0;

      h5 {
        margin: 0 0 0.5rem 0;
        color: #444;
        font-size: 1rem;
      }

      p {
        margin: 0 0 1rem 0;
        color: #666;
        font-size: 0.9rem;
      }
    }

    .config-demo {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .config-step {
        display: flex;
        align-items: center;
        gap: 1rem;

        label {
          min-width: 100px;
          font-weight: 500;
        }

        select,
        input {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          flex: 1;
          max-width: 300px;
        }

        .api-call-btn {
          padding: 0.5rem 1rem;
          background: #007bff;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;

          &:hover:not(:disabled) {
            background: #0056b3;
          }

          &:disabled {
            background: #ccc;
            cursor: not-allowed;
          }
        }
      }

      .api-result {
        padding: 1rem;
        background: #e7f3ff;
        border-radius: 4px;
        border-left: 4px solid #007bff;
      }
    }

    .event-demo {
      .event-config {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;

        label {
          min-width: 100px;
          font-weight: 500;
        }

        select {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          max-width: 200px;
        }
      }

      .event-targets {
        display: flex;
        gap: 1rem;
        margin: 1rem 0;

        .event-target-btn {
          padding: 0.8rem 1.2rem;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 500;

          &.primary {
            background: #007bff;
            color: white;

            &:hover {
              background: #0056b3;
            }
          }

          &.secondary {
            background: #6c757d;
            color: white;

            &:hover {
              background: #545b62;
            }
          }

          &.success {
            background: #28a745;
            color: white;

            &:hover {
              background: #1e7e34;
            }
          }
        }
      }

      .event-log {
        h6 {
          margin: 1rem 0 0.5rem 0;
          color: #444;
        }

        .log-entries {
          max-height: 150px;
          overflow-y: auto;
          background: #f8f9fa;
          border-radius: 4px;
          padding: 0.5rem;
          margin-bottom: 0.5rem;

          .log-entry {
            display: flex;
            gap: 1rem;
            padding: 0.3rem 0;
            border-bottom: 1px solid #eee;

            &:last-child {
              border-bottom: none;
            }

            .log-time {
              color: #6c757d;
              font-size: 0.85rem;
              min-width: 80px;
            }

            .log-content {
              color: #333;
              font-size: 0.9rem;
            }
          }
        }

        .clear-log-btn {
          padding: 0.3rem 0.6rem;
          background: #dc3545;
          color: white;
          border: none;
          border-radius: 3px;
          cursor: pointer;
          font-size: 0.85rem;

          &:hover {
            background: #c82333;
          }
        }
      }
    }

    .pipeline-demo {
      .data-input {
        width: 100%;
        height: 60px;
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-family: monospace;
        resize: vertical;
      }

      .pipeline-steps {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin: 1rem 0;

        .pipeline-step {
          display: flex;
          align-items: center;
          gap: 1rem;

          label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 150px;
            font-weight: 500;
          }

          input,
          select {
            padding: 0.4rem;
            border: 1px solid #ddd;
            border-radius: 4px;
          }
        }
      }

      .execute-btn {
        padding: 0.6rem 1.2rem;
        background: #28a745;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;

        &:hover {
          background: #1e7e34;
        }
      }

      .pipeline-result {
        margin-top: 1rem;
        padding: 1rem;
        background: #e8f5e8;
        border-radius: 4px;
        border-left: 4px solid #28a745;

        h6 {
          margin: 0 0 0.5rem 0;
          color: #444;
        }

        .result-steps {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          .result-step {
            display: flex;
            gap: 1rem;

            .step-name {
              min-width: 100px;
              font-weight: 500;
              color: #666;
            }

            .step-value {
              color: #333;
              font-family: monospace;
            }
          }
        }
      }
    }
  }

  .principle-explanation {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.1rem;
    }

    .principle-content {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .principle-item {
        h5 {
          margin: 0 0 0.5rem 0;
          color: #444;
          font-size: 1rem;
        }

        p {
          margin: 0;
          color: #666;
          line-height: 1.5;
        }

        ul {
          margin: 0.5rem 0 0 0;
          padding-left: 1.5rem;

          li {
            color: #666;
            line-height: 1.5;
            margin-bottom: 0.3rem;

            strong {
              color: #333;
            }
          }
        }
      }
    }
  }
}
</style>
