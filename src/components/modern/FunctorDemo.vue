<template>
  <div class="functor-demo">
    <!-- 模式信息说明 -->
    <div class="pattern-info">
      <p class="description">
        函子模式（Functor
        Pattern）是函数式编程中的核心概念，它定义了一个可以被映射的容器。
        函子提供map操作，允许我们将函数应用到容器中的值，同时保持容器的结构不变。
        这种模式在数据转换、错误处理、异步操作等场景中非常有用。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>数据转换管道</li>
          <li>错误处理链</li>
          <li>异步操作链</li>
          <li>集合操作</li>
          <li>可选值处理</li>
          <li>状态管理</li>
        </ul>
      </div>
    </div>

    <!-- 函子架构图 -->
    <div class="architecture-section">
      <h4>🏗️ 函子架构图</h4>
      <div class="architecture-diagram">
        <div class="functor-container">
          <div class="functor-box">
            <h5>Maybe&lt;T&gt;</h5>
            <div class="functor-content">
              <div class="value-box">{{ maybeValue.getValue() || "None" }}</div>
              <div class="map-arrow">↓ map(f)</div>
              <div class="result-box">
                {{ mappedMaybe.getValue() || "None" }}
              </div>
            </div>
          </div>

          <div class="functor-box">
            <h5>List&lt;T&gt;</h5>
            <div class="functor-content">
              <div class="value-box">
                [{{ listFunctor.getValue().join(", ") }}]
              </div>
              <div class="map-arrow">↓ map(f)</div>
              <div class="result-box">
                [{{ mappedList.getValue().join(", ") }}]
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 交互式演示 -->
    <div class="demo-section">
      <h4>交互式演示：</h4>

      <!-- Maybe函子演示 -->
      <div class="demo-subsection">
        <h5>1. Maybe函子 - 安全的值处理</h5>
        <div class="demo-controls">
          <div class="input-group">
            <label>输入值：</label>
            <input
              v-model="inputValue"
              type="text"
              placeholder="输入一个数字或留空"
              class="demo-input"
            />
          </div>
          <div class="function-group">
            <label>选择转换函数：</label>
            <select v-model="selectedFunction" class="demo-select">
              <option value="double">x => x * 2 (双倍)</option>
              <option value="square">x => x * x (平方)</option>
              <option value="toString">x => x.toString() (转字符串)</option>
              <option value="addTen">x => x + 10 (加10)</option>
            </select>
          </div>
          <button @click="applyMaybeTransform" class="demo-btn">
            应用转换
          </button>
        </div>

        <div class="demo-output">
          <div class="result-display">
            <div class="step">
              <strong>原始值:</strong>
              <span class="value">{{ maybeValue.getValue() || "None" }}</span>
            </div>
            <div class="step">
              <strong>转换后:</strong>
              <span class="value">{{ mappedMaybe.getValue() || "None" }}</span>
            </div>
            <div class="step">
              <strong>是否有值:</strong>
              <span
                :class="{
                  'has-value': maybeValue.hasValue(),
                  'no-value': !maybeValue.hasValue(),
                }"
              >
                {{ maybeValue.hasValue() ? "✅ 有值" : "❌ 无值" }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- List函子演示 -->
      <div class="demo-subsection">
        <h5>2. List函子 - 集合转换</h5>
        <div class="demo-controls">
          <div class="input-group">
            <label>输入数组（逗号分隔）：</label>
            <input
              v-model="listInput"
              type="text"
              placeholder="1,2,3,4,5"
              class="demo-input"
            />
          </div>
          <div class="function-group">
            <label>选择转换函数：</label>
            <select v-model="selectedListFunction" class="demo-select">
              <option value="double">x => x * 2</option>
              <option value="square">x => x * x</option>
              <option value="isEven">x => x % 2 === 0</option>
              <option value="format">x => `[${x}]`</option>
            </select>
          </div>
          <button @click="applyListTransform" class="demo-btn">应用转换</button>
        </div>

        <div class="demo-output">
          <div class="result-display">
            <div class="step">
              <strong>原始数组:</strong>
              <span class="value"
                >[{{ listFunctor.getValue().join(", ") }}]</span
              >
            </div>
            <div class="step">
              <strong>转换后:</strong>
              <span class="value"
                >[{{ mappedList.getValue().join(", ") }}]</span
              >
            </div>
            <div class="step">
              <strong>数组长度:</strong>
              <span class="value"
                >{{ listFunctor.getValue().length }} →
                {{ mappedList.getValue().length }}</span
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 函子法则验证 -->
      <div class="demo-subsection">
        <h5>3. 函子法则验证</h5>
        <div class="demo-controls">
          <button @click="verifyIdentityLaw" class="demo-btn">
            验证恒等法则
          </button>
          <button @click="verifyCompositionLaw" class="demo-btn">
            验证组合法则
          </button>
        </div>

        <div class="demo-output">
          <div class="laws-result">
            <div
              v-for="(result, index) in lawResults"
              :key="index"
              class="law-result"
            >
              <div class="law-name">{{ result.name }}</div>
              <div class="law-description">{{ result.description }}</div>
              <div
                class="law-verification"
                :class="{ success: result.passed, failure: !result.passed }"
              >
                {{ result.passed ? "✅ 通过" : "❌ 失败" }} -
                {{ result.details }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优缺点分析 -->
    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 保持容器结构，安全地转换值</li>
        <li>✅ 支持函数组合，构建复杂的转换管道</li>
        <li>✅ 提供统一的接口处理不同类型的容器</li>
        <li>✅ 避免空值异常，提高代码安全性</li>
        <li>✅ 支持链式调用，代码更加简洁</li>
        <li>✅ 遵循数学法则，行为可预测</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 学习曲线较陡，需要理解函数式概念</li>
        <li>❌ 可能增加代码的抽象层次</li>
        <li>❌ 调试时需要理解函子的内部结构</li>
        <li>❌ 在某些简单场景下可能过度设计</li>
      </ul>

      <h4>函子法则：</h4>
      <ul>
        <li>🔍 <strong>恒等法则:</strong> functor.map(x => x) === functor</li>
        <li>
          🔍 <strong>组合法则:</strong> functor.map(f).map(g) === functor.map(x
          => g(f(x)))
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { Maybe, ListFunctor, identity, compose } from "@/patterns/Functor";

// 响应式数据
const inputValue = ref<string>("42");
const listInput = ref<string>("1,2,3,4,5");
const selectedFunction = ref<string>("double");
const selectedListFunction = ref<string>("double");
const lawResults = ref<
  Array<{
    name: string;
    description: string;
    passed: boolean;
    details: string;
  }>
>([]);

// 函子实例
const maybeValue = ref(Maybe.of(42));
const listFunctor = ref(ListFunctor.of([1, 2, 3, 4, 5]));

// 计算属性
const mappedMaybe = computed(() => {
  const func = getFunctionByName(selectedFunction.value);
  return maybeValue.value.map(func);
});

const mappedList = computed(() => {
  const func = getListFunctionByName(selectedListFunction.value);
  return listFunctor.value.map(func);
});

// 转换函数映射
const getFunctionByName = (name: string) => {
  const functions: Record<string, (x: any) => any> = {
    double: (x: number) => x * 2,
    square: (x: number) => x * x,
    toString: (x: any) => x.toString(),
    addTen: (x: number) => x + 10,
  };
  return functions[name] || functions.double;
};

const getListFunctionByName = (name: string) => {
  const functions: Record<string, (x: any) => any> = {
    double: (x: number) => x * 2,
    square: (x: number) => x * x,
    isEven: (x: number) => x % 2 === 0,
    format: (x: any) => `[${x}]`,
  };
  return functions[name] || functions.double;
};

// 方法
const applyMaybeTransform = () => {
  const value = inputValue.value.trim();
  if (value === "") {
    maybeValue.value = Maybe.none();
  } else {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      maybeValue.value = Maybe.none();
    } else {
      maybeValue.value = Maybe.of(numValue);
    }
  }
};

const applyListTransform = () => {
  const values = listInput.value
    .split(",")
    .map((v) => v.trim())
    .filter((v) => v !== "")
    .map((v) => parseFloat(v))
    .filter((v) => !isNaN(v));

  listFunctor.value = ListFunctor.of(values);
};

const verifyIdentityLaw = () => {
  // 验证恒等法则: functor.map(identity) === functor
  const original = Maybe.of(42);
  const mapped = original.map(identity);

  const passed = original.getValue() === mapped.getValue();

  lawResults.value.push({
    name: "恒等法则",
    description: "functor.map(x => x) 应该等于 functor",
    passed,
    details: `Maybe(42).map(identity) = Maybe(${mapped.getValue()})`,
  });
};

const verifyCompositionLaw = () => {
  // 验证组合法则: functor.map(f).map(g) === functor.map(compose(g, f))
  const f = (x: number) => x * 2;
  const g = (x: number) => x + 1;

  const original = Maybe.of(5);
  const stepByStep = original.map(f).map(g);
  const composed = original.map(compose(g, f));

  const passed = stepByStep.getValue() === composed.getValue();

  lawResults.value.push({
    name: "组合法则",
    description: "functor.map(f).map(g) 应该等于 functor.map(compose(g, f))",
    passed,
    details: `Maybe(5).map(x*2).map(x+1) = ${stepByStep.getValue()}, Maybe(5).map(compose) = ${composed.getValue()}`,
  });
};

// 初始化
applyMaybeTransform();
applyListTransform();
</script>

<style lang="scss" scoped>
.functor-demo {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .pattern-info {
    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #1890ff;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .architecture-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .architecture-diagram {
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .functor-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;

        .functor-box {
          background: white;
          padding: 1.5rem;
          border-radius: 8px;
          border: 2px solid #1890ff;
          text-align: center;

          h5 {
            margin: 0 0 1rem 0;
            color: #1890ff;
            font-weight: 600;
          }

          .functor-content {
            .value-box,
            .result-box {
              background: #e3f2fd;
              padding: 0.8rem;
              border-radius: 6px;
              margin: 0.5rem 0;
              font-family: "Monaco", "Consolas", monospace;
              font-size: 0.9rem;
              color: #1565c0;
              font-weight: 500;
            }

            .map-arrow {
              color: #666;
              font-weight: 600;
              margin: 0.5rem 0;
            }
          }
        }
      }
    }
  }

  .demo-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .demo-subsection {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      margin-bottom: 1.5rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #1890ff;
        font-weight: 600;
      }

      .demo-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1.5rem;
        align-items: end;

        .input-group,
        .function-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          label {
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
          }
        }

        .demo-input,
        .demo-select {
          padding: 0.6rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
          min-width: 150px;

          &:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .demo-btn {
          padding: 0.6rem 1.2rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #1890ff, #722ed1);
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          font-size: 0.9rem;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
          }
        }
      }

      .demo-output {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .result-display {
          .step {
            margin: 0.8rem 0;
            font-size: 0.95rem;

            .value {
              font-family: "Monaco", "Consolas", monospace;
              background: #e3f2fd;
              padding: 0.3rem 0.6rem;
              border-radius: 4px;
              color: #1565c0;
              font-weight: 500;
            }

            .has-value {
              color: #28a745;
              font-weight: 600;
            }

            .no-value {
              color: #dc3545;
              font-weight: 600;
            }
          }
        }

        .laws-result {
          .law-result {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            border-left: 4px solid #1890ff;

            .law-name {
              font-weight: 600;
              color: #333;
              margin-bottom: 0.5rem;
            }

            .law-description {
              color: #666;
              font-size: 0.9rem;
              margin-bottom: 0.5rem;
            }

            .law-verification {
              font-family: "Monaco", "Consolas", monospace;
              font-size: 0.85rem;
              padding: 0.5rem;
              border-radius: 4px;

              &.success {
                background: #d4edda;
                color: #155724;
              }

              &.failure {
                background: #f8d7da;
                color: #721c24;
              }
            }
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .functor-demo {
    .architecture-section {
      .architecture-diagram {
        .functor-container {
          grid-template-columns: 1fr;
        }
      }
    }

    .demo-section {
      .demo-subsection {
        .demo-controls {
          flex-direction: column;
          align-items: stretch;

          .demo-input,
          .demo-select {
            min-width: auto;
          }
        }
      }
    }
  }
}
</style>
