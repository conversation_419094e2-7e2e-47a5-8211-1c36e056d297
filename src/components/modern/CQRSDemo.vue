<template>
  <div class="cqrs-demo">
    <!-- 模式信息说明 -->
    <div class="pattern-info">
      <p class="description">
        CQRS模式（Command Query Responsibility Segregation
        Pattern）是一种架构模式，
        将读操作（Query）和写操作（Command）分离到不同的模型中。命令负责修改数据，
        查询负责读取数据，两者使用不同的数据模型和存储，从而实现读写分离，
        提高系统的性能、可扩展性和安全性。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>高并发读写分离系统</li>
          <li>复杂业务逻辑的企业应用</li>
          <li>需要不同查询视图的系统</li>
          <li>微服务架构中的数据管理</li>
          <li>事件驱动架构</li>
          <li>大数据分析平台</li>
        </ul>
      </div>
    </div>

    <!-- CQRS架构图 -->
    <div class="architecture-section">
      <h4>🏗️ CQRS架构图</h4>
      <div class="architecture-diagram">
        <div class="cqrs-container">
          <!-- 命令端 -->
          <div class="command-side">
            <h5>命令端 (Command Side)</h5>
            <div class="command-content">
              <div class="command-handlers">
                <h6>命令处理器</h6>
                <div
                  v-for="handler in commandHandlers"
                  :key="handler.name"
                  class="handler-item"
                  :class="{ active: handler.active }"
                >
                  <div class="handler-name">{{ handler.name }}</div>
                  <div class="handler-status">{{ handler.status }}</div>
                </div>
              </div>
              <div class="write-model">
                <h6>写模型</h6>
                <div class="model-data">
                  <div class="data-item">
                    <strong>用户数:</strong> {{ writeModel.userCount }}
                  </div>
                  <div class="data-item">
                    <strong>订单数:</strong> {{ writeModel.orderCount }}
                  </div>
                  <div class="data-item">
                    <strong>最后更新:</strong>
                    {{ formatTime(writeModel.lastUpdate) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 事件总线 -->
          <div class="event-bus">
            <h5>事件总线</h5>
            <div class="event-flow">
              <div
                v-for="event in recentEvents"
                :key="event.id"
                class="event-item"
                :class="{ flowing: event.flowing }"
              >
                <div class="event-type">{{ event.type }}</div>
                <div class="event-time">{{ formatTime(event.timestamp) }}</div>
              </div>
            </div>
          </div>

          <!-- 查询端 -->
          <div class="query-side">
            <h5>查询端 (Query Side)</h5>
            <div class="query-content">
              <div class="read-models">
                <h6>读模型</h6>
                <div class="read-model-item">
                  <strong>用户视图:</strong>
                  {{ readModels.userView.count }} 个用户
                </div>
                <div class="read-model-item">
                  <strong>订单视图:</strong>
                  {{ readModels.orderView.count }} 个订单
                </div>
                <div class="read-model-item">
                  <strong>统计视图:</strong>
                  {{ readModels.statsView.totalRevenue }} 收入
                </div>
              </div>
              <div class="query-handlers">
                <h6>查询处理器</h6>
                <div
                  v-for="handler in queryHandlers"
                  :key="handler.name"
                  class="handler-item"
                  :class="{ active: handler.active }"
                >
                  <div class="handler-name">{{ handler.name }}</div>
                  <div class="handler-status">{{ handler.status }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 交互式演示 -->
    <div class="demo-section">
      <h4>交互式演示：</h4>

      <!-- 命令操作演示 -->
      <div class="demo-subsection">
        <h5>1. 命令操作 (写操作)</h5>
        <div class="demo-controls">
          <div class="command-group">
            <h6>用户命令</h6>
            <div class="input-group">
              <input
                v-model="newUser.name"
                type="text"
                placeholder="用户名"
                class="demo-input"
              />
              <input
                v-model="newUser.email"
                type="email"
                placeholder="邮箱"
                class="demo-input"
              />
              <button @click="createUser" class="demo-btn">创建用户</button>
            </div>
          </div>
          <div class="command-group">
            <h6>订单命令</h6>
            <div class="input-group">
              <select v-model="newOrder.userId" class="demo-select">
                <option value="">选择用户</option>
                <option v-for="user in users" :key="user.id" :value="user.id">
                  {{ user.name }}
                </option>
              </select>
              <input
                v-model.number="newOrder.amount"
                type="number"
                placeholder="金额"
                class="demo-input"
              />
              <button @click="createOrder" class="demo-btn">创建订单</button>
            </div>
          </div>
        </div>

        <div class="demo-output">
          <div class="command-results">
            <h6>命令执行结果</h6>
            <div class="result-list">
              <div
                v-for="result in commandResults.slice(-5)"
                :key="result.id"
                class="result-item"
                :class="result.success ? 'success' : 'error'"
              >
                <span class="result-command">{{ result.command }}</span>
                <span class="result-message">{{ result.message }}</span>
                <span class="result-time">{{
                  formatTime(result.timestamp)
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 查询操作演示 -->
      <div class="demo-subsection">
        <h5>2. 查询操作 (读操作)</h5>
        <div class="demo-controls">
          <div class="query-group">
            <button @click="queryAllUsers" class="demo-btn">
              查询所有用户
            </button>
            <button @click="queryAllOrders" class="demo-btn">
              查询所有订单
            </button>
            <button @click="queryUserStats" class="demo-btn">
              查询用户统计
            </button>
            <button @click="queryOrderStats" class="demo-btn">
              查询订单统计
            </button>
          </div>
          <div class="query-filters">
            <div class="input-group">
              <label>用户筛选：</label>
              <input
                v-model="queryFilters.userName"
                type="text"
                placeholder="用户名"
                class="demo-input"
              />
              <button @click="queryUsersByName" class="demo-btn">
                筛选查询
              </button>
            </div>
          </div>
        </div>

        <div class="demo-output">
          <div class="query-results">
            <div class="result-tabs">
              <button
                v-for="tab in queryTabs"
                :key="tab.id"
                @click="activeQueryTab = tab.id"
                class="tab-btn"
                :class="{ active: activeQueryTab === tab.id }"
              >
                {{ tab.name }}
              </button>
            </div>
            <div class="tab-content">
              <div v-if="activeQueryTab === 'users'" class="users-view">
                <h6>用户列表</h6>
                <div class="data-table">
                  <div class="table-header">
                    <span>ID</span>
                    <span>姓名</span>
                    <span>邮箱</span>
                    <span>创建时间</span>
                  </div>
                  <div v-for="user in users" :key="user.id" class="table-row">
                    <span>{{ user.id }}</span>
                    <span>{{ user.name }}</span>
                    <span>{{ user.email }}</span>
                    <span>{{ formatDate(user.createdAt) }}</span>
                  </div>
                </div>
              </div>

              <div v-if="activeQueryTab === 'orders'" class="orders-view">
                <h6>订单列表</h6>
                <div class="data-table">
                  <div class="table-header">
                    <span>ID</span>
                    <span>用户</span>
                    <span>金额</span>
                    <span>状态</span>
                    <span>创建时间</span>
                  </div>
                  <div
                    v-for="order in orders"
                    :key="order.id"
                    class="table-row"
                  >
                    <span>{{ order.id }}</span>
                    <span>{{ getUserName(order.userId) }}</span>
                    <span>¥{{ order.amount.toFixed(2) }}</span>
                    <span class="status" :class="order.status">{{
                      order.status
                    }}</span>
                    <span>{{ formatDate(order.createdAt) }}</span>
                  </div>
                </div>
              </div>

              <div v-if="activeQueryTab === 'stats'" class="stats-view">
                <h6>统计信息</h6>
                <div class="stats-grid">
                  <div class="stat-card">
                    <div class="stat-value">{{ statistics.totalUsers }}</div>
                    <div class="stat-label">总用户数</div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-value">{{ statistics.totalOrders }}</div>
                    <div class="stat-label">总订单数</div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-value">
                      ¥{{ statistics.totalRevenue.toFixed(2) }}
                    </div>
                    <div class="stat-label">总收入</div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-value">
                      ¥{{ statistics.averageOrderValue.toFixed(2) }}
                    </div>
                    <div class="stat-label">平均订单价值</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 读写分离演示 -->
      <div class="demo-subsection">
        <h5>3. 读写分离性能对比</h5>
        <div class="demo-controls">
          <button
            @click="runPerformanceTest"
            class="demo-btn"
            :disabled="isTestRunning"
          >
            {{ isTestRunning ? "测试中..." : "运行性能测试" }}
          </button>
          <button @click="simulateHighLoad" class="demo-btn">模拟高负载</button>
          <button @click="resetPerformanceData" class="demo-btn">
            重置数据
          </button>
        </div>

        <div class="demo-output">
          <div class="performance-metrics">
            <div class="metrics-grid">
              <div class="metric-card">
                <h6>写操作性能</h6>
                <div class="metric-value">
                  {{ performanceMetrics.writeLatency }}ms
                </div>
                <div class="metric-label">平均延迟</div>
                <div class="metric-throughput">
                  {{ performanceMetrics.writeThroughput }} ops/s
                </div>
              </div>
              <div class="metric-card">
                <h6>读操作性能</h6>
                <div class="metric-value">
                  {{ performanceMetrics.readLatency }}ms
                </div>
                <div class="metric-label">平均延迟</div>
                <div class="metric-throughput">
                  {{ performanceMetrics.readThroughput }} ops/s
                </div>
              </div>
              <div class="metric-card">
                <h6>系统负载</h6>
                <div class="metric-value">
                  {{ performanceMetrics.systemLoad }}%
                </div>
                <div class="metric-label">CPU使用率</div>
                <div class="metric-throughput">
                  {{ performanceMetrics.memoryUsage }}MB 内存
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 事件流演示 -->
      <div class="demo-subsection">
        <h5>4. 事件流和最终一致性</h5>
        <div class="demo-controls">
          <button @click="showEventFlow" class="demo-btn">显示事件流</button>
          <button @click="simulateEventualConsistency" class="demo-btn">
            模拟最终一致性
          </button>
          <button @click="checkConsistency" class="demo-btn">检查一致性</button>
        </div>

        <div class="demo-output">
          <div class="event-flow-display">
            <div class="consistency-status">
              <h6>一致性状态</h6>
              <div class="status-indicator" :class="consistencyStatus.status">
                {{ consistencyStatus.message }}
              </div>
              <div class="sync-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :style="{ width: consistencyStatus.progress + '%' }"
                  ></div>
                </div>
                <span>{{ consistencyStatus.progress }}% 同步完成</span>
              </div>
            </div>

            <div class="event-timeline">
              <h6>事件时间线</h6>
              <div class="timeline">
                <div
                  v-for="event in eventTimeline"
                  :key="event.id"
                  class="timeline-event"
                  :class="{ processed: event.processed }"
                >
                  <div class="event-dot"></div>
                  <div class="event-content">
                    <div class="event-title">{{ event.type }}</div>
                    <div class="event-description">{{ event.description }}</div>
                    <div class="event-timestamp">
                      {{ formatTime(event.timestamp) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优缺点分析 -->
    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 读写分离，提高系统性能和可扩展性</li>
        <li>✅ 不同的查询模型可以针对特定需求优化</li>
        <li>✅ 写模型专注于业务逻辑，读模型专注于查询</li>
        <li>✅ 支持复杂的查询和报表需求</li>
        <li>✅ 提高系统的安全性（读写权限分离）</li>
        <li>✅ 便于水平扩展和负载均衡</li>
        <li>✅ 支持事件驱动架构</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 增加系统复杂度，需要维护多个模型</li>
        <li>❌ 最终一致性，可能存在数据延迟</li>
        <li>❌ 需要处理事件同步和失败重试</li>
        <li>❌ 开发和维护成本较高</li>
        <li>❌ 调试和监控更加复杂</li>
        <li>❌ 不适合简单的CRUD应用</li>
      </ul>

      <h4>核心概念：</h4>
      <ul>
        <li>🔍 <strong>命令:</strong> 表示要执行的操作，修改系统状态</li>
        <li>🔍 <strong>查询:</strong> 表示读取操作，不修改系统状态</li>
        <li>🔍 <strong>写模型:</strong> 专门处理命令的数据模型</li>
        <li>🔍 <strong>读模型:</strong> 专门处理查询的数据模型</li>
        <li>🔍 <strong>事件总线:</strong> 连接读写两端的消息传递机制</li>
        <li>🔍 <strong>最终一致性:</strong> 读写模型最终会达到一致状态</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  CQRSSystem,
  CommandBus,
  QueryBus,
  EventBus,
  User,
  Order,
} from "@/patterns/CQRS";

// 响应式数据
const newUser = ref<{ name: string; email: string }>({
  name: "",
  email: "",
});

const newOrder = ref<{ userId: string; amount: number }>({
  userId: "",
  amount: 0,
});

const queryFilters = ref<{ userName: string }>({
  userName: "",
});

const activeQueryTab = ref<string>("users");
const isTestRunning = ref<boolean>(false);

// CQRS系统组件
const cqrsSystem = ref<CQRSSystem | null>(null);

// 状态数据
const writeModel = ref<{
  userCount: number;
  orderCount: number;
  lastUpdate: Date;
}>({
  userCount: 0,
  orderCount: 0,
  lastUpdate: new Date(),
});

const readModels = ref<{
  userView: { count: number };
  orderView: { count: number };
  statsView: { totalRevenue: number };
}>({
  userView: { count: 0 },
  orderView: { count: 0 },
  statsView: { totalRevenue: 0 },
});

const commandHandlers = ref<
  Array<{
    name: string;
    status: string;
    active: boolean;
  }>
>([
  { name: "CreateUserHandler", status: "就绪", active: false },
  { name: "CreateOrderHandler", status: "就绪", active: false },
  { name: "UpdateUserHandler", status: "就绪", active: false },
]);

const queryHandlers = ref<
  Array<{
    name: string;
    status: string;
    active: boolean;
  }>
>([
  { name: "UserQueryHandler", status: "就绪", active: false },
  { name: "OrderQueryHandler", status: "就绪", active: false },
  { name: "StatsQueryHandler", status: "就绪", active: false },
]);

const recentEvents = ref<
  Array<{
    id: string;
    type: string;
    timestamp: Date;
    flowing: boolean;
  }>
>([]);

const users = ref<User[]>([]);
const orders = ref<Order[]>([]);

const commandResults = ref<
  Array<{
    id: string;
    command: string;
    message: string;
    success: boolean;
    timestamp: Date;
  }>
>([]);

const queryTabs = ref([
  { id: "users", name: "用户" },
  { id: "orders", name: "订单" },
  { id: "stats", name: "统计" },
]);

const statistics = ref<{
  totalUsers: number;
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
}>({
  totalUsers: 0,
  totalOrders: 0,
  totalRevenue: 0,
  averageOrderValue: 0,
});

const performanceMetrics = ref<{
  writeLatency: number;
  readLatency: number;
  writeThroughput: number;
  readThroughput: number;
  systemLoad: number;
  memoryUsage: number;
}>({
  writeLatency: 0,
  readLatency: 0,
  writeThroughput: 0,
  readThroughput: 0,
  systemLoad: 0,
  memoryUsage: 0,
});

const consistencyStatus = ref<{
  status: "consistent" | "inconsistent" | "syncing";
  message: string;
  progress: number;
}>({
  status: "consistent",
  message: "系统状态一致",
  progress: 100,
});

const eventTimeline = ref<
  Array<{
    id: string;
    type: string;
    description: string;
    timestamp: Date;
    processed: boolean;
  }>
>([]);

// 方法
const createUser = async () => {
  if (!cqrsSystem.value || !newUser.value.name || !newUser.value.email) return;

  try {
    activateCommandHandler("CreateUserHandler");

    const result = await cqrsSystem.value.executeCommand("CreateUser", {
      name: newUser.value.name,
      email: newUser.value.email,
    });

    commandResults.value.push({
      id: generateId(),
      command: "CreateUser",
      message: `用户 ${newUser.value.name} 创建成功`,
      success: true,
      timestamp: new Date(),
    });

    // 清空表单
    newUser.value = { name: "", email: "" };

    // 更新数据
    await updateData();
  } catch (error) {
    commandResults.value.push({
      id: generateId(),
      command: "CreateUser",
      message: "创建用户失败: " + (error as Error).message,
      success: false,
      timestamp: new Date(),
    });
  } finally {
    deactivateCommandHandler("CreateUserHandler");
  }
};

const createOrder = async () => {
  if (!cqrsSystem.value || !newOrder.value.userId || !newOrder.value.amount)
    return;

  try {
    activateCommandHandler("CreateOrderHandler");

    const result = await cqrsSystem.value.executeCommand("CreateOrder", {
      userId: newOrder.value.userId,
      amount: newOrder.value.amount,
    });

    commandResults.value.push({
      id: generateId(),
      command: "CreateOrder",
      message: `订单创建成功，金额 ¥${newOrder.value.amount}`,
      success: true,
      timestamp: new Date(),
    });

    // 清空表单
    newOrder.value = { userId: "", amount: 0 };

    // 更新数据
    await updateData();
  } catch (error) {
    commandResults.value.push({
      id: generateId(),
      command: "CreateOrder",
      message: "创建订单失败: " + (error as Error).message,
      success: false,
      timestamp: new Date(),
    });
  } finally {
    deactivateCommandHandler("CreateOrderHandler");
  }
};

const queryAllUsers = async () => {
  if (!cqrsSystem.value) return;

  activateQueryHandler("UserQueryHandler");

  try {
    const result = await cqrsSystem.value.executeQuery("GetAllUsers", {});
    users.value = result;
  } finally {
    deactivateQueryHandler("UserQueryHandler");
  }
};

const queryAllOrders = async () => {
  if (!cqrsSystem.value) return;

  activateQueryHandler("OrderQueryHandler");

  try {
    const result = await cqrsSystem.value.executeQuery("GetAllOrders", {});
    orders.value = result;
  } finally {
    deactivateQueryHandler("OrderQueryHandler");
  }
};

const queryUserStats = async () => {
  if (!cqrsSystem.value) return;

  activateQueryHandler("StatsQueryHandler");

  try {
    const result = await cqrsSystem.value.executeQuery("GetUserStats", {});
    statistics.value = { ...statistics.value, ...result };
    activeQueryTab.value = "stats";
  } finally {
    deactivateQueryHandler("StatsQueryHandler");
  }
};

const queryOrderStats = async () => {
  if (!cqrsSystem.value) return;

  activateQueryHandler("StatsQueryHandler");

  try {
    const result = await cqrsSystem.value.executeQuery("GetOrderStats", {});
    statistics.value = { ...statistics.value, ...result };
    activeQueryTab.value = "stats";
  } finally {
    deactivateQueryHandler("StatsQueryHandler");
  }
};

const queryUsersByName = async () => {
  if (!cqrsSystem.value || !queryFilters.value.userName) return;

  activateQueryHandler("UserQueryHandler");

  try {
    const result = await cqrsSystem.value.executeQuery("GetUsersByName", {
      name: queryFilters.value.userName,
    });
    users.value = result;
    activeQueryTab.value = "users";
  } finally {
    deactivateQueryHandler("UserQueryHandler");
  }
};

const runPerformanceTest = async () => {
  isTestRunning.value = true;

  try {
    // 模拟性能测试
    const writeStart = Date.now();
    for (let i = 0; i < 100; i++) {
      await new Promise((resolve) => setTimeout(resolve, 1));
    }
    const writeEnd = Date.now();

    const readStart = Date.now();
    for (let i = 0; i < 1000; i++) {
      await new Promise((resolve) => setTimeout(resolve, 0.1));
    }
    const readEnd = Date.now();

    performanceMetrics.value = {
      writeLatency: (writeEnd - writeStart) / 100,
      readLatency: (readEnd - readStart) / 1000,
      writeThroughput: Math.round(100000 / (writeEnd - writeStart)),
      readThroughput: Math.round(1000000 / (readEnd - readStart)),
      systemLoad: Math.round(Math.random() * 30 + 20),
      memoryUsage: Math.round(Math.random() * 200 + 100),
    };
  } finally {
    isTestRunning.value = false;
  }
};

const simulateHighLoad = () => {
  // 模拟高负载场景
  performanceMetrics.value.systemLoad = Math.round(Math.random() * 40 + 60);
  performanceMetrics.value.writeLatency = Math.round(Math.random() * 50 + 20);
  performanceMetrics.value.readLatency = Math.round(Math.random() * 10 + 2);
};

const resetPerformanceData = () => {
  performanceMetrics.value = {
    writeLatency: 0,
    readLatency: 0,
    writeThroughput: 0,
    readThroughput: 0,
    systemLoad: 0,
    memoryUsage: 0,
  };
};

const showEventFlow = () => {
  // 添加事件到流中
  const event = {
    id: generateId(),
    type: "UserCreated",
    timestamp: new Date(),
    flowing: true,
  };

  recentEvents.value.push(event);

  // 模拟事件流动
  setTimeout(() => {
    event.flowing = false;
  }, 2000);
};

const simulateEventualConsistency = () => {
  consistencyStatus.value = {
    status: "inconsistent",
    message: "检测到数据不一致",
    progress: 0,
  };

  // 模拟同步过程
  const syncInterval = setInterval(() => {
    consistencyStatus.value.progress += 10;

    if (consistencyStatus.value.progress >= 100) {
      consistencyStatus.value = {
        status: "consistent",
        message: "系统状态已同步",
        progress: 100,
      };
      clearInterval(syncInterval);
    } else {
      consistencyStatus.value.status = "syncing";
      consistencyStatus.value.message = "正在同步数据...";
    }
  }, 300);
};

const checkConsistency = () => {
  // 检查读写模型一致性
  const isConsistent =
    writeModel.value.userCount === readModels.value.userView.count;

  consistencyStatus.value = {
    status: isConsistent ? "consistent" : "inconsistent",
    message: isConsistent ? "读写模型状态一致" : "检测到读写模型不一致",
    progress: isConsistent ? 100 : 75,
  };
};

const activateCommandHandler = (handlerName: string) => {
  const handler = commandHandlers.value.find((h) => h.name === handlerName);
  if (handler) {
    handler.active = true;
    handler.status = "处理中";
  }
};

const deactivateCommandHandler = (handlerName: string) => {
  const handler = commandHandlers.value.find((h) => h.name === handlerName);
  if (handler) {
    handler.active = false;
    handler.status = "就绪";
  }
};

const activateQueryHandler = (handlerName: string) => {
  const handler = queryHandlers.value.find((h) => h.name === handlerName);
  if (handler) {
    handler.active = true;
    handler.status = "查询中";
  }
};

const deactivateQueryHandler = (handlerName: string) => {
  const handler = queryHandlers.value.find((h) => h.name === handlerName);
  if (handler) {
    handler.active = false;
    handler.status = "就绪";
  }
};

const updateData = async () => {
  if (!cqrsSystem.value) return;

  // 更新写模型数据
  const writeStats = await cqrsSystem.value.getWriteModelStats();
  writeModel.value = {
    userCount: writeStats.userCount,
    orderCount: writeStats.orderCount,
    lastUpdate: new Date(),
  };

  // 更新读模型数据
  const readStats = await cqrsSystem.value.getReadModelStats();
  readModels.value = readStats;

  // 更新统计数据
  const stats = await cqrsSystem.value.executeQuery("GetStats", {});
  statistics.value = stats;

  // 刷新列表数据
  await queryAllUsers();
  await queryAllOrders();
};

const getUserName = (userId: string): string => {
  const user = users.value.find((u) => u.id === userId);
  return user ? user.name : "未知用户";
};

const generateId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

const formatDate = (date: Date | undefined): string => {
  if (!date) return "N/A";
  return date.toLocaleString();
};

const formatTime = (date: Date | undefined): string => {
  if (!date) return "N/A";
  return date.toLocaleTimeString();
};

// 生命周期
onMounted(async () => {
  // 初始化CQRS系统
  cqrsSystem.value = new CQRSSystem();

  // 初始化数据
  await updateData();

  // 模拟一些初始数据
  setTimeout(async () => {
    if (cqrsSystem.value) {
      // 创建示例用户
      await cqrsSystem.value.executeCommand("CreateUser", {
        name: "Alice Johnson",
        email: "<EMAIL>",
      });

      await cqrsSystem.value.executeCommand("CreateUser", {
        name: "Bob Smith",
        email: "<EMAIL>",
      });

      // 创建示例订单
      const users = await cqrsSystem.value.executeQuery("GetAllUsers", {});
      if (users.length > 0) {
        await cqrsSystem.value.executeCommand("CreateOrder", {
          userId: users[0].id,
          amount: 299.99,
        });

        await cqrsSystem.value.executeCommand("CreateOrder", {
          userId: users[1].id,
          amount: 159.5,
        });
      }

      // 更新数据
      await updateData();
    }
  }, 1000);
});
</script>

<style lang="scss" scoped>
.cqrs-demo {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .pattern-info {
    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #1890ff;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .architecture-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .architecture-diagram {
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .cqrs-container {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        gap: 2rem;
        align-items: start;

        .command-side,
        .query-side {
          background: white;
          padding: 1.5rem;
          border-radius: 8px;
          border: 2px solid;
          min-height: 400px;

          h5 {
            margin: 0 0 1rem 0;
            font-weight: 600;
            text-align: center;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
          }
        }

        .command-side {
          border-color: #ff4d4f;

          h5 {
            color: #ff4d4f;
          }

          .command-content {
            .command-handlers,
            .write-model {
              margin-bottom: 1.5rem;

              h6 {
                margin: 0 0 0.8rem 0;
                color: #333;
                font-size: 0.9rem;
              }

              .handler-item {
                background: #fff2f0;
                border: 1px solid #ffccc7;
                border-radius: 6px;
                padding: 0.8rem;
                margin-bottom: 0.5rem;
                transition: all 0.3s ease;

                &.active {
                  background: #ff7875;
                  color: white;
                  animation: pulse 1s infinite;
                }

                .handler-name {
                  font-weight: 600;
                  font-size: 0.8rem;
                }

                .handler-status {
                  font-size: 0.7rem;
                  opacity: 0.8;
                }
              }

              .model-data {
                background: #fff2f0;
                padding: 1rem;
                border-radius: 6px;

                .data-item {
                  padding: 0.3rem 0;
                  font-size: 0.9rem;
                  color: #333;

                  strong {
                    color: #ff4d4f;
                  }
                }
              }
            }
          }
        }

        .event-bus {
          background: white;
          padding: 1rem;
          border-radius: 8px;
          border: 2px solid #722ed1;
          min-width: 200px;

          h5 {
            margin: 0 0 1rem 0;
            font-weight: 600;
            text-align: center;
            color: #722ed1;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
          }

          .event-flow {
            max-height: 350px;
            overflow-y: auto;

            .event-item {
              background: #f9f0ff;
              border: 1px solid #d3adf7;
              border-radius: 6px;
              padding: 0.6rem;
              margin-bottom: 0.5rem;
              text-align: center;
              transition: all 0.3s ease;

              &.flowing {
                background: #722ed1;
                color: white;
                animation: flow 2s ease-in-out;
              }

              .event-type {
                font-weight: 600;
                font-size: 0.8rem;
                margin-bottom: 0.3rem;
              }

              .event-time {
                font-size: 0.7rem;
                opacity: 0.8;
              }
            }
          }
        }

        .query-side {
          border-color: #52c41a;

          h5 {
            color: #52c41a;
          }

          .query-content {
            .read-models,
            .query-handlers {
              margin-bottom: 1.5rem;

              h6 {
                margin: 0 0 0.8rem 0;
                color: #333;
                font-size: 0.9rem;
              }

              .read-model-item {
                background: #f6ffed;
                padding: 0.6rem;
                border-radius: 4px;
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
                color: #333;

                strong {
                  color: #52c41a;
                }
              }

              .handler-item {
                background: #f6ffed;
                border: 1px solid #b7eb8f;
                border-radius: 6px;
                padding: 0.8rem;
                margin-bottom: 0.5rem;
                transition: all 0.3s ease;

                &.active {
                  background: #73d13d;
                  color: white;
                  animation: pulse 1s infinite;
                }

                .handler-name {
                  font-weight: 600;
                  font-size: 0.8rem;
                }

                .handler-status {
                  font-size: 0.7rem;
                  opacity: 0.8;
                }
              }
            }
          }
        }
      }
    }
  }

  .demo-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .demo-subsection {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      margin-bottom: 1.5rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #1890ff;
        font-weight: 600;
      }

      h6 {
        margin: 0 0 0.8rem 0;
        color: #333;
        font-weight: 600;
      }

      .demo-controls {
        margin-bottom: 1.5rem;

        .command-group,
        .query-group,
        .query-filters {
          margin-bottom: 1rem;

          .input-group {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            flex-wrap: wrap;

            label {
              font-weight: 500;
              color: #333;
              font-size: 0.9rem;
              white-space: nowrap;
            }
          }
        }

        .demo-input,
        .demo-select {
          padding: 0.6rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
          min-width: 120px;

          &:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .demo-btn {
          padding: 0.6rem 1.2rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #1890ff, #722ed1);
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          font-size: 0.9rem;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }

      .demo-output {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .command-results {
          .result-list {
            .result-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0.8rem;
              margin-bottom: 0.5rem;
              border-radius: 6px;
              font-size: 0.9rem;

              &.success {
                background: #f6ffed;
                border-left: 4px solid #52c41a;
              }

              &.error {
                background: #fff2f0;
                border-left: 4px solid #ff4d4f;
              }

              .result-command {
                font-weight: 600;
                color: #1890ff;
              }

              .result-message {
                flex: 1;
                margin: 0 1rem;
                color: #333;
              }

              .result-time {
                color: #666;
                font-size: 0.8rem;
              }
            }
          }
        }

        .query-results {
          .result-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            border-bottom: 1px solid #e9ecef;

            .tab-btn {
              padding: 0.6rem 1.2rem;
              border: none;
              background: transparent;
              color: #666;
              cursor: pointer;
              border-bottom: 2px solid transparent;
              transition: all 0.3s ease;

              &.active {
                color: #1890ff;
                border-bottom-color: #1890ff;
              }

              &:hover {
                color: #1890ff;
              }
            }
          }

          .tab-content {
            .data-table {
              .table-header {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 1rem;
                padding: 0.8rem;
                background: #f8f9fa;
                border-radius: 6px 6px 0 0;
                font-weight: 600;
                color: #333;
                font-size: 0.9rem;
              }

              .table-row {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 1rem;
                padding: 0.8rem;
                border-bottom: 1px solid #f0f0f0;
                font-size: 0.9rem;
                color: #333;

                &:hover {
                  background: #f8f9fa;
                }

                .status {
                  padding: 0.2rem 0.6rem;
                  border-radius: 4px;
                  font-size: 0.8rem;
                  font-weight: 600;
                  text-align: center;

                  &.pending {
                    background: #fff7e6;
                    color: #fa8c16;
                  }

                  &.completed {
                    background: #f6ffed;
                    color: #52c41a;
                  }

                  &.cancelled {
                    background: #fff2f0;
                    color: #ff4d4f;
                  }
                }
              }
            }

            .stats-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 1rem;

              .stat-card {
                background: #f8f9fa;
                padding: 1.5rem;
                border-radius: 8px;
                text-align: center;
                border-left: 4px solid #1890ff;

                .stat-value {
                  font-size: 2rem;
                  font-weight: 600;
                  color: #1890ff;
                  margin-bottom: 0.5rem;
                }

                .stat-label {
                  color: #666;
                  font-size: 0.9rem;
                }
              }
            }
          }
        }

        .performance-metrics {
          .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;

            .metric-card {
              background: #f8f9fa;
              padding: 1.5rem;
              border-radius: 8px;
              border-left: 4px solid #722ed1;

              h6 {
                margin: 0 0 1rem 0;
                color: #722ed1;
              }

              .metric-value {
                font-size: 1.8rem;
                font-weight: 600;
                color: #1890ff;
                margin-bottom: 0.3rem;
              }

              .metric-label {
                color: #666;
                font-size: 0.9rem;
                margin-bottom: 0.5rem;
              }

              .metric-throughput {
                color: #52c41a;
                font-size: 0.8rem;
                font-weight: 600;
              }
            }
          }
        }

        .event-flow-display {
          display: grid;
          grid-template-columns: 1fr 2fr;
          gap: 2rem;

          .consistency-status {
            .status-indicator {
              padding: 0.8rem;
              border-radius: 6px;
              text-align: center;
              font-weight: 600;
              margin-bottom: 1rem;

              &.consistent {
                background: #f6ffed;
                color: #52c41a;
                border: 1px solid #b7eb8f;
              }

              &.inconsistent {
                background: #fff2f0;
                color: #ff4d4f;
                border: 1px solid #ffccc7;
              }

              &.syncing {
                background: #fff7e6;
                color: #fa8c16;
                border: 1px solid #ffd591;
              }
            }

            .sync-progress {
              .progress-bar {
                width: 100%;
                height: 8px;
                background: #f0f0f0;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 0.5rem;

                .progress-fill {
                  height: 100%;
                  background: linear-gradient(90deg, #1890ff, #52c41a);
                  transition: width 0.3s ease;
                }
              }

              span {
                font-size: 0.8rem;
                color: #666;
              }
            }
          }

          .event-timeline {
            .timeline {
              position: relative;
              padding-left: 2rem;

              &::before {
                content: "";
                position: absolute;
                left: 10px;
                top: 0;
                bottom: 0;
                width: 2px;
                background: #e9ecef;
              }

              .timeline-event {
                position: relative;
                margin-bottom: 1.5rem;

                &.processed .event-dot {
                  background: #52c41a;
                }

                .event-dot {
                  position: absolute;
                  left: -25px;
                  top: 5px;
                  width: 12px;
                  height: 12px;
                  border-radius: 50%;
                  background: #d9d9d9;
                  border: 2px solid white;
                }

                .event-content {
                  background: white;
                  padding: 1rem;
                  border-radius: 6px;
                  border: 1px solid #e9ecef;

                  .event-title {
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 0.3rem;
                  }

                  .event-description {
                    color: #666;
                    font-size: 0.9rem;
                    margin-bottom: 0.3rem;
                  }

                  .event-timestamp {
                    color: #999;
                    font-size: 0.8rem;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes flow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@media (max-width: 768px) {
  .cqrs-demo {
    .architecture-section {
      .architecture-diagram {
        .cqrs-container {
          grid-template-columns: 1fr;

          .event-bus {
            order: 2;
          }
        }
      }
    }

    .demo-section {
      .demo-subsection {
        .demo-controls {
          .input-group {
            flex-direction: column;
            align-items: stretch;
            gap: 0.3rem;
          }
        }

        .demo-output {
          .event-flow-display {
            grid-template-columns: 1fr;
          }

          .metrics-grid {
            grid-template-columns: 1fr;
          }

          .data-table {
            .table-header,
            .table-row {
              grid-template-columns: 1fr;
              gap: 0.5rem;
            }
          }
        }
      }
    }
  }
}
</style>
