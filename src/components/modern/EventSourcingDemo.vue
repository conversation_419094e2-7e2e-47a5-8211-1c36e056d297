<template>
  <div class="event-sourcing-demo">
    <!-- 模式信息说明 -->
    <div class="pattern-info">
      <p class="description">
        事件溯源模式（Event Sourcing
        Pattern）是一种数据存储模式，通过存储事件序列而不是当前状态来管理数据。
        每个状态变化都被记录为一个不可变的事件，当前状态可以通过重放所有事件来重建。
        这种模式提供了完整的审计跟踪、时间旅行调试能力，并支持复杂的业务分析和数据恢复。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>金融交易系统（完整的交易历史）</li>
          <li>审计和合规系统</li>
          <li>版本控制系统（Git等）</li>
          <li>电商订单管理</li>
          <li>游戏状态管理</li>
          <li>数据分析和商业智能</li>
        </ul>
      </div>
    </div>

    <!-- 事件溯源架构图 -->
    <div class="architecture-section">
      <h4>🏗️ 事件溯源架构图</h4>
      <div class="architecture-diagram">
        <div class="event-sourcing-container">
          <!-- 事件存储 -->
          <div class="event-store">
            <h5>事件存储 (Event Store)</h5>
            <div class="event-list">
              <div
                v-for="(event, index) in eventStore?.getAllEvents() || []"
                :key="event.id"
                class="event-item"
                :class="{
                  replaying: replayingEvents.includes(event.id),
                  latest:
                    index === (eventStore?.getAllEvents()?.length || 0) - 1,
                }"
              >
                <div class="event-header">
                  <span class="event-type">{{ event.type }}</span>
                  <span class="event-timestamp">{{
                    formatTime(event.timestamp)
                  }}</span>
                </div>
                <div class="event-data">{{ JSON.stringify(event.data) }}</div>
                <div class="event-version">v{{ event.version }}</div>
              </div>
            </div>
          </div>

          <!-- 聚合根 -->
          <div class="aggregate-root">
            <h5>聚合根 (Aggregate Root)</h5>
            <div class="aggregate-content">
              <div class="current-state">
                <h6>当前状态</h6>
                <div class="state-display">
                  <div class="state-item">
                    <strong>账户ID:</strong>
                    {{ currentState?.accountId || "N/A" }}
                  </div>
                  <div class="state-item">
                    <strong>余额:</strong> ¥{{
                      (currentState?.balance || 0).toFixed(2)
                    }}
                  </div>
                  <div class="state-item">
                    <strong>状态:</strong> {{ currentState?.status || "N/A" }}
                  </div>
                  <div class="state-item">
                    <strong>版本:</strong> {{ currentState?.version || 0 }}
                  </div>
                </div>
              </div>
              <div class="replay-controls">
                <h6>时间旅行</h6>
                <div class="replay-slider">
                  <input
                    type="range"
                    :min="0"
                    :max="eventStore?.getAllEvents()?.length || 0"
                    v-model="replayToVersion"
                    @input="replayToVersion"
                    class="version-slider"
                  />
                  <div class="version-label">版本: {{ replayToVersion }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 投影 -->
          <div class="projections">
            <h5>投影 (Projections)</h5>
            <div class="projection-list">
              <div class="projection-item">
                <h6>账户摘要</h6>
                <div class="projection-data">
                  <div>
                    总交易次数:
                    {{ projections?.summary?.totalTransactions || 0 }}
                  </div>
                  <div>
                    总存款: ¥{{
                      (projections?.summary?.totalDeposits || 0).toFixed(2)
                    }}
                  </div>
                  <div>
                    总取款: ¥{{
                      (projections?.summary?.totalWithdrawals || 0).toFixed(2)
                    }}
                  </div>
                </div>
              </div>
              <div class="projection-item">
                <h6>月度统计</h6>
                <div class="projection-data">
                  <div
                    v-for="(stat, month) in projections?.monthly || {}"
                    :key="month"
                  >
                    {{ month }}: {{ stat.transactions }}笔交易
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 交互式演示 -->
    <div class="demo-section">
      <h4>交互式演示：</h4>

      <!-- 银行账户演示 -->
      <div class="demo-subsection">
        <h5>1. 银行账户事件溯源</h5>
        <div class="demo-controls">
          <div class="input-group">
            <label>存款金额：</label>
            <input
              v-model.number="depositAmount"
              type="number"
              min="1"
              step="0.01"
              class="demo-input"
            />
            <button @click="deposit" class="demo-btn">存款</button>
          </div>
          <div class="input-group">
            <label>取款金额：</label>
            <input
              v-model.number="withdrawAmount"
              type="number"
              min="1"
              step="0.01"
              class="demo-input"
            />
            <button @click="withdraw" class="demo-btn">取款</button>
          </div>
          <div class="input-group">
            <button @click="freezeAccount" class="demo-btn">冻结账户</button>
            <button @click="unfreezeAccount" class="demo-btn">解冻账户</button>
            <button @click="closeAccount" class="demo-btn">关闭账户</button>
          </div>
        </div>

        <div class="demo-output">
          <div class="account-info">
            <div class="info-card">
              <h6>账户信息</h6>
              <div class="info-content">
                <div>账户ID: {{ currentState?.accountId || "N/A" }}</div>
                <div>
                  当前余额: ¥{{ (currentState?.balance || 0).toFixed(2) }}
                </div>
                <div>
                  账户状态:
                  {{ getStatusText(currentState?.status || "UNKNOWN") }}
                </div>
                <div>创建时间: {{ formatDate(currentState?.createdAt) }}</div>
                <div>最后更新: {{ formatDate(currentState?.lastUpdated) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 事件重放演示 -->
      <div class="demo-subsection">
        <h5>2. 事件重放和时间旅行</h5>
        <div class="demo-controls">
          <button
            @click="replayAllEvents"
            class="demo-btn"
            :disabled="isReplaying"
          >
            {{ isReplaying ? "重放中..." : "重放所有事件" }}
          </button>
          <button @click="replayToSpecificVersion" class="demo-btn">
            重放到指定版本
          </button>
          <button @click="showEventDetails" class="demo-btn">
            显示事件详情
          </button>
          <button @click="createSnapshot" class="demo-btn">创建快照</button>
        </div>

        <div class="demo-output">
          <div class="replay-info">
            <div class="replay-status">
              <h6>重放状态</h6>
              <div>当前版本: {{ currentState?.version || 0 }}</div>
              <div>重放到版本: {{ replayToVersion }}</div>
              <div>重放进度: {{ replayProgress }}%</div>
            </div>
            <div class="event-timeline">
              <h6>事件时间线</h6>
              <div class="timeline">
                <div
                  v-for="(event, index) in eventStore"
                  :key="event.id"
                  class="timeline-item"
                  :class="{
                    active: index < replayToVersion,
                    current: index === replayToVersion - 1,
                  }"
                >
                  <div class="timeline-dot"></div>
                  <div class="timeline-content">
                    <div class="timeline-type">{{ event.type }}</div>
                    <div class="timeline-time">
                      {{ formatTime(event.timestamp) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 投影演示 -->
      <div class="demo-subsection">
        <h5>3. 投影和查询模型</h5>
        <div class="demo-controls">
          <button @click="updateProjections" class="demo-btn">更新投影</button>
          <button @click="generateReport" class="demo-btn">生成报告</button>
          <button @click="exportEvents" class="demo-btn">导出事件</button>
        </div>

        <div class="demo-output">
          <div class="projections-display">
            <div class="projection-card">
              <h6>交易统计</h6>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">
                    {{ projections?.summary?.totalTransactions || 0 }}
                  </div>
                  <div class="stat-label">总交易数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">
                    ¥{{ (projections?.summary?.totalDeposits || 0).toFixed(2) }}
                  </div>
                  <div class="stat-label">总存款</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">
                    ¥{{
                      (projections?.summary?.totalWithdrawals || 0).toFixed(2)
                    }}
                  </div>
                  <div class="stat-label">总取款</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">
                    ¥{{
                      (
                        (projections?.summary?.totalDeposits || 0) -
                        (projections?.summary?.totalWithdrawals || 0)
                      ).toFixed(2)
                    }}
                  </div>
                  <div class="stat-label">净流入</div>
                </div>
              </div>
            </div>

            <div class="projection-card">
              <h6>最近事件</h6>
              <div class="recent-events">
                <div
                  v-for="event in eventStore?.getAllEvents()?.slice(-5) || []"
                  :key="event.id"
                  class="recent-event"
                >
                  <span class="event-type">{{ event.type }}</span>
                  <span class="event-amount" v-if="event.data.amount">
                    ¥{{ event.data.amount.toFixed(2) }}
                  </span>
                  <span class="event-time">{{
                    formatTime(event.timestamp)
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快照演示 -->
      <div class="demo-subsection">
        <h5>4. 快照和性能优化</h5>
        <div class="demo-controls">
          <div class="input-group">
            <label>快照间隔：</label>
            <select v-model="snapshotInterval" class="demo-select">
              <option value="5">每5个事件</option>
              <option value="10">每10个事件</option>
              <option value="20">每20个事件</option>
            </select>
          </div>
          <button @click="enableAutoSnapshot" class="demo-btn">
            启用自动快照
          </button>
          <button @click="loadFromSnapshot" class="demo-btn">从快照加载</button>
        </div>

        <div class="demo-output">
          <div class="snapshot-info">
            <div class="snapshot-list">
              <h6>快照列表</h6>
              <div
                v-for="snapshot in snapshots"
                :key="snapshot.id"
                class="snapshot-item"
              >
                <div class="snapshot-version">版本 {{ snapshot.version }}</div>
                <div class="snapshot-time">
                  {{ formatDate(snapshot.timestamp) }}
                </div>
                <div class="snapshot-size">{{ snapshot.size }} bytes</div>
              </div>
            </div>
            <div class="performance-metrics">
              <h6>性能指标</h6>
              <div class="metric-item">
                <strong>重建时间:</strong>
                {{ performanceMetrics.rebuildTime }}ms
              </div>
              <div class="metric-item">
                <strong>事件数量:</strong>
                {{ eventStore?.getAllEvents()?.length || 0 }}
              </div>
              <div class="metric-item">
                <strong>存储大小:</strong>
                {{ performanceMetrics.storageSize }}KB
              </div>
              <div class="metric-item">
                <strong>快照数量:</strong> {{ snapshots?.length || 0 }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优缺点分析 -->
    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 完整的审计跟踪，所有变化都有记录</li>
        <li>✅ 时间旅行能力，可以重建任意时间点的状态</li>
        <li>✅ 高度可扩展，支持多种投影和查询模型</li>
        <li>✅ 数据不会丢失，事件是不可变的</li>
        <li>✅ 支持复杂的业务分析和报告</li>
        <li>✅ 便于调试和问题追踪</li>
        <li>✅ 支持事件重放和数据恢复</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 存储空间需求较大</li>
        <li>❌ 查询复杂度高，需要事件重放</li>
        <li>❌ 学习曲线陡峭，概念复杂</li>
        <li>❌ 事件模式演进困难</li>
        <li>❌ 最终一致性，不适合强一致性需求</li>
        <li>❌ 删除数据困难（GDPR等合规要求）</li>
      </ul>

      <h4>核心概念：</h4>
      <ul>
        <li>🔍 <strong>事件:</strong> 不可变的业务事实，记录已发生的变化</li>
        <li>🔍 <strong>聚合根:</strong> 业务实体，通过事件重放重建状态</li>
        <li>🔍 <strong>投影:</strong> 从事件流派生的查询模型</li>
        <li>🔍 <strong>快照:</strong> 某个时间点的状态快照，优化重建性能</li>
        <li>🔍 <strong>事件存储:</strong> 专门存储事件的数据库</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import {
  EventStore,
  BankAccount,
  AccountEvent,
  EventProjections,
  SnapshotManager,
} from "@/patterns/EventSourcing";

// 响应式数据
const depositAmount = ref<number>(100);
const withdrawAmount = ref<number>(50);
const replayToVersion = ref<number>(0);
const isReplaying = ref<boolean>(false);
const snapshotInterval = ref<number>(5);

// 事件溯源组件
const eventStore = ref<EventStore | null>(null);
const bankAccount = ref<BankAccount | null>(null);
const projections = ref<EventProjections | null>(null);
const snapshotManager = ref<SnapshotManager | null>(null);

// 状态数据
const currentState = ref<{
  accountId: string;
  balance: number;
  status: string;
  version: number;
  createdAt: Date;
  lastUpdated: Date;
}>({
  accountId: "ACC-001",
  balance: 0,
  status: "active",
  version: 0,
  createdAt: new Date(),
  lastUpdated: new Date(),
});

const replayingEvents = ref<string[]>([]);
const snapshots = ref<
  Array<{
    id: string;
    version: number;
    timestamp: Date;
    size: number;
  }>
>([]);

const performanceMetrics = ref<{
  rebuildTime: number;
  storageSize: number;
}>({
  rebuildTime: 0,
  storageSize: 0,
});

// 计算属性
const replayProgress = computed(() => {
  if (eventStore.value) {
    const totalEvents = eventStore.value.getAllEvents().length;
    return totalEvents > 0
      ? Math.round((replayToVersion.value / totalEvents) * 100)
      : 0;
  }
  return 0;
});

// 方法
const deposit = () => {
  if (bankAccount.value && depositAmount.value > 0) {
    bankAccount.value.deposit(depositAmount.value);
    updateCurrentState();
    updateProjections();
    checkAutoSnapshot();
  }
};

const withdraw = () => {
  if (bankAccount.value && withdrawAmount.value > 0) {
    try {
      bankAccount.value.withdraw(withdrawAmount.value);
      updateCurrentState();
      updateProjections();
      checkAutoSnapshot();
    } catch (error) {
      alert("取款失败: " + (error as Error).message);
    }
  }
};

const freezeAccount = () => {
  if (bankAccount.value) {
    bankAccount.value.freeze();
    updateCurrentState();
    updateProjections();
  }
};

const unfreezeAccount = () => {
  if (bankAccount.value) {
    bankAccount.value.unfreeze();
    updateCurrentState();
    updateProjections();
  }
};

const closeAccount = () => {
  if (bankAccount.value) {
    bankAccount.value.close();
    updateCurrentState();
    updateProjections();
  }
};

const updateCurrentState = () => {
  if (bankAccount.value) {
    const state = bankAccount.value.getState();
    currentState.value = {
      ...state,
      lastUpdated: new Date(),
    };
  }
};

const updateProjections = () => {
  if (projections.value && eventStore.value) {
    projections.value.updateFromEvents(eventStore.value.getAllEvents());
  }
};

const replayAllEvents = async () => {
  if (!eventStore.value || !bankAccount.value) return;

  isReplaying.value = true;
  const events = eventStore.value.getAllEvents();

  // 重置账户状态
  bankAccount.value.reset();

  // 逐个重放事件
  for (let i = 0; i < events.length; i++) {
    replayingEvents.value = [events[i].id];
    await new Promise((resolve) => setTimeout(resolve, 300));
    bankAccount.value!.applyEvent(events[i]);
    updateCurrentState();
  }

  replayingEvents.value = [];
  isReplaying.value = false;
};

const replayToSpecificVersion = () => {
  if (!eventStore.value || !bankAccount.value) return;

  const events = eventStore.value
    .getAllEvents()
    .slice(0, replayToVersion.value);

  // 重置账户状态
  bankAccount.value.reset();

  // 重放到指定版本
  events.forEach((event) => {
    bankAccount.value!.applyEvent(event);
  });

  updateCurrentState();
};

const showEventDetails = () => {
  if (eventStore.value) {
    const events = eventStore.value.getAllEvents();
    const details = events.map((event) => ({
      type: event.type,
      timestamp: event.timestamp,
      data: event.data,
      version: event.version,
    }));
    console.table(details);
    alert("事件详情已输出到控制台");
  }
};

const createSnapshot = () => {
  if (snapshotManager.value && bankAccount.value) {
    const snapshot = snapshotManager.value.createSnapshot(
      bankAccount.value.getState()
    );
    snapshots.value.push({
      id: snapshot.id,
      version: snapshot.version,
      timestamp: snapshot.timestamp,
      size: JSON.stringify(snapshot.data).length,
    });
  }
};

const enableAutoSnapshot = () => {
  // 启用自动快照逻辑
  alert(`已启用自动快照，每${snapshotInterval.value}个事件创建一次快照`);
};

const loadFromSnapshot = () => {
  if (snapshots.value.length > 0) {
    const latestSnapshot = snapshots.value[snapshots.value.length - 1];
    alert(`从快照版本${latestSnapshot.version}加载状态`);
  } else {
    alert("没有可用的快照");
  }
};

const checkAutoSnapshot = () => {
  if (eventStore.value) {
    const eventCount = eventStore.value.getAllEvents().length;
    if (eventCount % snapshotInterval.value === 0) {
      createSnapshot();
    }
  }
};

const generateReport = () => {
  if (projections.value) {
    const report = projections.value.generateReport();
    console.log("生成的报告:", report);
    alert("报告已生成，请查看控制台");
  }
};

const exportEvents = () => {
  if (eventStore.value) {
    const events = eventStore.value.getAllEvents();
    const exportData = JSON.stringify(events, null, 2);
    console.log("导出的事件数据:", exportData);
    alert("事件数据已导出到控制台");
  }
};

const getStatusText = (status: string): string => {
  const statusMap = {
    active: "活跃",
    frozen: "冻结",
    closed: "关闭",
  };
  return statusMap[status as keyof typeof statusMap] || status;
};

const formatDate = (date: Date | undefined): string => {
  if (!date) return "N/A";
  return date.toLocaleString();
};

const formatTime = (date: Date | undefined): string => {
  if (!date) return "N/A";
  return date.toLocaleTimeString();
};

// 监听重放版本变化
watch(replayToVersion, (newVersion) => {
  replayToSpecificVersion();
});

// 生命周期
onMounted(() => {
  // 初始化事件溯源组件
  eventStore.value = new EventStore();
  bankAccount.value = new BankAccount("ACC-001", eventStore.value);
  projections.value = new EventProjections();
  snapshotManager.value = new SnapshotManager();

  // 初始化账户状态
  updateCurrentState();

  // 设置重放版本的最大值
  replayToVersion.value = 0;

  // 模拟一些初始事件
  setTimeout(() => {
    if (bankAccount.value) {
      bankAccount.value.deposit(1000);
      bankAccount.value.withdraw(200);
      bankAccount.value.deposit(500);
      updateCurrentState();
      updateProjections();

      // 更新重放版本范围
      if (eventStore.value) {
        replayToVersion.value = eventStore.value.getAllEvents().length;
      }
    }
  }, 1000);
});
</script>

<style lang="scss" scoped>
.event-sourcing-demo {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .pattern-info {
    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #1890ff;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .architecture-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .architecture-diagram {
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .event-sourcing-container {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr;
        gap: 2rem;

        .event-store,
        .aggregate-root,
        .projections {
          background: white;
          padding: 1.5rem;
          border-radius: 8px;
          border: 2px solid;
          min-height: 400px;

          h5 {
            margin: 0 0 1rem 0;
            font-weight: 600;
            text-align: center;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
          }
        }

        .event-store {
          border-color: #52c41a;

          h5 {
            color: #52c41a;
          }

          .event-list {
            max-height: 350px;
            overflow-y: auto;

            .event-item {
              background: #f6ffed;
              border: 1px solid #b7eb8f;
              border-radius: 6px;
              padding: 0.8rem;
              margin-bottom: 0.5rem;
              transition: all 0.3s ease;

              &.replaying {
                background: #fff2e8;
                border-color: #ffbb96;
                animation: pulse 1s infinite;
              }

              &.latest {
                background: #e6f7ff;
                border-color: #91d5ff;
              }

              .event-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.5rem;

                .event-type {
                  background: #52c41a;
                  color: white;
                  padding: 0.2rem 0.6rem;
                  border-radius: 4px;
                  font-size: 0.8rem;
                  font-weight: 600;
                }

                .event-timestamp {
                  color: #666;
                  font-size: 0.7rem;
                  font-family: "Monaco", "Consolas", monospace;
                }
              }

              .event-data {
                background: white;
                padding: 0.5rem;
                border-radius: 4px;
                font-family: "Monaco", "Consolas", monospace;
                font-size: 0.8rem;
                color: #333;
                margin-bottom: 0.3rem;
              }

              .event-version {
                text-align: right;
                color: #999;
                font-size: 0.7rem;
              }
            }
          }
        }

        .aggregate-root {
          border-color: #1890ff;

          h5 {
            color: #1890ff;
          }

          .current-state {
            margin-bottom: 1.5rem;

            h6 {
              margin: 0 0 0.8rem 0;
              color: #333;
              font-size: 0.9rem;
            }

            .state-display {
              background: #e6f7ff;
              padding: 1rem;
              border-radius: 6px;

              .state-item {
                padding: 0.3rem 0;
                border-bottom: 1px solid #d6f7ff;
                font-size: 0.9rem;

                &:last-child {
                  border-bottom: none;
                }

                strong {
                  color: #1890ff;
                }
              }
            }
          }

          .replay-controls {
            h6 {
              margin: 0 0 0.8rem 0;
              color: #333;
              font-size: 0.9rem;
            }

            .replay-slider {
              .version-slider {
                width: 100%;
                margin-bottom: 0.5rem;
              }

              .version-label {
                text-align: center;
                color: #1890ff;
                font-weight: 600;
              }
            }
          }
        }

        .projections {
          border-color: #722ed1;

          h5 {
            color: #722ed1;
          }

          .projection-list {
            .projection-item {
              background: #f9f0ff;
              padding: 1rem;
              border-radius: 6px;
              margin-bottom: 1rem;

              h6 {
                margin: 0 0 0.5rem 0;
                color: #722ed1;
                font-size: 0.9rem;
              }

              .projection-data {
                font-size: 0.8rem;
                color: #333;

                div {
                  padding: 0.2rem 0;
                }
              }
            }
          }
        }
      }
    }
  }

  .demo-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .demo-subsection {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      margin-bottom: 1.5rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #1890ff;
        font-weight: 600;
      }

      h6 {
        margin: 0 0 0.8rem 0;
        color: #333;
        font-weight: 600;
      }

      .demo-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1.5rem;
        align-items: end;

        .input-group {
          display: flex;
          align-items: center;
          gap: 0.5rem;

          label {
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
            white-space: nowrap;
          }
        }

        .demo-input,
        .demo-select {
          padding: 0.6rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
          min-width: 100px;

          &:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .demo-btn {
          padding: 0.6rem 1.2rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #1890ff, #722ed1);
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          font-size: 0.9rem;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }

      .demo-output {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .account-info {
          .info-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid #1890ff;

            h6 {
              margin: 0 0 0.8rem 0;
              color: #1890ff;
            }

            .info-content {
              div {
                padding: 0.3rem 0;
                color: #333;
                font-size: 0.9rem;
              }
            }
          }
        }

        .replay-info {
          display: grid;
          grid-template-columns: 1fr 2fr;
          gap: 2rem;

          .replay-status {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;

            div {
              padding: 0.3rem 0;
              color: #333;
              font-size: 0.9rem;
            }
          }

          .event-timeline {
            .timeline {
              position: relative;
              padding-left: 2rem;

              &::before {
                content: "";
                position: absolute;
                left: 10px;
                top: 0;
                bottom: 0;
                width: 2px;
                background: #e9ecef;
              }

              .timeline-item {
                position: relative;
                margin-bottom: 1rem;

                &.active .timeline-dot {
                  background: #52c41a;
                }

                &.current .timeline-dot {
                  background: #1890ff;
                  animation: pulse 2s infinite;
                }

                .timeline-dot {
                  position: absolute;
                  left: -25px;
                  top: 5px;
                  width: 12px;
                  height: 12px;
                  border-radius: 50%;
                  background: #d9d9d9;
                  border: 2px solid white;
                }

                .timeline-content {
                  background: white;
                  padding: 0.5rem;
                  border-radius: 4px;
                  border: 1px solid #e9ecef;

                  .timeline-type {
                    font-weight: 600;
                    color: #333;
                    font-size: 0.8rem;
                  }

                  .timeline-time {
                    color: #666;
                    font-size: 0.7rem;
                  }
                }
              }
            }
          }
        }

        .projections-display {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 2rem;

          .projection-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid #722ed1;

            h6 {
              margin: 0 0 1rem 0;
              color: #722ed1;
            }

            .stats-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 1rem;

              .stat-item {
                text-align: center;
                background: white;
                padding: 0.8rem;
                border-radius: 4px;

                .stat-value {
                  font-size: 1.2rem;
                  font-weight: 600;
                  color: #1890ff;
                  margin-bottom: 0.3rem;
                }

                .stat-label {
                  font-size: 0.8rem;
                  color: #666;
                }
              }
            }

            .recent-events {
              .recent-event {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                margin-bottom: 0.3rem;
                background: white;
                border-radius: 4px;
                font-size: 0.8rem;

                .event-type {
                  background: #722ed1;
                  color: white;
                  padding: 0.2rem 0.4rem;
                  border-radius: 3px;
                  font-size: 0.7rem;
                }

                .event-amount {
                  font-weight: 600;
                  color: #1890ff;
                }

                .event-time {
                  color: #666;
                  font-size: 0.7rem;
                }
              }
            }
          }
        }

        .snapshot-info {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 2rem;

          .snapshot-list {
            .snapshot-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0.5rem;
              margin-bottom: 0.3rem;
              background: #f8f9fa;
              border-radius: 4px;
              font-size: 0.8rem;

              .snapshot-version {
                font-weight: 600;
                color: #1890ff;
              }

              .snapshot-time {
                color: #666;
                font-size: 0.7rem;
              }

              .snapshot-size {
                color: #999;
                font-size: 0.7rem;
              }
            }
          }

          .performance-metrics {
            .metric-item {
              padding: 0.5rem 0;
              border-bottom: 1px solid #f0f0f0;
              font-size: 0.9rem;

              &:last-child {
                border-bottom: none;
              }

              strong {
                color: #333;
              }
            }
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@media (max-width: 768px) {
  .event-sourcing-demo {
    .architecture-section {
      .architecture-diagram {
        .event-sourcing-container {
          grid-template-columns: 1fr;
        }
      }
    }

    .demo-section {
      .demo-subsection {
        .demo-controls {
          flex-direction: column;
          align-items: stretch;

          .input-group {
            flex-direction: column;
            align-items: stretch;
            gap: 0.3rem;
          }
        }

        .demo-output {
          .replay-info,
          .projections-display,
          .snapshot-info {
            grid-template-columns: 1fr;
          }

          .stats-grid {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
}
</style>
