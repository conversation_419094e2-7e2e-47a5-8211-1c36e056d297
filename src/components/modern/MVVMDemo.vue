<template>
  <div class="mvvm-demo">
    <div class="demo-header">
      <h3>MVVM模式演示</h3>
      <p>展示Model-View-ViewModel架构模式，实现数据绑定和视图更新的自动化</p>
    </div>

    <div class="demo-container">
      <!-- MVVM架构图 -->
      <div class="architecture-section">
        <h4>🏗️ MVVM架构图</h4>
        <div class="architecture-diagram">
          <div class="layer model-layer">
            <h5>Model (数据模型)</h5>
            <div class="model-content">
              <div class="data-item">
                <span class="label">用户数据:</span>
                <span class="value">{{
                  JSON.stringify(userModel, null, 2)
                }}</span>
              </div>
              <div class="data-item">
                <span class="label">产品数据:</span>
                <span class="value">{{
                  JSON.stringify(productModel, null, 2)
                }}</span>
              </div>
            </div>
          </div>

          <div class="arrows">
            <div class="arrow down">
              <span>数据流向</span>
              <div class="arrow-line">↓</div>
            </div>
          </div>

          <div class="layer viewmodel-layer">
            <h5>ViewModel (视图模型)</h5>
            <div class="viewmodel-content">
              <div class="vm-section">
                <h6>用户ViewModel</h6>
                <div class="vm-data">
                  <div>计算属性: {{ userViewModel.fullName }}</div>
                  <div>验证状态: {{ userViewModel.isValid ? "✓" : "✗" }}</div>
                  <div>显示状态: {{ userViewModel.displayStatus }}</div>
                </div>
              </div>
              <div class="vm-section">
                <h6>产品ViewModel</h6>
                <div class="vm-data">
                  <div>格式化价格: {{ productViewModel.formattedPrice }}</div>
                  <div>库存状态: {{ productViewModel.stockStatus }}</div>
                  <div>推荐等级: {{ productViewModel.recommendLevel }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="arrows">
            <div class="arrow down">
              <span>双向绑定</span>
              <div class="arrow-line">↕</div>
            </div>
          </div>

          <div class="layer view-layer">
            <h5>View (视图)</h5>
            <div class="view-content">
              <div class="view-section">
                <h6>用户信息视图</h6>
                <div class="user-view">
                  <div class="form-group">
                    <label>姓名:</label>
                    <input
                      v-model="userModel.firstName"
                      placeholder="名字"
                      class="input-field"
                    />
                    <input
                      v-model="userModel.lastName"
                      placeholder="姓氏"
                      class="input-field"
                    />
                  </div>
                  <div class="form-group">
                    <label>邮箱:</label>
                    <input
                      v-model="userModel.email"
                      type="email"
                      placeholder="邮箱地址"
                      class="input-field"
                      :class="{ invalid: !userViewModel.isEmailValid }"
                    />
                  </div>
                  <div class="form-group">
                    <label>年龄:</label>
                    <input
                      v-model.number="userModel.age"
                      type="number"
                      placeholder="年龄"
                      class="input-field"
                    />
                  </div>
                  <div class="display-info">
                    <p><strong>全名:</strong> {{ userViewModel.fullName }}</p>
                    <p>
                      <strong>状态:</strong>
                      <span :class="userViewModel.statusClass">{{
                        userViewModel.displayStatus
                      }}</span>
                    </p>
                  </div>
                </div>
              </div>

              <div class="view-section">
                <h6>产品信息视图</h6>
                <div class="product-view">
                  <div class="form-group">
                    <label>产品名称:</label>
                    <input
                      v-model="productModel.name"
                      placeholder="产品名称"
                      class="input-field"
                    />
                  </div>
                  <div class="form-group">
                    <label>价格:</label>
                    <input
                      v-model.number="productModel.price"
                      type="number"
                      step="0.01"
                      placeholder="价格"
                      class="input-field"
                    />
                  </div>
                  <div class="form-group">
                    <label>库存:</label>
                    <input
                      v-model.number="productModel.stock"
                      type="number"
                      placeholder="库存数量"
                      class="input-field"
                    />
                  </div>
                  <div class="form-group">
                    <label>评分:</label>
                    <input
                      v-model.number="productModel.rating"
                      type="number"
                      min="0"
                      max="5"
                      step="0.1"
                      placeholder="评分 (0-5)"
                      class="input-field"
                    />
                  </div>
                  <div class="display-info">
                    <p>
                      <strong>格式化价格:</strong>
                      {{ productViewModel.formattedPrice }}
                    </p>
                    <p>
                      <strong>库存状态:</strong>
                      <span :class="productViewModel.stockClass">{{
                        productViewModel.stockStatus
                      }}</span>
                    </p>
                    <p>
                      <strong>推荐等级:</strong>
                      <span :class="productViewModel.recommendClass">{{
                        productViewModel.recommendLevel
                      }}</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据绑定演示 -->
      <div class="binding-demo-section">
        <h4>🔗 数据绑定演示</h4>
        <div class="binding-examples">
          <div class="binding-example">
            <h5>单向绑定 (Model → View)</h5>
            <p>模型数据自动反映到视图中</p>
            <div class="demo-controls">
              <button @click="updateUserModel" class="demo-btn">
                更新用户模型
              </button>
              <button @click="updateProductModel" class="demo-btn">
                更新产品模型
              </button>
            </div>
          </div>

          <div class="binding-example">
            <h5>双向绑定 (Model ↔ View)</h5>
            <p>视图输入自动更新模型，模型变化自动更新视图</p>
            <div class="demo-controls">
              <button @click="resetModels" class="demo-btn secondary">
                重置所有数据
              </button>
              <button @click="loadSampleData" class="demo-btn">
                加载示例数据
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 命令模式集成 -->
      <div class="command-integration-section">
        <h4>⚡ 命令模式集成</h4>
        <p>MVVM中的用户操作通过命令模式处理</p>
        <div class="command-demo">
          <div class="command-buttons">
            <button
              @click="executeCommand('save')"
              :disabled="!canExecuteCommand('save')"
              class="command-btn save"
            >
              保存数据
            </button>
            <button
              @click="executeCommand('validate')"
              :disabled="!canExecuteCommand('validate')"
              class="command-btn validate"
            >
              验证数据
            </button>
            <button
              @click="executeCommand('export')"
              :disabled="!canExecuteCommand('export')"
              class="command-btn export"
            >
              导出数据
            </button>
            <button
              @click="executeCommand('undo')"
              :disabled="!canExecuteCommand('undo')"
              class="command-btn undo"
            >
              撤销操作
            </button>
          </div>

          <div class="command-history">
            <h6>命令历史:</h6>
            <div class="history-list">
              <div
                v-for="(cmd, index) in commandHistory.slice(-5)"
                :key="index"
                class="history-item"
              >
                <span class="cmd-time">{{ cmd.timestamp }}</span>
                <span class="cmd-name">{{ cmd.name }}</span>
                <span class="cmd-status" :class="cmd.status">{{
                  cmd.status
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 响应式更新演示 -->
      <div class="reactivity-section">
        <h4>🔄 响应式更新演示</h4>
        <div class="reactivity-demo">
          <div class="update-triggers">
            <h5>触发更新:</h5>
            <div class="trigger-buttons">
              <button @click="triggerBatchUpdate" class="trigger-btn">
                批量更新
              </button>
              <button @click="triggerAsyncUpdate" class="trigger-btn">
                异步更新
              </button>
              <button @click="triggerComputedUpdate" class="trigger-btn">
                计算属性更新
              </button>
            </div>
          </div>

          <div class="update-log">
            <h5>更新日志:</h5>
            <div class="log-entries">
              <div
                v-for="(entry, index) in updateLog.slice(-8)"
                :key="index"
                class="log-entry"
              >
                <span class="log-time">{{ entry.time }}</span>
                <span class="log-message">{{ entry.message }}</span>
              </div>
            </div>
            <button @click="clearUpdateLog" class="clear-btn">清空日志</button>
          </div>
        </div>
      </div>
    </div>

    <!-- MVVM原理说明 -->
    <div class="principle-explanation">
      <h4>🧠 MVVM模式原理</h4>
      <div class="principle-content">
        <div class="principle-item">
          <h5>核心概念</h5>
          <ul>
            <li><strong>Model</strong>: 数据模型，包含业务逻辑和数据</li>
            <li><strong>View</strong>: 用户界面，负责数据展示和用户交互</li>
            <li><strong>ViewModel</strong>: 视图模型，连接Model和View的桥梁</li>
          </ul>
        </div>
        <div class="principle-item">
          <h5>关键特性</h5>
          <ul>
            <li><strong>数据绑定</strong>: 自动同步Model和View之间的数据</li>
            <li><strong>命令模式</strong>: 将用户操作封装为命令对象</li>
            <li><strong>依赖属性</strong>: 基于其他属性计算的派生属性</li>
            <li><strong>变更通知</strong>: 数据变化时自动通知相关组件</li>
          </ul>
        </div>
        <div class="principle-item">
          <h5>优势</h5>
          <ul>
            <li><strong>松耦合</strong>: View和Model完全分离</li>
            <li><strong>可测试性</strong>: ViewModel可以独立测试</li>
            <li><strong>可维护性</strong>: 清晰的职责分离</li>
            <li><strong>可重用性</strong>: ViewModel可以被多个View使用</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, nextTick } from "vue";

// Model 定义
interface UserModel {
  firstName: string;
  lastName: string;
  email: string;
  age: number;
}

interface ProductModel {
  name: string;
  price: number;
  stock: number;
  rating: number;
}

interface CommandHistoryItem {
  name: string;
  timestamp: string;
  status: "success" | "error" | "pending";
}

interface UpdateLogEntry {
  time: string;
  message: string;
}

// 响应式数据模型
const userModel = reactive<UserModel>({
  firstName: "",
  lastName: "",
  email: "",
  age: 0,
});

const productModel = reactive<ProductModel>({
  name: "",
  price: 0,
  stock: 0,
  rating: 0,
});

// ViewModel - 计算属性和业务逻辑
const userViewModel = computed(() => ({
  fullName: `${userModel.firstName} ${userModel.lastName}`.trim() || "未设置",
  isValid:
    userModel.firstName &&
    userModel.lastName &&
    userModel.email &&
    userModel.age > 0,
  isEmailValid: /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userModel.email),
  displayStatus: (() => {
    if (!userModel.firstName || !userModel.lastName) return "信息不完整";
    if (!userModel.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userModel.email))
      return "邮箱无效";
    if (userModel.age <= 0) return "年龄无效";
    if (userModel.age < 18) return "未成年";
    if (userModel.age >= 60) return "老年用户";
    return "正常用户";
  })(),
  statusClass: computed(() => {
    const status = userViewModel.value.displayStatus;
    if (status === "正常用户") return "status-success";
    if (
      status === "信息不完整" ||
      status === "邮箱无效" ||
      status === "年龄无效"
    )
      return "status-error";
    return "status-warning";
  }),
}));

const productViewModel = computed(() => ({
  formattedPrice:
    productModel.price > 0 ? `¥${productModel.price.toFixed(2)}` : "未设置价格",
  stockStatus: (() => {
    if (productModel.stock <= 0) return "缺货";
    if (productModel.stock < 10) return "库存不足";
    if (productModel.stock < 50) return "库存正常";
    return "库存充足";
  })(),
  stockClass: computed(() => {
    const status = productViewModel.value.stockStatus;
    if (status === "库存充足" || status === "库存正常") return "status-success";
    if (status === "库存不足") return "status-warning";
    return "status-error";
  }),
  recommendLevel: (() => {
    if (productModel.rating >= 4.5) return "强烈推荐";
    if (productModel.rating >= 4.0) return "推荐";
    if (productModel.rating >= 3.0) return "一般";
    if (productModel.rating >= 2.0) return "不推荐";
    return "评分过低";
  })(),
  recommendClass: computed(() => {
    const level = productViewModel.value.recommendLevel;
    if (level === "强烈推荐" || level === "推荐") return "status-success";
    if (level === "一般") return "status-warning";
    return "status-error";
  }),
}));

// 命令历史和更新日志
const commandHistory = ref<CommandHistoryItem[]>([]);
const updateLog = ref<UpdateLogEntry[]>([]);

// 命令模式实现
const commands = {
  save: {
    canExecute: () =>
      userViewModel.value.isValid &&
      productModel.name &&
      productModel.price > 0,
    execute: async () => {
      addUpdateLog("执行保存命令...");
      await new Promise((resolve) => setTimeout(resolve, 500));
      addUpdateLog("数据保存成功");
      return "success";
    },
  },
  validate: {
    canExecute: () => true,
    execute: async () => {
      addUpdateLog("执行验证命令...");
      const userValid = userViewModel.value.isValid;
      const productValid = productModel.name && productModel.price > 0;
      addUpdateLog(
        `验证结果: 用户数据${userValid ? "有效" : "无效"}, 产品数据${
          productValid ? "有效" : "无效"
        }`
      );
      return userValid && productValid ? "success" : "error";
    },
  },
  export: {
    canExecute: () =>
      userViewModel.value.isValid ||
      (productModel.name && productModel.price > 0),
    execute: async () => {
      addUpdateLog("执行导出命令...");
      await new Promise((resolve) => setTimeout(resolve, 300));
      const data = { user: userModel, product: productModel };
      console.log("导出数据:", data);
      addUpdateLog("数据导出完成");
      return "success";
    },
  },
  undo: {
    canExecute: () => commandHistory.value.length > 0,
    execute: async () => {
      addUpdateLog("执行撤销命令...");
      if (commandHistory.value.length > 0) {
        commandHistory.value.pop();
        addUpdateLog("撤销操作完成");
        return "success";
      }
      return "error";
    },
  },
};

const canExecuteCommand = (commandName: keyof typeof commands) => {
  return commands[commandName].canExecute();
};

const executeCommand = async (commandName: keyof typeof commands) => {
  const timestamp = new Date().toLocaleTimeString();
  const historyItem: CommandHistoryItem = {
    name: commandName,
    timestamp,
    status: "pending",
  };

  commandHistory.value.push(historyItem);

  try {
    const result = await commands[commandName].execute();
    historyItem.status = result as "success" | "error";
  } catch (error) {
    historyItem.status = "error";
    addUpdateLog(`命令执行失败: ${error}`);
  }
};

// 辅助函数
const addUpdateLog = (message: string) => {
  updateLog.value.push({
    time: new Date().toLocaleTimeString(),
    message,
  });
};

const updateUserModel = () => {
  const samples = [
    {
      firstName: "张",
      lastName: "三",
      email: "<EMAIL>",
      age: 25,
    },
    { firstName: "李", lastName: "四", email: "<EMAIL>", age: 30 },
    { firstName: "王", lastName: "五", email: "<EMAIL>", age: 28 },
  ];
  const sample = samples[Math.floor(Math.random() * samples.length)];
  Object.assign(userModel, sample);
  addUpdateLog("用户模型已更新");
};

const updateProductModel = () => {
  const samples = [
    { name: "iPhone 15", price: 5999, stock: 25, rating: 4.8 },
    { name: "MacBook Pro", price: 12999, stock: 8, rating: 4.9 },
    { name: "AirPods Pro", price: 1999, stock: 50, rating: 4.6 },
  ];
  const sample = samples[Math.floor(Math.random() * samples.length)];
  Object.assign(productModel, sample);
  addUpdateLog("产品模型已更新");
};

const resetModels = () => {
  Object.assign(userModel, { firstName: "", lastName: "", email: "", age: 0 });
  Object.assign(productModel, { name: "", price: 0, stock: 0, rating: 0 });
  addUpdateLog("所有模型已重置");
};

const loadSampleData = () => {
  updateUserModel();
  updateProductModel();
  addUpdateLog("示例数据加载完成");
};

const triggerBatchUpdate = () => {
  addUpdateLog("开始批量更新...");
  userModel.firstName = "批量";
  userModel.lastName = "更新";
  userModel.email = "<EMAIL>";
  userModel.age = 25;

  productModel.name = "批量更新产品";
  productModel.price = 999;
  productModel.stock = 100;
  productModel.rating = 4.5;
  addUpdateLog("批量更新完成");
};

const triggerAsyncUpdate = async () => {
  addUpdateLog("开始异步更新...");
  await nextTick();

  setTimeout(() => {
    userModel.firstName = "异步";
    addUpdateLog("异步更新用户名");
  }, 100);

  setTimeout(() => {
    productModel.name = "异步更新产品";
    addUpdateLog("异步更新产品名");
  }, 200);

  setTimeout(() => {
    addUpdateLog("异步更新完成");
  }, 300);
};

const triggerComputedUpdate = () => {
  addUpdateLog("触发计算属性更新...");
  userModel.firstName = "计算";
  userModel.lastName = "属性";
  addUpdateLog(`计算属性更新: ${userViewModel.value.fullName}`);
};

const clearUpdateLog = () => {
  updateLog.value = [];
};

// 监听数据变化
watch(
  userModel,
  (newVal) => {
    addUpdateLog(`用户模型变化: ${JSON.stringify(newVal)}`);
  },
  { deep: true }
);

watch(
  productModel,
  (newVal) => {
    addUpdateLog(`产品模型变化: ${JSON.stringify(newVal)}`);
  },
  { deep: true }
);

// 初始化
loadSampleData();
</script>

<style lang="scss" scoped>
.mvvm-demo {
  padding: 1.5rem;

  .demo-header {
    margin-bottom: 1.5rem;

    h3 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.3rem;
    }

    p {
      margin: 0;
      color: #666;
      line-height: 1.5;
    }
  }

  .demo-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  // 架构图部分
  .architecture-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.1rem;
    }

    .architecture-diagram {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .layer {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        border: 2px solid #ddd;

        h5 {
          margin: 0 0 1rem 0;
          color: #444;
          font-size: 1rem;
          text-align: center;
          padding: 0.5rem;
          border-radius: 4px;
        }

        &.model-layer {
          border-color: #e74c3c;

          h5 {
            background: #e74c3c;
            color: white;
          }

          .model-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .data-item {
              display: flex;
              flex-direction: column;
              gap: 0.5rem;

              .label {
                font-weight: 600;
                color: #e74c3c;
              }

              .value {
                background: #f8f9fa;
                padding: 0.8rem;
                border-radius: 4px;
                font-family: monospace;
                font-size: 0.85rem;
                white-space: pre-wrap;
                border: 1px solid #eee;
              }
            }
          }
        }

        &.viewmodel-layer {
          border-color: #3498db;

          h5 {
            background: #3498db;
            color: white;
          }

          .viewmodel-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;

            @media (max-width: 768px) {
              grid-template-columns: 1fr;
            }

            .vm-section {
              h6 {
                margin: 0 0 0.5rem 0;
                color: #3498db;
                font-weight: 600;
              }

              .vm-data {
                display: flex;
                flex-direction: column;
                gap: 0.3rem;

                div {
                  padding: 0.4rem 0.6rem;
                  background: #e3f2fd;
                  border-radius: 4px;
                  font-size: 0.9rem;
                  border-left: 3px solid #3498db;
                }
              }
            }
          }
        }

        &.view-layer {
          border-color: #27ae60;

          h5 {
            background: #27ae60;
            color: white;
          }

          .view-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;

            @media (max-width: 1024px) {
              grid-template-columns: 1fr;
            }

            .view-section {
              h6 {
                margin: 0 0 1rem 0;
                color: #27ae60;
                font-weight: 600;
              }

              .form-group {
                margin-bottom: 1rem;

                label {
                  display: block;
                  margin-bottom: 0.5rem;
                  font-weight: 500;
                  color: #333;
                }

                .input-field {
                  width: 100%;
                  padding: 0.6rem;
                  border: 1px solid #ddd;
                  border-radius: 4px;
                  font-size: 0.9rem;
                  margin-bottom: 0.5rem;

                  &:focus {
                    outline: none;
                    border-color: #27ae60;
                    box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2);
                  }

                  &.invalid {
                    border-color: #e74c3c;
                    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
                  }
                }
              }

              .display-info {
                background: #e8f5e8;
                padding: 1rem;
                border-radius: 4px;
                border-left: 4px solid #27ae60;

                p {
                  margin: 0.5rem 0;
                  font-size: 0.9rem;

                  &:first-child {
                    margin-top: 0;
                  }

                  &:last-child {
                    margin-bottom: 0;
                  }
                }
              }
            }
          }
        }
      }

      .arrows {
        display: flex;
        justify-content: center;
        align-items: center;

        .arrow {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;

          span {
            font-size: 0.85rem;
            color: #666;
            font-weight: 500;
          }

          .arrow-line {
            font-size: 1.5rem;
            color: #3498db;
            font-weight: bold;
          }
        }
      }
    }
  }

  // 数据绑定演示
  .binding-demo-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.1rem;
    }

    .binding-examples {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }

      .binding-example {
        h5 {
          margin: 0 0 0.5rem 0;
          color: #444;
          font-size: 1rem;
        }

        p {
          margin: 0 0 1rem 0;
          color: #666;
          font-size: 0.9rem;
          line-height: 1.4;
        }

        .demo-controls {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          .demo-btn {
            padding: 0.6rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;

            &:not(.secondary) {
              background: #3498db;
              color: white;

              &:hover {
                background: #2980b9;
              }
            }

            &.secondary {
              background: #95a5a6;
              color: white;

              &:hover {
                background: #7f8c8d;
              }
            }
          }
        }
      }
    }
  }

  // 命令集成部分
  .command-integration-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;

    h4 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.1rem;
    }

    p {
      margin: 0 0 1rem 0;
      color: #666;
      font-size: 0.9rem;
    }

    .command-demo {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;

      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
      }

      .command-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;

        .command-btn {
          padding: 0.8rem 1rem;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          &.save {
            background: #27ae60;
            color: white;

            &:hover:not(:disabled) {
              background: #229954;
            }
          }

          &.validate {
            background: #f39c12;
            color: white;

            &:hover:not(:disabled) {
              background: #e67e22;
            }
          }

          &.export {
            background: #3498db;
            color: white;

            &:hover:not(:disabled) {
              background: #2980b9;
            }
          }

          &.undo {
            background: #95a5a6;
            color: white;

            &:hover:not(:disabled) {
              background: #7f8c8d;
            }
          }
        }
      }

      .command-history {
        h6 {
          margin: 0 0 0.5rem 0;
          color: #444;
          font-weight: 600;
        }

        .history-list {
          max-height: 150px;
          overflow-y: auto;
          background: #f8f9fa;
          border-radius: 4px;
          padding: 0.5rem;

          .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.4rem 0;
            border-bottom: 1px solid #eee;
            font-size: 0.85rem;

            &:last-child {
              border-bottom: none;
            }

            .cmd-time {
              color: #666;
              min-width: 80px;
            }

            .cmd-name {
              flex: 1;
              margin: 0 0.5rem;
              color: #333;
            }

            .cmd-status {
              padding: 0.2rem 0.4rem;
              border-radius: 3px;
              font-size: 0.75rem;
              font-weight: 500;

              &.success {
                background: #d4edda;
                color: #155724;
              }

              &.error {
                background: #f8d7da;
                color: #721c24;
              }

              &.pending {
                background: #fff3cd;
                color: #856404;
              }
            }
          }
        }
      }
    }
  }

  // 响应式更新部分
  .reactivity-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.1rem;
    }

    .reactivity-demo {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;

      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
      }

      .update-triggers {
        h5 {
          margin: 0 0 1rem 0;
          color: #444;
          font-size: 1rem;
        }

        .trigger-buttons {
          display: flex;
          flex-direction: column;
          gap: 0.8rem;

          .trigger-btn {
            padding: 0.6rem 1rem;
            background: #9b59b6;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
              background: #8e44ad;
            }
          }
        }
      }

      .update-log {
        h5 {
          margin: 0 0 0.5rem 0;
          color: #444;
          font-size: 1rem;
        }

        .log-entries {
          max-height: 200px;
          overflow-y: auto;
          background: #f8f9fa;
          border-radius: 4px;
          padding: 0.5rem;
          margin-bottom: 0.5rem;

          .log-entry {
            display: flex;
            gap: 1rem;
            padding: 0.3rem 0;
            border-bottom: 1px solid #eee;
            font-size: 0.85rem;

            &:last-child {
              border-bottom: none;
            }

            .log-time {
              color: #666;
              min-width: 80px;
              flex-shrink: 0;
            }

            .log-message {
              color: #333;
              line-height: 1.3;
            }
          }
        }

        .clear-btn {
          padding: 0.4rem 0.8rem;
          background: #dc3545;
          color: white;
          border: none;
          border-radius: 3px;
          cursor: pointer;
          font-size: 0.85rem;

          &:hover {
            background: #c82333;
          }
        }
      }
    }
  }

  // 状态样式
  .status-success {
    color: #27ae60;
    font-weight: 500;
  }

  .status-warning {
    color: #f39c12;
    font-weight: 500;
  }

  .status-error {
    color: #e74c3c;
    font-weight: 500;
  }

  // 原理说明部分
  .principle-explanation {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.1rem;
    }

    .principle-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;

      .principle-item {
        h5 {
          margin: 0 0 0.5rem 0;
          color: #444;
          font-size: 1rem;
        }

        ul {
          margin: 0;
          padding-left: 1.5rem;

          li {
            color: #666;
            line-height: 1.5;
            margin-bottom: 0.5rem;

            strong {
              color: #333;
            }
          }
        }
      }
    }
  }
}
</style>
