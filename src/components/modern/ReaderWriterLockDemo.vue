<template>
  <div class="reader-writer-lock-demo">
    <!-- 模式信息说明 -->
    <div class="pattern-info">
      <p class="description">
        读写锁模式（Reader-Writer Lock
        Pattern）是一种并发控制机制，允许多个读者同时访问共享资源，
        但写者需要独占访问。这种模式在读多写少的场景下能显著提升性能，避免了传统互斥锁的读者间不必要的阻塞。
        读写锁维护读者优先或写者优先的策略，确保数据一致性的同时最大化并发性能。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>缓存系统（读多写少）</li>
          <li>配置管理（频繁读取，偶尔更新）</li>
          <li>数据库索引（查询频繁，更新较少）</li>
          <li>共享数据结构</li>
          <li>文件系统访问</li>
          <li>统计信息收集</li>
        </ul>
      </div>
    </div>

    <!-- 读写锁架构图 -->
    <div class="architecture-section">
      <h4>🏗️ 读写锁架构图</h4>
      <div class="architecture-diagram">
        <div class="lock-container">
          <!-- 共享资源 -->
          <div class="shared-resource">
            <h5>共享资源</h5>
            <div class="resource-content">
              <div class="data-display">
                <strong>当前数据:</strong> {{ sharedData }}
              </div>
              <div class="lock-status">
                <strong>锁状态:</strong>
                <span :class="lockStatusClass">{{ lockStatus }}</span>
              </div>
              <div class="access-count">
                <strong>活跃读者:</strong> {{ activeReaders }}
                <strong>等待写者:</strong> {{ waitingWriters }}
              </div>
            </div>
          </div>

          <!-- 读者队列 -->
          <div class="readers-section">
            <h5>读者队列</h5>
            <div class="readers-list">
              <div
                v-for="reader in readers"
                :key="reader.id"
                class="reader-item"
                :class="{
                  active: reader.status === 'reading',
                  waiting: reader.status === 'waiting',
                  completed: reader.status === 'completed',
                }"
              >
                <div class="reader-id">R{{ reader.id }}</div>
                <div class="reader-status">
                  {{ getStatusText(reader.status) }}
                </div>
                <div v-if="reader.readData" class="read-data">
                  {{ reader.readData }}
                </div>
              </div>
            </div>
          </div>

          <!-- 写者队列 -->
          <div class="writers-section">
            <h5>写者队列</h5>
            <div class="writers-list">
              <div
                v-for="writer in writers"
                :key="writer.id"
                class="writer-item"
                :class="{
                  active: writer.status === 'writing',
                  waiting: writer.status === 'waiting',
                  completed: writer.status === 'completed',
                }"
              >
                <div class="writer-id">W{{ writer.id }}</div>
                <div class="writer-status">
                  {{ getStatusText(writer.status) }}
                </div>
                <div v-if="writer.newData" class="write-data">
                  → {{ writer.newData }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 交互式演示 -->
    <div class="demo-section">
      <h4>交互式演示：</h4>

      <!-- 锁配置 -->
      <div class="demo-subsection">
        <h5>1. 读写锁配置</h5>
        <div class="demo-controls">
          <div class="input-group">
            <label>优先策略：</label>
            <select
              v-model="lockPolicy"
              class="demo-select"
              @change="updateLockPolicy"
            >
              <option value="reader-priority">读者优先</option>
              <option value="writer-priority">写者优先</option>
              <option value="fair">公平策略</option>
            </select>
          </div>
          <div class="input-group">
            <label>最大并发读者：</label>
            <input
              v-model.number="maxReaders"
              type="number"
              min="1"
              max="10"
              class="demo-input"
            />
          </div>
          <button @click="resetLock" class="demo-btn">重置锁</button>
        </div>
      </div>

      <!-- 操作控制 -->
      <div class="demo-subsection">
        <h5>2. 读写操作</h5>
        <div class="demo-controls">
          <div class="input-group">
            <label>读取延迟（ms）：</label>
            <input
              v-model.number="readDelay"
              type="number"
              min="500"
              max="5000"
              class="demo-input"
            />
          </div>
          <div class="input-group">
            <label>写入延迟（ms）：</label>
            <input
              v-model.number="writeDelay"
              type="number"
              min="1000"
              max="8000"
              class="demo-input"
            />
          </div>
          <button @click="addReader" class="demo-btn">添加读者</button>
          <button @click="addWriter" class="demo-btn">添加写者</button>
          <button @click="addBatchReaders" class="demo-btn">
            批量添加读者
          </button>
        </div>

        <div class="demo-output">
          <div class="stats">
            <div class="stat-item">
              <strong>总读取次数:</strong> {{ totalReads }}
            </div>
            <div class="stat-item">
              <strong>总写入次数:</strong> {{ totalWrites }}
            </div>
            <div class="stat-item">
              <strong>平均读取时间:</strong> {{ averageReadTime }}ms
            </div>
            <div class="stat-item">
              <strong>平均写入时间:</strong> {{ averageWriteTime }}ms
            </div>
          </div>
        </div>
      </div>

      <!-- 性能对比 -->
      <div class="demo-subsection">
        <h5>3. 性能对比演示</h5>
        <div class="demo-controls">
          <button
            @click="runPerformanceTest"
            class="demo-btn"
            :disabled="isRunningTest"
          >
            {{ isRunningTest ? "测试中..." : "运行性能测试" }}
          </button>
          <button @click="clearResults" class="demo-btn">清空结果</button>
        </div>

        <div class="demo-output">
          <div v-if="performanceResults.length > 0" class="performance-results">
            <h6>性能测试结果：</h6>
            <div
              class="test-result"
              v-for="result in performanceResults"
              :key="result.id"
            >
              <div class="test-name">{{ result.name }}</div>
              <div class="test-metrics">
                <span>总时间: {{ result.totalTime }}ms</span>
                <span>读取吞吐量: {{ result.readThroughput }} 次/秒</span>
                <span>写入吞吐量: {{ result.writeThroughput }} 次/秒</span>
                <span>并发效率: {{ result.concurrencyEfficiency }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作日志 -->
      <div class="demo-subsection">
        <h5>4. 操作日志</h5>
        <div class="operation-log">
          <div
            v-for="log in operationLogs.slice(-10)"
            :key="log.id"
            class="log-entry"
            :class="log.type"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 优缺点分析 -->
    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 允许多个读者并发访问，提高读取性能</li>
        <li>✅ 保证写操作的独占性，确保数据一致性</li>
        <li>✅ 在读多写少的场景下性能优异</li>
        <li>✅ 支持不同的优先策略（读者优先、写者优先、公平）</li>
        <li>✅ 减少读者间的不必要阻塞</li>
        <li>✅ 提供更细粒度的并发控制</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 实现复杂度高于简单互斥锁</li>
        <li>❌ 可能出现读者饥饿或写者饥饿问题</li>
        <li>❌ 在写操作频繁时性能可能不如互斥锁</li>
        <li>❌ 需要额外的内存开销维护读者计数</li>
        <li>❌ 调试和排错相对困难</li>
      </ul>

      <h4>优先策略：</h4>
      <ul>
        <li>
          🔍 <strong>读者优先:</strong> 只要有读者在读取，新的读者可以立即获得锁
        </li>
        <li>🔍 <strong>写者优先:</strong> 一旦有写者等待，新的读者必须等待</li>
        <li>🔍 <strong>公平策略:</strong> 按照请求顺序分配锁，避免饥饿</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { ReaderWriterLock, LockPolicy } from "@/patterns/ReaderWriterLock";

// 响应式数据
const lockPolicy = ref<string>("reader-priority");
const maxReaders = ref<number>(5);
const readDelay = ref<number>(2000);
const writeDelay = ref<number>(3000);
const isRunningTest = ref<boolean>(false);

// 读写锁实例
const rwLock = ref<ReaderWriterLock | null>(null);

// 状态数据
const sharedData = ref<string>("初始数据");
const readers = ref<
  Array<{
    id: number;
    status: "waiting" | "reading" | "completed";
    readData?: string;
  }>
>([]);
const writers = ref<
  Array<{
    id: number;
    status: "waiting" | "writing" | "completed";
    newData?: string;
  }>
>([]);

const operationLogs = ref<
  Array<{
    id: string;
    timestamp: Date;
    message: string;
    type: "read" | "write" | "lock" | "unlock";
  }>
>([]);

const performanceResults = ref<
  Array<{
    id: string;
    name: string;
    totalTime: number;
    readThroughput: number;
    writeThroughput: number;
    concurrencyEfficiency: number;
  }>
>([]);

// 统计数据
const totalReads = ref<number>(0);
const totalWrites = ref<number>(0);
const readTimes = ref<number[]>([]);
const writeTimes = ref<number[]>([]);

// 计算属性
const lockStatus = computed(() => {
  if (!rwLock.value) return "未初始化";
  const status = rwLock.value.getStatus();
  if (status.activeReaders > 0) return `读取中 (${status.activeReaders}个读者)`;
  if (status.isWriting) return "写入中";
  return "空闲";
});

const lockStatusClass = computed(() => {
  const status = lockStatus.value;
  if (status.includes("读取中")) return "status-reading";
  if (status === "写入中") return "status-writing";
  return "status-idle";
});

const activeReaders = computed(() => {
  return rwLock.value?.getStatus().activeReaders || 0;
});

const waitingWriters = computed(() => {
  return rwLock.value?.getStatus().waitingWriters || 0;
});

const averageReadTime = computed(() => {
  if (readTimes.value.length === 0) return 0;
  const total = readTimes.value.reduce((sum, time) => sum + time, 0);
  return Math.round(total / readTimes.value.length);
});

const averageWriteTime = computed(() => {
  if (writeTimes.value.length === 0) return 0;
  const total = writeTimes.value.reduce((sum, time) => sum + time, 0);
  return Math.round(total / writeTimes.value.length);
});

// 方法
const updateLockPolicy = () => {
  if (rwLock.value) {
    const policy = lockPolicy.value as LockPolicy;
    rwLock.value.setPolicy(policy);
    addLog("lock", `切换到${getPolicyName(policy)}策略`);
  }
};

const getPolicyName = (policy: LockPolicy): string => {
  const names = {
    "reader-priority": "读者优先",
    "writer-priority": "写者优先",
    fair: "公平策略",
  };
  return names[policy] || policy;
};

const resetLock = () => {
  if (rwLock.value) {
    rwLock.value.reset();
  }

  readers.value = [];
  writers.value = [];
  operationLogs.value = [];
  totalReads.value = 0;
  totalWrites.value = 0;
  readTimes.value = [];
  writeTimes.value = [];
  sharedData.value = "初始数据";

  addLog("lock", "读写锁已重置");
};

const addReader = async () => {
  if (!rwLock.value) return;

  const readerId = Date.now();
  const reader = {
    id: readerId,
    status: "waiting" as const,
  };

  readers.value.push(reader);
  addLog("read", `读者 R${readerId} 请求读取`);

  try {
    const startTime = Date.now();
    reader.status = "waiting";

    await rwLock.value.readLock();
    reader.status = "reading";
    addLog("read", `读者 R${readerId} 开始读取`);

    // 模拟读取操作
    await new Promise((resolve) => setTimeout(resolve, readDelay.value));
    reader.readData = sharedData.value;

    const endTime = Date.now();
    const duration = endTime - startTime;
    readTimes.value.push(duration);
    totalReads.value++;

    rwLock.value.readUnlock();
    reader.status = "completed";
    addLog("read", `读者 R${readerId} 完成读取: ${reader.readData}`);
  } catch (error) {
    addLog("read", `读者 R${readerId} 读取失败: ${error}`);
  }
};

const addWriter = async () => {
  if (!rwLock.value) return;

  const writerId = Date.now();
  const newData = `数据-${writerId}`;
  const writer = {
    id: writerId,
    status: "waiting" as const,
    newData,
  };

  writers.value.push(writer);
  addLog("write", `写者 W${writerId} 请求写入`);

  try {
    const startTime = Date.now();
    writer.status = "waiting";

    await rwLock.value.writeLock();
    writer.status = "writing";
    addLog("write", `写者 W${writerId} 开始写入`);

    // 模拟写入操作
    await new Promise((resolve) => setTimeout(resolve, writeDelay.value));
    sharedData.value = newData;

    const endTime = Date.now();
    const duration = endTime - startTime;
    writeTimes.value.push(duration);
    totalWrites.value++;

    rwLock.value.writeUnlock();
    writer.status = "completed";
    addLog("write", `写者 W${writerId} 完成写入: ${newData}`);
  } catch (error) {
    addLog("write", `写者 W${writerId} 写入失败: ${error}`);
  }
};

const addBatchReaders = () => {
  for (let i = 0; i < 3; i++) {
    setTimeout(() => addReader(), i * 200);
  }
};

const runPerformanceTest = async () => {
  if (isRunningTest.value) return;

  isRunningTest.value = true;
  addLog("lock", "开始性能测试");

  // 测试不同策略的性能
  const policies: LockPolicy[] = ["reader-priority", "writer-priority", "fair"];

  for (const policy of policies) {
    resetLock();
    lockPolicy.value = policy;
    updateLockPolicy();

    const startTime = Date.now();
    const readPromises: Promise<void>[] = [];
    const writePromises: Promise<void>[] = [];

    // 创建多个读者和写者
    for (let i = 0; i < 10; i++) {
      readPromises.push(addReader());
    }

    for (let i = 0; i < 3; i++) {
      writePromises.push(addWriter());
    }

    // 等待所有操作完成
    await Promise.all([...readPromises, ...writePromises]);

    const totalTime = Date.now() - startTime;
    const readThroughput = Math.round((10 / totalTime) * 1000);
    const writeThroughput = Math.round((3 / totalTime) * 1000);
    const concurrencyEfficiency = Math.round(
      (13 / (totalTime / 1000) / 13) * 100
    );

    performanceResults.value.push({
      id: `test-${policy}-${Date.now()}`,
      name: `${getPolicyName(policy)}策略`,
      totalTime,
      readThroughput,
      writeThroughput,
      concurrencyEfficiency,
    });
  }

  isRunningTest.value = false;
  addLog("lock", "性能测试完成");
};

const clearResults = () => {
  performanceResults.value = [];
  operationLogs.value = [];
};

const getStatusText = (status: string): string => {
  const statusMap = {
    waiting: "等待中",
    reading: "读取中",
    writing: "写入中",
    completed: "已完成",
  };
  return statusMap[status as keyof typeof statusMap] || status;
};

const addLog = (
  type: "read" | "write" | "lock" | "unlock",
  message: string
) => {
  operationLogs.value.push({
    id: `log-${Date.now()}-${Math.random()}`,
    timestamp: new Date(),
    message,
    type,
  });
};

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString();
};

// 生命周期
onMounted(() => {
  const policy = lockPolicy.value as LockPolicy;
  rwLock.value = new ReaderWriterLock(policy, maxReaders.value);
  addLog("lock", "读写锁初始化完成");
});

onUnmounted(() => {
  if (rwLock.value) {
    rwLock.value.reset();
  }
});
</script>

<style lang="scss" scoped>
.reader-writer-lock-demo {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .pattern-info {
    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #1890ff;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .architecture-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .architecture-diagram {
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .lock-container {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr;
        gap: 2rem;
        align-items: start;

        .shared-resource,
        .readers-section,
        .writers-section {
          background: white;
          padding: 1.5rem;
          border-radius: 8px;
          border: 2px solid #1890ff;

          h5 {
            margin: 0 0 1rem 0;
            color: #1890ff;
            font-weight: 600;
            text-align: center;
          }
        }

        .shared-resource {
          .resource-content {
            .data-display {
              background: #e3f2fd;
              padding: 1rem;
              border-radius: 6px;
              margin-bottom: 1rem;
              font-family: "Monaco", "Consolas", monospace;
              color: #1565c0;
            }

            .lock-status {
              margin-bottom: 0.8rem;

              .status-idle {
                color: #4caf50;
                font-weight: 600;
              }

              .status-reading {
                color: #2196f3;
                font-weight: 600;
              }

              .status-writing {
                color: #ff9800;
                font-weight: 600;
              }
            }

            .access-count {
              display: flex;
              justify-content: space-between;
              font-size: 0.9rem;
              color: #666;

              strong {
                color: #333;
              }
            }
          }
        }

        .readers-section,
        .writers-section {
          .readers-list,
          .writers-list {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;

            .reader-item,
            .writer-item {
              background: #f5f5f5;
              padding: 0.8rem;
              border-radius: 6px;
              border: 2px solid transparent;
              transition: all 0.3s ease;

              &.waiting {
                border-color: #ffc107;
                background: #fff8e1;
              }

              &.active {
                border-color: #2196f3;
                background: #e3f2fd;
                animation: working 2s infinite;
              }

              &.completed {
                border-color: #4caf50;
                background: #e8f5e9;
              }

              .reader-id,
              .writer-id {
                font-weight: 600;
                color: #333;
                margin-bottom: 0.3rem;
              }

              .reader-status,
              .writer-status {
                font-size: 0.8rem;
                color: #666;
                margin-bottom: 0.3rem;
              }

              .read-data,
              .write-data {
                font-size: 0.7rem;
                color: #1565c0;
                background: #e3f2fd;
                padding: 0.2rem 0.4rem;
                border-radius: 3px;
                font-family: "Monaco", "Consolas", monospace;
              }
            }
          }
        }
      }
    }
  }

  .demo-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .demo-subsection {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      margin-bottom: 1.5rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #1890ff;
        font-weight: 600;
      }

      h6 {
        margin: 0 0 0.8rem 0;
        color: #333;
        font-weight: 600;
      }

      .demo-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1.5rem;
        align-items: end;

        .input-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          label {
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
          }
        }

        .demo-input,
        .demo-select {
          padding: 0.6rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
          min-width: 120px;

          &:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .demo-btn {
          padding: 0.6rem 1.2rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #1890ff, #722ed1);
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          font-size: 0.9rem;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }

      .demo-output {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .stats {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 1rem;

          .stat-item {
            text-align: center;
            padding: 0.8rem;
            background: #f8f9fa;
            border-radius: 6px;
            font-size: 0.9rem;

            strong {
              display: block;
              color: #333;
              margin-bottom: 0.3rem;
            }
          }
        }

        .performance-results {
          .test-result {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            border-left: 4px solid #1890ff;

            .test-name {
              font-weight: 600;
              color: #333;
              margin-bottom: 0.5rem;
            }

            .test-metrics {
              display: flex;
              gap: 1rem;
              flex-wrap: wrap;

              span {
                background: white;
                padding: 0.3rem 0.6rem;
                border-radius: 4px;
                font-size: 0.8rem;
                color: #666;
              }
            }
          }
        }
      }

      .operation-log {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        max-height: 300px;
        overflow-y: auto;

        .log-entry {
          display: flex;
          gap: 1rem;
          padding: 0.5rem;
          border-radius: 4px;
          margin-bottom: 0.5rem;
          font-size: 0.85rem;

          &.read {
            background: #e3f2fd;
            border-left: 3px solid #2196f3;
          }

          &.write {
            background: #fff3e0;
            border-left: 3px solid #ff9800;
          }

          &.lock {
            background: #f3e5f5;
            border-left: 3px solid #9c27b0;
          }

          &.unlock {
            background: #e8f5e9;
            border-left: 3px solid #4caf50;
          }

          .log-time {
            color: #666;
            font-family: "Monaco", "Consolas", monospace;
            min-width: 80px;
          }

          .log-message {
            color: #333;
            flex: 1;
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}

@keyframes working {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@media (max-width: 768px) {
  .reader-writer-lock-demo {
    .architecture-section {
      .architecture-diagram {
        .lock-container {
          grid-template-columns: 1fr;
          gap: 1rem;
        }
      }
    }

    .demo-section {
      .demo-subsection {
        .demo-controls {
          flex-direction: column;
          align-items: stretch;

          .demo-input,
          .demo-select {
            min-width: auto;
          }
        }

        .demo-output {
          .stats {
            grid-template-columns: 1fr;
          }

          .performance-results {
            .test-result {
              .test-metrics {
                flex-direction: column;
                gap: 0.5rem;
              }
            }
          }
        }
      }
    }
  }
}
</style>
