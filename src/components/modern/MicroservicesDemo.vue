<template>
  <div class="microservices-demo">
    <!-- 模式信息说明 -->
    <div class="pattern-info">
      <p class="description">
        微服务模式（Microservices
        Pattern）是一种架构风格，将单一应用程序开发为一套小型服务，
        每个服务运行在自己的进程中，并使用轻量级机制（通常是HTTP API）进行通信。
        这些服务围绕业务功能构建，可以由全自动部署机制独立部署。
        微服务架构提供了更好的可扩展性、技术多样性和团队自主性。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>大型分布式系统</li>
          <li>云原生应用开发</li>
          <li>高并发Web应用</li>
          <li>多团队协作开发</li>
          <li>需要独立扩展的业务模块</li>
          <li>技术栈多样化的项目</li>
        </ul>
      </div>
    </div>

    <!-- 微服务架构图 -->
    <div class="architecture-section">
      <h4>🏗️ 微服务架构图</h4>
      <div class="architecture-diagram">
        <div class="microservices-container">
          <!-- API网关 -->
          <div class="api-gateway">
            <h5>API网关</h5>
            <div class="gateway-content">
              <div class="gateway-features">
                <div class="feature-item">路由</div>
                <div class="feature-item">认证</div>
                <div class="feature-item">限流</div>
                <div class="feature-item">监控</div>
              </div>
              <div class="request-count">
                <strong>请求数:</strong> {{ gatewayStats.totalRequests }}
              </div>
            </div>
          </div>

          <!-- 微服务集群 -->
          <div class="services-cluster">
            <div
              v-for="service in services"
              :key="service.id"
              class="microservice"
              :class="{
                active: service.status === 'running',
                error: service.status === 'error',
                loading: service.status === 'starting',
              }"
            >
              <div class="service-header">
                <h6>{{ service.name }}</h6>
                <div class="service-status" :class="service.status">
                  {{ getStatusText(service.status) }}
                </div>
              </div>
              <div class="service-info">
                <div class="info-item">
                  <strong>端口:</strong> {{ service.port }}
                </div>
                <div class="info-item">
                  <strong>实例:</strong> {{ service.instances }}
                </div>
                <div class="info-item">
                  <strong>CPU:</strong> {{ service.cpu }}%
                </div>
                <div class="info-item">
                  <strong>内存:</strong> {{ service.memory }}MB
                </div>
              </div>
              <div class="service-endpoints">
                <div
                  v-for="endpoint in service.endpoints"
                  :key="endpoint"
                  class="endpoint"
                >
                  {{ endpoint }}
                </div>
              </div>
            </div>
          </div>

          <!-- 服务发现 -->
          <div class="service-discovery">
            <h5>服务发现</h5>
            <div class="discovery-content">
              <div class="registered-services">
                <h6>已注册服务</h6>
                <div
                  v-for="service in services.filter(
                    (s) => s.status === 'running'
                  )"
                  :key="service.id"
                  class="registered-service"
                >
                  <span class="service-name">{{ service.name }}</span>
                  <span class="service-address">{{ service.address }}</span>
                </div>
              </div>
              <div class="health-checks">
                <h6>健康检查</h6>
                <div class="health-status">
                  <span class="healthy">健康: {{ healthyServices }}</span>
                  <span class="unhealthy">异常: {{ unhealthyServices }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 消息总线 -->
          <div class="message-bus">
            <h5>消息总线</h5>
            <div class="bus-content">
              <div class="message-flow">
                <div
                  v-for="message in recentMessages"
                  :key="message.id"
                  class="message-item"
                  :class="{ flowing: message.flowing }"
                >
                  <div class="message-type">{{ message.type }}</div>
                  <div class="message-from">{{ message.from }}</div>
                  <div class="message-to">{{ message.to }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 交互式演示 -->
    <div class="demo-section">
      <h4>交互式演示：</h4>

      <!-- 服务管理演示 -->
      <div class="demo-subsection">
        <h5>1. 服务管理</h5>
        <div class="demo-controls">
          <div class="service-controls">
            <button @click="startService('user-service')" class="demo-btn">
              启动用户服务
            </button>
            <button @click="startService('order-service')" class="demo-btn">
              启动订单服务
            </button>
            <button @click="startService('payment-service')" class="demo-btn">
              启动支付服务
            </button>
            <button
              @click="startService('notification-service')"
              class="demo-btn"
            >
              启动通知服务
            </button>
          </div>
          <div class="service-controls">
            <button
              @click="stopService('user-service')"
              class="demo-btn secondary"
            >
              停止用户服务
            </button>
            <button
              @click="stopService('order-service')"
              class="demo-btn secondary"
            >
              停止订单服务
            </button>
            <button @click="restartAllServices" class="demo-btn">
              重启所有服务
            </button>
            <button @click="scaleService" class="demo-btn">扩容服务</button>
          </div>
        </div>

        <div class="demo-output">
          <div class="service-logs">
            <h6>服务日志</h6>
            <div class="log-container">
              <div
                v-for="log in serviceLogs.slice(-10)"
                :key="log.id"
                class="log-entry"
                :class="log.level"
              >
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span class="log-service">{{ log.service }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- API调用演示 -->
      <div class="demo-subsection">
        <h5>2. API调用和服务通信</h5>
        <div class="demo-controls">
          <div class="api-controls">
            <div class="input-group">
              <label>API端点：</label>
              <select v-model="selectedEndpoint" class="demo-select">
                <option value="">选择API端点</option>
                <option
                  v-for="endpoint in availableEndpoints"
                  :key="endpoint.path"
                  :value="endpoint.path"
                >
                  {{ endpoint.service }} - {{ endpoint.path }}
                </option>
              </select>
              <button
                @click="callAPI"
                class="demo-btn"
                :disabled="!selectedEndpoint"
              >
                调用API
              </button>
            </div>
          </div>
          <div class="load-test-controls">
            <button
              @click="runLoadTest"
              class="demo-btn"
              :disabled="isLoadTesting"
            >
              {{ isLoadTesting ? "负载测试中..." : "运行负载测试" }}
            </button>
            <button @click="simulateFailure" class="demo-btn">
              模拟服务故障
            </button>
            <button @click="enableCircuitBreaker" class="demo-btn">
              启用熔断器
            </button>
          </div>
        </div>

        <div class="demo-output">
          <div class="api-results">
            <div class="result-tabs">
              <button
                v-for="tab in apiTabs"
                :key="tab.id"
                @click="activeApiTab = tab.id"
                class="tab-btn"
                :class="{ active: activeApiTab === tab.id }"
              >
                {{ tab.name }}
              </button>
            </div>
            <div class="tab-content">
              <div v-if="activeApiTab === 'responses'" class="api-responses">
                <h6>API响应</h6>
                <div
                  v-for="response in apiResponses.slice(-5)"
                  :key="response.id"
                  class="response-item"
                  :class="response.status >= 400 ? 'error' : 'success'"
                >
                  <span class="response-method">{{ response.method }}</span>
                  <span class="response-path">{{ response.path }}</span>
                  <span class="response-status">{{ response.status }}</span>
                  <span class="response-time"
                    >{{ response.responseTime }}ms</span
                  >
                </div>
              </div>

              <div v-if="activeApiTab === 'metrics'" class="api-metrics">
                <h6>性能指标</h6>
                <div class="metrics-grid">
                  <div class="metric-card">
                    <div class="metric-value">
                      {{ performanceMetrics.totalRequests }}
                    </div>
                    <div class="metric-label">总请求数</div>
                  </div>
                  <div class="metric-card">
                    <div class="metric-value">
                      {{ performanceMetrics.averageResponseTime }}ms
                    </div>
                    <div class="metric-label">平均响应时间</div>
                  </div>
                  <div class="metric-card">
                    <div class="metric-value">
                      {{ performanceMetrics.errorRate }}%
                    </div>
                    <div class="metric-label">错误率</div>
                  </div>
                  <div class="metric-card">
                    <div class="metric-value">
                      {{ performanceMetrics.throughput }}
                    </div>
                    <div class="metric-label">吞吐量 (req/s)</div>
                  </div>
                </div>
              </div>

              <div
                v-if="activeApiTab === 'tracing'"
                class="distributed-tracing"
              >
                <h6>分布式追踪</h6>
                <div class="trace-timeline">
                  <div
                    v-for="trace in distributedTraces"
                    :key="trace.id"
                    class="trace-item"
                  >
                    <div class="trace-header">
                      <span class="trace-id">{{ trace.traceId }}</span>
                      <span class="trace-duration">{{ trace.duration }}ms</span>
                    </div>
                    <div class="trace-spans">
                      <div
                        v-for="span in trace.spans"
                        :key="span.id"
                        class="span-item"
                        :style="{
                          width: (span.duration / trace.duration) * 100 + '%',
                        }"
                      >
                        <span class="span-service">{{ span.service }}</span>
                        <span class="span-operation">{{ span.operation }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 服务发现和负载均衡演示 -->
      <div class="demo-subsection">
        <h5>3. 服务发现和负载均衡</h5>
        <div class="demo-controls">
          <button @click="registerService" class="demo-btn">注册新服务</button>
          <button @click="deregisterService" class="demo-btn">注销服务</button>
          <button @click="updateLoadBalancer" class="demo-btn">
            更新负载均衡
          </button>
          <button @click="showServiceTopology" class="demo-btn">
            显示服务拓扑
          </button>
        </div>

        <div class="demo-output">
          <div class="service-topology">
            <h6>服务拓扑图</h6>
            <div class="topology-container">
              <div class="topology-node gateway-node">
                <div class="node-label">API Gateway</div>
                <div class="node-connections">
                  <div
                    v-for="service in runningServices"
                    :key="service.id"
                    class="connection-line"
                    :class="{ active: service.receiving }"
                  ></div>
                </div>
              </div>
              <div class="topology-services">
                <div
                  v-for="service in runningServices"
                  :key="service.id"
                  class="topology-node service-node"
                  :class="{ active: service.receiving }"
                >
                  <div class="node-label">{{ service.name }}</div>
                  <div class="node-load">负载: {{ service.load }}%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 监控和告警演示 -->
      <div class="demo-subsection">
        <h5>4. 监控和告警</h5>
        <div class="demo-controls">
          <button @click="generateMetrics" class="demo-btn">
            生成监控数据
          </button>
          <button @click="triggerAlert" class="demo-btn">触发告警</button>
          <button @click="showDashboard" class="demo-btn">显示监控面板</button>
          <button @click="exportLogs" class="demo-btn">导出日志</button>
        </div>

        <div class="demo-output">
          <div class="monitoring-dashboard">
            <div class="dashboard-metrics">
              <div class="metric-chart">
                <h6>系统资源使用率</h6>
                <div class="chart-container">
                  <div class="chart-bar">
                    <div
                      class="bar-fill cpu"
                      :style="{ height: systemMetrics.cpu + '%' }"
                    ></div>
                    <div class="bar-label">CPU</div>
                  </div>
                  <div class="chart-bar">
                    <div
                      class="bar-fill memory"
                      :style="{ height: systemMetrics.memory + '%' }"
                    ></div>
                    <div class="bar-label">内存</div>
                  </div>
                  <div class="chart-bar">
                    <div
                      class="bar-fill disk"
                      :style="{ height: systemMetrics.disk + '%' }"
                    ></div>
                    <div class="bar-label">磁盘</div>
                  </div>
                  <div class="chart-bar">
                    <div
                      class="bar-fill network"
                      :style="{ height: systemMetrics.network + '%' }"
                    ></div>
                    <div class="bar-label">网络</div>
                  </div>
                </div>
              </div>

              <div class="alerts-panel">
                <h6>告警信息</h6>
                <div class="alerts-list">
                  <div
                    v-for="alert in alerts"
                    :key="alert.id"
                    class="alert-item"
                    :class="alert.severity"
                  >
                    <div class="alert-icon">⚠️</div>
                    <div class="alert-content">
                      <div class="alert-title">{{ alert.title }}</div>
                      <div class="alert-message">{{ alert.message }}</div>
                      <div class="alert-time">
                        {{ formatTime(alert.timestamp) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优缺点分析 -->
    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 独立部署和扩展，提高系统灵活性</li>
        <li>✅ 技术栈多样化，团队可以选择最适合的技术</li>
        <li>✅ 故障隔离，单个服务故障不影响整个系统</li>
        <li>✅ 团队自主性，不同团队可以独立开发和维护</li>
        <li>✅ 更好的可测试性和可维护性</li>
        <li>✅ 支持持续集成和持续部署</li>
        <li>✅ 业务边界清晰，符合领域驱动设计</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 分布式系统复杂性，网络延迟和故障处理</li>
        <li>❌ 数据一致性挑战，需要处理分布式事务</li>
        <li>❌ 服务间通信开销，性能可能不如单体应用</li>
        <li>❌ 运维复杂度增加，需要更多的监控和管理工具</li>
        <li>❌ 测试复杂度提高，需要集成测试和端到端测试</li>
        <li>❌ 初期开发成本较高</li>
        <li>❌ 服务拆分边界难以确定</li>
      </ul>

      <h4>核心概念：</h4>
      <ul>
        <li>🔍 <strong>服务自治:</strong> 每个微服务独立开发、部署和运行</li>
        <li>🔍 <strong>API网关:</strong> 统一入口，处理路由、认证、限流等</li>
        <li>🔍 <strong>服务发现:</strong> 动态发现和注册服务实例</li>
        <li>🔍 <strong>负载均衡:</strong> 在多个服务实例间分配请求</li>
        <li>🔍 <strong>熔断器:</strong> 防止级联故障的保护机制</li>
        <li>🔍 <strong>分布式追踪:</strong> 跟踪请求在多个服务间的流转</li>
        <li>🔍 <strong>配置中心:</strong> 集中管理各服务的配置信息</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  MicroserviceSystem,
  ServiceRegistry,
  APIGateway,
  LoadBalancer,
  Service,
} from "@/patterns/Microservices";

// 响应式数据
const selectedEndpoint = ref<string>("");
const activeApiTab = ref<string>("responses");
const isLoadTesting = ref<boolean>(false);

// 微服务系统组件
const microserviceSystem = ref<MicroserviceSystem | null>(null);

// 服务数据
const services = ref<Service[]>([]);

const serviceLogs = ref<
  Array<{
    id: string;
    timestamp: Date;
    service: string;
    level: "info" | "warn" | "error";
    message: string;
  }>
>([]);

const apiResponses = ref<
  Array<{
    id: string;
    method: string;
    path: string;
    status: number;
    responseTime: number;
    timestamp: Date;
  }>
>([]);

const recentMessages = ref<
  Array<{
    id: string;
    type: string;
    from: string;
    to: string;
    flowing: boolean;
  }>
>([]);

const distributedTraces = ref<
  Array<{
    id: string;
    traceId: string;
    duration: number;
    spans: Array<{
      id: string;
      service: string;
      operation: string;
      duration: number;
    }>;
  }>
>([]);

const alerts = ref<
  Array<{
    id: string;
    title: string;
    message: string;
    severity: "low" | "medium" | "high" | "critical";
    timestamp: Date;
  }>
>([]);

// 统计数据
const gatewayStats = ref<{
  totalRequests: number;
}>({
  totalRequests: 0,
});

const performanceMetrics = ref<{
  totalRequests: number;
  averageResponseTime: number;
  errorRate: number;
  throughput: number;
}>({
  totalRequests: 0,
  averageResponseTime: 0,
  errorRate: 0,
  throughput: 0,
});

const systemMetrics = ref<{
  cpu: number;
  memory: number;
  disk: number;
  network: number;
}>({
  cpu: 0,
  memory: 0,
  disk: 0,
  network: 0,
});

// 计算属性
const healthyServices = computed(() => {
  return services.value.filter((s) => s.status === "running").length;
});

const unhealthyServices = computed(() => {
  return services.value.filter((s) => s.status === "error").length;
});

const runningServices = computed(() => {
  return services.value.filter((s) => s.status === "running");
});

const availableEndpoints = computed(() => {
  const endpoints = [];
  for (const service of runningServices.value) {
    for (const endpoint of service.endpoints) {
      endpoints.push({
        service: service.name,
        path: endpoint,
      });
    }
  }
  return endpoints;
});

const apiTabs = ref([
  { id: "responses", name: "API响应" },
  { id: "metrics", name: "性能指标" },
  { id: "tracing", name: "分布式追踪" },
]);

// 方法
const startService = async (serviceId: string) => {
  const service = services.value.find((s) => s.id === serviceId);
  if (service && microserviceSystem.value) {
    service.status = "starting";

    addServiceLog("info", service.name, `正在启动服务...`);

    // 模拟启动延迟
    setTimeout(() => {
      service.status = "running";
      addServiceLog("info", service.name, `服务启动成功`);

      // 注册到服务发现
      microserviceSystem.value?.registerService(service);
    }, 2000);
  }
};

const stopService = async (serviceId: string) => {
  const service = services.value.find((s) => s.id === serviceId);
  if (service && microserviceSystem.value) {
    service.status = "stopped";
    addServiceLog("warn", service.name, `服务已停止`);

    // 从服务发现注销
    microserviceSystem.value?.deregisterService(serviceId);
  }
};

const restartAllServices = async () => {
  for (const service of services.value) {
    if (service.status === "running") {
      await stopService(service.id);
      setTimeout(() => startService(service.id), 1000);
    }
  }
  addServiceLog("info", "System", "正在重启所有服务...");
};

const scaleService = () => {
  const service = services.value.find((s) => s.id === "user-service");
  if (service) {
    service.instances += 1;
    addServiceLog("info", service.name, `扩容到 ${service.instances} 个实例`);
  }
};

const callAPI = async () => {
  if (!selectedEndpoint.value || !microserviceSystem.value) return;

  const startTime = Date.now();

  try {
    // 模拟API调用
    const response = await microserviceSystem.value.callAPI(
      "GET",
      selectedEndpoint.value
    );
    const responseTime = Date.now() - startTime;

    apiResponses.value.push({
      id: generateId(),
      method: "GET",
      path: selectedEndpoint.value,
      status: response.status,
      responseTime,
      timestamp: new Date(),
    });

    gatewayStats.value.totalRequests++;
    updatePerformanceMetrics();

    // 添加分布式追踪
    addDistributedTrace(selectedEndpoint.value, responseTime);
  } catch (error) {
    apiResponses.value.push({
      id: generateId(),
      method: "GET",
      path: selectedEndpoint.value,
      status: 500,
      responseTime: Date.now() - startTime,
      timestamp: new Date(),
    });
  }
};

const runLoadTest = async () => {
  isLoadTesting.value = true;
  addServiceLog("info", "LoadTest", "开始负载测试...");

  try {
    // 模拟负载测试
    for (let i = 0; i < 50; i++) {
      await new Promise((resolve) => setTimeout(resolve, 100));
      await callAPI();
    }

    addServiceLog("info", "LoadTest", "负载测试完成");
  } finally {
    isLoadTesting.value = false;
  }
};

const simulateFailure = () => {
  const service = services.value.find((s) => s.status === "running");
  if (service) {
    service.status = "error";
    addServiceLog("error", service.name, "服务发生故障");

    // 触发告警
    alerts.value.push({
      id: generateId(),
      title: "服务故障",
      message: `${service.name} 服务不可用`,
      severity: "critical",
      timestamp: new Date(),
    });
  }
};

const enableCircuitBreaker = () => {
  addServiceLog("info", "CircuitBreaker", "熔断器已启用");

  alerts.value.push({
    id: generateId(),
    title: "熔断器启用",
    message: "检测到服务异常，熔断器已启用保护",
    severity: "medium",
    timestamp: new Date(),
  });
};

const registerService = () => {
  const newService: Service = {
    id: `service-${Date.now()}`,
    name: `动态服务-${Math.floor(Math.random() * 100)}`,
    address: `http://localhost:${8000 + Math.floor(Math.random() * 1000)}`,
    port: 8000 + Math.floor(Math.random() * 1000),
    status: "running",
    instances: 1,
    cpu: Math.floor(Math.random() * 50),
    memory: Math.floor(Math.random() * 200) + 100,
    endpoints: ["/health", "/metrics"],
    load: Math.floor(Math.random() * 80),
    receiving: false,
  };

  services.value.push(newService);
  addServiceLog("info", "ServiceRegistry", `注册新服务: ${newService.name}`);
};

const deregisterService = () => {
  const dynamicServices = services.value.filter((s) =>
    s.name.includes("动态服务")
  );
  if (dynamicServices.length > 0) {
    const service = dynamicServices[0];
    const index = services.value.indexOf(service);
    services.value.splice(index, 1);
    addServiceLog("info", "ServiceRegistry", `注销服务: ${service.name}`);
  }
};

const updateLoadBalancer = () => {
  // 更新负载均衡权重
  for (const service of runningServices.value) {
    service.load = Math.floor(Math.random() * 100);
  }
  addServiceLog("info", "LoadBalancer", "负载均衡配置已更新");
};

const showServiceTopology = () => {
  // 模拟服务间通信
  for (const service of runningServices.value) {
    service.receiving = true;
    setTimeout(() => {
      service.receiving = false;
    }, 2000);
  }
};

const generateMetrics = () => {
  systemMetrics.value = {
    cpu: Math.floor(Math.random() * 80) + 10,
    memory: Math.floor(Math.random() * 70) + 20,
    disk: Math.floor(Math.random() * 60) + 15,
    network: Math.floor(Math.random() * 90) + 5,
  };

  addServiceLog("info", "Metrics", "监控数据已更新");
};

const triggerAlert = () => {
  const alertTypes = [
    {
      title: "CPU使用率过高",
      message: "系统CPU使用率超过80%",
      severity: "high" as const,
    },
    {
      title: "内存不足",
      message: "可用内存低于20%",
      severity: "critical" as const,
    },
    {
      title: "磁盘空间不足",
      message: "磁盘使用率超过90%",
      severity: "medium" as const,
    },
    {
      title: "网络延迟异常",
      message: "网络响应时间超过阈值",
      severity: "low" as const,
    },
  ];

  const alert = alertTypes[Math.floor(Math.random() * alertTypes.length)];
  alerts.value.push({
    id: generateId(),
    ...alert,
    timestamp: new Date(),
  });
};

const showDashboard = () => {
  generateMetrics();
  addServiceLog("info", "Dashboard", "监控面板已刷新");
};

const exportLogs = () => {
  const logs = serviceLogs.value.map((log) => ({
    timestamp: log.timestamp.toISOString(),
    service: log.service,
    level: log.level,
    message: log.message,
  }));

  console.log("导出的日志数据:", logs);
  addServiceLog("info", "Export", "日志数据已导出到控制台");
};

const addServiceLog = (
  level: "info" | "warn" | "error",
  service: string,
  message: string
) => {
  serviceLogs.value.push({
    id: generateId(),
    timestamp: new Date(),
    service,
    level,
    message,
  });

  // 保持日志数量在合理范围内
  if (serviceLogs.value.length > 100) {
    serviceLogs.value = serviceLogs.value.slice(-50);
  }
};

const addDistributedTrace = (endpoint: string, totalDuration: number) => {
  const spans = [
    {
      id: generateId(),
      service: "api-gateway",
      operation: "route",
      duration: Math.floor(totalDuration * 0.1),
    },
    {
      id: generateId(),
      service: "user-service",
      operation: "authenticate",
      duration: Math.floor(totalDuration * 0.3),
    },
    {
      id: generateId(),
      service: "order-service",
      operation: "process",
      duration: Math.floor(totalDuration * 0.4),
    },
    {
      id: generateId(),
      service: "payment-service",
      operation: "charge",
      duration: Math.floor(totalDuration * 0.2),
    },
  ];

  distributedTraces.value.push({
    id: generateId(),
    traceId: `trace-${Date.now()}`,
    duration: totalDuration,
    spans,
  });

  // 保持追踪数量在合理范围内
  if (distributedTraces.value.length > 20) {
    distributedTraces.value = distributedTraces.value.slice(-10);
  }
};

const updatePerformanceMetrics = () => {
  const responses = apiResponses.value;
  if (responses.length === 0) return;

  const totalTime = responses.reduce((sum, r) => sum + r.responseTime, 0);
  const errorCount = responses.filter((r) => r.status >= 400).length;

  performanceMetrics.value = {
    totalRequests: responses.length,
    averageResponseTime: Math.round(totalTime / responses.length),
    errorRate: Math.round((errorCount / responses.length) * 100),
    throughput: Math.round(responses.length / 60), // 假设1分钟内的请求
  };
};

const getStatusText = (status: string): string => {
  const statusMap = {
    running: "运行中",
    stopped: "已停止",
    starting: "启动中",
    error: "故障",
  };
  return statusMap[status as keyof typeof statusMap] || status;
};

const generateId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

const formatTime = (date: Date | undefined): string => {
  if (!date) return "N/A";
  return date.toLocaleTimeString();
};

// 生命周期
onMounted(() => {
  // 初始化微服务系统
  microserviceSystem.value = new MicroserviceSystem();

  // 初始化服务列表
  services.value = [
    {
      id: "user-service",
      name: "用户服务",
      address: "http://localhost:8001",
      port: 8001,
      status: "stopped",
      instances: 2,
      cpu: 25,
      memory: 128,
      endpoints: ["/users", "/auth", "/profile"],
      load: 45,
      receiving: false,
    },
    {
      id: "order-service",
      name: "订单服务",
      address: "http://localhost:8002",
      port: 8002,
      status: "stopped",
      instances: 3,
      cpu: 35,
      memory: 256,
      endpoints: ["/orders", "/cart", "/checkout"],
      load: 60,
      receiving: false,
    },
    {
      id: "payment-service",
      name: "支付服务",
      address: "http://localhost:8003",
      port: 8003,
      status: "stopped",
      instances: 2,
      cpu: 20,
      memory: 192,
      endpoints: ["/payments", "/refunds", "/billing"],
      load: 30,
      receiving: false,
    },
    {
      id: "notification-service",
      name: "通知服务",
      address: "http://localhost:8004",
      port: 8004,
      status: "stopped",
      instances: 1,
      cpu: 15,
      memory: 96,
      endpoints: ["/notifications", "/email", "/sms"],
      load: 20,
      receiving: false,
    },
  ];

  // 初始化一些示例数据
  addServiceLog("info", "System", "微服务系统初始化完成");
  generateMetrics();

  // 模拟一些初始API调用
  setTimeout(() => {
    selectedEndpoint.value = "/users";
    callAPI();
  }, 2000);
});
</script>

<style lang="scss" scoped>
.microservices-demo {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .pattern-info {
    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #1890ff;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .architecture-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .architecture-diagram {
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .microservices-container {
        display: grid;
        grid-template-columns: 1fr 3fr 1fr 1fr;
        gap: 2rem;
        align-items: start;

        .api-gateway,
        .service-discovery,
        .message-bus {
          background: white;
          padding: 1.5rem;
          border-radius: 8px;
          border: 2px solid;
          min-height: 300px;

          h5 {
            margin: 0 0 1rem 0;
            font-weight: 600;
            text-align: center;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
          }
        }

        .api-gateway {
          border-color: #1890ff;

          h5 {
            color: #1890ff;
          }

          .gateway-content {
            .gateway-features {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 0.5rem;
              margin-bottom: 1rem;

              .feature-item {
                background: #e6f7ff;
                padding: 0.5rem;
                border-radius: 4px;
                text-align: center;
                font-size: 0.8rem;
                color: #1890ff;
                font-weight: 600;
              }
            }

            .request-count {
              background: #f0f0f0;
              padding: 0.8rem;
              border-radius: 6px;
              text-align: center;
              font-size: 0.9rem;

              strong {
                color: #1890ff;
              }
            }
          }
        }

        .services-cluster {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1rem;

          .microservice {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 2px solid #d9d9d9;
            transition: all 0.3s ease;

            &.active {
              border-color: #52c41a;
              box-shadow: 0 2px 8px rgba(82, 196, 26, 0.2);
            }

            &.error {
              border-color: #ff4d4f;
              box-shadow: 0 2px 8px rgba(255, 77, 79, 0.2);
            }

            &.loading {
              border-color: #fa8c16;
              animation: pulse 2s infinite;
            }

            .service-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 0.8rem;

              h6 {
                margin: 0;
                color: #333;
                font-size: 0.9rem;
              }

              .service-status {
                padding: 0.2rem 0.6rem;
                border-radius: 4px;
                font-size: 0.7rem;
                font-weight: 600;

                &.running {
                  background: #f6ffed;
                  color: #52c41a;
                }

                &.stopped {
                  background: #f5f5f5;
                  color: #999;
                }

                &.starting {
                  background: #fff7e6;
                  color: #fa8c16;
                }

                &.error {
                  background: #fff2f0;
                  color: #ff4d4f;
                }
              }
            }

            .service-info {
              margin-bottom: 0.8rem;

              .info-item {
                display: flex;
                justify-content: space-between;
                padding: 0.2rem 0;
                font-size: 0.8rem;
                color: #666;

                strong {
                  color: #333;
                }
              }
            }

            .service-endpoints {
              .endpoint {
                background: #f8f9fa;
                padding: 0.3rem 0.6rem;
                border-radius: 4px;
                margin-bottom: 0.3rem;
                font-size: 0.7rem;
                color: #666;
                font-family: "Monaco", "Consolas", monospace;
              }
            }
          }
        }

        .service-discovery {
          border-color: #722ed1;

          h5 {
            color: #722ed1;
          }

          .discovery-content {
            .registered-services,
            .health-checks {
              margin-bottom: 1rem;

              h6 {
                margin: 0 0 0.5rem 0;
                color: #333;
                font-size: 0.8rem;
              }

              .registered-service {
                display: flex;
                justify-content: space-between;
                padding: 0.4rem;
                margin-bottom: 0.3rem;
                background: #f9f0ff;
                border-radius: 4px;
                font-size: 0.7rem;

                .service-name {
                  font-weight: 600;
                  color: #722ed1;
                }

                .service-address {
                  color: #666;
                  font-family: "Monaco", "Consolas", monospace;
                }
              }

              .health-status {
                display: flex;
                justify-content: space-between;
                padding: 0.5rem;
                background: #f9f0ff;
                border-radius: 4px;
                font-size: 0.8rem;

                .healthy {
                  color: #52c41a;
                  font-weight: 600;
                }

                .unhealthy {
                  color: #ff4d4f;
                  font-weight: 600;
                }
              }
            }
          }
        }

        .message-bus {
          border-color: #fa8c16;

          h5 {
            color: #fa8c16;
          }

          .bus-content {
            .message-flow {
              max-height: 250px;
              overflow-y: auto;

              .message-item {
                background: #fff7e6;
                border: 1px solid #ffd591;
                border-radius: 6px;
                padding: 0.6rem;
                margin-bottom: 0.5rem;
                transition: all 0.3s ease;

                &.flowing {
                  background: #fa8c16;
                  color: white;
                  animation: messageFlow 2s ease-in-out;
                }

                .message-type {
                  font-weight: 600;
                  font-size: 0.8rem;
                  margin-bottom: 0.3rem;
                }

                .message-from,
                .message-to {
                  font-size: 0.7rem;
                  opacity: 0.8;
                }
              }
            }
          }
        }
      }
    }
  }

  .demo-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .demo-subsection {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      margin-bottom: 1.5rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #1890ff;
        font-weight: 600;
      }

      h6 {
        margin: 0 0 0.8rem 0;
        color: #333;
        font-weight: 600;
      }

      .demo-controls {
        margin-bottom: 1.5rem;

        .service-controls,
        .api-controls,
        .load-test-controls {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
          margin-bottom: 1rem;

          .input-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            label {
              font-weight: 500;
              color: #333;
              font-size: 0.9rem;
              white-space: nowrap;
            }
          }
        }

        .demo-select {
          padding: 0.6rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
          min-width: 200px;

          &:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .demo-btn {
          padding: 0.6rem 1.2rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #1890ff, #722ed1);
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          font-size: 0.9rem;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }

          &.secondary {
            background: linear-gradient(135deg, #ff4d4f, #fa8c16);
          }
        }
      }

      .demo-output {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .service-logs {
          .log-container {
            max-height: 300px;
            overflow-y: auto;
            background: #1f1f1f;
            padding: 1rem;
            border-radius: 6px;
            font-family: "Monaco", "Consolas", monospace;

            .log-entry {
              display: flex;
              gap: 1rem;
              padding: 0.3rem 0;
              font-size: 0.8rem;
              border-bottom: 1px solid #333;

              &:last-child {
                border-bottom: none;
              }

              &.info {
                color: #52c41a;
              }

              &.warn {
                color: #fa8c16;
              }

              &.error {
                color: #ff4d4f;
              }

              .log-time {
                color: #666;
                min-width: 80px;
              }

              .log-service {
                color: #1890ff;
                min-width: 120px;
                font-weight: 600;
              }

              .log-message {
                flex: 1;
              }
            }
          }
        }

        .api-results {
          .result-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            border-bottom: 1px solid #e9ecef;

            .tab-btn {
              padding: 0.6rem 1.2rem;
              border: none;
              background: transparent;
              color: #666;
              cursor: pointer;
              border-bottom: 2px solid transparent;
              transition: all 0.3s ease;

              &.active {
                color: #1890ff;
                border-bottom-color: #1890ff;
              }

              &:hover {
                color: #1890ff;
              }
            }
          }

          .tab-content {
            .api-responses {
              .response-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.8rem;
                margin-bottom: 0.5rem;
                border-radius: 6px;
                font-size: 0.9rem;

                &.success {
                  background: #f6ffed;
                  border-left: 4px solid #52c41a;
                }

                &.error {
                  background: #fff2f0;
                  border-left: 4px solid #ff4d4f;
                }

                .response-method {
                  background: #1890ff;
                  color: white;
                  padding: 0.2rem 0.6rem;
                  border-radius: 4px;
                  font-size: 0.8rem;
                  font-weight: 600;
                }

                .response-path {
                  font-family: "Monaco", "Consolas", monospace;
                  color: #333;
                }

                .response-status {
                  font-weight: 600;
                  color: #1890ff;
                }

                .response-time {
                  color: #666;
                  font-size: 0.8rem;
                }
              }
            }

            .api-metrics {
              .metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;

                .metric-card {
                  background: #f8f9fa;
                  padding: 1.5rem;
                  border-radius: 8px;
                  text-align: center;
                  border-left: 4px solid #1890ff;

                  .metric-value {
                    font-size: 2rem;
                    font-weight: 600;
                    color: #1890ff;
                    margin-bottom: 0.5rem;
                  }

                  .metric-label {
                    color: #666;
                    font-size: 0.9rem;
                  }
                }
              }
            }

            .distributed-tracing {
              .trace-timeline {
                .trace-item {
                  background: #f8f9fa;
                  padding: 1rem;
                  border-radius: 6px;
                  margin-bottom: 1rem;

                  .trace-header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 0.8rem;

                    .trace-id {
                      font-family: "Monaco", "Consolas", monospace;
                      color: #1890ff;
                      font-weight: 600;
                    }

                    .trace-duration {
                      color: #666;
                      font-size: 0.9rem;
                    }
                  }

                  .trace-spans {
                    .span-item {
                      background: linear-gradient(90deg, #1890ff, #722ed1);
                      color: white;
                      padding: 0.5rem;
                      margin-bottom: 0.3rem;
                      border-radius: 4px;
                      display: flex;
                      justify-content: space-between;
                      font-size: 0.8rem;

                      .span-service {
                        font-weight: 600;
                      }

                      .span-operation {
                        opacity: 0.8;
                      }
                    }
                  }
                }
              }
            }
          }
        }

        .service-topology {
          .topology-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2rem;

            .topology-node {
              background: #f8f9fa;
              padding: 1rem;
              border-radius: 8px;
              border: 2px solid #d9d9d9;
              text-align: center;
              transition: all 0.3s ease;

              &.gateway-node {
                border-color: #1890ff;

                .node-label {
                  color: #1890ff;
                  font-weight: 600;
                }
              }

              &.service-node {
                border-color: #52c41a;

                &.active {
                  border-color: #fa8c16;
                  background: #fff7e6;
                  animation: pulse 2s infinite;
                }

                .node-label {
                  color: #52c41a;
                  font-weight: 600;
                  margin-bottom: 0.3rem;
                }

                .node-load {
                  font-size: 0.8rem;
                  color: #666;
                }
              }
            }

            .topology-services {
              display: flex;
              gap: 1rem;
              flex-wrap: wrap;
              justify-content: center;
            }
          }
        }

        .monitoring-dashboard {
          .dashboard-metrics {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;

            .metric-chart {
              .chart-container {
                display: flex;
                justify-content: space-around;
                align-items: end;
                height: 200px;
                background: #f8f9fa;
                padding: 1rem;
                border-radius: 6px;

                .chart-bar {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  height: 100%;

                  .bar-fill {
                    width: 40px;
                    border-radius: 4px 4px 0 0;
                    transition: height 0.3s ease;
                    margin-bottom: 0.5rem;

                    &.cpu {
                      background: linear-gradient(180deg, #ff4d4f, #fa8c16);
                    }

                    &.memory {
                      background: linear-gradient(180deg, #1890ff, #722ed1);
                    }

                    &.disk {
                      background: linear-gradient(180deg, #52c41a, #13c2c2);
                    }

                    &.network {
                      background: linear-gradient(180deg, #fa8c16, #fadb14);
                    }
                  }

                  .bar-label {
                    font-size: 0.8rem;
                    color: #666;
                    font-weight: 600;
                  }
                }
              }
            }

            .alerts-panel {
              .alerts-list {
                max-height: 200px;
                overflow-y: auto;

                .alert-item {
                  display: flex;
                  gap: 0.8rem;
                  padding: 0.8rem;
                  margin-bottom: 0.5rem;
                  border-radius: 6px;
                  border-left: 4px solid;

                  &.low {
                    background: #f6ffed;
                    border-left-color: #52c41a;
                  }

                  &.medium {
                    background: #fff7e6;
                    border-left-color: #fa8c16;
                  }

                  &.high {
                    background: #fff2f0;
                    border-left-color: #ff4d4f;
                  }

                  &.critical {
                    background: #fff0f6;
                    border-left-color: #eb2f96;
                  }

                  .alert-icon {
                    font-size: 1.2rem;
                  }

                  .alert-content {
                    flex: 1;

                    .alert-title {
                      font-weight: 600;
                      color: #333;
                      margin-bottom: 0.3rem;
                    }

                    .alert-message {
                      color: #666;
                      font-size: 0.9rem;
                      margin-bottom: 0.3rem;
                    }

                    .alert-time {
                      color: #999;
                      font-size: 0.8rem;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes messageFlow {
  0% {
    transform: translateY(-10px);
    opacity: 0;
  }
  50% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(10px);
    opacity: 0;
  }
}

@media (max-width: 768px) {
  .microservices-demo {
    .architecture-section {
      .architecture-diagram {
        .microservices-container {
          grid-template-columns: 1fr;

          .services-cluster {
            grid-template-columns: 1fr;
          }
        }
      }
    }

    .demo-section {
      .demo-subsection {
        .demo-controls {
          .service-controls,
          .api-controls,
          .load-test-controls {
            flex-direction: column;
            align-items: stretch;

            .input-group {
              flex-direction: column;
              align-items: stretch;
              gap: 0.3rem;
            }
          }
        }

        .demo-output {
          .dashboard-metrics {
            grid-template-columns: 1fr;
          }

          .topology-services {
            flex-direction: column;
            align-items: center;
          }

          .metrics-grid {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
}
</style>
