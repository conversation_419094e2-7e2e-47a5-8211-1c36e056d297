<template>
  <div class="future-promise-demo">
    <div class="demo-header">
      <h3>Future/Promise模式演示</h3>
      <p>模拟HTTP请求处理，展示Promise的串行、并行和错误处理</p>
    </div>

    <div class="demo-controls">
      <button @click="runSerialDemo" :disabled="isLoading" class="control-btn">
        串行执行示例
      </button>
      <button @click="runParallelDemo" :disabled="isLoading" class="control-btn">
        并行执行示例
      </button>
      <button @click="runErrorHandlingDemo" :disabled="isLoading" class="control-btn">
        错误处理示例
      </button>
      <button @click="clearResults" class="control-btn clear">清空结果</button>
    </div>

    <div class="demo-content">
      <div class="request-status" v-if="isLoading">
        <div class="loading-indicator">
          <div class="spinner"></div>
          <span>{{ loadingMessage }}</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progress + '%' }"></div>
        </div>
      </div>

      <div class="results-container">
        <h4>执行结果</h4>
        <div class="results" ref="resultsContainer">
          <div 
            v-for="result in results" 
            :key="result.id"
            :class="['result-item', result.type]"
          >
            <div class="result-header">
              <span class="result-time">{{ result.time }}</span>
              <span :class="['result-status', result.status]">
                {{ result.status.toUpperCase() }}
              </span>
            </div>
            <div class="result-content">{{ result.message }}</div>
            <div v-if="result.data" class="result-data">
              <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';

interface Result {
  id: string;
  type: 'info' | 'success' | 'error';
  status: 'pending' | 'success' | 'error';
  time: string;
  message: string;
  data?: any;
}

const isLoading = ref(false);
const loadingMessage = ref('');
const progress = ref(0);
const results = ref<Result[]>([]);
const resultsContainer = ref<HTMLElement>();

let resultId = 0;

const addResult = (type: Result['type'], status: Result['status'], message: string, data?: any) => {
  const result: Result = {
    id: (++resultId).toString(),
    type,
    status,
    time: new Date().toLocaleTimeString(),
    message,
    data
  };
  
  results.value.push(result);
  
  // 自动滚动到底部
  nextTick(() => {
    if (resultsContainer.value) {
      resultsContainer.value.scrollTop = resultsContainer.value.scrollHeight;
    }
  });
};

// 模拟API请求
const mockApiRequest = (endpoint: string, delay: number = 1000, shouldFail: boolean = false): Promise<any> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (shouldFail) {
        reject(new Error(`请求失败: ${endpoint}`));
      } else {
        const mockData = {
          '/users/1': { id: 1, name: 'Alice', email: '<EMAIL>' },
          '/users/2': { id: 2, name: 'Bob', email: '<EMAIL>' },
          '/users/3': { id: 3, name: 'Charlie', email: '<EMAIL>' },
          '/orders/1': [
            { id: 101, amount: 299.99, status: 'completed' },
            { id: 102, amount: 159.99, status: 'pending' }
          ]
        };
        resolve({
          data: mockData[endpoint as keyof typeof mockData] || { message: 'Success' },
          status: 200
        });
      }
    }, delay);
  });
};

const updateProgress = (current: number, total: number) => {
  progress.value = Math.round((current / total) * 100);
};

const runSerialDemo = async () => {
  isLoading.value = true;
  loadingMessage.value = '串行执行中...';
  progress.value = 0;
  
  addResult('info', 'pending', '开始串行执行示例');
  
  try {
    // 步骤1: 获取用户信息
    updateProgress(1, 3);
    addResult('info', 'pending', '步骤1: 获取用户信息...');
    const userResponse = await mockApiRequest('/users/1', 1500);
    addResult('success', 'success', '用户信息获取成功', userResponse.data);
    
    // 步骤2: 获取订单信息
    updateProgress(2, 3);
    addResult('info', 'pending', '步骤2: 获取订单信息...');
    const ordersResponse = await mockApiRequest('/orders/1', 1200);
    addResult('success', 'success', '订单信息获取成功', ordersResponse.data);
    
    // 步骤3: 处理结果
    updateProgress(3, 3);
    addResult('info', 'pending', '步骤3: 处理结果...');
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const totalAmount = ordersResponse.data.reduce((sum: number, order: any) => sum + order.amount, 0);
    addResult('success', 'success', `串行执行完成！用户 ${userResponse.data.name} 的总消费: $${totalAmount.toFixed(2)}`);
    
  } catch (error) {
    addResult('error', 'error', `串行执行失败: ${error}`);
  } finally {
    isLoading.value = false;
    progress.value = 0;
  }
};

const runParallelDemo = async () => {
  isLoading.value = true;
  loadingMessage.value = '并行执行中...';
  progress.value = 0;
  
  addResult('info', 'pending', '开始并行执行示例');
  
  try {
    updateProgress(1, 2);
    addResult('info', 'pending', '同时发起多个请求...');
    
    // 并行执行多个请求
    const promises = [
      mockApiRequest('/users/1', 1200),
      mockApiRequest('/users/2', 1500),
      mockApiRequest('/users/3', 1000)
    ];
    
    const responses = await Promise.all(promises);
    updateProgress(2, 2);
    
    addResult('success', 'success', '所有请求完成！', {
      users: responses.map(r => r.data),
      totalTime: '约1.5秒（并行执行）'
    });
    
  } catch (error) {
    addResult('error', 'error', `并行执行失败: ${error}`);
  } finally {
    isLoading.value = false;
    progress.value = 0;
  }
};

const runErrorHandlingDemo = async () => {
  isLoading.value = true;
  loadingMessage.value = '错误处理演示中...';
  progress.value = 0;
  
  addResult('info', 'pending', '开始错误处理示例');
  
  try {
    updateProgress(1, 3);
    
    // 使用Promise.allSettled处理部分失败的情况
    const promises = [
      mockApiRequest('/users/1', 1000, false),
      mockApiRequest('/users/999', 1200, true), // 这个会失败
      mockApiRequest('/users/2', 800, false)
    ];
    
    addResult('info', 'pending', '发起请求（其中一个会失败）...');
    const results = await Promise.allSettled(promises);
    
    updateProgress(2, 3);
    
    const successful = results.filter(r => r.status === 'fulfilled');
    const failed = results.filter(r => r.status === 'rejected');
    
    updateProgress(3, 3);
    
    addResult('success', 'success', `错误处理完成！成功: ${successful.length}, 失败: ${failed.length}`, {
      successful: successful.map(r => (r as PromiseFulfilledResult<any>).value.data),
      failed: failed.map(r => (r as PromiseRejectedResult).reason.message)
    });
    
  } catch (error) {
    addResult('error', 'error', `错误处理示例失败: ${error}`);
  } finally {
    isLoading.value = false;
    progress.value = 0;
  }
};

const clearResults = () => {
  results.value = [];
  progress.value = 0;
  addResult('info', 'success', '结果已清空');
};
</script>

<style lang="scss" scoped>
.future-promise-demo {
  padding: 1.5rem;
  
  .demo-header {
    margin-bottom: 1.5rem;
    
    h3 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.3rem;
    }
    
    p {
      margin: 0;
      color: #666;
      line-height: 1.5;
    }
  }
  
  .demo-controls {
    display: flex;
    gap: 0.8rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    
    .control-btn {
      padding: 0.6rem 1rem;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      font-size: 0.85rem;
      transition: all 0.3s ease;
      background: #1890ff;
      color: white;
      
      &:hover:not(:disabled) {
        background: #096dd9;
      }
      
      &:disabled {
        background: #d9d9d9;
        cursor: not-allowed;
      }
      
      &.clear {
        background: #ff4d4f;
        
        &:hover {
          background: #cf1322;
        }
      }
    }
  }
  
  .request-status {
    background: #f0f8ff;
    border: 1px solid #d6e4ff;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    
    .loading-indicator {
      display: flex;
      align-items: center;
      gap: 0.8rem;
      margin-bottom: 0.8rem;
      
      .spinner {
        width: 20px;
        height: 20px;
        border: 2px solid #e0e0e0;
        border-top: 2px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      span {
        color: #1890ff;
        font-weight: 500;
      }
    }
    
    .progress-bar {
      height: 6px;
      background: #e0e0e0;
      border-radius: 3px;
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background: #1890ff;
        transition: width 0.3s ease;
      }
    }
  }
  
  .results-container {
    h4 {
      margin: 0 0 0.8rem 0;
      color: #333;
    }
    
    .results {
      background: #fafafa;
      border-radius: 8px;
      padding: 1rem;
      height: 400px;
      overflow-y: auto;
      
      .result-item {
        background: white;
        border-radius: 6px;
        padding: 0.8rem;
        margin-bottom: 0.8rem;
        border-left: 4px solid #d9d9d9;
        
        &.success {
          border-left-color: #52c41a;
        }
        
        &.error {
          border-left-color: #ff4d4f;
        }
        
        &.info {
          border-left-color: #1890ff;
        }
        
        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.5rem;
          
          .result-time {
            font-size: 0.8rem;
            color: #888;
          }
          
          .result-status {
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-weight: 600;
            
            &.success {
              background: #f6ffed;
              color: #52c41a;
            }
            
            &.error {
              background: #fff2f0;
              color: #ff4d4f;
            }
            
            &.pending {
              background: #e6f7ff;
              color: #1890ff;
            }
          }
        }
        
        .result-content {
          color: #333;
          line-height: 1.4;
          margin-bottom: 0.5rem;
        }
        
        .result-data {
          background: #f5f5f5;
          border-radius: 4px;
          padding: 0.5rem;
          
          pre {
            margin: 0;
            font-size: 0.8rem;
            color: #555;
            white-space: pre-wrap;
            word-break: break-word;
          }
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
