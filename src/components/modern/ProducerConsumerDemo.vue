<template>
  <div class="producer-consumer-demo">
    <div class="demo-header">
      <h3>生产者-消费者模式演示</h3>
      <p>模拟日志处理系统，生产者产生日志消息，消费者处理这些消息</p>
    </div>

    <div class="demo-controls">
      <button 
        @click="toggleSystem" 
        :class="['control-btn', isRunning ? 'stop' : 'start']"
      >
        {{ isRunning ? '停止系统' : '启动系统' }}
      </button>
      <button @click="clearLogs" class="control-btn clear">清空日志</button>
    </div>

    <div class="demo-content">
      <div class="queue-status">
        <h4>队列状态</h4>
        <div class="status-info">
          <span class="status-item">队列大小: {{ queueSize }}</span>
          <span class="status-item">最大容量: 10</span>
          <div class="queue-bar">
            <div 
              class="queue-fill" 
              :style="{ width: (queueSize / 10) * 100 + '%' }"
            ></div>
          </div>
        </div>
      </div>

      <div class="logs-container">
        <h4>系统日志</h4>
        <div class="logs" ref="logsContainer">
          <div 
            v-for="log in logs" 
            :key="log.id"
            :class="['log-item', log.type]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, nextTick } from 'vue';

interface LogMessage {
  id: string;
  type: 'producer' | 'consumer' | 'system';
  time: string;
  message: string;
}

const isRunning = ref(false);
const queueSize = ref(0);
const logs = ref<LogMessage[]>([]);
const logsContainer = ref<HTMLElement>();

let producerInterval: number | null = null;
let consumerInterval: number | null = null;
let logId = 0;

const addLog = (type: LogMessage['type'], message: string) => {
  const log: LogMessage = {
    id: (++logId).toString(),
    type,
    time: new Date().toLocaleTimeString(),
    message
  };
  
  logs.value.push(log);
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value.shift();
  }
  
  // 自动滚动到底部
  nextTick(() => {
    if (logsContainer.value) {
      logsContainer.value.scrollTop = logsContainer.value.scrollHeight;
    }
  });
};

const startProducer = () => {
  producerInterval = setInterval(() => {
    if (queueSize.value < 10) {
      queueSize.value++;
      const messageTypes = ['用户登录', '数据更新', '系统警告', '错误报告'];
      const messageType = messageTypes[Math.floor(Math.random() * messageTypes.length)];
      addLog('producer', `生产者: 产生消息 "${messageType}" (队列: ${queueSize.value})`);
    } else {
      addLog('producer', '生产者: 队列已满，等待消费者处理...');
    }
  }, 1500);
};

const startConsumer = () => {
  consumerInterval = setInterval(() => {
    if (queueSize.value > 0) {
      queueSize.value--;
      const processingTime = Math.floor(Math.random() * 2000) + 1000;
      addLog('consumer', `消费者: 开始处理消息 (队列: ${queueSize.value})`);
      
      setTimeout(() => {
        addLog('consumer', `消费者: 消息处理完成 (耗时: ${processingTime}ms)`);
      }, processingTime);
    }
  }, 2000);
};

const toggleSystem = () => {
  if (isRunning.value) {
    // 停止系统
    if (producerInterval) {
      clearInterval(producerInterval);
      producerInterval = null;
    }
    if (consumerInterval) {
      clearInterval(consumerInterval);
      consumerInterval = null;
    }
    addLog('system', '系统已停止');
  } else {
    // 启动系统
    addLog('system', '系统启动中...');
    startProducer();
    startConsumer();
  }
  isRunning.value = !isRunning.value;
};

const clearLogs = () => {
  logs.value = [];
  queueSize.value = 0;
  addLog('system', '日志已清空，队列已重置');
};

onUnmounted(() => {
  if (producerInterval) clearInterval(producerInterval);
  if (consumerInterval) clearInterval(consumerInterval);
});
</script>

<style lang="scss" scoped>
.producer-consumer-demo {
  padding: 1.5rem;
  
  .demo-header {
    margin-bottom: 1.5rem;
    
    h3 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.3rem;
    }
    
    p {
      margin: 0;
      color: #666;
      line-height: 1.5;
    }
  }
  
  .demo-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    
    .control-btn {
      padding: 0.6rem 1.2rem;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &.start {
        background: #52c41a;
        color: white;
        
        &:hover {
          background: #389e0d;
        }
      }
      
      &.stop {
        background: #ff4d4f;
        color: white;
        
        &:hover {
          background: #cf1322;
        }
      }
      
      &.clear {
        background: #1890ff;
        color: white;
        
        &:hover {
          background: #096dd9;
        }
      }
    }
  }
  
  .demo-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .queue-status {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    
    h4 {
      margin: 0 0 0.8rem 0;
      color: #333;
    }
    
    .status-info {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      
      .status-item {
        font-size: 0.9rem;
        color: #555;
      }
      
      .queue-bar {
        height: 8px;
        background: #e0e0e0;
        border-radius: 4px;
        overflow: hidden;
        
        .queue-fill {
          height: 100%;
          background: linear-gradient(90deg, #52c41a, #1890ff);
          transition: width 0.3s ease;
        }
      }
    }
  }
  
  .logs-container {
    h4 {
      margin: 0 0 0.8rem 0;
      color: #333;
    }
    
    .logs {
      background: #1e1e1e;
      border-radius: 8px;
      padding: 1rem;
      height: 300px;
      overflow-y: auto;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 0.85rem;
      
      .log-item {
        display: flex;
        margin-bottom: 0.5rem;
        padding: 0.3rem 0;
        
        .log-time {
          color: #888;
          margin-right: 1rem;
          min-width: 80px;
        }
        
        .log-message {
          flex: 1;
        }
        
        &.producer .log-message {
          color: #52c41a;
        }
        
        &.consumer .log-message {
          color: #1890ff;
        }
        
        &.system .log-message {
          color: #faad14;
        }
      }
    }
  }
}
</style>
