<template>
  <div class="monad-demo">
    <!-- 模式信息说明 -->
    <div class="pattern-info">
      <p class="description">
        单子模式（Monad Pattern）是函数式编程中的高级抽象，它扩展了函子的概念，
        提供了flatMap（或bind）操作来处理嵌套的计算上下文。单子遵循三个法则：左单位元、右单位元和结合律，
        能够优雅地处理副作用、异步操作、错误处理等复杂场景，是函数式编程的核心概念之一。
      </p>

      <div class="use-cases">
        <h4>常见使用场景：</h4>
        <ul>
          <li>异步操作链式调用</li>
          <li>错误处理和异常传播</li>
          <li>可选值的安全操作</li>
          <li>状态管理和计算</li>
          <li>I/O操作的抽象</li>
          <li>解析器组合子</li>
        </ul>
      </div>
    </div>

    <!-- 单子架构图 -->
    <div class="architecture-section">
      <h4>🏗️ 单子架构图</h4>
      <div class="architecture-diagram">
        <div class="monad-container">
          <div class="monad-box">
            <h5>Maybe单子</h5>
            <div class="monad-content">
              <div class="value-box">{{ maybeValue.toString() }}</div>
              <div class="operation-arrow">↓ flatMap(f)</div>
              <div class="result-box">{{ flatMappedMaybe.toString() }}</div>
            </div>
          </div>

          <div class="monad-box">
            <h5>IO单子</h5>
            <div class="monad-content">
              <div class="value-box">IO(() => {{ ioValue }})</div>
              <div class="operation-arrow">↓ flatMap(f)</div>
              <div class="result-box">{{ ioResult }}</div>
            </div>
          </div>

          <div class="monad-box">
            <h5>State单子</h5>
            <div class="monad-content">
              <div class="value-box">State: {{ currentState }}</div>
              <div class="operation-arrow">↓ flatMap(f)</div>
              <div class="result-box">New State: {{ newState }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 交互式演示 -->
    <div class="demo-section">
      <h4>交互式演示：</h4>

      <!-- Maybe单子演示 -->
      <div class="demo-subsection">
        <h5>1. Maybe单子 - 安全的链式操作</h5>
        <div class="demo-controls">
          <div class="input-group">
            <label>输入值：</label>
            <input
              v-model="inputValue"
              type="text"
              placeholder="输入一个数字"
              class="demo-input"
            />
          </div>
          <div class="function-group">
            <label>选择操作链：</label>
            <select v-model="selectedChain" class="demo-select">
              <option value="safe-math">安全数学运算</option>
              <option value="string-ops">字符串操作</option>
              <option value="validation">数据验证</option>
              <option value="nested">嵌套操作</option>
            </select>
          </div>
          <button @click="runMaybeChain" class="demo-btn">执行链式操作</button>
        </div>

        <div class="demo-output">
          <div class="chain-steps">
            <h6>操作步骤：</h6>
            <div
              v-for="(step, index) in maybeSteps"
              :key="index"
              class="step-item"
            >
              <span class="step-number">{{ index + 1 }}.</span>
              <span class="step-description">{{ step.description }}</span>
              <span class="step-result">{{ step.result }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- IO单子演示 -->
      <div class="demo-subsection">
        <h5>2. IO单子 - 副作用管理</h5>
        <div class="demo-controls">
          <div class="input-group">
            <label>文件名：</label>
            <input
              v-model="fileName"
              type="text"
              placeholder="example.txt"
              class="demo-input"
            />
          </div>
          <div class="input-group">
            <label>文件内容：</label>
            <input
              v-model="fileContent"
              type="text"
              placeholder="Hello, World!"
              class="demo-input"
            />
          </div>
          <button @click="runIOChain" class="demo-btn">执行IO操作链</button>
        </div>

        <div class="demo-output">
          <div class="io-operations">
            <h6>IO操作序列：</h6>
            <div
              v-for="(op, index) in ioOperations"
              :key="index"
              class="io-operation"
            >
              <span class="op-type">{{ op.type }}</span>
              <span class="op-description">{{ op.description }}</span>
              <span class="op-status" :class="op.status">{{ op.status }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- State单子演示 -->
      <div class="demo-subsection">
        <h5>3. State单子 - 状态管理</h5>
        <div class="demo-controls">
          <div class="input-group">
            <label>初始计数：</label>
            <input
              v-model.number="initialCount"
              type="number"
              class="demo-input"
            />
          </div>
          <div class="function-group">
            <label>状态操作：</label>
            <select v-model="selectedStateOp" class="demo-select">
              <option value="increment">递增操作</option>
              <option value="multiply">乘法操作</option>
              <option value="complex">复合操作</option>
              <option value="conditional">条件操作</option>
            </select>
          </div>
          <button @click="runStateChain" class="demo-btn">执行状态操作</button>
        </div>

        <div class="demo-output">
          <div class="state-transitions">
            <h6>状态转换：</h6>
            <div
              v-for="(transition, index) in stateTransitions"
              :key="index"
              class="transition-item"
            >
              <span class="from-state">{{ transition.from }}</span>
              <span class="transition-arrow">→</span>
              <span class="to-state">{{ transition.to }}</span>
              <span class="transition-op">{{ transition.operation }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 单子法则验证 -->
      <div class="demo-subsection">
        <h5>4. 单子法则验证</h5>
        <div class="demo-controls">
          <button @click="verifyLeftIdentity" class="demo-btn">
            验证左单位元法则
          </button>
          <button @click="verifyRightIdentity" class="demo-btn">
            验证右单位元法则
          </button>
          <button @click="verifyAssociativity" class="demo-btn">
            验证结合律
          </button>
        </div>

        <div class="demo-output">
          <div class="laws-result">
            <div
              v-for="(result, index) in lawResults"
              :key="index"
              class="law-result"
            >
              <div class="law-name">{{ result.name }}</div>
              <div class="law-description">{{ result.description }}</div>
              <div
                class="law-verification"
                :class="{ success: result.passed, failure: !result.passed }"
              >
                {{ result.passed ? "✅ 通过" : "❌ 失败" }} -
                {{ result.details }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实际应用场景 -->
      <div class="demo-subsection">
        <h5>5. 实际应用场景</h5>
        <div class="demo-controls">
          <button @click="runUserValidation" class="demo-btn">
            用户数据验证
          </button>
          <button @click="runAsyncPipeline" class="demo-btn">
            异步处理管道
          </button>
          <button @click="runErrorHandling" class="demo-btn">错误处理链</button>
        </div>

        <div class="demo-output">
          <div class="application-results">
            <div
              v-for="(result, index) in applicationResults"
              :key="index"
              class="app-result"
            >
              <div class="app-name">{{ result.name }}</div>
              <div class="app-steps">
                <div
                  v-for="(step, stepIndex) in result.steps"
                  :key="stepIndex"
                  class="app-step"
                >
                  {{ step }}
                </div>
              </div>
              <div
                class="app-final-result"
                :class="result.success ? 'success' : 'error'"
              >
                {{ result.finalResult }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优缺点分析 -->
    <div class="advantages">
      <h4>优点：</h4>
      <ul>
        <li>✅ 提供统一的接口处理复杂的计算上下文</li>
        <li>✅ 支持安全的链式操作，避免嵌套回调</li>
        <li>✅ 优雅地处理副作用和异步操作</li>
        <li>✅ 遵循数学法则，行为可预测</li>
        <li>✅ 提高代码的可组合性和可重用性</li>
        <li>✅ 强大的错误处理和异常传播机制</li>
      </ul>

      <h4>缺点：</h4>
      <ul>
        <li>❌ 学习曲线陡峭，需要深入理解函数式概念</li>
        <li>❌ 抽象层次高，可能增加代码复杂性</li>
        <li>❌ 调试相对困难，需要理解单子的内部机制</li>
        <li>❌ 在某些简单场景下可能过度设计</li>
        <li>❌ 性能开销相对较高</li>
      </ul>

      <h4>单子法则：</h4>
      <ul>
        <li>🔍 <strong>左单位元:</strong> unit(a).flatMap(f) === f(a)</li>
        <li>🔍 <strong>右单位元:</strong> m.flatMap(unit) === m</li>
        <li>
          🔍 <strong>结合律:</strong> m.flatMap(f).flatMap(g) === m.flatMap(x =>
          f(x).flatMap(g))
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { Maybe, IO, State, unit, MonadLaws } from "@/patterns/Monad";

// 响应式数据
const inputValue = ref<string>("42");
const selectedChain = ref<string>("safe-math");
const fileName = ref<string>("example.txt");
const fileContent = ref<string>("Hello, World!");
const initialCount = ref<number>(0);
const selectedStateOp = ref<string>("increment");

// 状态数据
const maybeValue = ref(Maybe.of(42));
const ioValue = ref<string>("initial value");
const currentState = ref<number>(0);
const newState = ref<number>(0);

const maybeSteps = ref<
  Array<{
    description: string;
    result: string;
  }>
>([]);

const ioOperations = ref<
  Array<{
    type: string;
    description: string;
    status: "pending" | "success" | "error";
  }>
>([]);

const stateTransitions = ref<
  Array<{
    from: number;
    to: number;
    operation: string;
  }>
>([]);

const lawResults = ref<
  Array<{
    name: string;
    description: string;
    passed: boolean;
    details: string;
  }>
>([]);

const applicationResults = ref<
  Array<{
    name: string;
    steps: string[];
    finalResult: string;
    success: boolean;
  }>
>([]);

// 计算属性
const flatMappedMaybe = computed(() => {
  return maybeValue.value.flatMap((x) => Maybe.of(x * 2));
});

const ioResult = computed(() => {
  return `IO(() => processed_${ioValue.value})`;
});

// 方法
const runMaybeChain = () => {
  maybeSteps.value = [];
  const value = inputValue.value.trim();

  if (value === "") {
    maybeValue.value = Maybe.none();
    maybeSteps.value.push({
      description: "输入为空，创建None",
      result: "Maybe.none()",
    });
    return;
  }

  const numValue = parseFloat(value);
  if (isNaN(numValue)) {
    maybeValue.value = Maybe.none();
    maybeSteps.value.push({
      description: "输入无效，创建None",
      result: "Maybe.none()",
    });
    return;
  }

  maybeValue.value = Maybe.of(numValue);
  maybeSteps.value.push({
    description: `创建Maybe(${numValue})`,
    result: `Maybe(${numValue})`,
  });

  switch (selectedChain.value) {
    case "safe-math":
      runSafeMathChain(numValue);
      break;
    case "string-ops":
      runStringOpsChain(numValue);
      break;
    case "validation":
      runValidationChain(numValue);
      break;
    case "nested":
      runNestedChain(numValue);
      break;
  }
};

const runSafeMathChain = (value: number) => {
  let result = Maybe.of(value);

  // 步骤1: 平方根
  result = result.flatMap((x) => {
    if (x < 0) return Maybe.none();
    const sqrt = Math.sqrt(x);
    maybeSteps.value.push({
      description: `计算平方根: √${x}`,
      result: `Maybe(${sqrt.toFixed(2)})`,
    });
    return Maybe.of(sqrt);
  });

  // 步骤2: 乘以2
  result = result.flatMap((x) => {
    const doubled = x * 2;
    maybeSteps.value.push({
      description: `乘以2: ${x.toFixed(2)} × 2`,
      result: `Maybe(${doubled.toFixed(2)})`,
    });
    return Maybe.of(doubled);
  });

  // 步骤3: 除以3
  result = result.flatMap((x) => {
    const divided = x / 3;
    maybeSteps.value.push({
      description: `除以3: ${x.toFixed(2)} ÷ 3`,
      result: `Maybe(${divided.toFixed(2)})`,
    });
    return Maybe.of(divided);
  });

  maybeValue.value = result;
};

const runStringOpsChain = (value: number) => {
  let result = Maybe.of(value.toString());

  result = result.flatMap((str) => {
    const padded = str.padStart(5, "0");
    maybeSteps.value.push({
      description: `左填充0: "${str}" → "${padded}"`,
      result: `Maybe("${padded}")`,
    });
    return Maybe.of(padded);
  });

  result = result.flatMap((str) => {
    const upper = str.toUpperCase();
    maybeSteps.value.push({
      description: `转大写: "${str}" → "${upper}"`,
      result: `Maybe("${upper}")`,
    });
    return Maybe.of(upper);
  });

  result = result.flatMap((str) => {
    const prefixed = `PREFIX_${str}`;
    maybeSteps.value.push({
      description: `添加前缀: "${str}" → "${prefixed}"`,
      result: `Maybe("${prefixed}")`,
    });
    return Maybe.of(prefixed);
  });
};

const runValidationChain = (value: number) => {
  let result = Maybe.of(value);

  result = result.flatMap((x) => {
    if (x < 0) {
      maybeSteps.value.push({
        description: `验证非负数: ${x} < 0`,
        result: "Maybe.none() - 验证失败",
      });
      return Maybe.none();
    }
    maybeSteps.value.push({
      description: `验证非负数: ${x} ≥ 0`,
      result: `Maybe(${x}) - 验证通过`,
    });
    return Maybe.of(x);
  });

  result = result.flatMap((x) => {
    if (x > 100) {
      maybeSteps.value.push({
        description: `验证范围: ${x} > 100`,
        result: "Maybe.none() - 超出范围",
      });
      return Maybe.none();
    }
    maybeSteps.value.push({
      description: `验证范围: ${x} ≤ 100`,
      result: `Maybe(${x}) - 范围有效`,
    });
    return Maybe.of(x);
  });

  result = result.flatMap((x) => {
    if (x % 2 !== 0) {
      maybeSteps.value.push({
        description: `验证偶数: ${x} 是奇数`,
        result: "Maybe.none() - 不是偶数",
      });
      return Maybe.none();
    }
    maybeSteps.value.push({
      description: `验证偶数: ${x} 是偶数`,
      result: `Maybe(${x}) - 验证通过`,
    });
    return Maybe.of(x);
  });

  maybeValue.value = result;
};

const runNestedChain = (value: number) => {
  const result = Maybe.of(value).flatMap((x) => {
    maybeSteps.value.push({
      description: `第一层: 处理 ${x}`,
      result: `Maybe(${x})`,
    });
    return Maybe.of(x).flatMap((y) => {
      maybeSteps.value.push({
        description: `第二层: 处理 ${y}`,
        result: `Maybe(${y * 2})`,
      });
      return Maybe.of(y * 2).flatMap((z) => {
        maybeSteps.value.push({
          description: `第三层: 处理 ${z}`,
          result: `Maybe(${z + 10})`,
        });
        return Maybe.of(z + 10);
      });
    });
  });

  maybeValue.value = result;
};

const runIOChain = () => {
  ioOperations.value = [];

  const writeFile = IO.of(fileContent.value).flatMap((content) => {
    ioOperations.value.push({
      type: "WRITE",
      description: `写入文件 ${fileName.value}`,
      status: "success",
    });
    return IO.of(`文件已写入: ${content}`);
  });

  const readFile = writeFile.flatMap((writeResult) => {
    ioOperations.value.push({
      type: "READ",
      description: `读取文件 ${fileName.value}`,
      status: "success",
    });
    return IO.of(`读取内容: ${fileContent.value}`);
  });

  const processFile = readFile.flatMap((readResult) => {
    ioOperations.value.push({
      type: "PROCESS",
      description: "处理文件内容",
      status: "success",
    });
    return IO.of(`处理结果: ${fileContent.value.toUpperCase()}`);
  });

  ioValue.value = processFile.run();
};

const runStateChain = () => {
  stateTransitions.value = [];
  currentState.value = initialCount.value;

  let stateMonad = State.of(initialCount.value);

  switch (selectedStateOp.value) {
    case "increment":
      stateMonad = stateMonad.flatMap((state) => {
        const newVal = state + 1;
        stateTransitions.value.push({
          from: state,
          to: newVal,
          operation: "+1",
        });
        return State.of(newVal);
      });
      break;

    case "multiply":
      stateMonad = stateMonad.flatMap((state) => {
        const newVal = state * 2;
        stateTransitions.value.push({
          from: state,
          to: newVal,
          operation: "×2",
        });
        return State.of(newVal);
      });
      break;

    case "complex":
      stateMonad = stateMonad
        .flatMap((state) => {
          const newVal = state + 5;
          stateTransitions.value.push({
            from: state,
            to: newVal,
            operation: "+5",
          });
          return State.of(newVal);
        })
        .flatMap((state) => {
          const newVal = state * 3;
          stateTransitions.value.push({
            from: state,
            to: newVal,
            operation: "×3",
          });
          return State.of(newVal);
        })
        .flatMap((state) => {
          const newVal = state - 2;
          stateTransitions.value.push({
            from: state,
            to: newVal,
            operation: "-2",
          });
          return State.of(newVal);
        });
      break;

    case "conditional":
      stateMonad = stateMonad.flatMap((state) => {
        if (state > 10) {
          const newVal = state / 2;
          stateTransitions.value.push({
            from: state,
            to: newVal,
            operation: "÷2 (>10)",
          });
          return State.of(newVal);
        } else {
          const newVal = state * 2;
          stateTransitions.value.push({
            from: state,
            to: newVal,
            operation: "×2 (≤10)",
          });
          return State.of(newVal);
        }
      });
      break;
  }

  newState.value = stateMonad.run();
};

const verifyLeftIdentity = () => {
  const a = 42;
  const f = (x: number) => Maybe.of(x * 2);

  const left = unit(a).flatMap(f);
  const right = f(a);

  const passed = left.getValue() === right.getValue();

  lawResults.value.push({
    name: "左单位元法则",
    description: "unit(a).flatMap(f) === f(a)",
    passed,
    details: `unit(${a}).flatMap(x => Maybe.of(x*2)) = ${left.getValue()}, f(${a}) = ${right.getValue()}`,
  });
};

const verifyRightIdentity = () => {
  const m = Maybe.of(42);
  const left = m.flatMap(unit);
  const right = m;

  const passed = left.getValue() === right.getValue();

  lawResults.value.push({
    name: "右单位元法则",
    description: "m.flatMap(unit) === m",
    passed,
    details: `Maybe(42).flatMap(unit) = ${left.getValue()}, Maybe(42) = ${right.getValue()}`,
  });
};

const verifyAssociativity = () => {
  const m = Maybe.of(5);
  const f = (x: number) => Maybe.of(x * 2);
  const g = (x: number) => Maybe.of(x + 1);

  const left = m.flatMap(f).flatMap(g);
  const right = m.flatMap((x) => f(x).flatMap(g));

  const passed = left.getValue() === right.getValue();

  lawResults.value.push({
    name: "结合律",
    description: "m.flatMap(f).flatMap(g) === m.flatMap(x => f(x).flatMap(g))",
    passed,
    details: `左边 = ${left.getValue()}, 右边 = ${right.getValue()}`,
  });
};

const runUserValidation = () => {
  const steps: string[] = [];

  const validateUser = (userData: any) => {
    return Maybe.fromNullable(userData)
      .flatMap((user) => {
        steps.push("检查用户数据是否存在");
        return user.name ? Maybe.of(user) : Maybe.none();
      })
      .flatMap((user) => {
        steps.push("验证用户名长度");
        return user.name.length >= 3 ? Maybe.of(user) : Maybe.none();
      })
      .flatMap((user) => {
        steps.push("验证邮箱格式");
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(user.email) ? Maybe.of(user) : Maybe.none();
      })
      .flatMap((user) => {
        steps.push("验证年龄范围");
        return user.age >= 18 && user.age <= 100
          ? Maybe.of(user)
          : Maybe.none();
      });
  };

  const userData = {
    name: "Alice",
    email: "<EMAIL>",
    age: 25,
  };

  const result = validateUser(userData);

  applicationResults.value.push({
    name: "用户数据验证",
    steps,
    finalResult: result.hasValue() ? "验证通过" : "验证失败",
    success: result.hasValue(),
  });
};

const runAsyncPipeline = () => {
  const steps: string[] = [];

  const pipeline = IO.of("input data")
    .flatMap((data) => {
      steps.push(`步骤1: 预处理数据 "${data}"`);
      return IO.of(`processed_${data}`);
    })
    .flatMap((data) => {
      steps.push(`步骤2: 验证数据 "${data}"`);
      return IO.of(`validated_${data}`);
    })
    .flatMap((data) => {
      steps.push(`步骤3: 转换数据 "${data}"`);
      return IO.of(`transformed_${data}`);
    })
    .flatMap((data) => {
      steps.push(`步骤4: 保存数据 "${data}"`);
      return IO.of(`saved_${data}`);
    });

  const result = pipeline.run();

  applicationResults.value.push({
    name: "异步处理管道",
    steps,
    finalResult: result,
    success: true,
  });
};

const runErrorHandling = () => {
  const steps: string[] = [];

  const errorPipeline = Maybe.of(-5)
    .flatMap((x) => {
      steps.push(`检查输入值: ${x}`);
      return x >= 0 ? Maybe.of(x) : Maybe.none();
    })
    .flatMap((x) => {
      steps.push(`计算平方根: √${x}`);
      return Maybe.of(Math.sqrt(x));
    })
    .flatMap((x) => {
      steps.push(`验证结果: ${x}`);
      return !isNaN(x) ? Maybe.of(x) : Maybe.none();
    });

  applicationResults.value.push({
    name: "错误处理链",
    steps,
    finalResult: errorPipeline.hasValue()
      ? `结果: ${errorPipeline.getValue()}`
      : "处理失败: 输入值为负数",
    success: errorPipeline.hasValue(),
  });
};
</script>

<style lang="scss" scoped>
.monad-demo {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .pattern-info {
    .description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-cases {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #1890ff;

      h4 {
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          color: #555;
        }
      }
    }
  }

  .architecture-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .architecture-diagram {
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .monad-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;

        .monad-box {
          background: white;
          padding: 1.5rem;
          border-radius: 8px;
          border: 2px solid #1890ff;
          text-align: center;

          h5 {
            margin: 0 0 1rem 0;
            color: #1890ff;
            font-weight: 600;
          }

          .monad-content {
            .value-box,
            .result-box {
              background: #e3f2fd;
              padding: 0.8rem;
              border-radius: 6px;
              margin: 0.5rem 0;
              font-family: "Monaco", "Consolas", monospace;
              font-size: 0.9rem;
              color: #1565c0;
              font-weight: 500;
            }

            .operation-arrow {
              color: #666;
              font-weight: 600;
              margin: 0.5rem 0;
            }
          }
        }
      }
    }
  }

  .demo-section {
    h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .demo-subsection {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      margin-bottom: 1.5rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #1890ff;
        font-weight: 600;
      }

      h6 {
        margin: 0 0 0.8rem 0;
        color: #333;
        font-weight: 600;
      }

      .demo-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1.5rem;
        align-items: end;

        .input-group,
        .function-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          label {
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
          }
        }

        .demo-input,
        .demo-select {
          padding: 0.6rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
          min-width: 150px;

          &:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .demo-btn {
          padding: 0.6rem 1.2rem;
          border: none;
          border-radius: 6px;
          background: linear-gradient(135deg, #1890ff, #722ed1);
          color: white;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          font-size: 0.9rem;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
          }
        }
      }

      .demo-output {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .chain-steps {
          .step-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #1890ff;

            .step-number {
              background: #1890ff;
              color: white;
              width: 24px;
              height: 24px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 0.8rem;
              font-weight: 600;
            }

            .step-description {
              flex: 1;
              color: #333;
              font-size: 0.9rem;
            }

            .step-result {
              font-family: "Monaco", "Consolas", monospace;
              background: #e3f2fd;
              padding: 0.3rem 0.6rem;
              border-radius: 4px;
              color: #1565c0;
              font-size: 0.8rem;
            }
          }
        }

        .io-operations {
          .io-operation {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 6px;

            .op-type {
              background: #ff9800;
              color: white;
              padding: 0.2rem 0.6rem;
              border-radius: 4px;
              font-size: 0.8rem;
              font-weight: 600;
              min-width: 60px;
              text-align: center;
            }

            .op-description {
              flex: 1;
              color: #333;
              font-size: 0.9rem;
            }

            .op-status {
              padding: 0.2rem 0.6rem;
              border-radius: 4px;
              font-size: 0.8rem;
              font-weight: 600;

              &.success {
                background: #4caf50;
                color: white;
              }

              &.error {
                background: #f44336;
                color: white;
              }

              &.pending {
                background: #ffc107;
                color: #333;
              }
            }
          }
        }

        .state-transitions {
          .transition-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #9c27b0;

            .from-state,
            .to-state {
              background: #e3f2fd;
              padding: 0.3rem 0.6rem;
              border-radius: 4px;
              font-family: "Monaco", "Consolas", monospace;
              color: #1565c0;
              font-weight: 600;
              min-width: 40px;
              text-align: center;
            }

            .transition-arrow {
              color: #666;
              font-weight: 600;
              font-size: 1.2rem;
            }

            .transition-op {
              background: #9c27b0;
              color: white;
              padding: 0.2rem 0.6rem;
              border-radius: 4px;
              font-size: 0.8rem;
              font-weight: 600;
            }
          }
        }

        .laws-result {
          .law-result {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            border-left: 4px solid #1890ff;

            .law-name {
              font-weight: 600;
              color: #333;
              margin-bottom: 0.5rem;
            }

            .law-description {
              color: #666;
              font-size: 0.9rem;
              margin-bottom: 0.5rem;
              font-family: "Monaco", "Consolas", monospace;
            }

            .law-verification {
              font-family: "Monaco", "Consolas", monospace;
              font-size: 0.85rem;
              padding: 0.5rem;
              border-radius: 4px;

              &.success {
                background: #d4edda;
                color: #155724;
              }

              &.failure {
                background: #f8d7da;
                color: #721c24;
              }
            }
          }
        }

        .application-results {
          .app-result {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            border-left: 4px solid #1890ff;

            .app-name {
              font-weight: 600;
              color: #333;
              margin-bottom: 0.8rem;
            }

            .app-steps {
              margin-bottom: 0.8rem;

              .app-step {
                padding: 0.3rem 0;
                color: #666;
                font-size: 0.9rem;
                border-bottom: 1px solid #eee;

                &:last-child {
                  border-bottom: none;
                }
              }
            }

            .app-final-result {
              padding: 0.5rem;
              border-radius: 4px;
              font-weight: 600;

              &.success {
                background: #d4edda;
                color: #155724;
              }

              &.error {
                background: #f8d7da;
                color: #721c24;
              }
            }
          }
        }
      }
    }
  }

  .advantages {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;

      &:not(:first-child) {
        margin-top: 1.5rem;
      }
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .monad-demo {
    .architecture-section {
      .architecture-diagram {
        .monad-container {
          grid-template-columns: 1fr;
        }
      }
    }

    .demo-section {
      .demo-subsection {
        .demo-controls {
          flex-direction: column;
          align-items: stretch;

          .demo-input,
          .demo-select {
            min-width: auto;
          }
        }

        .demo-output {
          .chain-steps {
            .step-item {
              flex-direction: column;
              align-items: stretch;
              gap: 0.5rem;
            }
          }

          .io-operations {
            .io-operation {
              flex-direction: column;
              align-items: stretch;
              gap: 0.5rem;
            }
          }

          .state-transitions {
            .transition-item {
              flex-direction: column;
              align-items: stretch;
              gap: 0.5rem;
            }
          }
        }
      }
    }
  }
}
</style>
