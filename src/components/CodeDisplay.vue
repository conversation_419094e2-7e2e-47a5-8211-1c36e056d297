<template>
  <div class="code-display">
    <div class="code-header">
      <h3 class="code-title">💻 {{ title }}</h3>
      <div class="code-actions">
        <button @click="copyCode" class="copy-btn" :class="{ copied: isCopied }">
          {{ isCopied ? '已复制 ✓' : '复制代码' }}
        </button>
      </div>
    </div>
    <div class="code-content">
      <pre class="code-block"><code>{{ code }}</code></pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface Props {
  title: string
  code: string
}

const props = defineProps<Props>()
const isCopied = ref(false)

const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(props.code)
    isCopied.value = true
    setTimeout(() => {
      isCopied.value = false
    }, 2000)
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 当代码改变时重置复制状态
watch(() => props.code, () => {
  isCopied.value = false
})
</script>

<style lang="scss" scoped>
.code-display {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.code-header {
  background: linear-gradient(135deg, #2d3748, #4a5568);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #4a5568;

  .code-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .code-actions {
    .copy-btn {
      padding: 0.5rem 1rem;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      cursor: pointer;
      font-size: 0.85rem;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
      }

      &.copied {
        background: #48bb78;
        border-color: #48bb78;
      }
    }
  }
}

.code-content {
  flex: 1;
  overflow: hidden;
  background: #2d3748;

  .code-block {
    height: 100%;
    margin: 0;
    padding: 1.5rem;
    background: #2d3748;
    color: #e2e8f0;
    font-family: "Fira Code", "Monaco", "Consolas", monospace;
    font-size: 0.85rem;
    line-height: 1.6;
    overflow: auto;
    white-space: pre-wrap;
    word-wrap: break-word;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #1a202c;
    }

    &::-webkit-scrollbar-thumb {
      background: #4a5568;
      border-radius: 4px;

      &:hover {
        background: #718096;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .code-display {
    display: none;
  }
}
</style>
