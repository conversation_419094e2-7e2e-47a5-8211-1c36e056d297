/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 12:10:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 12:10:40
 * @FilePath     : /src/patterns/State.ts
 * @Description  : 状态模式实现 - 订单状态管理
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 订单状态接口
interface OrderState {
  // 状态名称
  getStateName(): string;
  // 状态描述
  getDescription(): string;
  // 可执行的操作
  getAvailableActions(): string[];
  // 执行操作
  handleAction(context: OrderContext, action: string): boolean;
  // 状态颜色（用于UI显示）
  getStateColor(): string;
  // 状态图标
  getStateIcon(): string;
}

// 订单上下文
class OrderContext {
  private state: OrderState;
  private orderInfo: {
    id: string;
    amount: number;
    items: string[];
    customerName: string;
    createdAt: Date;
    history: Array<{ state: string; timestamp: Date; action?: string }>;
  };

  constructor(orderId: string, amount: number, items: string[], customerName: string) {
    this.orderInfo = {
      id: orderId,
      amount,
      items,
      customerName,
      createdAt: new Date(),
      history: []
    };

    // 初始状态为待支付
    this.state = new PendingPaymentState();
    this.addToHistory(this.state.getStateName(), '订单创建');
  }

  // 设置状态
  setState(state: OrderState, action?: string): void {
    this.state = state;
    this.addToHistory(state.getStateName(), action);
  }

  // 获取当前状态
  getState(): OrderState {
    return this.state;
  }

  // 执行操作
  executeAction(action: string): boolean {
    return this.state.handleAction(this, action);
  }

  // 获取订单信息
  getOrderInfo() {
    return { ...this.orderInfo };
  }

  // 添加历史记录
  private addToHistory(stateName: string, action?: string): void {
    this.orderInfo.history.push({
      state: stateName,
      timestamp: new Date(),
      action
    });
  }
}

// 具体状态实现

// 1. 待支付状态
class PendingPaymentState implements OrderState {
  getStateName(): string {
    return '待支付';
  }

  getDescription(): string {
    return '订单已创建，等待用户支付';
  }

  getAvailableActions(): string[] {
    return ['支付', '取消订单'];
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '支付':
        context.setState(new PaidState(), '用户完成支付');
        return true;
      case '取消订单':
        context.setState(new CancelledState(), '用户取消订单');
        return true;
      default:
        return false;
    }
  }

  getStateColor(): string {
    return '#f59e0b'; // 橙色
  }

  getStateIcon(): string {
    return '💳';
  }
}

// 2. 已支付状态
class PaidState implements OrderState {
  getStateName(): string {
    return '已支付';
  }

  getDescription(): string {
    return '支付成功，准备发货';
  }

  getAvailableActions(): string[] {
    return ['发货', '申请退款'];
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '发货':
        context.setState(new ShippedState(), '商家发货');
        return true;
      case '申请退款':
        context.setState(new RefundingState(), '用户申请退款');
        return true;
      default:
        return false;
    }
  }

  getStateColor(): string {
    return '#10b981'; // 绿色
  }

  getStateIcon(): string {
    return '✅';
  }
}

// 3. 已发货状态
class ShippedState implements OrderState {
  getStateName(): string {
    return '已发货';
  }

  getDescription(): string {
    return '商品已发货，正在配送中';
  }

  getAvailableActions(): string[] {
    return ['确认收货', '申请退货'];
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '确认收货':
        context.setState(new DeliveredState(), '用户确认收货');
        return true;
      case '申请退货':
        context.setState(new ReturningState(), '用户申请退货');
        return true;
      default:
        return false;
    }
  }

  getStateColor(): string {
    return '#3b82f6'; // 蓝色
  }

  getStateIcon(): string {
    return '🚚';
  }
}

// 4. 已完成状态
class DeliveredState implements OrderState {
  getStateName(): string {
    return '已完成';
  }

  getDescription(): string {
    return '订单已完成，交易成功';
  }

  getAvailableActions(): string[] {
    return ['评价商品'];
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '评价商品':
        // 评价不改变状态，只是添加记录
        return true;
      default:
        return false;
    }
  }

  getStateColor(): string {
    return '#059669'; // 深绿色
  }

  getStateIcon(): string {
    return '🎉';
  }
}

// 5. 已取消状态
class CancelledState implements OrderState {
  getStateName(): string {
    return '已取消';
  }

  getDescription(): string {
    return '订单已取消';
  }

  getAvailableActions(): string[] {
    return []; // 取消状态无可执行操作
  }

  handleAction(context: OrderContext, action: string): boolean {
    return false; // 取消状态不能执行任何操作
  }

  getStateColor(): string {
    return '#ef4444'; // 红色
  }

  getStateIcon(): string {
    return '❌';
  }
}

// 6. 退款中状态
class RefundingState implements OrderState {
  getStateName(): string {
    return '退款中';
  }

  getDescription(): string {
    return '正在处理退款申请';
  }

  getAvailableActions(): string[] {
    return ['同意退款', '拒绝退款'];
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '同意退款':
        context.setState(new RefundedState(), '商家同意退款');
        return true;
      case '拒绝退款':
        context.setState(new PaidState(), '商家拒绝退款');
        return true;
      default:
        return false;
    }
  }

  getStateColor(): string {
    return '#f59e0b'; // 橙色
  }

  getStateIcon(): string {
    return '🔄';
  }
}

// 7. 已退款状态
class RefundedState implements OrderState {
  getStateName(): string {
    return '已退款';
  }

  getDescription(): string {
    return '退款已完成';
  }

  getAvailableActions(): string[] {
    return [];
  }

  handleAction(context: OrderContext, action: string): boolean {
    return false;
  }

  getStateColor(): string {
    return '#6b7280'; // 灰色
  }

  getStateIcon(): string {
    return '💰';
  }
}

// 8. 退货中状态
class ReturningState implements OrderState {
  getStateName(): string {
    return '退货中';
  }

  getDescription(): string {
    return '正在处理退货申请';
  }

  getAvailableActions(): string[] {
    return ['同意退货', '拒绝退货'];
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '同意退货':
        context.setState(new RefundedState(), '商家同意退货');
        return true;
      case '拒绝退货':
        context.setState(new DeliveredState(), '商家拒绝退货');
        return true;
      default:
        return false;
    }
  }

  getStateColor(): string {
    return '#f59e0b'; // 橙色
  }

  getStateIcon(): string {
    return '📦';
  }
}

// 订单工厂
class OrderFactory {
  static createOrder(
    orderId: string,
    amount: number,
    items: string[],
    customerName: string
  ): OrderContext {
    return new OrderContext(orderId, amount, items, customerName);
  }

  static getStateByName(stateName: string): OrderState | null {
    const stateMap: Record<string, () => OrderState> = {
      '待支付': () => new PendingPaymentState(),
      '已支付': () => new PaidState(),
      '已发货': () => new ShippedState(),
      '已完成': () => new DeliveredState(),
      '已取消': () => new CancelledState(),
      '退款中': () => new RefundingState(),
      '已退款': () => new RefundedState(),
      '退货中': () => new ReturningState(),
    };

    return stateMap[stateName] ? stateMap[stateName]() : null;
  }
}

// 导出类型和工厂
export { OrderFactory };
export type OrderStateType = OrderState;
export type OrderContextType = OrderContext;
