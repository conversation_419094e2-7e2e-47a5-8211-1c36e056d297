/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-27 19:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-27 19:00:00
 * @FilePath     : /src/patterns/EventSourcing.ts
 * @Description  : 事件溯源模式的TypeScript实现
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-27 19:00:00
 */

// 基础事件接口
export interface BaseEvent {
  id: string;
  type: string;
  aggregateId: string;
  version: number;
  timestamp: Date;
  data: any;
  metadata?: any;
}

// 账户事件类型
export type AccountEvent = 
  | AccountCreatedEvent
  | MoneyDepositedEvent
  | MoneyWithdrawnEvent
  | AccountFrozenEvent
  | AccountUnfrozenEvent
  | AccountClosedEvent;

// 具体事件定义
export interface AccountCreatedEvent extends BaseEvent {
  type: 'AccountCreated';
  data: {
    accountId: string;
    initialBalance: number;
  };
}

export interface MoneyDepositedEvent extends BaseEvent {
  type: 'MoneyDeposited';
  data: {
    amount: number;
    description?: string;
  };
}

export interface MoneyWithdrawnEvent extends BaseEvent {
  type: 'MoneyWithdrawn';
  data: {
    amount: number;
    description?: string;
  };
}

export interface AccountFrozenEvent extends BaseEvent {
  type: 'AccountFrozen';
  data: {
    reason: string;
  };
}

export interface AccountUnfrozenEvent extends BaseEvent {
  type: 'AccountUnfrozen';
  data: {
    reason: string;
  };
}

export interface AccountClosedEvent extends BaseEvent {
  type: 'AccountClosed';
  data: {
    reason: string;
    finalBalance: number;
  };
}

// 事件存储接口
export interface IEventStore {
  saveEvent(event: BaseEvent): void;
  getEvents(aggregateId: string): BaseEvent[];
  getAllEvents(): BaseEvent[];
  getEventsFromVersion(aggregateId: string, fromVersion: number): BaseEvent[];
}

// 事件存储实现
export class EventStore implements IEventStore {
  private events: BaseEvent[] = [];

  saveEvent(event: BaseEvent): void {
    this.events.push({ ...event });
  }

  getEvents(aggregateId: string): BaseEvent[] {
    return this.events
      .filter(event => event.aggregateId === aggregateId)
      .sort((a, b) => a.version - b.version);
  }

  getAllEvents(): BaseEvent[] {
    return [...this.events].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  getEventsFromVersion(aggregateId: string, fromVersion: number): BaseEvent[] {
    return this.events
      .filter(event => event.aggregateId === aggregateId && event.version >= fromVersion)
      .sort((a, b) => a.version - b.version);
  }

  // 获取事件数量
  getEventCount(): number {
    return this.events.length;
  }

  // 清空事件存储
  clear(): void {
    this.events = [];
  }

  // 获取聚合的最新版本
  getLatestVersion(aggregateId: string): number {
    const events = this.getEvents(aggregateId);
    return events.length > 0 ? events[events.length - 1].version : 0;
  }
}

// 银行账户聚合根
export class BankAccount {
  private accountId: string;
  private balance: number = 0;
  private status: 'active' | 'frozen' | 'closed' = 'active';
  private version: number = 0;
  private createdAt: Date;
  private eventStore: IEventStore;

  constructor(accountId: string, eventStore: IEventStore) {
    this.accountId = accountId;
    this.eventStore = eventStore;
    this.createdAt = new Date();
    
    // 创建账户事件
    this.applyAndSaveEvent({
      id: this.generateEventId(),
      type: 'AccountCreated',
      aggregateId: accountId,
      version: this.getNextVersion(),
      timestamp: new Date(),
      data: {
        accountId,
        initialBalance: 0
      }
    } as AccountCreatedEvent);
  }

  // 存款
  deposit(amount: number, description?: string): void {
    if (this.status !== 'active') {
      throw new Error('账户未激活，无法存款');
    }

    if (amount <= 0) {
      throw new Error('存款金额必须大于0');
    }

    this.applyAndSaveEvent({
      id: this.generateEventId(),
      type: 'MoneyDeposited',
      aggregateId: this.accountId,
      version: this.getNextVersion(),
      timestamp: new Date(),
      data: {
        amount,
        description
      }
    } as MoneyDepositedEvent);
  }

  // 取款
  withdraw(amount: number, description?: string): void {
    if (this.status !== 'active') {
      throw new Error('账户未激活，无法取款');
    }

    if (amount <= 0) {
      throw new Error('取款金额必须大于0');
    }

    if (amount > this.balance) {
      throw new Error('余额不足');
    }

    this.applyAndSaveEvent({
      id: this.generateEventId(),
      type: 'MoneyWithdrawn',
      aggregateId: this.accountId,
      version: this.getNextVersion(),
      timestamp: new Date(),
      data: {
        amount,
        description
      }
    } as MoneyWithdrawnEvent);
  }

  // 冻结账户
  freeze(reason: string = '风险控制'): void {
    if (this.status === 'closed') {
      throw new Error('账户已关闭，无法冻结');
    }

    if (this.status === 'frozen') {
      throw new Error('账户已冻结');
    }

    this.applyAndSaveEvent({
      id: this.generateEventId(),
      type: 'AccountFrozen',
      aggregateId: this.accountId,
      version: this.getNextVersion(),
      timestamp: new Date(),
      data: {
        reason
      }
    } as AccountFrozenEvent);
  }

  // 解冻账户
  unfreeze(reason: string = '风险解除'): void {
    if (this.status !== 'frozen') {
      throw new Error('账户未冻结');
    }

    this.applyAndSaveEvent({
      id: this.generateEventId(),
      type: 'AccountUnfrozen',
      aggregateId: this.accountId,
      version: this.getNextVersion(),
      timestamp: new Date(),
      data: {
        reason
      }
    } as AccountUnfrozenEvent);
  }

  // 关闭账户
  close(reason: string = '用户申请'): void {
    if (this.status === 'closed') {
      throw new Error('账户已关闭');
    }

    this.applyAndSaveEvent({
      id: this.generateEventId(),
      type: 'AccountClosed',
      aggregateId: this.accountId,
      version: this.getNextVersion(),
      timestamp: new Date(),
      data: {
        reason,
        finalBalance: this.balance
      }
    } as AccountClosedEvent);
  }

  // 应用并保存事件
  private applyAndSaveEvent(event: AccountEvent): void {
    this.applyEvent(event);
    this.eventStore.saveEvent(event);
  }

  // 应用事件到聚合状态
  applyEvent(event: AccountEvent): void {
    switch (event.type) {
      case 'AccountCreated':
        this.balance = event.data.initialBalance;
        this.status = 'active';
        break;

      case 'MoneyDeposited':
        this.balance += event.data.amount;
        break;

      case 'MoneyWithdrawn':
        this.balance -= event.data.amount;
        break;

      case 'AccountFrozen':
        this.status = 'frozen';
        break;

      case 'AccountUnfrozen':
        this.status = 'active';
        break;

      case 'AccountClosed':
        this.status = 'closed';
        break;
    }

    this.version = event.version;
  }

  // 从事件重建聚合状态
  static fromEvents(accountId: string, events: AccountEvent[], eventStore: IEventStore): BankAccount {
    const account = Object.create(BankAccount.prototype);
    account.accountId = accountId;
    account.balance = 0;
    account.status = 'active';
    account.version = 0;
    account.eventStore = eventStore;
    account.createdAt = new Date();

    events.forEach(event => {
      account.applyEvent(event);
    });

    return account;
  }

  // 重置聚合状态
  reset(): void {
    this.balance = 0;
    this.status = 'active';
    this.version = 0;
  }

  // 获取下一个版本号
  private getNextVersion(): number {
    return this.version + 1;
  }

  // 生成事件ID
  private generateEventId(): string {
    return `evt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // 获取账户状态
  getState(): {
    accountId: string;
    balance: number;
    status: string;
    version: number;
    createdAt: Date;
  } {
    return {
      accountId: this.accountId,
      balance: this.balance,
      status: this.status,
      version: this.version,
      createdAt: this.createdAt
    };
  }

  // 获取账户余额
  getBalance(): number {
    return this.balance;
  }

  // 获取账户状态
  getStatus(): string {
    return this.status;
  }

  // 获取版本号
  getVersion(): number {
    return this.version;
  }
}

// 事件投影
export class EventProjections {
  private summary: {
    totalTransactions: number;
    totalDeposits: number;
    totalWithdrawals: number;
    averageTransactionAmount: number;
  } = {
    totalTransactions: 0,
    totalDeposits: 0,
    totalWithdrawals: 0,
    averageTransactionAmount: 0
  };

  private monthly: Record<string, {
    transactions: number;
    deposits: number;
    withdrawals: number;
  }> = {};

  // 从事件更新投影
  updateFromEvents(events: BaseEvent[]): void {
    this.resetProjections();

    events.forEach(event => {
      this.processEvent(event);
    });

    this.calculateAverages();
  }

  // 处理单个事件
  private processEvent(event: BaseEvent): void {
    const monthKey = this.getMonthKey(event.timestamp);

    // 初始化月度数据
    if (!this.monthly[monthKey]) {
      this.monthly[monthKey] = {
        transactions: 0,
        deposits: 0,
        withdrawals: 0
      };
    }

    switch (event.type) {
      case 'MoneyDeposited':
        this.summary.totalTransactions++;
        this.summary.totalDeposits += event.data.amount;
        this.monthly[monthKey].transactions++;
        this.monthly[monthKey].deposits += event.data.amount;
        break;

      case 'MoneyWithdrawn':
        this.summary.totalTransactions++;
        this.summary.totalWithdrawals += event.data.amount;
        this.monthly[monthKey].transactions++;
        this.monthly[monthKey].withdrawals += event.data.amount;
        break;
    }
  }

  // 计算平均值
  private calculateAverages(): void {
    if (this.summary.totalTransactions > 0) {
      const totalAmount = this.summary.totalDeposits + this.summary.totalWithdrawals;
      this.summary.averageTransactionAmount = totalAmount / this.summary.totalTransactions;
    }
  }

  // 重置投影
  private resetProjections(): void {
    this.summary = {
      totalTransactions: 0,
      totalDeposits: 0,
      totalWithdrawals: 0,
      averageTransactionAmount: 0
    };
    this.monthly = {};
  }

  // 获取月份键
  private getMonthKey(date: Date): string {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
  }

  // 获取摘要投影
  getSummary() {
    return { ...this.summary };
  }

  // 获取月度投影
  getMonthly() {
    return { ...this.monthly };
  }

  // 生成报告
  generateReport(): {
    summary: any;
    monthly: any;
    insights: string[];
  } {
    const insights: string[] = [];

    if (this.summary.totalDeposits > this.summary.totalWithdrawals) {
      insights.push('账户呈现净流入趋势');
    } else if (this.summary.totalWithdrawals > this.summary.totalDeposits) {
      insights.push('账户呈现净流出趋势');
    }

    if (this.summary.averageTransactionAmount > 1000) {
      insights.push('平均交易金额较高');
    }

    const monthlyKeys = Object.keys(this.monthly);
    if (monthlyKeys.length > 1) {
      const latestMonth = monthlyKeys[monthlyKeys.length - 1];
      const previousMonth = monthlyKeys[monthlyKeys.length - 2];
      
      if (this.monthly[latestMonth].transactions > this.monthly[previousMonth].transactions) {
        insights.push('最近一个月交易活跃度上升');
      }
    }

    return {
      summary: this.summary,
      monthly: this.monthly,
      insights
    };
  }
}

// 快照管理器
export class SnapshotManager {
  private snapshots: Map<string, {
    id: string;
    aggregateId: string;
    version: number;
    timestamp: Date;
    data: any;
  }> = new Map();

  // 创建快照
  createSnapshot(aggregateState: any): {
    id: string;
    aggregateId: string;
    version: number;
    timestamp: Date;
    data: any;
  } {
    const snapshot = {
      id: this.generateSnapshotId(),
      aggregateId: aggregateState.accountId,
      version: aggregateState.version,
      timestamp: new Date(),
      data: { ...aggregateState }
    };

    this.snapshots.set(snapshot.id, snapshot);
    return snapshot;
  }

  // 获取最新快照
  getLatestSnapshot(aggregateId: string): any | null {
    const snapshots = Array.from(this.snapshots.values())
      .filter(snapshot => snapshot.aggregateId === aggregateId)
      .sort((a, b) => b.version - a.version);

    return snapshots.length > 0 ? snapshots[0] : null;
  }

  // 获取所有快照
  getAllSnapshots(): any[] {
    return Array.from(this.snapshots.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  // 清理旧快照
  cleanupOldSnapshots(aggregateId: string, keepCount: number = 5): void {
    const snapshots = Array.from(this.snapshots.values())
      .filter(snapshot => snapshot.aggregateId === aggregateId)
      .sort((a, b) => b.version - a.version);

    if (snapshots.length > keepCount) {
      const toDelete = snapshots.slice(keepCount);
      toDelete.forEach(snapshot => {
        this.snapshots.delete(snapshot.id);
      });
    }
  }

  // 生成快照ID
  private generateSnapshotId(): string {
    return `snap-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 事件溯源仓储
export class EventSourcedRepository {
  private eventStore: IEventStore;
  private snapshotManager: SnapshotManager;

  constructor(eventStore: IEventStore, snapshotManager: SnapshotManager) {
    this.eventStore = eventStore;
    this.snapshotManager = snapshotManager;
  }

  // 保存聚合
  save(aggregate: BankAccount): void {
    // 事件已经在聚合内部保存到事件存储
    // 这里可以添加额外的保存逻辑，如创建快照
    
    const state = aggregate.getState();
    if (state.version % 10 === 0) { // 每10个版本创建一次快照
      this.snapshotManager.createSnapshot(state);
    }
  }

  // 加载聚合
  load(aggregateId: string): BankAccount | null {
    // 尝试从快照加载
    const snapshot = this.snapshotManager.getLatestSnapshot(aggregateId);
    let fromVersion = 0;
    let account: BankAccount;

    if (snapshot) {
      // 从快照重建
      account = BankAccount.fromEvents(aggregateId, [], this.eventStore);
      account.applyEvent({
        id: 'snapshot-restore',
        type: 'AccountCreated',
        aggregateId,
        version: snapshot.version,
        timestamp: snapshot.timestamp,
        data: snapshot.data
      } as any);
      fromVersion = snapshot.version + 1;
    } else {
      // 从头开始重建
      const events = this.eventStore.getEvents(aggregateId) as AccountEvent[];
      if (events.length === 0) {
        return null;
      }
      account = BankAccount.fromEvents(aggregateId, events, this.eventStore);
      return account;
    }

    // 应用快照之后的事件
    const remainingEvents = this.eventStore.getEventsFromVersion(aggregateId, fromVersion) as AccountEvent[];
    remainingEvents.forEach(event => {
      account.applyEvent(event);
    });

    return account;
  }
}

// 导出类型
export type { BaseEvent, AccountEvent, IEventStore };
