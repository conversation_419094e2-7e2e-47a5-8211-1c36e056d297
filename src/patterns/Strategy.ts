/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 11:54:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 11:54:00
 * @FilePath     : /src/patterns/Strategy.ts
 * @Description  : 策略模式实现
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 商品接口
interface CartItem {
  name: string;
  price: number;
}

// 折扣策略接口
interface DiscountStrategy {
  calculate(
    total: number,
    items: CartItem[]
  ): { discount: number; details: string };
}

// 具体折扣策略实现
class NoDiscountStrategy implements DiscountStrategy {
  calculate(total: number): { discount: number; details: string } {
    return {
      discount: 0,
      details: "无折扣优惠",
    };
  }
}

class PercentageDiscountStrategy implements DiscountStrategy {
  private percentage: number;

  constructor(percentage: number) {
    this.percentage = percentage;
  }

  calculate(total: number): { discount: number; details: string } {
    const discount = total * (this.percentage / 100);
    return {
      discount,
      details: `${this.percentage}% 折扣优惠: ¥${total.toFixed(2)} × ${
        this.percentage
      }% = ¥${discount.toFixed(2)}`,
    };
  }
}

class FixedAmountDiscountStrategy implements DiscountStrategy {
  private amount: number;
  private minTotal: number;

  constructor(amount: number, minTotal: number) {
    this.amount = amount;
    this.minTotal = minTotal;
  }

  calculate(total: number): { discount: number; details: string } {
    if (total >= this.minTotal) {
      return {
        discount: this.amount,
        details: `满¥${this.minTotal.toFixed(2)}减¥${this.amount.toFixed(2)}优惠`,
      };
    }
    return {
      discount: 0,
      details: `需满¥${this.minTotal.toFixed(
        2
      )}才能享受¥${this.amount.toFixed(
        2
      )}优惠（当前¥${total.toFixed(2)}）`,
    };
  }
}

class VipDiscountStrategy implements DiscountStrategy {
  calculate(
    total: number,
    items: CartItem[]
  ): { discount: number; details: string } {
    const baseDiscount = total * 0.15; // VIP 85折
    
    // 额外优惠：每满100元再减5元
    const extraDiscount = Math.floor(total / 100) * 5;
    const totalDiscount = baseDiscount + extraDiscount;

    return {
      discount: totalDiscount,
      details: `VIP专享: 85折优惠¥${baseDiscount.toFixed(2)} + 每满100减5优惠¥${extraDiscount.toFixed(2)} = 总优惠¥${totalDiscount.toFixed(2)}`,
    };
  }
}

// 折扣计算器上下文
class DiscountCalculator {
  private strategy: DiscountStrategy;

  constructor(strategy: DiscountStrategy) {
    this.strategy = strategy;
  }

  setStrategy(strategy: DiscountStrategy): void {
    this.strategy = strategy;
  }

  calculateDiscount(
    total: number,
    items: CartItem[]
  ): { discount: number; details: string } {
    return this.strategy.calculate(total, items);
  }
}

// 策略工厂
class StrategyFactory {
  static createStrategy(type: string): DiscountStrategy {
    switch (type) {
      case "none":
        return new NoDiscountStrategy();
      case "percentage":
        return new PercentageDiscountStrategy(10); // 默认10%折扣
      case "fixed":
        return new FixedAmountDiscountStrategy(20, 100); // 满100减20
      case "vip":
        return new VipDiscountStrategy();
      default:
        return new NoDiscountStrategy();
    }
  }

  static getSupportedStrategies(): Array<{
    id: string;
    name: string;
    description: string;
  }> {
    return [
      { id: "none", name: "无折扣", description: "原价购买" },
      { id: "percentage", name: "百分比折扣", description: "享受10%折扣" },
      { id: "fixed", name: "满减优惠", description: "满100元减20元" },
      { id: "vip", name: "VIP专享", description: "85折 + 每满100减5" },
    ];
  }
}

// 导出类型和工厂
export { StrategyFactory, DiscountCalculator };
export type CartItemType = CartItem;
export type DiscountStrategyType = DiscountStrategy;
export type DiscountCalculatorType = DiscountCalculator;
