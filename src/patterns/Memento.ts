// 备忘录模式 (Memento Pattern) - 文档编辑器撤销/重做系统

// 备忘录接口
interface Memento {
  getState(): any
  getTimestamp(): Date
  getDescription(): string
}

// 具体备忘录
class DocumentMemento implements Memento {
  private state: DocumentState
  private timestamp: Date
  private description: string

  constructor(state: DocumentState, description: string) {
    this.state = { ...state } // 深拷贝状态
    this.timestamp = new Date()
    this.description = description
  }

  getState(): DocumentState {
    return { ...this.state }
  }

  getTimestamp(): Date {
    return this.timestamp
  }

  getDescription(): string {
    return this.description
  }
}

// 文档状态接口
interface DocumentState {
  content: string
  fontSize: number
  fontFamily: string
  textColor: string
  backgroundColor: string
  wordCount: number
}

// 原发器 - 文档编辑器
export class DocumentEditor {
  private state: DocumentState = {
    content: '',
    fontSize: 14,
    fontFamily: 'Arial',
    textColor: '#000000',
    backgroundColor: '#ffffff',
    wordCount: 0
  }

  // 创建备忘录
  createMemento(description: string): Memento {
    return new DocumentMemento(this.state, description)
  }

  // 恢复状态
  restoreFromMemento(memento: Memento): void {
    this.state = memento.getState()
  }

  // 文档操作方法
  setContent(content: string): void {
    this.state.content = content
    this.state.wordCount = content.split(/\s+/).filter(word => word.length > 0).length
  }

  appendContent(text: string): void {
    this.state.content += text
    this.state.wordCount = this.state.content.split(/\s+/).filter(word => word.length > 0).length
  }

  setFontSize(size: number): void {
    this.state.fontSize = Math.max(8, Math.min(72, size))
  }

  setFontFamily(family: string): void {
    this.state.fontFamily = family
  }

  setTextColor(color: string): void {
    this.state.textColor = color
  }

  setBackgroundColor(color: string): void {
    this.state.backgroundColor = color
  }

  // 获取当前状态
  getContent(): string {
    return this.state.content
  }

  getFontSize(): number {
    return this.state.fontSize
  }

  getFontFamily(): string {
    return this.state.fontFamily
  }

  getTextColor(): string {
    return this.state.textColor
  }

  getBackgroundColor(): string {
    return this.state.backgroundColor
  }

  getWordCount(): number {
    return this.state.wordCount
  }

  getState(): DocumentState {
    return { ...this.state }
  }
}

// 管理者 - 历史记录管理器
export class HistoryManager {
  private mementos: Memento[] = []
  private currentIndex: number = -1
  private maxHistorySize: number = 50

  constructor(maxHistorySize?: number) {
    if (maxHistorySize !== undefined) {
      this.maxHistorySize = maxHistorySize
    }
  }

  // 保存状态
  save(memento: Memento): void {
    // 如果当前不在历史记录的末尾，删除后面的记录
    if (this.currentIndex < this.mementos.length - 1) {
      this.mementos = this.mementos.slice(0, this.currentIndex + 1)
    }

    this.mementos.push(memento)
    this.currentIndex++

    // 限制历史记录大小
    if (this.mementos.length > this.maxHistorySize) {
      this.mementos.shift()
      this.currentIndex--
    }
  }

  // 撤销
  undo(): Memento | null {
    if (this.canUndo()) {
      this.currentIndex--
      return this.mementos[this.currentIndex]
    }
    return null
  }

  // 重做
  redo(): Memento | null {
    if (this.canRedo()) {
      this.currentIndex++
      return this.mementos[this.currentIndex]
    }
    return null
  }

  // 检查是否可以撤销
  canUndo(): boolean {
    return this.currentIndex > 0
  }

  // 检查是否可以重做
  canRedo(): boolean {
    return this.currentIndex < this.mementos.length - 1
  }

  // 获取历史记录
  getHistory(): Array<{ description: string; timestamp: Date; isCurrent: boolean }> {
    return this.mementos.map((memento, index) => ({
      description: memento.getDescription(),
      timestamp: memento.getTimestamp(),
      isCurrent: index === this.currentIndex
    }))
  }

  // 跳转到指定历史记录
  jumpTo(index: number): Memento | null {
    if (index >= 0 && index < this.mementos.length) {
      this.currentIndex = index
      return this.mementos[index]
    }
    return null
  }

  // 清空历史记录
  clear(): void {
    this.mementos = []
    this.currentIndex = -1
  }

  // 获取统计信息
  getStats(): {
    totalRecords: number
    currentIndex: number
    canUndo: boolean
    canRedo: boolean
    memoryUsage: string
  } {
    return {
      totalRecords: this.mementos.length,
      currentIndex: this.currentIndex,
      canUndo: this.canUndo(),
      canRedo: this.canRedo(),
      memoryUsage: `${this.mementos.length}/${this.maxHistorySize}`
    }
  }
}

// 文档编辑器应用 - 整合编辑器和历史管理
export class DocumentApp {
  private editor: DocumentEditor
  private historyManager: HistoryManager
  private autoSaveEnabled: boolean = true

  constructor(maxHistorySize?: number) {
    this.editor = new DocumentEditor()
    this.historyManager = new HistoryManager(maxHistorySize)
    
    // 保存初始状态
    this.saveState('文档创建')
  }

  // 保存当前状态
  saveState(description: string): void {
    const memento = this.editor.createMemento(description)
    this.historyManager.save(memento)
  }

  // 执行操作并自动保存状态
  executeCommand(command: () => void, description: string): void {
    command()
    if (this.autoSaveEnabled) {
      this.saveState(description)
    }
  }

  // 撤销操作
  undo(): boolean {
    const memento = this.historyManager.undo()
    if (memento) {
      this.editor.restoreFromMemento(memento)
      return true
    }
    return false
  }

  // 重做操作
  redo(): boolean {
    const memento = this.historyManager.redo()
    if (memento) {
      this.editor.restoreFromMemento(memento)
      return true
    }
    return false
  }

  // 跳转到指定历史记录
  jumpToHistory(index: number): boolean {
    const memento = this.historyManager.jumpTo(index)
    if (memento) {
      this.editor.restoreFromMemento(memento)
      return true
    }
    return false
  }

  // 编辑器操作方法
  setContent(content: string): void {
    this.executeCommand(() => this.editor.setContent(content), '设置文档内容')
  }

  appendText(text: string): void {
    this.executeCommand(() => this.editor.appendContent(text), `添加文本: "${text.substring(0, 20)}..."`)
  }

  setFontSize(size: number): void {
    this.executeCommand(() => this.editor.setFontSize(size), `设置字体大小: ${size}px`)
  }

  setFontFamily(family: string): void {
    this.executeCommand(() => this.editor.setFontFamily(family), `设置字体: ${family}`)
  }

  setTextColor(color: string): void {
    this.executeCommand(() => this.editor.setTextColor(color), `设置文字颜色: ${color}`)
  }

  setBackgroundColor(color: string): void {
    this.executeCommand(() => this.editor.setBackgroundColor(color), `设置背景颜色: ${color}`)
  }

  // 获取编辑器状态
  getEditor(): DocumentEditor {
    return this.editor
  }

  // 获取历史管理器
  getHistoryManager(): HistoryManager {
    return this.historyManager
  }

  // 设置自动保存
  setAutoSave(enabled: boolean): void {
    this.autoSaveEnabled = enabled
  }

  // 手动保存快照
  createSnapshot(description?: string): void {
    this.saveState(description || `手动快照 - ${new Date().toLocaleTimeString()}`)
  }

  // 获取应用状态
  getAppState(): {
    document: DocumentState
    history: ReturnType<HistoryManager['getStats']>
    autoSave: boolean
  } {
    return {
      document: this.editor.getState(),
      history: this.historyManager.getStats(),
      autoSave: this.autoSaveEnabled
    }
  }
}
