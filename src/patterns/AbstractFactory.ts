/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 12:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 12:00:00
 * @FilePath     : /src/patterns/AbstractFactory.ts
 * @Description  : 抽象工厂模式实现 - UI组件库主题系统
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 抽象产品接口 - 按钮
interface Button {
  render(): string;
  getStyle(): string;
  getType(): string;
}

// 抽象产品接口 - 输入框
interface Input {
  render(): string;
  getStyle(): string;
  getType(): string;
}

// 抽象产品接口 - 卡片
interface Card {
  render(): string;
  getStyle(): string;
  getType(): string;
}

// 具体产品实现 - 现代风格按钮
class ModernButton implements Button {
  render(): string {
    return '<button class="modern-btn">现代按钮</button>';
  }

  getStyle(): string {
    return 'border-radius: 8px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 24px; font-weight: 500; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);';
  }

  getType(): string {
    return '现代风格按钮';
  }
}

// 具体产品实现 - 现代风格输入框
class ModernInput implements Input {
  render(): string {
    return '<input class="modern-input" placeholder="现代输入框" />';
  }

  getStyle(): string {
    return 'border: 2px solid #e1e5e9; border-radius: 8px; padding: 12px 16px; font-size: 14px; transition: all 0.3s ease; background: #f8f9fa;';
  }

  getType(): string {
    return '现代风格输入框';
  }
}

// 具体产品实现 - 现代风格卡片
class ModernCard implements Card {
  render(): string {
    return '<div class="modern-card">现代卡片内容</div>';
  }

  getStyle(): string {
    return 'background: white; border-radius: 12px; padding: 24px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); border: 1px solid #f0f0f0;';
  }

  getType(): string {
    return '现代风格卡片';
  }
}

// 具体产品实现 - 经典风格按钮
class ClassicButton implements Button {
  render(): string {
    return '<button class="classic-btn">经典按钮</button>';
  }

  getStyle(): string {
    return 'border: 2px solid #333; background: #fff; color: #333; padding: 10px 20px; font-weight: bold; border-radius: 4px; cursor: pointer;';
  }

  getType(): string {
    return '经典风格按钮';
  }
}

// 具体产品实现 - 经典风格输入框
class ClassicInput implements Input {
  render(): string {
    return '<input class="classic-input" placeholder="经典输入框" />';
  }

  getStyle(): string {
    return 'border: 1px solid #ccc; padding: 8px 12px; font-size: 14px; border-radius: 4px; background: #fff;';
  }

  getType(): string {
    return '经典风格输入框';
  }
}

// 具体产品实现 - 经典风格卡片
class ClassicCard implements Card {
  render(): string {
    return '<div class="classic-card">经典卡片内容</div>';
  }

  getStyle(): string {
    return 'background: #f9f9f9; border: 1px solid #ddd; padding: 16px; border-radius: 4px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);';
  }

  getType(): string {
    return '经典风格卡片';
  }
}

// 具体产品实现 - 暗黑风格按钮
class DarkButton implements Button {
  render(): string {
    return '<button class="dark-btn">暗黑按钮</button>';
  }

  getStyle(): string {
    return 'background: #2d3748; color: #fff; border: 1px solid #4a5568; padding: 12px 24px; border-radius: 6px; font-weight: 500;';
  }

  getType(): string {
    return '暗黑风格按钮';
  }
}

// 具体产品实现 - 暗黑风格输入框
class DarkInput implements Input {
  render(): string {
    return '<input class="dark-input" placeholder="暗黑输入框" />';
  }

  getStyle(): string {
    return 'background: #2d3748; color: #fff; border: 1px solid #4a5568; padding: 12px 16px; border-radius: 6px; font-size: 14px;';
  }

  getType(): string {
    return '暗黑风格输入框';
  }
}

// 具体产品实现 - 暗黑风格卡片
class DarkCard implements Card {
  render(): string {
    return '<div class="dark-card">暗黑卡片内容</div>';
  }

  getStyle(): string {
    return 'background: #1a202c; color: #fff; border: 1px solid #2d3748; padding: 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);';
  }

  getType(): string {
    return '暗黑风格卡片';
  }
}

// 抽象工厂接口
interface UIComponentFactory {
  createButton(): Button;
  createInput(): Input;
  createCard(): Card;
  getThemeName(): string;
}

// 具体工厂实现 - 现代主题工厂
class ModernThemeFactory implements UIComponentFactory {
  createButton(): Button {
    return new ModernButton();
  }

  createInput(): Input {
    return new ModernInput();
  }

  createCard(): Card {
    return new ModernCard();
  }

  getThemeName(): string {
    return '现代主题';
  }
}

// 具体工厂实现 - 经典主题工厂
class ClassicThemeFactory implements UIComponentFactory {
  createButton(): Button {
    return new ClassicButton();
  }

  createInput(): Input {
    return new ClassicInput();
  }

  createCard(): Card {
    return new ClassicCard();
  }

  getThemeName(): string {
    return '经典主题';
  }
}

// 具体工厂实现 - 暗黑主题工厂
class DarkThemeFactory implements UIComponentFactory {
  createButton(): Button {
    return new DarkButton();
  }

  createInput(): Input {
    return new DarkInput();
  }

  createCard(): Card {
    return new DarkCard();
  }

  getThemeName(): string {
    return '暗黑主题';
  }
}

// 工厂创建器
class ThemeFactoryCreator {
  static createFactory(theme: string): UIComponentFactory {
    switch (theme) {
      case 'modern':
        return new ModernThemeFactory();
      case 'classic':
        return new ClassicThemeFactory();
      case 'dark':
        return new DarkThemeFactory();
      default:
        throw new Error(`不支持的主题类型: ${theme}`);
    }
  }

  static getSupportedThemes(): string[] {
    return ['modern', 'classic', 'dark'];
  }

  static getThemeDisplayNames(): Record<string, string> {
    return {
      modern: '现代主题',
      classic: '经典主题',
      dark: '暗黑主题'
    };
  }
}

// 导出工厂和类型
export const themeFactoryCreator = ThemeFactoryCreator;
export type UIComponentFactoryType = UIComponentFactory;
export type ButtonType = Button;
export type InputType = Input;
export type CardType = Card;
export type ThemeFactoryCreatorType = typeof ThemeFactoryCreator;
