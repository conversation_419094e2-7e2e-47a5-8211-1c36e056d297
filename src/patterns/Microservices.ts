/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-27 20:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-27 20:00:00
 * @FilePath     : /src/patterns/Microservices.ts
 * @Description  : 微服务模式的TypeScript实现
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-27 20:00:00
 */

// 服务接口
export interface Service {
  id: string;
  name: string;
  address: string;
  port: number;
  status: 'running' | 'stopped' | 'starting' | 'error';
  instances: number;
  cpu: number;
  memory: number;
  endpoints: string[];
  load: number;
  receiving: boolean;
}

// 服务健康检查接口
export interface HealthCheck {
  serviceId: string;
  status: 'healthy' | 'unhealthy';
  lastCheck: Date;
  responseTime: number;
}

// API请求接口
export interface APIRequest {
  id: string;
  method: string;
  path: string;
  headers: Record<string, string>;
  body?: any;
  timestamp: Date;
}

// API响应接口
export interface APIResponse {
  id: string;
  status: number;
  headers: Record<string, string>;
  body?: any;
  responseTime: number;
  timestamp: Date;
}

// 负载均衡策略
export type LoadBalancingStrategy = 'round-robin' | 'least-connections' | 'weighted' | 'random';

// 服务注册中心
export class ServiceRegistry {
  private services: Map<string, Service> = new Map();
  private healthChecks: Map<string, HealthCheck> = new Map();

  // 注册服务
  register(service: Service): void {
    this.services.set(service.id, { ...service });
    
    // 初始化健康检查
    this.healthChecks.set(service.id, {
      serviceId: service.id,
      status: 'healthy',
      lastCheck: new Date(),
      responseTime: 0
    });

    console.log(`Service registered: ${service.name} at ${service.address}:${service.port}`);
  }

  // 注销服务
  deregister(serviceId: string): void {
    const service = this.services.get(serviceId);
    if (service) {
      this.services.delete(serviceId);
      this.healthChecks.delete(serviceId);
      console.log(`Service deregistered: ${service.name}`);
    }
  }

  // 获取服务
  getService(serviceId: string): Service | undefined {
    return this.services.get(serviceId);
  }

  // 获取所有服务
  getAllServices(): Service[] {
    return Array.from(this.services.values());
  }

  // 根据名称查找服务
  findServicesByName(name: string): Service[] {
    return Array.from(this.services.values()).filter(service => 
      service.name.toLowerCase().includes(name.toLowerCase())
    );
  }

  // 获取健康的服务
  getHealthyServices(): Service[] {
    return Array.from(this.services.values()).filter(service => {
      const healthCheck = this.healthChecks.get(service.id);
      return service.status === 'running' && healthCheck?.status === 'healthy';
    });
  }

  // 更新服务健康状态
  updateHealthStatus(serviceId: string, status: 'healthy' | 'unhealthy', responseTime: number = 0): void {
    const healthCheck = this.healthChecks.get(serviceId);
    if (healthCheck) {
      healthCheck.status = status;
      healthCheck.lastCheck = new Date();
      healthCheck.responseTime = responseTime;
    }
  }

  // 获取服务健康检查信息
  getHealthCheck(serviceId: string): HealthCheck | undefined {
    return this.healthChecks.get(serviceId);
  }

  // 获取所有健康检查信息
  getAllHealthChecks(): HealthCheck[] {
    return Array.from(this.healthChecks.values());
  }
}

// 负载均衡器
export class LoadBalancer {
  private strategy: LoadBalancingStrategy = 'round-robin';
  private currentIndex: number = 0;
  private connectionCounts: Map<string, number> = new Map();

  constructor(strategy: LoadBalancingStrategy = 'round-robin') {
    this.strategy = strategy;
  }

  // 选择服务实例
  selectService(services: Service[]): Service | null {
    if (services.length === 0) {
      return null;
    }

    switch (this.strategy) {
      case 'round-robin':
        return this.roundRobin(services);
      case 'least-connections':
        return this.leastConnections(services);
      case 'weighted':
        return this.weighted(services);
      case 'random':
        return this.random(services);
      default:
        return this.roundRobin(services);
    }
  }

  // 轮询策略
  private roundRobin(services: Service[]): Service {
    const service = services[this.currentIndex % services.length];
    this.currentIndex = (this.currentIndex + 1) % services.length;
    return service;
  }

  // 最少连接策略
  private leastConnections(services: Service[]): Service {
    let selectedService = services[0];
    let minConnections = this.connectionCounts.get(selectedService.id) || 0;

    for (const service of services) {
      const connections = this.connectionCounts.get(service.id) || 0;
      if (connections < minConnections) {
        minConnections = connections;
        selectedService = service;
      }
    }

    return selectedService;
  }

  // 加权策略
  private weighted(services: Service[]): Service {
    // 基于CPU和内存使用率的权重计算
    const weights = services.map(service => {
      const cpuWeight = (100 - service.cpu) / 100;
      const memoryWeight = (500 - service.memory) / 500;
      return Math.max(0.1, (cpuWeight + memoryWeight) / 2);
    });

    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;

    for (let i = 0; i < services.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return services[i];
      }
    }

    return services[services.length - 1];
  }

  // 随机策略
  private random(services: Service[]): Service {
    const index = Math.floor(Math.random() * services.length);
    return services[index];
  }

  // 增加连接计数
  incrementConnections(serviceId: string): void {
    const current = this.connectionCounts.get(serviceId) || 0;
    this.connectionCounts.set(serviceId, current + 1);
  }

  // 减少连接计数
  decrementConnections(serviceId: string): void {
    const current = this.connectionCounts.get(serviceId) || 0;
    this.connectionCounts.set(serviceId, Math.max(0, current - 1));
  }

  // 设置负载均衡策略
  setStrategy(strategy: LoadBalancingStrategy): void {
    this.strategy = strategy;
    this.currentIndex = 0; // 重置轮询索引
  }

  // 获取当前策略
  getStrategy(): LoadBalancingStrategy {
    return this.strategy;
  }
}

// API网关
export class APIGateway {
  private serviceRegistry: ServiceRegistry;
  private loadBalancer: LoadBalancer;
  private requestCount: number = 0;
  private routes: Map<string, string> = new Map(); // path -> serviceId

  constructor(serviceRegistry: ServiceRegistry, loadBalancer: LoadBalancer) {
    this.serviceRegistry = serviceRegistry;
    this.loadBalancer = loadBalancer;
    this.initializeRoutes();
  }

  // 初始化路由规则
  private initializeRoutes(): void {
    this.routes.set('/users', 'user-service');
    this.routes.set('/auth', 'user-service');
    this.routes.set('/profile', 'user-service');
    this.routes.set('/orders', 'order-service');
    this.routes.set('/cart', 'order-service');
    this.routes.set('/checkout', 'order-service');
    this.routes.set('/payments', 'payment-service');
    this.routes.set('/refunds', 'payment-service');
    this.routes.set('/billing', 'payment-service');
    this.routes.set('/notifications', 'notification-service');
    this.routes.set('/email', 'notification-service');
    this.routes.set('/sms', 'notification-service');
  }

  // 处理API请求
  async handleRequest(request: APIRequest): Promise<APIResponse> {
    this.requestCount++;
    const startTime = Date.now();

    try {
      // 路由解析
      const serviceId = this.resolveRoute(request.path);
      if (!serviceId) {
        return this.createErrorResponse(request.id, 404, 'Route not found', startTime);
      }

      // 服务发现
      const services = this.serviceRegistry.findServicesByName(serviceId);
      const healthyServices = services.filter(service => {
        const healthCheck = this.serviceRegistry.getHealthCheck(service.id);
        return service.status === 'running' && healthCheck?.status === 'healthy';
      });

      if (healthyServices.length === 0) {
        return this.createErrorResponse(request.id, 503, 'Service unavailable', startTime);
      }

      // 负载均衡
      const selectedService = this.loadBalancer.selectService(healthyServices);
      if (!selectedService) {
        return this.createErrorResponse(request.id, 503, 'No available service instance', startTime);
      }

      // 转发请求
      this.loadBalancer.incrementConnections(selectedService.id);
      
      try {
        const response = await this.forwardRequest(request, selectedService);
        return response;
      } finally {
        this.loadBalancer.decrementConnections(selectedService.id);
      }

    } catch (error) {
      return this.createErrorResponse(request.id, 500, 'Internal server error', startTime);
    }
  }

  // 路由解析
  private resolveRoute(path: string): string | null {
    // 简单的路径匹配
    for (const [routePath, serviceId] of this.routes.entries()) {
      if (path.startsWith(routePath)) {
        return serviceId;
      }
    }
    return null;
  }

  // 转发请求到目标服务
  private async forwardRequest(request: APIRequest, service: Service): Promise<APIResponse> {
    const startTime = Date.now();
    
    // 模拟网络延迟和处理时间
    const baseLatency = 50 + Math.random() * 100; // 50-150ms
    const serviceLatency = service.cpu * 2; // CPU使用率影响响应时间
    const totalLatency = baseLatency + serviceLatency;
    
    await new Promise(resolve => setTimeout(resolve, totalLatency));

    // 模拟服务响应
    const responseTime = Date.now() - startTime;
    
    // 根据服务负载模拟成功率
    const successRate = Math.max(0.7, 1 - (service.cpu / 200) - (service.memory / 1000));
    const isSuccess = Math.random() < successRate;

    if (isSuccess) {
      return {
        id: this.generateId(),
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Service-Id': service.id,
          'X-Service-Instance': service.address
        },
        body: {
          message: 'Success',
          service: service.name,
          timestamp: new Date().toISOString()
        },
        responseTime,
        timestamp: new Date()
      };
    } else {
      return {
        id: this.generateId(),
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'X-Service-Id': service.id
        },
        body: {
          error: 'Service error',
          message: 'Internal service error'
        },
        responseTime,
        timestamp: new Date()
      };
    }
  }

  // 创建错误响应
  private createErrorResponse(requestId: string, status: number, message: string, startTime: number): APIResponse {
    return {
      id: this.generateId(),
      status,
      headers: {
        'Content-Type': 'application/json'
      },
      body: {
        error: message,
        requestId
      },
      responseTime: Date.now() - startTime,
      timestamp: new Date()
    };
  }

  // 添加路由
  addRoute(path: string, serviceId: string): void {
    this.routes.set(path, serviceId);
  }

  // 移除路由
  removeRoute(path: string): void {
    this.routes.delete(path);
  }

  // 获取所有路由
  getRoutes(): Map<string, string> {
    return new Map(this.routes);
  }

  // 获取请求统计
  getRequestCount(): number {
    return this.requestCount;
  }

  // 重置请求计数
  resetRequestCount(): void {
    this.requestCount = 0;
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 熔断器
export class CircuitBreaker {
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';
  
  constructor(
    private failureThreshold: number = 5,
    private timeout: number = 60000, // 1分钟
    private successThreshold: number = 3
  ) {}

  // 执行请求
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'half-open';
      } else {
        throw new Error('Circuit breaker is open');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  // 成功处理
  private onSuccess(): void {
    this.failureCount = 0;
    if (this.state === 'half-open') {
      this.state = 'closed';
    }
  }

  // 失败处理
  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.failureCount >= this.failureThreshold) {
      this.state = 'open';
    }
  }

  // 获取状态
  getState(): 'closed' | 'open' | 'half-open' {
    return this.state;
  }

  // 重置熔断器
  reset(): void {
    this.failureCount = 0;
    this.lastFailureTime = 0;
    this.state = 'closed';
  }
}

// 微服务系统主类
export class MicroserviceSystem {
  private serviceRegistry: ServiceRegistry;
  private loadBalancer: LoadBalancer;
  private apiGateway: APIGateway;
  private circuitBreaker: CircuitBreaker;

  constructor() {
    this.serviceRegistry = new ServiceRegistry();
    this.loadBalancer = new LoadBalancer('round-robin');
    this.apiGateway = new APIGateway(this.serviceRegistry, this.loadBalancer);
    this.circuitBreaker = new CircuitBreaker();
  }

  // 注册服务
  registerService(service: Service): void {
    this.serviceRegistry.register(service);
  }

  // 注销服务
  deregisterService(serviceId: string): void {
    this.serviceRegistry.deregister(serviceId);
  }

  // 调用API
  async callAPI(method: string, path: string, body?: any): Promise<APIResponse> {
    const request: APIRequest = {
      id: this.generateId(),
      method,
      path,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'MicroserviceSystem/1.0'
      },
      body,
      timestamp: new Date()
    };

    return await this.circuitBreaker.execute(async () => {
      return await this.apiGateway.handleRequest(request);
    });
  }

  // 获取服务注册中心
  getServiceRegistry(): ServiceRegistry {
    return this.serviceRegistry;
  }

  // 获取负载均衡器
  getLoadBalancer(): LoadBalancer {
    return this.loadBalancer;
  }

  // 获取API网关
  getAPIGateway(): APIGateway {
    return this.apiGateway;
  }

  // 获取熔断器
  getCircuitBreaker(): CircuitBreaker {
    return this.circuitBreaker;
  }

  // 健康检查
  async performHealthCheck(serviceId: string): Promise<boolean> {
    const service = this.serviceRegistry.getService(serviceId);
    if (!service || service.status !== 'running') {
      this.serviceRegistry.updateHealthStatus(serviceId, 'unhealthy');
      return false;
    }

    try {
      // 模拟健康检查请求
      const startTime = Date.now();
      await new Promise(resolve => setTimeout(resolve, 10 + Math.random() * 40));
      const responseTime = Date.now() - startTime;

      // 基于服务负载判断健康状态
      const isHealthy = service.cpu < 90 && service.memory < 400;
      
      this.serviceRegistry.updateHealthStatus(
        serviceId, 
        isHealthy ? 'healthy' : 'unhealthy',
        responseTime
      );

      return isHealthy;
    } catch (error) {
      this.serviceRegistry.updateHealthStatus(serviceId, 'unhealthy');
      return false;
    }
  }

  // 批量健康检查
  async performBatchHealthCheck(): Promise<Map<string, boolean>> {
    const services = this.serviceRegistry.getAllServices();
    const results = new Map<string, boolean>();

    const promises = services.map(async service => {
      const isHealthy = await this.performHealthCheck(service.id);
      results.set(service.id, isHealthy);
    });

    await Promise.all(promises);
    return results;
  }

  // 获取系统统计信息
  getSystemStats(): {
    totalServices: number;
    runningServices: number;
    healthyServices: number;
    totalRequests: number;
    circuitBreakerState: string;
  } {
    const allServices = this.serviceRegistry.getAllServices();
    const runningServices = allServices.filter(s => s.status === 'running');
    const healthyServices = this.serviceRegistry.getHealthyServices();

    return {
      totalServices: allServices.length,
      runningServices: runningServices.length,
      healthyServices: healthyServices.length,
      totalRequests: this.apiGateway.getRequestCount(),
      circuitBreakerState: this.circuitBreaker.getState()
    };
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 导出类型
export type { 
  Service, 
  HealthCheck, 
  APIRequest, 
  APIResponse, 
  LoadBalancingStrategy 
};
