/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 12:30:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 12:30:00
 * @FilePath     : /src/patterns/Flyweight.ts
 * @Description  : 享元模式实现 - 文本编辑器字符渲染系统
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 享元接口
interface CharacterFlyweight {
  render(x: number, y: number, fontSize: number, color: string): string;
  getCharacter(): string;
  getFont(): string;
  getStyle(): string;
}

// 具体享元类 - 字符
class Character implements CharacterFlyweight {
  private character: string; // 内在状态
  private font: string;      // 内在状态
  private style: string;     // 内在状态

  constructor(character: string, font: string, style: string) {
    this.character = character;
    this.font = font;
    this.style = style;
  }

  // 渲染方法，接受外在状态作为参数
  render(x: number, y: number, fontSize: number, color: string): string {
    return `渲染字符 '${this.character}' - 位置:(${x},${y}) 字体:${this.font} 样式:${this.style} 大小:${fontSize}px 颜色:${color}`;
  }

  getCharacter(): string {
    return this.character;
  }

  getFont(): string {
    return this.font;
  }

  getStyle(): string {
    return this.style;
  }

  // 获取内在状态的唯一标识
  getIntrinsicKey(): string {
    return `${this.character}-${this.font}-${this.style}`;
  }
}

// 享元工厂
class CharacterFlyweightFactory {
  private static flyweights: Map<string, CharacterFlyweight> = new Map();
  private static createdCount: number = 0;

  // 获取享元对象
  static getFlyweight(character: string, font: string, style: string): CharacterFlyweight {
    const key = `${character}-${font}-${style}`;
    
    if (!this.flyweights.has(key)) {
      this.flyweights.set(key, new Character(character, font, style));
      this.createdCount++;
      console.log(`创建新的享元对象: ${key} (总计: ${this.createdCount})`);
    }
    
    return this.flyweights.get(key)!;
  }

  // 获取已创建的享元对象数量
  static getCreatedCount(): number {
    return this.createdCount;
  }

  // 获取享元池大小
  static getPoolSize(): number {
    return this.flyweights.size;
  }

  // 获取所有享元对象的信息
  static getFlyweightInfo(): string[] {
    const info: string[] = [];
    this.flyweights.forEach((flyweight, key) => {
      info.push(`${key}: ${flyweight.getCharacter()}`);
    });
    return info;
  }

  // 清空享元池（用于测试）
  static clearPool(): void {
    this.flyweights.clear();
    this.createdCount = 0;
  }

  // 获取内存使用统计
  static getMemoryStats(): {
    poolSize: number;
    createdCount: number;
    memoryEfficiency: string;
  } {
    return {
      poolSize: this.flyweights.size,
      createdCount: this.createdCount,
      memoryEfficiency: `节省了 ${((1 - this.flyweights.size / Math.max(this.createdCount, 1)) * 100).toFixed(1)}% 的内存`
    };
  }
}

// 外在状态类 - 字符位置和样式
class CharacterContext {
  constructor(
    public x: number,
    public y: number,
    public fontSize: number,
    public color: string
  ) {}

  // 更新位置
  updatePosition(x: number, y: number): void {
    this.x = x;
    this.y = y;
  }

  // 更新样式
  updateStyle(fontSize: number, color: string): void {
    this.fontSize = fontSize;
    this.color = color;
  }

  getInfo(): string {
    return `位置:(${this.x},${this.y}) 大小:${this.fontSize}px 颜色:${this.color}`;
  }
}

// 文档字符类 - 组合享元和外在状态
class DocumentCharacter {
  private flyweight: CharacterFlyweight;
  private context: CharacterContext;

  constructor(
    character: string,
    font: string,
    style: string,
    x: number,
    y: number,
    fontSize: number,
    color: string
  ) {
    this.flyweight = CharacterFlyweightFactory.getFlyweight(character, font, style);
    this.context = new CharacterContext(x, y, fontSize, color);
  }

  // 渲染字符
  render(): string {
    return this.flyweight.render(
      this.context.x,
      this.context.y,
      this.context.fontSize,
      this.context.color
    );
  }

  // 移动字符
  moveTo(x: number, y: number): void {
    this.context.updatePosition(x, y);
  }

  // 更新样式
  updateStyle(fontSize: number, color: string): void {
    this.context.updateStyle(fontSize, color);
  }

  // 获取字符信息
  getCharacterInfo(): string {
    return `字符:'${this.flyweight.getCharacter()}' 字体:${this.flyweight.getFont()} 样式:${this.flyweight.getStyle()}`;
  }

  // 获取上下文信息
  getContextInfo(): string {
    return this.context.getInfo();
  }

  // 获取完整信息
  getFullInfo(): string {
    return `${this.getCharacterInfo()} ${this.getContextInfo()}`;
  }

  getCharacter(): string {
    return this.flyweight.getCharacter();
  }

  getX(): number {
    return this.context.x;
  }

  getY(): number {
    return this.context.y;
  }
}

// 文本文档类
class TextDocument {
  private characters: DocumentCharacter[] = [];
  private defaultFont: string = 'Arial';
  private defaultStyle: string = 'normal';
  private defaultFontSize: number = 14;
  private defaultColor: string = '#000000';

  // 添加字符
  addCharacter(
    character: string,
    x: number,
    y: number,
    font?: string,
    style?: string,
    fontSize?: number,
    color?: string
  ): void {
    const docChar = new DocumentCharacter(
      character,
      font || this.defaultFont,
      style || this.defaultStyle,
      x,
      y,
      fontSize || this.defaultFontSize,
      color || this.defaultColor
    );
    this.characters.push(docChar);
  }

  // 添加文本
  addText(
    text: string,
    startX: number,
    startY: number,
    font?: string,
    style?: string,
    fontSize?: number,
    color?: string
  ): void {
    const charWidth = (fontSize || this.defaultFontSize) * 0.6; // 估算字符宽度
    
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      const x = startX + (i * charWidth);
      this.addCharacter(char, x, startY, font, style, fontSize, color);
    }
  }

  // 渲染整个文档
  render(): string[] {
    return this.characters.map(char => char.render());
  }

  // 获取文档统计信息
  getStatistics(): {
    totalCharacters: number;
    uniqueCharacters: number;
    flyweightPoolSize: number;
    memoryEfficiency: string;
    characterDistribution: Record<string, number>;
  } {
    const characterDistribution: Record<string, number> = {};
    const uniqueChars = new Set<string>();

    this.characters.forEach(docChar => {
      const char = docChar.getCharacter();
      uniqueChars.add(char);
      characterDistribution[char] = (characterDistribution[char] || 0) + 1;
    });

    const memoryStats = CharacterFlyweightFactory.getMemoryStats();

    return {
      totalCharacters: this.characters.length,
      uniqueCharacters: uniqueChars.size,
      flyweightPoolSize: CharacterFlyweightFactory.getPoolSize(),
      memoryEfficiency: memoryStats.memoryEfficiency,
      characterDistribution
    };
  }

  // 查找字符
  findCharacters(character: string): DocumentCharacter[] {
    return this.characters.filter(char => char.getCharacter() === character);
  }

  // 移动所有字符
  moveAllCharacters(deltaX: number, deltaY: number): void {
    this.characters.forEach(char => {
      char.moveTo(char.getX() + deltaX, char.getY() + deltaY);
    });
  }

  // 更改所有字符的样式
  changeAllCharactersStyle(fontSize: number, color: string): void {
    this.characters.forEach(char => {
      char.updateStyle(fontSize, color);
    });
  }

  // 获取文档内容（仅字符）
  getContent(): string {
    return this.characters.map(char => char.getCharacter()).join('');
  }

  // 清空文档
  clear(): void {
    this.characters = [];
  }

  // 获取字符数量
  getCharacterCount(): number {
    return this.characters.length;
  }

  // 设置默认样式
  setDefaultStyle(font: string, style: string, fontSize: number, color: string): void {
    this.defaultFont = font;
    this.defaultStyle = style;
    this.defaultFontSize = fontSize;
    this.defaultColor = color;
  }
}

// 文档管理器
class DocumentManager {
  private documents: Map<string, TextDocument> = new Map();

  // 创建新文档
  createDocument(name: string): TextDocument {
    const document = new TextDocument();
    this.documents.set(name, document);
    return document;
  }

  // 获取文档
  getDocument(name: string): TextDocument | null {
    return this.documents.get(name) || null;
  }

  // 删除文档
  deleteDocument(name: string): boolean {
    return this.documents.delete(name);
  }

  // 获取所有文档名称
  getDocumentNames(): string[] {
    return Array.from(this.documents.keys());
  }

  // 获取总体统计信息
  getOverallStatistics(): {
    totalDocuments: number;
    totalCharacters: number;
    flyweightPoolSize: number;
    memoryStats: any;
  } {
    let totalCharacters = 0;
    this.documents.forEach(doc => {
      totalCharacters += doc.getCharacterCount();
    });

    return {
      totalDocuments: this.documents.size,
      totalCharacters,
      flyweightPoolSize: CharacterFlyweightFactory.getPoolSize(),
      memoryStats: CharacterFlyweightFactory.getMemoryStats()
    };
  }

  // 创建示例文档
  createSampleDocument(): TextDocument {
    const doc = this.createDocument('示例文档');
    
    // 添加标题
    doc.addText('设计模式学习', 50, 50, 'Arial', 'bold', 24, '#333333');
    
    // 添加正文
    doc.addText('享元模式是一种结构型设计模式，', 50, 100, 'Arial', 'normal', 16, '#666666');
    doc.addText('它通过共享技术有效地支持大量细粒度对象。', 50, 130, 'Arial', 'normal', 16, '#666666');
    
    // 添加代码示例
    doc.addText('class Flyweight {', 50, 180, 'Courier New', 'normal', 14, '#0066cc');
    doc.addText('  render() { ... }', 50, 200, 'Courier New', 'normal', 14, '#0066cc');
    doc.addText('}', 50, 220, 'Courier New', 'normal', 14, '#0066cc');

    return doc;
  }
}

// 导出类和工厂
export { DocumentManager, TextDocument, CharacterFlyweightFactory };
export type CharacterFlyweightType = CharacterFlyweight;
export type DocumentCharacterType = DocumentCharacter;
export type TextDocumentType = TextDocument;
export type DocumentManagerType = DocumentManager;
