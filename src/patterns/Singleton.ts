/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 11:43:08
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 11:45:43
 * @FilePath     : /src/patterns/Singleton.ts
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-26 11:43:08
 */
// 单例模式实现
class Logger {
  private static instance: Logger | null = null;
  private logs: string[] = [];
  private id: string;

  private constructor() {
    this.id = Math.random().toString(36).slice(2, 11);
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public log(message: string): void {
    const timestamp = new Date().toLocaleTimeString();
    this.logs.push(`[${timestamp}] ${message}`);
  }

  public getLogs(): string[] {
    return [...this.logs];
  }

  public clearLogs(): void {
    this.logs = [];
  }

  public getId(): string {
    return this.id;
  }
}

// 导出单例实例
export const logger = Logger.getInstance();

// 导出类型供 TypeScript 使用
export type LoggerType = Logger;
