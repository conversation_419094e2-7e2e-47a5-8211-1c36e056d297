// 模板方法模式 (Template Method Pattern) - 数据处理流程

// 抽象数据处理器
export abstract class DataProcessor {
  // 模板方法 - 定义算法骨架
  public processData(data: any[]): ProcessResult {
    console.log('开始数据处理流程...')
    
    const result: ProcessResult = {
      originalCount: data.length,
      processedCount: 0,
      errors: [],
      processedData: [],
      processingTime: 0,
      processorType: this.getProcessorType()
    }

    const startTime = Date.now()

    try {
      // 步骤1: 验证数据
      if (!this.validateData(data)) {
        throw new Error('数据验证失败')
      }

      // 步骤2: 预处理数据
      const preprocessedData = this.preprocessData(data)
      
      // 步骤3: 处理核心逻辑（由子类实现）
      const processedData = this.processCore(preprocessedData)
      
      // 步骤4: 后处理数据
      const finalData = this.postprocessData(processedData)
      
      // 步骤5: 保存结果（可选，由子类决定是否实现）
      if (this.shouldSaveResult()) {
        this.saveResult(finalData)
      }

      result.processedData = finalData
      result.processedCount = finalData.length
      
    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : String(error))
    }

    result.processingTime = Date.now() - startTime
    console.log(`数据处理完成，耗时: ${result.processingTime}ms`)
    
    return result
  }

  // 抽象方法 - 子类必须实现
  protected abstract processCore(data: any[]): any[]
  protected abstract getProcessorType(): string

  // 具体方法 - 提供默认实现，子类可以重写
  protected validateData(data: any[]): boolean {
    return Array.isArray(data) && data.length > 0
  }

  protected preprocessData(data: any[]): any[] {
    // 默认预处理：过滤空值
    return data.filter(item => item != null)
  }

  protected postprocessData(data: any[]): any[] {
    // 默认后处理：返回原数据
    return data
  }

  // 钩子方法 - 子类可以重写来改变行为
  protected shouldSaveResult(): boolean {
    return false
  }

  protected saveResult(data: any[]): void {
    console.log('保存处理结果...')
  }
}

// 处理结果接口
export interface ProcessResult {
  originalCount: number
  processedCount: number
  errors: string[]
  processedData: any[]
  processingTime: number
  processorType: string
}

// 具体处理器 - 用户数据处理器
export class UserDataProcessor extends DataProcessor {
  protected processCore(data: any[]): any[] {
    return data.map(user => ({
      id: user.id,
      name: user.name?.trim(),
      email: user.email?.toLowerCase(),
      age: parseInt(user.age) || 0,
      status: 'processed',
      processedAt: new Date().toISOString()
    }))
  }

  protected getProcessorType(): string {
    return '用户数据处理器'
  }

  protected validateData(data: any[]): boolean {
    if (!super.validateData(data)) return false
    
    // 额外验证：检查必要字段
    return data.every(user => user.id && user.name)
  }

  protected preprocessData(data: any[]): any[] {
    // 先调用父类预处理
    const filtered = super.preprocessData(data)
    
    // 额外预处理：去重
    const seen = new Set()
    return filtered.filter(user => {
      if (seen.has(user.id)) {
        return false
      }
      seen.add(user.id)
      return true
    })
  }

  protected shouldSaveResult(): boolean {
    return true
  }

  protected saveResult(data: any[]): void {
    console.log(`保存 ${data.length} 条用户数据到数据库`)
  }
}

// 具体处理器 - 订单数据处理器
export class OrderDataProcessor extends DataProcessor {
  private taxRate: number = 0.1

  constructor(taxRate?: number) {
    super()
    if (taxRate !== undefined) {
      this.taxRate = taxRate
    }
  }

  protected processCore(data: any[]): any[] {
    return data.map(order => ({
      id: order.id,
      customerId: order.customerId,
      amount: parseFloat(order.amount) || 0,
      tax: (parseFloat(order.amount) || 0) * this.taxRate,
      total: (parseFloat(order.amount) || 0) * (1 + this.taxRate),
      status: 'calculated',
      processedAt: new Date().toISOString()
    }))
  }

  protected getProcessorType(): string {
    return '订单数据处理器'
  }

  protected validateData(data: any[]): boolean {
    if (!super.validateData(data)) return false
    
    // 验证订单必要字段
    return data.every(order => order.id && order.customerId && order.amount)
  }

  protected postprocessData(data: any[]): any[] {
    // 按金额排序
    return data.sort((a, b) => b.total - a.total)
  }

  protected shouldSaveResult(): boolean {
    return true
  }

  protected saveResult(data: any[]): void {
    console.log(`保存 ${data.length} 条订单数据，总金额: ${data.reduce((sum, order) => sum + order.total, 0).toFixed(2)}`)
  }
}

// 具体处理器 - 日志数据处理器
export class LogDataProcessor extends DataProcessor {
  private logLevel: string = 'INFO'

  constructor(logLevel?: string) {
    super()
    if (logLevel) {
      this.logLevel = logLevel
    }
  }

  protected processCore(data: any[]): any[] {
    const levelPriority = { 'DEBUG': 0, 'INFO': 1, 'WARN': 2, 'ERROR': 3 }
    const minPriority = levelPriority[this.logLevel as keyof typeof levelPriority] || 1

    return data
      .filter(log => {
        const logPriority = levelPriority[log.level as keyof typeof levelPriority] || 0
        return logPriority >= minPriority
      })
      .map(log => ({
        timestamp: new Date(log.timestamp).toISOString(),
        level: log.level,
        message: log.message,
        source: log.source || 'unknown',
        processed: true
      }))
  }

  protected getProcessorType(): string {
    return `日志数据处理器 (${this.logLevel}+)`
  }

  protected validateData(data: any[]): boolean {
    if (!super.validateData(data)) return false
    
    // 验证日志必要字段
    return data.every(log => log.timestamp && log.level && log.message)
  }

  protected postprocessData(data: any[]): any[] {
    // 按时间戳排序
    return data.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
  }

  // 日志处理器不保存结果，只输出统计
  protected shouldSaveResult(): boolean {
    return false
  }
}

// 数据处理工厂
export class DataProcessorFactory {
  static createProcessor(type: 'user' | 'order' | 'log', options?: any): DataProcessor {
    switch (type) {
      case 'user':
        return new UserDataProcessor()
      case 'order':
        return new OrderDataProcessor(options?.taxRate)
      case 'log':
        return new LogDataProcessor(options?.logLevel)
      default:
        throw new Error(`不支持的处理器类型: ${type}`)
    }
  }

  static getSupportedTypes(): string[] {
    return ['user', 'order', 'log']
  }
}

// 批处理管理器
export class BatchProcessor {
  private processors: Map<string, DataProcessor> = new Map()

  addProcessor(name: string, processor: DataProcessor): void {
    this.processors.set(name, processor)
  }

  processAll(datasets: Map<string, any[]>): Map<string, ProcessResult> {
    const results = new Map<string, ProcessResult>()

    for (const [name, processor] of this.processors) {
      const data = datasets.get(name)
      if (data) {
        console.log(`\n处理数据集: ${name}`)
        const result = processor.processData(data)
        results.set(name, result)
      }
    }

    return results
  }

  getProcessorCount(): number {
    return this.processors.size
  }

  getProcessorNames(): string[] {
    return Array.from(this.processors.keys())
  }
}
