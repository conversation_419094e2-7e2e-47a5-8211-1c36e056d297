/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-27 18:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-27 18:00:00
 * @FilePath     : /src/patterns/Monad.ts
 * @Description  : 单子模式的TypeScript实现
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-27 18:00:00
 */

// 单子接口定义
export interface Monad<T> {
  flatMap<U>(fn: (value: T) => Monad<U>): Monad<U>;
  map<U>(fn: (value: T) => U): Monad<U>;
}

// 单位元函数（unit/return）
export const unit = <T>(value: T): Maybe<T> => Maybe.of(value);

// Maybe单子 - 处理可能为空的值
export class Maybe<T> implements Monad<T> {
  private constructor(private value: T | null) {}

  // 创建有值的Maybe
  static of<T>(value: T): Maybe<T> {
    return new Maybe(value);
  }

  // 创建空的Maybe
  static none<T>(): Maybe<T> {
    return new Maybe<T>(null);
  }

  // 从可能为空的值创建Maybe
  static fromNullable<T>(value: T | null | undefined): Maybe<T> {
    return value != null ? Maybe.of(value) : Maybe.none<T>();
  }

  // 单子的flatMap操作（bind）
  flatMap<U>(fn: (value: T) => Maybe<U>): Maybe<U> {
    if (this.value === null) {
      return Maybe.none<U>();
    }
    try {
      return fn(this.value);
    } catch (error) {
      return Maybe.none<U>();
    }
  }

  // 函子的map操作
  map<U>(fn: (value: T) => U): Maybe<U> {
    return this.flatMap(value => Maybe.of(fn(value)));
  }

  // 获取值（如果存在）
  getValue(): T | null {
    return this.value;
  }

  // 检查是否有值
  hasValue(): boolean {
    return this.value !== null;
  }

  // 获取值或默认值
  getOrElse(defaultValue: T): T {
    return this.value !== null ? this.value : defaultValue;
  }

  // 过滤操作
  filter(predicate: (value: T) => boolean): Maybe<T> {
    if (this.value === null || !predicate(this.value)) {
      return Maybe.none<T>();
    }
    return this;
  }

  // 应用操作（Applicative）
  apply<U>(fn: Maybe<(value: T) => U>): Maybe<U> {
    return fn.flatMap(f => this.map(f));
  }

  // 转换为字符串
  toString(): string {
    return this.value !== null ? `Maybe(${this.value})` : 'Maybe.none()';
  }
}

// IO单子 - 处理副作用
export class IO<T> implements Monad<T> {
  constructor(private effect: () => T) {}

  // 创建IO单子
  static of<T>(value: T): IO<T> {
    return new IO(() => value);
  }

  // 从副作用函数创建IO
  static from<T>(effect: () => T): IO<T> {
    return new IO(effect);
  }

  // 单子的flatMap操作
  flatMap<U>(fn: (value: T) => IO<U>): IO<U> {
    return new IO(() => fn(this.effect()).run());
  }

  // 函子的map操作
  map<U>(fn: (value: T) => U): IO<U> {
    return this.flatMap(value => IO.of(fn(value)));
  }

  // 执行副作用
  run(): T {
    return this.effect();
  }

  // 转换为字符串
  toString(): string {
    return 'IO(...)';
  }
}

// State单子 - 处理状态计算
export class State<S, T> implements Monad<T> {
  constructor(private computation: (state: S) => [T, S]) {}

  // 创建State单子
  static of<S, T>(value: T): State<S, T> {
    return new State(state => [value, state]);
  }

  // 获取当前状态
  static get<S>(): State<S, S> {
    return new State(state => [state, state]);
  }

  // 设置新状态
  static put<S>(newState: S): State<S, void> {
    return new State(() => [undefined as any, newState]);
  }

  // 修改状态
  static modify<S>(fn: (state: S) => S): State<S, void> {
    return new State(state => [undefined as any, fn(state)]);
  }

  // 单子的flatMap操作
  flatMap<U>(fn: (value: T) => State<S, U>): State<S, U> {
    return new State(state => {
      const [value, newState] = this.computation(state);
      return fn(value).computation(newState);
    });
  }

  // 函子的map操作
  map<U>(fn: (value: T) => U): State<S, U> {
    return this.flatMap(value => State.of<S, U>(fn(value)));
  }

  // 运行状态计算
  run(initialState: S): T {
    return this.computation(initialState)[0];
  }

  // 运行状态计算并返回最终状态
  runState(initialState: S): [T, S] {
    return this.computation(initialState);
  }

  // 只获取最终状态
  execState(initialState: S): S {
    return this.computation(initialState)[1];
  }
}

// Either单子 - 处理错误或成功的值
export abstract class Either<L, R> implements Monad<R> {
  abstract flatMap<U>(fn: (value: R) => Either<L, U>): Either<L, U>;
  abstract map<U>(fn: (value: R) => U): Either<L, U>;
  abstract isLeft(): boolean;
  abstract isRight(): boolean;
}

export class Left<L, R> extends Either<L, R> {
  constructor(private value: L) {
    super();
  }

  flatMap<U>(_fn: (value: R) => Either<L, U>): Either<L, U> {
    return new Left<L, U>(this.value);
  }

  map<U>(_fn: (value: R) => U): Either<L, U> {
    return new Left<L, U>(this.value);
  }

  isLeft(): boolean {
    return true;
  }

  isRight(): boolean {
    return false;
  }

  getValue(): L {
    return this.value;
  }

  toString(): string {
    return `Left(${this.value})`;
  }
}

export class Right<L, R> extends Either<L, R> {
  constructor(private value: R) {
    super();
  }

  flatMap<U>(fn: (value: R) => Either<L, U>): Either<L, U> {
    try {
      return fn(this.value);
    } catch (error) {
      return new Left<L, U>(error as L);
    }
  }

  map<U>(fn: (value: R) => U): Either<L, U> {
    return this.flatMap(value => new Right<L, U>(fn(value)));
  }

  isLeft(): boolean {
    return false;
  }

  isRight(): boolean {
    return true;
  }

  getValue(): R {
    return this.value;
  }

  toString(): string {
    return `Right(${this.value})`;
  }
}

// Either工厂函数
export const left = <L, R>(value: L): Either<L, R> => new Left(value);
export const right = <L, R>(value: R): Either<L, R> => new Right(value);

// List单子 - 处理非确定性计算
export class List<T> implements Monad<T> {
  private constructor(private items: T[]) {}

  // 创建List单子
  static of<T>(...items: T[]): List<T> {
    return new List(items);
  }

  // 创建空List
  static empty<T>(): List<T> {
    return new List<T>([]);
  }

  // 从数组创建List
  static fromArray<T>(items: T[]): List<T> {
    return new List([...items]);
  }

  // 单子的flatMap操作
  flatMap<U>(fn: (value: T) => List<U>): List<U> {
    const result: U[] = [];
    for (const item of this.items) {
      result.push(...fn(item).toArray());
    }
    return List.fromArray(result);
  }

  // 函子的map操作
  map<U>(fn: (value: T) => U): List<U> {
    return this.flatMap(value => List.of(fn(value)));
  }

  // 转换为数组
  toArray(): T[] {
    return [...this.items];
  }

  // 获取长度
  length(): number {
    return this.items.length;
  }

  // 检查是否为空
  isEmpty(): boolean {
    return this.items.length === 0;
  }

  // 过滤操作
  filter(predicate: (value: T) => boolean): List<T> {
    return List.fromArray(this.items.filter(predicate));
  }

  // 折叠操作
  fold<U>(initial: U, fn: (acc: U, value: T) => U): U {
    return this.items.reduce(fn, initial);
  }

  // 连接操作
  concat(other: List<T>): List<T> {
    return List.fromArray([...this.items, ...other.toArray()]);
  }

  toString(): string {
    return `List([${this.items.join(', ')}])`;
  }
}

// 单子法则验证工具
export class MonadLaws {
  // 验证左单位元法则: unit(a).flatMap(f) === f(a)
  static verifyLeftIdentity<T, U>(
    a: T,
    f: (x: T) => Maybe<U>
  ): boolean {
    const left = unit(a).flatMap(f);
    const right = f(a);
    return JSON.stringify(left) === JSON.stringify(right);
  }

  // 验证右单位元法则: m.flatMap(unit) === m
  static verifyRightIdentity<T>(m: Maybe<T>): boolean {
    const left = m.flatMap(unit);
    const right = m;
    return JSON.stringify(left) === JSON.stringify(right);
  }

  // 验证结合律: m.flatMap(f).flatMap(g) === m.flatMap(x => f(x).flatMap(g))
  static verifyAssociativity<T, U, V>(
    m: Maybe<T>,
    f: (x: T) => Maybe<U>,
    g: (x: U) => Maybe<V>
  ): boolean {
    const left = m.flatMap(f).flatMap(g);
    const right = m.flatMap(x => f(x).flatMap(g));
    return JSON.stringify(left) === JSON.stringify(right);
  }
}

// 单子组合子
export class MonadCombinators {
  // 序列操作：将单子数组转换为数组单子
  static sequence<T>(maybes: Maybe<T>[]): Maybe<T[]> {
    return maybes.reduce(
      (acc: Maybe<T[]>, maybe: Maybe<T>) =>
        acc.flatMap(arr => maybe.map(val => [...arr, val])),
      Maybe.of([] as T[])
    );
  }

  // 遍历操作：对数组中的每个元素应用单子函数
  static traverse<T, U>(
    items: T[],
    fn: (item: T) => Maybe<U>
  ): Maybe<U[]> {
    return MonadCombinators.sequence(items.map(fn));
  }

  // 过滤操作：保留满足条件的元素
  static filterM<T>(
    items: T[],
    predicate: (item: T) => Maybe<boolean>
  ): Maybe<T[]> {
    return items.reduce(
      (acc: Maybe<T[]>, item: T) =>
        acc.flatMap(arr =>
          predicate(item).flatMap(keep =>
            Maybe.of(keep ? [...arr, item] : arr)
          )
        ),
      Maybe.of([] as T[])
    );
  }

  // 折叠操作：从左到右折叠
  static foldM<T, U>(
    items: T[],
    initial: U,
    fn: (acc: U, item: T) => Maybe<U>
  ): Maybe<U> {
    return items.reduce(
      (acc: Maybe<U>, item: T) => acc.flatMap(a => fn(a, item)),
      Maybe.of(initial)
    );
  }
}

// 实用工具函数
export const liftM2 = <A, B, C>(
  fn: (a: A, b: B) => C
) => (ma: Maybe<A>, mb: Maybe<B>): Maybe<C> => {
  return ma.flatMap(a => mb.map(b => fn(a, b)));
};

export const liftM3 = <A, B, C, D>(
  fn: (a: A, b: B, c: C) => D
) => (ma: Maybe<A>, mb: Maybe<B>, mc: Maybe<C>): Maybe<D> => {
  return ma.flatMap(a =>
    mb.flatMap(b =>
      mc.map(c => fn(a, b, c))
    )
  );
};

// 条件执行
export const when = <T>(
  condition: boolean,
  action: () => Maybe<T>
): Maybe<void> => {
  return condition ? action().map(() => undefined) : Maybe.of(undefined);
};

export const unless = <T>(
  condition: boolean,
  action: () => Maybe<T>
): Maybe<void> => {
  return when(!condition, action);
};

// 重复执行
export const replicateM = <T>(
  n: number,
  action: () => Maybe<T>
): Maybe<T[]> => {
  const actions = Array(n).fill(null).map(() => action());
  return MonadCombinators.sequence(actions);
};

// 导出类型
export type { Monad };
