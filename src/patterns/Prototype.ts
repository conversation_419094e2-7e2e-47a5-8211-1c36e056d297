/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 12:10:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 12:10:00
 * @FilePath     : /src/patterns/Prototype.ts
 * @Description  : 原型模式实现 - 游戏角色克隆系统
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 原型接口
interface Prototype {
  clone(): Prototype;
  getInfo(): string;
}

// 装备类
class Equipment {
  constructor(
    public name: string,
    public type: string,
    public attack: number,
    public defense: number,
    public rarity: string
  ) {}

  clone(): Equipment {
    return new Equipment(
      this.name,
      this.type,
      this.attack,
      this.defense,
      this.rarity
    );
  }

  getInfo(): string {
    return `${this.name} (${this.type}) - 攻击:${this.attack} 防御:${this.defense} 稀有度:${this.rarity}`;
  }
}

// 技能类
class Skill {
  constructor(
    public name: string,
    public type: string,
    public damage: number,
    public cooldown: number,
    public description: string
  ) {}

  clone(): Skill {
    return new Skill(
      this.name,
      this.type,
      this.damage,
      this.cooldown,
      this.description
    );
  }

  getInfo(): string {
    return `${this.name} (${this.type}) - 伤害:${this.damage} 冷却:${this.cooldown}秒 - ${this.description}`;
  }
}

// 属性类
class Attributes {
  constructor(
    public strength: number,
    public agility: number,
    public intelligence: number,
    public vitality: number,
    public luck: number
  ) {}

  clone(): Attributes {
    return new Attributes(
      this.strength,
      this.agility,
      this.intelligence,
      this.vitality,
      this.luck
    );
  }

  getInfo(): string {
    return `力量:${this.strength} 敏捷:${this.agility} 智力:${this.intelligence} 体力:${this.vitality} 幸运:${this.luck}`;
  }

  getTotalPoints(): number {
    return this.strength + this.agility + this.intelligence + this.vitality + this.luck;
  }
}

// 游戏角色类 - 实现原型接口
class GameCharacter implements Prototype {
  private id: string;
  private createdAt: Date;

  constructor(
    public name: string,
    public characterClass: string,
    public level: number,
    public experience: number,
    public attributes: Attributes,
    public equipment: Equipment[],
    public skills: Skill[]
  ) {
    this.id = Math.random().toString(36).slice(2, 11);
    this.createdAt = new Date();
  }

  // 深度克隆方法
  clone(): GameCharacter {
    // 克隆属性
    const clonedAttributes = this.attributes.clone();
    
    // 克隆装备数组
    const clonedEquipment = this.equipment.map(item => item.clone());
    
    // 克隆技能数组
    const clonedSkills = this.skills.map(skill => skill.clone());

    // 创建新角色实例
    const clonedCharacter = new GameCharacter(
      `${this.name}_副本`,
      this.characterClass,
      this.level,
      this.experience,
      clonedAttributes,
      clonedEquipment,
      clonedSkills
    );

    return clonedCharacter;
  }

  // 浅克隆方法（仅用于演示对比）
  shallowClone(): GameCharacter {
    return new GameCharacter(
      `${this.name}_浅拷贝`,
      this.characterClass,
      this.level,
      this.experience,
      this.attributes, // 注意：这里是引用拷贝
      this.equipment,  // 注意：这里是引用拷贝
      this.skills      // 注意：这里是引用拷贝
    );
  }

  getId(): string {
    return this.id;
  }

  getCreatedAt(): Date {
    return this.createdAt;
  }

  getInfo(): string {
    const equipmentInfo = this.equipment.map(item => item.getInfo()).join('\n  ');
    const skillsInfo = this.skills.map(skill => skill.getInfo()).join('\n  ');
    
    return `
角色信息:
- ID: ${this.id}
- 名称: ${this.name}
- 职业: ${this.characterClass}
- 等级: ${this.level}
- 经验: ${this.experience}
- 创建时间: ${this.createdAt.toLocaleString()}
- 属性: ${this.attributes.getInfo()}
- 装备:
  ${equipmentInfo}
- 技能:
  ${skillsInfo}
    `.trim();
  }

  // 修改属性（用于测试深拷贝效果）
  modifyAttributes(strength: number, agility: number, intelligence: number, vitality: number, luck: number): void {
    this.attributes.strength = strength;
    this.attributes.agility = agility;
    this.attributes.intelligence = intelligence;
    this.attributes.vitality = vitality;
    this.attributes.luck = luck;
  }

  // 添加装备
  addEquipment(equipment: Equipment): void {
    this.equipment.push(equipment);
  }

  // 添加技能
  addSkill(skill: Skill): void {
    this.skills.push(skill);
  }

  // 升级
  levelUp(): void {
    this.level++;
    this.experience = 0;
    // 升级时随机增加属性点
    this.attributes.strength += Math.floor(Math.random() * 3) + 1;
    this.attributes.agility += Math.floor(Math.random() * 3) + 1;
    this.attributes.intelligence += Math.floor(Math.random() * 3) + 1;
    this.attributes.vitality += Math.floor(Math.random() * 3) + 1;
    this.attributes.luck += Math.floor(Math.random() * 2) + 1;
  }

  getCombatPower(): number {
    const attributesPower = this.attributes.getTotalPoints() * this.level;
    const equipmentPower = this.equipment.reduce((total, item) => total + item.attack + item.defense, 0);
    const skillsPower = this.skills.reduce((total, skill) => total + skill.damage, 0);
    return attributesPower + equipmentPower + skillsPower;
  }
}

// 角色原型注册表
class CharacterPrototypeRegistry {
  private prototypes: Map<string, GameCharacter> = new Map();

  // 注册原型
  registerPrototype(key: string, prototype: GameCharacter): void {
    this.prototypes.set(key, prototype);
  }

  // 获取原型克隆
  getPrototype(key: string): GameCharacter | null {
    const prototype = this.prototypes.get(key);
    return prototype ? prototype.clone() : null;
  }

  // 获取所有可用原型的键
  getAvailablePrototypes(): string[] {
    return Array.from(this.prototypes.keys());
  }

  // 移除原型
  removePrototype(key: string): boolean {
    return this.prototypes.delete(key);
  }

  // 获取原型信息（不克隆）
  getPrototypeInfo(key: string): string | null {
    const prototype = this.prototypes.get(key);
    return prototype ? prototype.getInfo() : null;
  }
}

// 预定义角色工厂
class PredefinedCharacterFactory {
  static createWarrior(): GameCharacter {
    const attributes = new Attributes(25, 15, 10, 20, 12);
    const equipment = [
      new Equipment('钢铁长剑', '武器', 45, 5, '普通'),
      new Equipment('皮甲', '防具', 0, 25, '普通'),
      new Equipment('铁盾', '盾牌', 5, 20, '普通')
    ];
    const skills = [
      new Skill('重击', '攻击', 80, 3, '造成大量物理伤害'),
      new Skill('防御姿态', '防御', 0, 5, '提高防御力50%')
    ];

    return new GameCharacter('战士', '战士', 10, 0, attributes, equipment, skills);
  }

  static createMage(): GameCharacter {
    const attributes = new Attributes(8, 12, 30, 15, 18);
    const equipment = [
      new Equipment('法师杖', '武器', 25, 0, '稀有'),
      new Equipment('法袍', '防具', 0, 15, '稀有'),
      new Equipment('魔法护符', '饰品', 10, 10, '史诗')
    ];
    const skills = [
      new Skill('火球术', '魔法', 120, 2, '发射火球造成魔法伤害'),
      new Skill('冰霜护盾', '防御', 0, 8, '创造冰霜护盾吸收伤害'),
      new Skill('闪电链', '魔法', 90, 4, '连锁闪电攻击多个敌人')
    ];

    return new GameCharacter('法师', '法师', 10, 0, attributes, equipment, skills);
  }

  static createRogue(): GameCharacter {
    const attributes = new Attributes(18, 28, 15, 16, 20);
    const equipment = [
      new Equipment('双刃匕首', '武器', 35, 0, '稀有'),
      new Equipment('皮甲', '防具', 0, 18, '普通'),
      new Equipment('隐身斗篷', '饰品', 5, 5, '史诗')
    ];
    const skills = [
      new Skill('背刺', '攻击', 150, 6, '从背后攻击造成暴击伤害'),
      new Skill('隐身', '辅助', 0, 10, '进入隐身状态3秒'),
      new Skill('毒刃', '攻击', 60, 4, '武器附毒，造成持续伤害')
    ];

    return new GameCharacter('盗贼', '盗贼', 10, 0, attributes, equipment, skills);
  }
}

// 创建默认注册表实例
const characterRegistry = new CharacterPrototypeRegistry();

// 注册预定义角色原型
characterRegistry.registerPrototype('warrior', PredefinedCharacterFactory.createWarrior());
characterRegistry.registerPrototype('mage', PredefinedCharacterFactory.createMage());
characterRegistry.registerPrototype('rogue', PredefinedCharacterFactory.createRogue());

// 导出类和实例
export { characterRegistry, PredefinedCharacterFactory };
export type GameCharacterType = GameCharacter;
export type CharacterPrototypeRegistryType = CharacterPrototypeRegistry;
export type EquipmentType = Equipment;
export type SkillType = Skill;
export type AttributesType = Attributes;
