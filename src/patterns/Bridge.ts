/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 12:15:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 12:15:00
 * @FilePath     : /src/patterns/Bridge.ts
 * @Description  : 桥接模式实现 - 跨平台消息推送系统
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 实现接口 - 消息发送器
interface MessageSender {
  sendMessage(message: string, recipient: string): string;
  getProviderName(): string;
  getDeliveryMethod(): string;
  isAvailable(): boolean;
}

// 具体实现 - 邮件发送器
class EmailSender implements MessageSender {
  sendMessage(message: string, recipient: string): string {
    return `📧 邮件已发送到 ${recipient}\n内容: ${message}\n发送方式: SMTP 协议`;
  }

  getProviderName(): string {
    return '邮件服务';
  }

  getDeliveryMethod(): string {
    return 'SMTP';
  }

  isAvailable(): boolean {
    return true; // 模拟邮件服务总是可用
  }
}

// 具体实现 - 短信发送器
class SMSSender implements MessageSender {
  sendMessage(message: string, recipient: string): string {
    return `📱 短信已发送到 ${recipient}\n内容: ${message}\n发送方式: GSM 网络`;
  }

  getProviderName(): string {
    return '短信服务';
  }

  getDeliveryMethod(): string {
    return 'GSM';
  }

  isAvailable(): boolean {
    return Math.random() > 0.1; // 模拟90%的可用性
  }
}

// 具体实现 - 微信发送器
class WeChatSender implements MessageSender {
  sendMessage(message: string, recipient: string): string {
    return `💬 微信消息已发送到 ${recipient}\n内容: ${message}\n发送方式: 微信 API`;
  }

  getProviderName(): string {
    return '微信服务';
  }

  getDeliveryMethod(): string {
    return 'WeChat API';
  }

  isAvailable(): boolean {
    return Math.random() > 0.05; // 模拟95%的可用性
  }
}

// 具体实现 - 钉钉发送器
class DingTalkSender implements MessageSender {
  sendMessage(message: string, recipient: string): string {
    return `🔔 钉钉消息已发送到 ${recipient}\n内容: ${message}\n发送方式: 钉钉 API`;
  }

  getProviderName(): string {
    return '钉钉服务';
  }

  getDeliveryMethod(): string {
    return 'DingTalk API';
  }

  isAvailable(): boolean {
    return Math.random() > 0.08; // 模拟92%的可用性
  }
}

// 抽象类 - 消息通知
abstract class Notification {
  protected sender: MessageSender;

  constructor(sender: MessageSender) {
    this.sender = sender;
  }

  // 设置发送器
  setSender(sender: MessageSender): void {
    this.sender = sender;
  }

  // 获取发送器信息
  getSenderInfo(): string {
    return `${this.sender.getProviderName()} (${this.sender.getDeliveryMethod()})`;
  }

  // 检查服务可用性
  checkAvailability(): boolean {
    return this.sender.isAvailable();
  }

  // 抽象方法 - 发送通知
  abstract send(recipient: string): string;
}

// 具体抽象 - 简单通知
class SimpleNotification extends Notification {
  private message: string;

  constructor(sender: MessageSender, message: string) {
    super(sender);
    this.message = message;
  }

  setMessage(message: string): void {
    this.message = message;
  }

  send(recipient: string): string {
    if (!this.checkAvailability()) {
      return `❌ ${this.sender.getProviderName()} 当前不可用，消息发送失败`;
    }
    return this.sender.sendMessage(this.message, recipient);
  }

  getMessage(): string {
    return this.message;
  }
}

// 具体抽象 - 紧急通知
class UrgentNotification extends Notification {
  private message: string;
  private priority: string;

  constructor(sender: MessageSender, message: string, priority: string = '高') {
    super(sender);
    this.message = `🚨 [紧急通知-${priority}优先级] ${message}`;
    this.priority = priority;
  }

  setMessage(message: string): void {
    this.message = `🚨 [紧急通知-${this.priority}优先级] ${message}`;
  }

  setPriority(priority: string): void {
    this.priority = priority;
    // 重新设置消息以包含新的优先级
    const originalMessage = this.message.replace(/🚨 \[紧急通知-.*?优先级\] /, '');
    this.message = `🚨 [紧急通知-${priority}优先级] ${originalMessage}`;
  }

  send(recipient: string): string {
    if (!this.checkAvailability()) {
      return `❌ ${this.sender.getProviderName()} 当前不可用，紧急消息发送失败！`;
    }
    const result = this.sender.sendMessage(this.message, recipient);
    return `${result}\n⚡ 紧急通知已标记为 ${this.priority} 优先级`;
  }

  getMessage(): string {
    return this.message;
  }

  getPriority(): string {
    return this.priority;
  }
}

// 具体抽象 - 营销通知
class MarketingNotification extends Notification {
  private message: string;
  private campaign: string;
  private trackingId: string;

  constructor(sender: MessageSender, message: string, campaign: string) {
    super(sender);
    this.message = message;
    this.campaign = campaign;
    this.trackingId = Math.random().toString(36).slice(2, 11);
  }

  setMessage(message: string): void {
    this.message = message;
  }

  setCampaign(campaign: string): void {
    this.campaign = campaign;
  }

  send(recipient: string): string {
    if (!this.checkAvailability()) {
      return `❌ ${this.sender.getProviderName()} 当前不可用，营销消息发送失败`;
    }
    
    const marketingMessage = `
📢 ${this.campaign}

${this.message}

---
追踪ID: ${this.trackingId}
退订请回复 TD
    `.trim();

    const result = this.sender.sendMessage(marketingMessage, recipient);
    return `${result}\n📊 营销活动: ${this.campaign} | 追踪ID: ${this.trackingId}`;
  }

  getMessage(): string {
    return this.message;
  }

  getCampaign(): string {
    return this.campaign;
  }

  getTrackingId(): string {
    return this.trackingId;
  }
}

// 通知管理器
class NotificationManager {
  private notifications: Notification[] = [];

  addNotification(notification: Notification): void {
    this.notifications.push(notification);
  }

  sendAll(recipient: string): string[] {
    return this.notifications.map(notification => notification.send(recipient));
  }

  sendByType(recipient: string, type: string): string[] {
    const filteredNotifications = this.notifications.filter(notification => {
      if (type === 'simple') return notification instanceof SimpleNotification;
      if (type === 'urgent') return notification instanceof UrgentNotification;
      if (type === 'marketing') return notification instanceof MarketingNotification;
      return false;
    });

    return filteredNotifications.map(notification => notification.send(recipient));
  }

  getNotificationCount(): number {
    return this.notifications.length;
  }

  clearNotifications(): void {
    this.notifications = [];
  }

  getAvailableSenders(): string[] {
    const senders = this.notifications.map(notification => notification.getSenderInfo());
    return [...new Set(senders)]; // 去重
  }
}

// 发送器工厂
class MessageSenderFactory {
  static createSender(type: string): MessageSender {
    switch (type) {
      case 'email':
        return new EmailSender();
      case 'sms':
        return new SMSSender();
      case 'wechat':
        return new WeChatSender();
      case 'dingtalk':
        return new DingTalkSender();
      default:
        throw new Error(`不支持的发送器类型: ${type}`);
    }
  }

  static getSupportedTypes(): string[] {
    return ['email', 'sms', 'wechat', 'dingtalk'];
  }

  static getTypeDisplayNames(): Record<string, string> {
    return {
      email: '邮件',
      sms: '短信',
      wechat: '微信',
      dingtalk: '钉钉'
    };
  }
}

// 导出类和工厂
export const messageSenderFactory = MessageSenderFactory;
export { NotificationManager, SimpleNotification, UrgentNotification, MarketingNotification };
export type MessageSenderType = MessageSender;
export type NotificationType = Notification;
export type NotificationManagerType = NotificationManager;
export type MessageSenderFactoryType = typeof MessageSenderFactory;
