/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 12:05:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 12:05:00
 * @FilePath     : /src/patterns/Builder.ts
 * @Description  : 建造者模式实现 - 电脑配置构建器
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 产品类 - 电脑
class Computer {
  private cpu: string = '';
  private memory: string = '';
  private storage: string = '';
  private graphics: string = '';
  private motherboard: string = '';
  private powerSupply: string = '';
  private coolingSystem: string = '';
  private caseType: string = '';
  private price: number = 0;

  // 设置组件的方法
  setCPU(cpu: string, price: number): void {
    this.cpu = cpu;
    this.price += price;
  }

  setMemory(memory: string, price: number): void {
    this.memory = memory;
    this.price += price;
  }

  setStorage(storage: string, price: number): void {
    this.storage = storage;
    this.price += price;
  }

  setGraphics(graphics: string, price: number): void {
    this.graphics = graphics;
    this.price += price;
  }

  setMotherboard(motherboard: string, price: number): void {
    this.motherboard = motherboard;
    this.price += price;
  }

  setPowerSupply(powerSupply: string, price: number): void {
    this.powerSupply = powerSupply;
    this.price += price;
  }

  setCoolingSystem(coolingSystem: string, price: number): void {
    this.coolingSystem = coolingSystem;
    this.price += price;
  }

  setCaseType(caseType: string, price: number): void {
    this.caseType = caseType;
    this.price += price;
  }

  // 获取配置信息
  getConfiguration(): {
    cpu: string;
    memory: string;
    storage: string;
    graphics: string;
    motherboard: string;
    powerSupply: string;
    coolingSystem: string;
    caseType: string;
    totalPrice: number;
  } {
    return {
      cpu: this.cpu,
      memory: this.memory,
      storage: this.storage,
      graphics: this.graphics,
      motherboard: this.motherboard,
      powerSupply: this.powerSupply,
      coolingSystem: this.coolingSystem,
      caseType: this.caseType,
      totalPrice: this.price
    };
  }

  getSpecifications(): string {
    const config = this.getConfiguration();
    return `
电脑配置清单:
- CPU: ${config.cpu}
- 内存: ${config.memory}
- 存储: ${config.storage}
- 显卡: ${config.graphics}
- 主板: ${config.motherboard}
- 电源: ${config.powerSupply}
- 散热: ${config.coolingSystem}
- 机箱: ${config.caseType}
- 总价: ¥${config.totalPrice.toLocaleString()}
    `.trim();
  }

  getPerformanceLevel(): string {
    if (this.price >= 15000) return '高端配置';
    if (this.price >= 8000) return '中高端配置';
    if (this.price >= 4000) return '中端配置';
    return '入门配置';
  }
}

// 抽象建造者接口
interface ComputerBuilder {
  reset(): ComputerBuilder;
  buildCPU(): ComputerBuilder;
  buildMemory(): ComputerBuilder;
  buildStorage(): ComputerBuilder;
  buildGraphics(): ComputerBuilder;
  buildMotherboard(): ComputerBuilder;
  buildPowerSupply(): ComputerBuilder;
  buildCoolingSystem(): ComputerBuilder;
  buildCase(): ComputerBuilder;
  getResult(): Computer;
}

// 具体建造者 - 游戏电脑建造者
class GamingComputerBuilder implements ComputerBuilder {
  private computer: Computer;

  constructor() {
    this.computer = new Computer();
  }

  reset(): ComputerBuilder {
    this.computer = new Computer();
    return this;
  }

  buildCPU(): ComputerBuilder {
    this.computer.setCPU('Intel Core i7-13700K', 3200);
    return this;
  }

  buildMemory(): ComputerBuilder {
    this.computer.setMemory('32GB DDR5-5600', 1200);
    return this;
  }

  buildStorage(): ComputerBuilder {
    this.computer.setStorage('1TB NVMe SSD + 2TB HDD', 800);
    return this;
  }

  buildGraphics(): ComputerBuilder {
    this.computer.setGraphics('NVIDIA RTX 4070 Ti', 6500);
    return this;
  }

  buildMotherboard(): ComputerBuilder {
    this.computer.setMotherboard('ASUS ROG STRIX Z790-E', 2200);
    return this;
  }

  buildPowerSupply(): ComputerBuilder {
    this.computer.setPowerSupply('850W 80+ Gold 全模组', 800);
    return this;
  }

  buildCoolingSystem(): ComputerBuilder {
    this.computer.setCoolingSystem('240mm 一体式水冷', 600);
    return this;
  }

  buildCase(): ComputerBuilder {
    this.computer.setCaseType('中塔式游戏机箱 RGB', 500);
    return this;
  }

  getResult(): Computer {
    return this.computer;
  }
}

// 具体建造者 - 办公电脑建造者
class OfficeComputerBuilder implements ComputerBuilder {
  private computer: Computer;

  constructor() {
    this.computer = new Computer();
  }

  reset(): ComputerBuilder {
    this.computer = new Computer();
    return this;
  }

  buildCPU(): ComputerBuilder {
    this.computer.setCPU('Intel Core i5-13400', 1500);
    return this;
  }

  buildMemory(): ComputerBuilder {
    this.computer.setMemory('16GB DDR4-3200', 400);
    return this;
  }

  buildStorage(): ComputerBuilder {
    this.computer.setStorage('512GB NVMe SSD', 300);
    return this;
  }

  buildGraphics(): ComputerBuilder {
    this.computer.setGraphics('集成显卡', 0);
    return this;
  }

  buildMotherboard(): ComputerBuilder {
    this.computer.setMotherboard('华硕 B760M 主板', 600);
    return this;
  }

  buildPowerSupply(): ComputerBuilder {
    this.computer.setPowerSupply('500W 80+ Bronze', 300);
    return this;
  }

  buildCoolingSystem(): ComputerBuilder {
    this.computer.setCoolingSystem('原装散热器', 0);
    return this;
  }

  buildCase(): ComputerBuilder {
    this.computer.setCaseType('小机箱', 200);
    return this;
  }

  getResult(): Computer {
    return this.computer;
  }
}

// 具体建造者 - 工作站建造者
class WorkstationBuilder implements ComputerBuilder {
  private computer: Computer;

  constructor() {
    this.computer = new Computer();
  }

  reset(): ComputerBuilder {
    this.computer = new Computer();
    return this;
  }

  buildCPU(): ComputerBuilder {
    this.computer.setCPU('Intel Core i9-13900K', 4500);
    return this;
  }

  buildMemory(): ComputerBuilder {
    this.computer.setMemory('64GB DDR5-5600 ECC', 3000);
    return this;
  }

  buildStorage(): ComputerBuilder {
    this.computer.setStorage('2TB NVMe SSD + 4TB HDD', 1500);
    return this;
  }

  buildGraphics(): ComputerBuilder {
    this.computer.setGraphics('NVIDIA RTX A4000', 8000);
    return this;
  }

  buildMotherboard(): ComputerBuilder {
    this.computer.setMotherboard('华硕 Pro WS W790-ACE', 3500);
    return this;
  }

  buildPowerSupply(): ComputerBuilder {
    this.computer.setPowerSupply('1000W 80+ Platinum', 1200);
    return this;
  }

  buildCoolingSystem(): ComputerBuilder {
    this.computer.setCoolingSystem('360mm 一体式水冷', 1000);
    return this;
  }

  buildCase(): ComputerBuilder {
    this.computer.setCaseType('全塔式工作站机箱', 800);
    return this;
  }

  getResult(): Computer {
    return this.computer;
  }
}

// 指挥者类
class ComputerDirector {
  private builder: ComputerBuilder;

  constructor(builder: ComputerBuilder) {
    this.builder = builder;
  }

  setBuilder(builder: ComputerBuilder): void {
    this.builder = builder;
  }

  buildFullComputer(): Computer {
    return this.builder
      .reset()
      .buildCPU()
      .buildMemory()
      .buildStorage()
      .buildGraphics()
      .buildMotherboard()
      .buildPowerSupply()
      .buildCoolingSystem()
      .buildCase()
      .getResult();
  }

  buildBasicComputer(): Computer {
    return this.builder
      .reset()
      .buildCPU()
      .buildMemory()
      .buildStorage()
      .buildMotherboard()
      .buildPowerSupply()
      .getResult();
  }
}

// 建造者工厂
class ComputerBuilderFactory {
  static createBuilder(type: string): ComputerBuilder {
    switch (type) {
      case 'gaming':
        return new GamingComputerBuilder();
      case 'office':
        return new OfficeComputerBuilder();
      case 'workstation':
        return new WorkstationBuilder();
      default:
        throw new Error(`不支持的电脑类型: ${type}`);
    }
  }

  static getSupportedTypes(): string[] {
    return ['gaming', 'office', 'workstation'];
  }

  static getTypeDisplayNames(): Record<string, string> {
    return {
      gaming: '游戏电脑',
      office: '办公电脑',
      workstation: '工作站'
    };
  }
}

// 导出类和类型
export const computerBuilderFactory = ComputerBuilderFactory;
export { ComputerDirector };
export type ComputerType = Computer;
export type ComputerBuilderType = ComputerBuilder;
export type ComputerDirectorType = ComputerDirector;
export type ComputerBuilderFactoryType = typeof ComputerBuilderFactory;
