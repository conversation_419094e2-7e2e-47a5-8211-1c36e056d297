// Proxy Pattern Implementation

// 主题接口 - 定义实体和代理的共同接口
interface ImageInterface {
  display(): Promise<string>;
  getUrl(): string;
  getSize(): number;
  getTitle(): string;
  getStatus(): 'unloaded' | 'loading' | 'loaded' | 'error';
}

// 真实主题 - 实际加载图片的对象
class RealImage implements ImageInterface {
  private url: string;
  private title: string;
  private size: number;
  private status: 'unloaded' | 'loading' | 'loaded' | 'error';
  private loadingTime: number; // 模拟加载时间(毫秒)
  private loadedContent: string | null;

  constructor(url: string, title: string, size: number, loadingTime = 2000) {
    this.url = url;
    this.title = title;
    this.size = size; // 图片大小(KB)
    this.status = 'unloaded';
    this.loadingTime = loadingTime;
    this.loadedContent = null;
  }

  async display(): Promise<string> {
    if (this.status === 'loaded' && this.loadedContent) {
      return this.loadedContent;
    }

    this.status = 'loading';

    try {
      // 模拟网络请求延迟
      const content = await new Promise<string>((resolve, reject) => {
        setTimeout(() => {
          // 随机模拟失败的情况 (10%几率)
          if (Math.random() < 0.1) {
            this.status = 'error';
            reject(new Error(`Failed to load image: ${this.url}`));
          } else {
            this.status = 'loaded';
            const content = `Image content from ${this.url}`;
            this.loadedContent = content;
            resolve(content);
          }
        }, this.loadingTime);
      });

      return content;
    } catch (error) {
      this.status = 'error';
      throw error;
    }
  }

  getUrl(): string {
    return this.url;
  }

  getTitle(): string {
    return this.title;
  }

  getSize(): number {
    return this.size;
  }

  getStatus(): 'unloaded' | 'loading' | 'loaded' | 'error' {
    return this.status;
  }
}

// 代理 - 控制对实际图片对象的访问
class ImageProxy implements ImageInterface {
  private realImage: RealImage | null;
  private url: string;
  private title: string;
  private size: number;
  private status: 'unloaded' | 'loading' | 'loaded' | 'error';
  private cachedContent: string | null;
  private loadingTime: number;

  constructor(url: string, title: string, size: number, loadingTime = 2000) {
    this.realImage = null;
    this.url = url;
    this.title = title;
    this.size = size;
    this.status = 'unloaded';
    this.cachedContent = null;
    this.loadingTime = loadingTime;
  }

  async display(): Promise<string> {
    // 已加载过且有缓存，直接返回缓存内容
    if (this.status === 'loaded' && this.cachedContent) {
      console.log(`[Proxy] Returning cached image: ${this.url}`);
      return this.cachedContent;
    }

    // 如果未初始化真实对象，延迟创建
    if (!this.realImage) {
      console.log(`[Proxy] Creating RealImage for: ${this.url}`);
      this.realImage = new RealImage(this.url, this.title, this.size, this.loadingTime);
    }

    try {
      this.status = 'loading';
      // 委托给真实对象
      const content = await this.realImage.display();
      this.status = 'loaded';
      this.cachedContent = content;
      return content;
    } catch (error) {
      this.status = 'error';
      throw error;
    }
  }

  getUrl(): string {
    return this.url;
  }

  getTitle(): string {
    return this.title;
  }

  getSize(): number {
    return this.size;
  }

  getStatus(): 'unloaded' | 'loading' | 'loaded' | 'error' {
    return this.status;
  }
}

// 图片画廊 - 使用代理管理多个图片
class ImageGallery {
  private images: ImageProxy[];

  constructor() {
    this.images = [];
  }

  addImage(url: string, title: string, size: number, loadingTime?: number): void {
    const image = new ImageProxy(url, title, size, loadingTime);
    this.images.push(image);
  }

  getImages(): ImageProxy[] {
    return this.images;
  }

  getImageByUrl(url: string): ImageProxy | undefined {
    return this.images.find(img => img.getUrl() === url);
  }
}

// 创建带代理的图库实例
function createProxyImageGallery(): ImageGallery {
  const gallery = new ImageGallery();

  // 添加一些示例图片
  gallery.addImage('https://example.com/image1.jpg', '自然风景', 1240, 1500);
  gallery.addImage('https://example.com/image2.jpg', '城市夜景', 2100, 3000);
  gallery.addImage('https://example.com/image3.jpg', '海滩日落', 980, 800);
  gallery.addImage('https://example.com/image4.jpg', '山脉全景', 3500, 4000);
  gallery.addImage('https://example.com/image5.jpg', '花卉特写', 750, 1000);

  return gallery;
}

// 创建无代理直接加载的图库实例
function createDirectImageGallery(): RealImage[] {
  return [
    new RealImage('https://example.com/image1.jpg', '自然风景', 1240, 1500),
    new RealImage('https://example.com/image2.jpg', '城市夜景', 2100, 3000),
    new RealImage('https://example.com/image3.jpg', '海滩日落', 980, 800),
    new RealImage('https://example.com/image4.jpg', '山脉全景', 3500, 4000),
    new RealImage('https://example.com/image5.jpg', '花卉特写', 750, 1000)
  ];
}

// 在文件末尾统一导出
export {
  ImageInterface,
  RealImage,
  ImageProxy,
  ImageGallery,
  createProxyImageGallery,
  createDirectImageGallery
}