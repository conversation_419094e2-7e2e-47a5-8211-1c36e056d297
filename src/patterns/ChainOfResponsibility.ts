/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 12:35:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 12:35:00
 * @FilePath     : /src/patterns/ChainOfResponsibility.ts
 * @Description  : 责任链模式实现 - 客服工单处理系统
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 工单类型枚举
enum TicketType {
  TECHNICAL = 'technical',
  BILLING = 'billing',
  GENERAL = 'general',
  COMPLAINT = 'complaint',
  URGENT = 'urgent'
}

// 工单优先级枚举
enum Priority {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4
}

// 工单类
class SupportTicket {
  private id: string;
  private createdAt: Date;

  constructor(
    public title: string,
    public description: string,
    public type: TicketType,
    public priority: Priority,
    public customerName: string,
    public customerEmail: string
  ) {
    this.id = Math.random().toString(36).slice(2, 11);
    this.createdAt = new Date();
  }

  getId(): string {
    return this.id;
  }

  getCreatedAt(): Date {
    return this.createdAt;
  }

  getInfo(): string {
    return `工单 #${this.id} - ${this.title} (${this.type}, 优先级: ${this.priority}) - 客户: ${this.customerName}`;
  }

  getFullDetails(): string {
    return `
工单详情:
- ID: ${this.id}
- 标题: ${this.title}
- 描述: ${this.description}
- 类型: ${this.type}
- 优先级: ${this.priority}
- 客户: ${this.customerName} (${this.customerEmail})
- 创建时间: ${this.createdAt.toLocaleString()}
    `.trim();
  }
}

// 处理结果类
class HandlingResult {
  constructor(
    public handled: boolean,
    public handlerName: string,
    public message: string,
    public processingTime: number = 0
  ) {}

  getInfo(): string {
    return `${this.handled ? '✅' : '❌'} ${this.handlerName}: ${this.message}${this.processingTime > 0 ? ` (处理时间: ${this.processingTime}分钟)` : ''}`;
  }
}

// 抽象处理者
abstract class SupportHandler {
  protected nextHandler: SupportHandler | null = null;

  // 设置下一个处理者
  setNext(handler: SupportHandler): SupportHandler {
    this.nextHandler = handler;
    return handler;
  }

  // 处理请求的模板方法
  handle(ticket: SupportTicket): HandlingResult {
    const result = this.processTicket(ticket);
    
    if (result.handled) {
      return result;
    }
    
    if (this.nextHandler) {
      return this.nextHandler.handle(ticket);
    }
    
    return new HandlingResult(
      false,
      '系统',
      '没有合适的处理者能够处理此工单，已转入人工处理队列'
    );
  }

  // 抽象方法，由具体处理者实现
  protected abstract processTicket(ticket: SupportTicket): HandlingResult;
  
  // 获取处理者名称
  abstract getHandlerName(): string;
  
  // 获取处理能力描述
  abstract getCapabilities(): string[];
}

// 具体处理者 - 一级客服
class Level1SupportHandler extends SupportHandler {
  protected processTicket(ticket: SupportTicket): HandlingResult {
    // 一级客服只处理一般问题和低优先级问题
    if (ticket.type === TicketType.GENERAL && ticket.priority <= Priority.MEDIUM) {
      const processingTime = Math.floor(Math.random() * 15) + 5; // 5-20分钟
      return new HandlingResult(
        true,
        this.getHandlerName(),
        `已为客户 ${ticket.customerName} 解决一般咨询问题`,
        processingTime
      );
    }
    
    return new HandlingResult(
      false,
      this.getHandlerName(),
      '问题超出一级客服处理范围，转交上级处理'
    );
  }

  getHandlerName(): string {
    return '一级客服';
  }

  getCapabilities(): string[] {
    return ['一般咨询', '基础问题解答', '账户信息查询'];
  }
}

// 具体处理者 - 技术支持
class TechnicalSupportHandler extends SupportHandler {
  protected processTicket(ticket: SupportTicket): HandlingResult {
    // 技术支持处理技术问题
    if (ticket.type === TicketType.TECHNICAL && ticket.priority <= Priority.HIGH) {
      const processingTime = Math.floor(Math.random() * 45) + 15; // 15-60分钟
      return new HandlingResult(
        true,
        this.getHandlerName(),
        `已为客户 ${ticket.customerName} 解决技术问题: ${ticket.title}`,
        processingTime
      );
    }
    
    return new HandlingResult(
      false,
      this.getHandlerName(),
      '问题不属于技术支持范围或优先级过高，需要其他部门处理'
    );
  }

  getHandlerName(): string {
    return '技术支持';
  }

  getCapabilities(): string[] {
    return ['技术故障排查', '软件问题解决', '系统配置指导'];
  }
}

// 具体处理者 - 财务部门
class BillingSupportHandler extends SupportHandler {
  protected processTicket(ticket: SupportTicket): HandlingResult {
    // 财务部门处理账单相关问题
    if (ticket.type === TicketType.BILLING) {
      const processingTime = Math.floor(Math.random() * 30) + 10; // 10-40分钟
      return new HandlingResult(
        true,
        this.getHandlerName(),
        `已为客户 ${ticket.customerName} 处理账单问题: ${ticket.title}`,
        processingTime
      );
    }
    
    return new HandlingResult(
      false,
      this.getHandlerName(),
      '问题不属于财务部门处理范围'
    );
  }

  getHandlerName(): string {
    return '财务部门';
  }

  getCapabilities(): string[] {
    return ['账单查询', '付款问题', '退款处理', '价格咨询'];
  }
}

// 具体处理者 - 客户关系经理
class CustomerRelationshipHandler extends SupportHandler {
  protected processTicket(ticket: SupportTicket): HandlingResult {
    // 客户关系经理处理投诉和高优先级问题
    if (ticket.type === TicketType.COMPLAINT || ticket.priority >= Priority.HIGH) {
      const processingTime = Math.floor(Math.random() * 60) + 30; // 30-90分钟
      return new HandlingResult(
        true,
        this.getHandlerName(),
        `已为客户 ${ticket.customerName} 处理${ticket.type === TicketType.COMPLAINT ? '投诉' : '高优先级'}问题，并提供专属服务`,
        processingTime
      );
    }
    
    return new HandlingResult(
      false,
      this.getHandlerName(),
      '问题不需要客户关系经理介入'
    );
  }

  getHandlerName(): string {
    return '客户关系经理';
  }

  getCapabilities(): string[] {
    return ['客户投诉处理', '高优先级问题', '客户关系维护', '特殊需求处理'];
  }
}

// 具体处理者 - 紧急响应团队
class EmergencyResponseHandler extends SupportHandler {
  protected processTicket(ticket: SupportTicket): HandlingResult {
    // 紧急响应团队处理紧急和关键问题
    if (ticket.type === TicketType.URGENT || ticket.priority === Priority.CRITICAL) {
      const processingTime = Math.floor(Math.random() * 20) + 5; // 5-25分钟
      return new HandlingResult(
        true,
        this.getHandlerName(),
        `紧急响应团队已立即处理客户 ${ticket.customerName} 的紧急问题，并启动应急预案`,
        processingTime
      );
    }
    
    return new HandlingResult(
      false,
      this.getHandlerName(),
      '问题不属于紧急响应范围'
    );
  }

  getHandlerName(): string {
    return '紧急响应团队';
  }

  getCapabilities(): string[] {
    return ['紧急故障处理', '系统宕机响应', '关键客户支持', '24/7应急服务'];
  }
}

// 客服系统管理器
class CustomerSupportSystem {
  private handlerChain: SupportHandler;
  private processedTickets: Map<string, HandlingResult> = new Map();

  constructor() {
    // 构建责任链
    this.handlerChain = this.buildHandlerChain();
  }

  private buildHandlerChain(): SupportHandler {
    // 创建处理者实例
    const emergencyHandler = new EmergencyResponseHandler();
    const customerRelationshipHandler = new CustomerRelationshipHandler();
    const technicalHandler = new TechnicalSupportHandler();
    const billingHandler = new BillingSupportHandler();
    const level1Handler = new Level1SupportHandler();

    // 构建责任链：紧急 -> 客户关系 -> 技术支持 -> 财务 -> 一级客服
    emergencyHandler
      .setNext(customerRelationshipHandler)
      .setNext(technicalHandler)
      .setNext(billingHandler)
      .setNext(level1Handler);

    return emergencyHandler;
  }

  // 处理工单
  processTicket(ticket: SupportTicket): HandlingResult {
    console.log(`开始处理工单: ${ticket.getInfo()}`);
    
    const result = this.handlerChain.handle(ticket);
    this.processedTickets.set(ticket.getId(), result);
    
    console.log(`处理结果: ${result.getInfo()}`);
    return result;
  }

  // 批量处理工单
  processMultipleTickets(tickets: SupportTicket[]): HandlingResult[] {
    return tickets.map(ticket => this.processTicket(ticket));
  }

  // 获取处理历史
  getProcessingHistory(): Map<string, HandlingResult> {
    return new Map(this.processedTickets);
  }

  // 获取处理统计
  getProcessingStatistics(): {
    totalProcessed: number;
    successfullyHandled: number;
    unhandled: number;
    averageProcessingTime: number;
    handlerStats: Record<string, number>;
  } {
    const results = Array.from(this.processedTickets.values());
    const totalProcessed = results.length;
    const successfullyHandled = results.filter(r => r.handled).length;
    const unhandled = totalProcessed - successfullyHandled;
    
    const handledResults = results.filter(r => r.handled && r.processingTime > 0);
    const averageProcessingTime = handledResults.length > 0 
      ? handledResults.reduce((sum, r) => sum + r.processingTime, 0) / handledResults.length 
      : 0;

    const handlerStats: Record<string, number> = {};
    results.forEach(result => {
      if (result.handled) {
        handlerStats[result.handlerName] = (handlerStats[result.handlerName] || 0) + 1;
      }
    });

    return {
      totalProcessed,
      successfullyHandled,
      unhandled,
      averageProcessingTime: Math.round(averageProcessingTime * 100) / 100,
      handlerStats
    };
  }

  // 重新配置责任链
  reconfigureChain(handlers: SupportHandler[]): void {
    if (handlers.length === 0) return;
    
    this.handlerChain = handlers[0];
    for (let i = 0; i < handlers.length - 1; i++) {
      handlers[i].setNext(handlers[i + 1]);
    }
  }

  // 获取所有处理者的能力描述
  getHandlerCapabilities(): Record<string, string[]> {
    const handlers = [
      new EmergencyResponseHandler(),
      new CustomerRelationshipHandler(),
      new TechnicalSupportHandler(),
      new BillingSupportHandler(),
      new Level1SupportHandler()
    ];

    const capabilities: Record<string, string[]> = {};
    handlers.forEach(handler => {
      capabilities[handler.getHandlerName()] = handler.getCapabilities();
    });

    return capabilities;
  }
}

// 工单工厂
class TicketFactory {
  static createTechnicalTicket(title: string, description: string, priority: Priority, customerName: string, customerEmail: string): SupportTicket {
    return new SupportTicket(title, description, TicketType.TECHNICAL, priority, customerName, customerEmail);
  }

  static createBillingTicket(title: string, description: string, priority: Priority, customerName: string, customerEmail: string): SupportTicket {
    return new SupportTicket(title, description, TicketType.BILLING, priority, customerName, customerEmail);
  }

  static createComplaintTicket(title: string, description: string, priority: Priority, customerName: string, customerEmail: string): SupportTicket {
    return new SupportTicket(title, description, TicketType.COMPLAINT, priority, customerName, customerEmail);
  }

  static createUrgentTicket(title: string, description: string, priority: Priority, customerName: string, customerEmail: string): SupportTicket {
    return new SupportTicket(title, description, TicketType.URGENT, priority, customerName, customerEmail);
  }

  static createGeneralTicket(title: string, description: string, priority: Priority, customerName: string, customerEmail: string): SupportTicket {
    return new SupportTicket(title, description, TicketType.GENERAL, priority, customerName, customerEmail);
  }
}

// 导出类和枚举
export { CustomerSupportSystem, TicketFactory, TicketType, Priority };
export type SupportTicketType = SupportTicket;
export type SupportHandlerType = SupportHandler;
export type HandlingResultType = HandlingResult;
export type CustomerSupportSystemType = CustomerSupportSystem;
