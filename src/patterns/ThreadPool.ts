/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-27 17:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-27 17:00:00
 * @FilePath     : /src/patterns/ThreadPool.ts
 * @Description  : 线程池模式的TypeScript实现
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-27 17:00:00
 */

// 任务接口
export interface ITask {
  id: string;
  name: string;
  execute(): Promise<any>;
}

// 任务类
export class Task implements ITask {
  public readonly id: string;
  public readonly name: string;
  private readonly taskFunction: () => Promise<any>;

  constructor(name: string, taskFunction: () => Promise<any>) {
    this.id = `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    this.name = name;
    this.taskFunction = taskFunction;
  }

  async execute(): Promise<any> {
    return await this.taskFunction();
  }
}

// 工作线程类
export class WorkerThread {
  public readonly id: number;
  private busy: boolean = false;
  private currentTask: Task | null = null;
  private threadPool: ThreadPool;

  constructor(id: number, threadPool: ThreadPool) {
    this.id = id;
    this.threadPool = threadPool;
  }

  isBusy(): boolean {
    return this.busy;
  }

  getCurrentTask(): Task | null {
    return this.currentTask;
  }

  async executeTask(task: Task): Promise<void> {
    this.busy = true;
    this.currentTask = task;

    try {
      // 通知任务开始
      if (this.threadPool.onTaskStart) {
        this.threadPool.onTaskStart(task);
      }

      const startTime = Date.now();
      await task.execute();
      const executionTime = Date.now() - startTime;

      // 通知任务完成
      if (this.threadPool.onTaskComplete) {
        this.threadPool.onTaskComplete(task, executionTime);
      }
    } catch (error) {
      // 通知任务错误
      if (this.threadPool.onTaskError) {
        this.threadPool.onTaskError(task, error);
      }
    } finally {
      this.busy = false;
      this.currentTask = null;

      // 尝试获取下一个任务
      this.threadPool.tryAssignTask(this);
    }
  }
}

// 线程池配置接口
export interface ThreadPoolConfig {
  corePoolSize: number;        // 核心线程数
  maximumPoolSize?: number;    // 最大线程数
  queueCapacity: number;       // 队列容量
  keepAliveTime?: number;      // 线程空闲时间（毫秒）
  rejectedExecutionHandler?: (task: Task) => void; // 拒绝策略
}

// 拒绝策略枚举
export enum RejectedExecutionPolicy {
  ABORT = 'abort',           // 抛出异常
  DISCARD = 'discard',       // 丢弃任务
  DISCARD_OLDEST = 'discard_oldest', // 丢弃最老的任务
  CALLER_RUNS = 'caller_runs' // 调用者执行
}

// 线程池类
export class ThreadPool {
  private config: ThreadPoolConfig;
  private threads: WorkerThread[] = [];
  private taskQueue: Task[] = [];
  private isShutdown: boolean = false;
  private completedTaskCount: number = 0;

  // 事件回调
  public onTaskStart?: (task: Task) => void;
  public onTaskComplete?: (task: Task, executionTime: number) => void;
  public onTaskError?: (task: Task, error: any) => void;

  constructor(config: ThreadPoolConfig) {
    this.config = {
      maximumPoolSize: config.corePoolSize,
      keepAliveTime: 60000, // 默认60秒
      rejectedExecutionHandler: this.defaultRejectedHandler.bind(this),
      ...config
    };

    this.initializeThreads();
  }

  private initializeThreads(): void {
    for (let i = 0; i < this.config.corePoolSize; i++) {
      const thread = new WorkerThread(i + 1, this);
      this.threads.push(thread);
    }
  }

  private defaultRejectedHandler(task: Task): void {
    throw new Error(`Task ${task.name} rejected: Queue is full`);
  }

  // 提交任务
  async submit(task: Task): Promise<void> {
    if (this.isShutdown) {
      throw new Error('ThreadPool is shutdown');
    }

    // 尝试分配给空闲线程
    const idleThread = this.threads.find(thread => !thread.isBusy());
    if (idleThread) {
      idleThread.executeTask(task);
      return;
    }

    // 检查队列容量
    if (this.taskQueue.length >= this.config.queueCapacity) {
      if (this.config.rejectedExecutionHandler) {
        this.config.rejectedExecutionHandler(task);
      }
      return;
    }

    // 添加到队列
    this.taskQueue.push(task);
  }

  // 尝试分配任务给线程
  tryAssignTask(thread: WorkerThread): void {
    if (this.taskQueue.length > 0 && !thread.isBusy()) {
      const task = this.taskQueue.shift();
      if (task) {
        thread.executeTask(task);
      }
    }
  }

  // 调整线程池大小
  resize(newSize: number): void {
    if (newSize < 1) {
      throw new Error('Pool size must be at least 1');
    }

    const currentSize = this.threads.length;

    if (newSize > currentSize) {
      // 增加线程
      for (let i = currentSize; i < newSize; i++) {
        const thread = new WorkerThread(i + 1, this);
        this.threads.push(thread);

        // 尝试分配任务
        this.tryAssignTask(thread);
      }
    } else if (newSize < currentSize) {
      // 减少线程（只移除空闲线程）
      const threadsToRemove = this.threads
        .filter(thread => !thread.isBusy())
        .slice(0, currentSize - newSize);

      this.threads = this.threads.filter(thread =>
        !threadsToRemove.includes(thread)
      );
    }

    this.config.corePoolSize = newSize;
    this.config.maximumPoolSize = newSize;
  }

  // 获取线程信息
  getThreads(): WorkerThread[] {
    return [...this.threads];
  }

  // 获取队列中的任务
  getQueuedTasks(): Task[] {
    return [...this.taskQueue];
  }

  // 获取活跃线程数
  getActiveCount(): number {
    return this.threads.filter(thread => thread.isBusy()).length;
  }

  // 获取队列大小
  getQueueSize(): number {
    return this.taskQueue.length;
  }

  // 获取已完成任务数
  getCompletedTaskCount(): number {
    return this.completedTaskCount;
  }

  // 获取线程池状态
  getPoolStatus(): {
    corePoolSize: number;
    maximumPoolSize: number;
    activeCount: number;
    queueSize: number;
    completedTaskCount: number;
    isShutdown: boolean;
  } {
    return {
      corePoolSize: this.config.corePoolSize,
      maximumPoolSize: this.config.maximumPoolSize || this.config.corePoolSize,
      activeCount: this.getActiveCount(),
      queueSize: this.getQueueSize(),
      completedTaskCount: this.completedTaskCount,
      isShutdown: this.isShutdown
    };
  }

  // 等待所有任务完成
  async awaitTermination(timeout?: number): Promise<boolean> {
    const startTime = Date.now();

    while (this.getActiveCount() > 0 || this.getQueueSize() > 0) {
      if (timeout && (Date.now() - startTime) > timeout) {
        return false;
      }

      // 等待一小段时间
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return true;
  }

  // 关闭线程池
  shutdown(): void {
    this.isShutdown = true;
    this.taskQueue = [];
  }

  // 立即关闭线程池
  shutdownNow(): Task[] {
    this.isShutdown = true;
    const remainingTasks = [...this.taskQueue];
    this.taskQueue = [];
    return remainingTasks;
  }
}

// 线程池工厂类
export class ThreadPoolFactory {
  // 创建固定大小的线程池
  static newFixedThreadPool(nThreads: number, queueCapacity: number = 50): ThreadPool {
    return new ThreadPool({
      corePoolSize: nThreads,
      maximumPoolSize: nThreads,
      queueCapacity
    });
  }

  // 创建单线程池
  static newSingleThreadExecutor(queueCapacity: number = 50): ThreadPool {
    return new ThreadPool({
      corePoolSize: 1,
      maximumPoolSize: 1,
      queueCapacity
    });
  }

  // 创建缓存线程池（模拟）
  static newCachedThreadPool(queueCapacity: number = 100): ThreadPool {
    return new ThreadPool({
      corePoolSize: 0,
      maximumPoolSize: 10,
      queueCapacity,
      keepAliveTime: 60000
    });
  }
}

// 线程池监控器
export class ThreadPoolMonitor {
  private threadPool: ThreadPool;
  private metrics: {
    submittedTasks: number;
    completedTasks: number;
    rejectedTasks: number;
    averageExecutionTime: number;
    peakActiveThreads: number;
  };

  constructor(threadPool: ThreadPool) {
    this.threadPool = threadPool;
    this.metrics = {
      submittedTasks: 0,
      completedTasks: 0,
      rejectedTasks: 0,
      averageExecutionTime: 0,
      peakActiveThreads: 0
    };

    this.setupMonitoring();
  }

  private setupMonitoring(): void {
    // 监控任务完成
    const originalOnTaskComplete = this.threadPool.onTaskComplete;
    this.threadPool.onTaskComplete = (task: Task, executionTime: number) => {
      this.metrics.completedTasks++;
      this.updateAverageExecutionTime(executionTime);
      this.updatePeakActiveThreads();

      if (originalOnTaskComplete) {
        originalOnTaskComplete(task, executionTime);
      }
    };
  }

  private updateAverageExecutionTime(executionTime: number): void {
    const totalTime = this.metrics.averageExecutionTime * (this.metrics.completedTasks - 1);
    this.metrics.averageExecutionTime = (totalTime + executionTime) / this.metrics.completedTasks;
  }

  private updatePeakActiveThreads(): void {
    const currentActive = this.threadPool.getActiveCount();
    if (currentActive > this.metrics.peakActiveThreads) {
      this.metrics.peakActiveThreads = currentActive;
    }
  }

  getMetrics() {
    return { ...this.metrics };
  }

  reset(): void {
    this.metrics = {
      submittedTasks: 0,
      completedTasks: 0,
      rejectedTasks: 0,
      averageExecutionTime: 0,
      peakActiveThreads: 0
    };
  }
}

// 导出类型和类
export type { ITask, ThreadPoolConfig };
