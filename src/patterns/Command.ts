// Command Pattern Implementation

// 命令接口
interface Command {
  execute(): void;
  undo(): void;
  getName(): string;
  getDescription(): string;
}

// 接收者类 - 文本编辑器
class TextEditor {
  private content: string;
  private selectionStart: number;
  private selectionEnd: number;
  private clipboard: string;

  constructor() {
    this.content = '';
    this.selectionStart = 0;
    this.selectionEnd = 0;
    this.clipboard = '';
  }

  insertText(text: string): void {
    // 如果有选中文本，先删除
    if (this.hasSelection()) {
      this.deleteSelectedText();
    }

    // 插入文本
    this.content =
      this.content.substring(0, this.selectionStart) +
      text +
      this.content.substring(this.selectionStart);

    // 更新光标位置
    this.selectionStart += text.length;
    this.selectionEnd = this.selectionStart;
  }

  deleteText(start: number, end: number): string {
    const deletedText = this.content.substring(start, end);
    this.content = this.content.substring(0, start) + this.content.substring(end);

    // 更新光标位置
    this.selectionStart = start;
    this.selectionEnd = start;

    return deletedText;
  }

  deleteSelectedText(): string {
    if (!this.hasSelection()) return '';
    return this.deleteText(this.selectionStart, this.selectionEnd);
  }

  select(start: number, end: number): void {
    this.selectionStart = Math.max(0, Math.min(start, this.content.length));
    this.selectionEnd = Math.max(0, Math.min(end, this.content.length));
  }

  hasSelection(): boolean {
    return this.selectionStart !== this.selectionEnd;
  }

  getSelectedText(): string {
    return this.content.substring(this.selectionStart, this.selectionEnd);
  }

  copySelectedText(): void {
    if (this.hasSelection()) {
      this.clipboard = this.getSelectedText();
    }
  }

  cutSelectedText(): void {
    if (this.hasSelection()) {
      this.clipboard = this.deleteSelectedText();
    }
  }

  paste(): void {
    this.insertText(this.clipboard);
  }

  getContent(): string {
    return this.content;
  }

  getSelectionStart(): number {
    return this.selectionStart;
  }

  getSelectionEnd(): number {
    return this.selectionEnd;
  }

  getClipboard(): string {
    return this.clipboard;
  }

  setCursorPosition(position: number): void {
    this.selectionStart = Math.max(0, Math.min(position, this.content.length));
    this.selectionEnd = this.selectionStart;
  }
}

// 具体命令类 - 插入文本
class InsertTextCommand implements Command {
  private editor: TextEditor;
  private text: string;
  private previousContent: string;
  private previousSelectionStart: number;
  private previousSelectionEnd: number;

  constructor(editor: TextEditor, text: string) {
    this.editor = editor;
    this.text = text;
    this.previousContent = editor.getContent();
    this.previousSelectionStart = editor.getSelectionStart();
    this.previousSelectionEnd = editor.getSelectionEnd();
  }

  execute(): void {
    this.editor.insertText(this.text);
  }

  undo(): void {
    // 恢复到执行命令前的状态
    const currentContent = this.editor.getContent();
    const newTextLength = currentContent.length - this.previousContent.length;

    if (newTextLength > 0) {
      // 删除插入的文本
      const insertPosition = this.previousSelectionStart;
      this.editor.select(insertPosition, insertPosition + newTextLength);
      this.editor.deleteSelectedText();

      // 恢复选区
      this.editor.select(this.previousSelectionStart, this.previousSelectionEnd);
    }
  }

  getName(): string {
    return '插入文本';
  }

  getDescription(): string {
    return `插入文本: "${this.text.length > 10 ? this.text.substring(0, 10) + '...' : this.text}"`;
  }
}

// 具体命令类 - 删除文本
class DeleteTextCommand implements Command {
  private editor: TextEditor;
  private startPosition: number;
  private endPosition: number;
  private deletedText: string;

  constructor(editor: TextEditor, start: number, end: number) {
    this.editor = editor;
    this.startPosition = start;
    this.endPosition = end;
    this.deletedText = '';
  }

  execute(): void {
    this.deletedText = this.editor.deleteText(this.startPosition, this.endPosition);
  }

  undo(): void {
    // 恢复删除的文本
    this.editor.select(this.startPosition, this.startPosition);
    this.editor.insertText(this.deletedText);

    // 恢复选区
    this.editor.select(this.startPosition, this.startPosition + this.deletedText.length);
  }

  getName(): string {
    return '删除文本';
  }

  getDescription(): string {
    const preview = this.deletedText || '空';
    return `删除文本: "${preview.length > 10 ? preview.substring(0, 10) + '...' : preview}"`;
  }
}

// 具体命令类 - 复制文本
class CopyCommand implements Command {
  private editor: TextEditor;
  private previousClipboard: string;

  constructor(editor: TextEditor) {
    this.editor = editor;
    this.previousClipboard = editor.getClipboard();
  }

  execute(): void {
    this.editor.copySelectedText();
  }

  undo(): void {
    // 恢复剪贴板
    // 这里需要访问内部实现，实际应用中可能需要更好的设计
    Object.defineProperty(this.editor, 'clipboard', {
      value: this.previousClipboard,
      writable: true
    });
  }

  getName(): string {
    return '复制文本';
  }

  getDescription(): string {
    const selectedText = this.editor.getSelectedText();
    return `复制: "${selectedText.length > 10 ? selectedText.substring(0, 10) + '...' : selectedText}"`;
  }
}

// 具体命令类 - 剪切文本
class CutCommand implements Command {
  private editor: TextEditor;
  private position: number;
  private cutText: string;
  private previousClipboard: string;

  constructor(editor: TextEditor) {
    this.editor = editor;
    this.position = editor.getSelectionStart();
    this.cutText = editor.getSelectedText();
    this.previousClipboard = editor.getClipboard();
  }

  execute(): void {
    this.editor.cutSelectedText();
  }

  undo(): void {
    // 恢复剪切的文本
    this.editor.setCursorPosition(this.position);
    this.editor.insertText(this.cutText);

    // 恢复选区
    this.editor.select(this.position, this.position + this.cutText.length);

    // 恢复剪贴板
    Object.defineProperty(this.editor, 'clipboard', {
      value: this.previousClipboard,
      writable: true
    });
  }

  getName(): string {
    return '剪切文本';
  }

  getDescription(): string {
    return `剪切: "${this.cutText.length > 10 ? this.cutText.substring(0, 10) + '...' : this.cutText}"`;
  }
}

// 具体命令类 - 粘贴文本
class PasteCommand implements Command {
  private editor: TextEditor;
  private previousContent: string;
  private previousSelectionStart: number;
  private previousSelectionEnd: number;
  private pastedText: string;

  constructor(editor: TextEditor) {
    this.editor = editor;
    this.previousContent = editor.getContent();
    this.previousSelectionStart = editor.getSelectionStart();
    this.previousSelectionEnd = editor.getSelectionEnd();
    this.pastedText = editor.getClipboard();
  }

  execute(): void {
    this.editor.paste();
  }

  undo(): void {
    // 撤销粘贴操作
    const newPosition = this.previousSelectionStart;

    // 删除粘贴的文本
    this.editor.select(newPosition, newPosition + this.pastedText.length);
    this.editor.deleteSelectedText();

    // 恢复原始选区
    this.editor.select(this.previousSelectionStart, this.previousSelectionEnd);
  }

  getName(): string {
    return '粘贴文本';
  }

  getDescription(): string {
    const clipboard = this.editor.getClipboard();
    return `粘贴: "${clipboard.length > 10 ? clipboard.substring(0, 10) + '...' : clipboard}"`;
  }
}

// 调用者类 - 命令历史记录管理
class CommandHistory {
  private history: Command[] = [];
  private undoneCommands: Command[] = [];
  private maxHistory: number;

  constructor(maxHistory = 100) {
    this.maxHistory = maxHistory;
  }

  execute(command: Command): void {
    command.execute();
    this.history.push(command);

    // 执行新命令后清除撤销历史
    this.undoneCommands = [];

    // 限制历史记录大小
    if (this.history.length > this.maxHistory) {
      this.history.shift(); // 移除最旧的命令
    }
  }

  undo(): boolean {
    if (this.history.length === 0) return false;

    const command = this.history.pop()!;
    command.undo();
    this.undoneCommands.push(command);

    return true;
  }

  redo(): boolean {
    if (this.undoneCommands.length === 0) return false;

    const command = this.undoneCommands.pop()!;
    command.execute();
    this.history.push(command);

    return true;
  }

  getHistory(): Command[] {
    return [...this.history];
  }

  getUndoneCommands(): Command[] {
    return [...this.undoneCommands];
  }

  clear(): void {
    this.history = [];
    this.undoneCommands = [];
  }
}

// 创建文本编辑器实例
function createTextEditor(): TextEditor {
  return new TextEditor();
}

// 创建命令历史记录管理器
function createCommandHistory(maxHistory = 100): CommandHistory {
  return new CommandHistory(maxHistory);
}

// 创建文本编辑器命令
function createInsertCommand(editor: TextEditor, text: string): InsertTextCommand {
  return new InsertTextCommand(editor, text);
}

function createDeleteCommand(editor: TextEditor, start: number, end: number): DeleteTextCommand {
  return new DeleteTextCommand(editor, start, end);
}

function createCopyCommand(editor: TextEditor): CopyCommand {
  return new CopyCommand(editor);
}

function createCutCommand(editor: TextEditor): CutCommand {
  return new CutCommand(editor);
}

function createPasteCommand(editor: TextEditor): PasteCommand {
  return new PasteCommand(editor);
}

// 导出
export {
  TextEditor,
  CommandHistory,
  InsertTextCommand,
  DeleteTextCommand,
  CopyCommand,
  CutCommand,
  PasteCommand,
  createTextEditor,
  createCommandHistory,
  createInsertCommand,
  createDeleteCommand,
  createCopyCommand,
  createCutCommand,
  createPasteCommand
};

// 导出类型
export type { Command };