// 解释器模式 (Interpreter Pattern) - 数学表达式计算器

// 上下文类 - 存储变量和计算状态
export class Context {
  private variables: Map<string, number> = new Map()
  private calculationHistory: string[] = []

  setVariable(name: string, value: number): void {
    this.variables.set(name, value)
  }

  getVariable(name: string): number {
    if (!this.variables.has(name)) {
      throw new Error(`未定义的变量: ${name}`)
    }
    return this.variables.get(name)!
  }

  hasVariable(name: string): boolean {
    return this.variables.has(name)
  }

  getVariables(): Map<string, number> {
    return new Map(this.variables)
  }

  addToHistory(expression: string): void {
    this.calculationHistory.push(expression)
  }

  getHistory(): string[] {
    return [...this.calculationHistory]
  }

  clearHistory(): void {
    this.calculationHistory = []
  }

  clearVariables(): void {
    this.variables.clear()
  }
}

// 抽象表达式接口
interface Expression {
  interpret(context: Context): number
  toString(): string
}

// 终结符表达式 - 数字
export class NumberExpression implements Expression {
  private value: number

  constructor(value: number) {
    this.value = value
  }

  interpret(context: Context): number {
    return this.value
  }

  toString(): string {
    return this.value.toString()
  }
}

// 终结符表达式 - 变量
export class VariableExpression implements Expression {
  private name: string

  constructor(name: string) {
    this.name = name
  }

  interpret(context: Context): number {
    return context.getVariable(this.name)
  }

  toString(): string {
    return this.name
  }
}

// 非终结符表达式 - 加法
export class AddExpression implements Expression {
  private left: Expression
  private right: Expression

  constructor(left: Expression, right: Expression) {
    this.left = left
    this.right = right
  }

  interpret(context: Context): number {
    return this.left.interpret(context) + this.right.interpret(context)
  }

  toString(): string {
    return `(${this.left.toString()} + ${this.right.toString()})`
  }
}

// 非终结符表达式 - 减法
export class SubtractExpression implements Expression {
  private left: Expression
  private right: Expression

  constructor(left: Expression, right: Expression) {
    this.left = left
    this.right = right
  }

  interpret(context: Context): number {
    return this.left.interpret(context) - this.right.interpret(context)
  }

  toString(): string {
    return `(${this.left.toString()} - ${this.right.toString()})`
  }
}

// 非终结符表达式 - 乘法
export class MultiplyExpression implements Expression {
  private left: Expression
  private right: Expression

  constructor(left: Expression, right: Expression) {
    this.left = left
    this.right = right
  }

  interpret(context: Context): number {
    return this.left.interpret(context) * this.right.interpret(context)
  }

  toString(): string {
    return `(${this.left.toString()} * ${this.right.toString()})`
  }
}

// 非终结符表达式 - 除法
export class DivideExpression implements Expression {
  private left: Expression
  private right: Expression

  constructor(left: Expression, right: Expression) {
    this.left = left
    this.right = right
  }

  interpret(context: Context): number {
    const rightValue = this.right.interpret(context)
    if (rightValue === 0) {
      throw new Error('除数不能为零')
    }
    return this.left.interpret(context) / rightValue
  }

  toString(): string {
    return `(${this.left.toString()} / ${this.right.toString()})`
  }
}

// 词法分析器 - 将输入字符串分解为标记
class Lexer {
  private input: string
  private position: number = 0

  constructor(input: string) {
    this.input = input.replace(/\s+/g, '') // 移除空格
  }

  getNextToken(): Token | null {
    if (this.position >= this.input.length) {
      return null
    }

    const char = this.input[this.position]

    // 数字
    if (/\d/.test(char)) {
      return this.readNumber()
    }

    // 变量名
    if (/[a-zA-Z]/.test(char)) {
      return this.readVariable()
    }

    // 操作符和括号
    this.position++
    switch (char) {
      case '+': return { type: 'PLUS', value: '+' }
      case '-': return { type: 'MINUS', value: '-' }
      case '*': return { type: 'MULTIPLY', value: '*' }
      case '/': return { type: 'DIVIDE', value: '/' }
      case '(': return { type: 'LPAREN', value: '(' }
      case ')': return { type: 'RPAREN', value: ')' }
      default: throw new Error(`未知字符: ${char}`)
    }
  }

  private readNumber(): Token {
    let value = ''
    while (this.position < this.input.length && /[\d.]/.test(this.input[this.position])) {
      value += this.input[this.position]
      this.position++
    }
    return { type: 'NUMBER', value }
  }

  private readVariable(): Token {
    let value = ''
    while (this.position < this.input.length && /[a-zA-Z0-9]/.test(this.input[this.position])) {
      value += this.input[this.position]
      this.position++
    }
    return { type: 'VARIABLE', value }
  }
}

// 标记接口
interface Token {
  type: 'NUMBER' | 'VARIABLE' | 'PLUS' | 'MINUS' | 'MULTIPLY' | 'DIVIDE' | 'LPAREN' | 'RPAREN'
  value: string
}

// 语法分析器 - 将标记序列转换为表达式树
class Parser {
  private tokens: Token[]
  private position: number = 0

  constructor(tokens: Token[]) {
    this.tokens = tokens
  }

  parse(): Expression {
    const expr = this.parseExpression()
    if (this.position < this.tokens.length) {
      throw new Error(`意外的标记: ${this.tokens[this.position].value}`)
    }
    return expr
  }

  private parseExpression(): Expression {
    let left = this.parseTerm()

    while (this.position < this.tokens.length) {
      const token = this.tokens[this.position]
      if (token.type === 'PLUS') {
        this.position++
        const right = this.parseTerm()
        left = new AddExpression(left, right)
      } else if (token.type === 'MINUS') {
        this.position++
        const right = this.parseTerm()
        left = new SubtractExpression(left, right)
      } else {
        break
      }
    }

    return left
  }

  private parseTerm(): Expression {
    let left = this.parseFactor()

    while (this.position < this.tokens.length) {
      const token = this.tokens[this.position]
      if (token.type === 'MULTIPLY') {
        this.position++
        const right = this.parseFactor()
        left = new MultiplyExpression(left, right)
      } else if (token.type === 'DIVIDE') {
        this.position++
        const right = this.parseFactor()
        left = new DivideExpression(left, right)
      } else {
        break
      }
    }

    return left
  }

  private parseFactor(): Expression {
    const token = this.tokens[this.position]

    if (token.type === 'NUMBER') {
      this.position++
      return new NumberExpression(parseFloat(token.value))
    }

    if (token.type === 'VARIABLE') {
      this.position++
      return new VariableExpression(token.value)
    }

    if (token.type === 'LPAREN') {
      this.position++
      const expr = this.parseExpression()
      if (this.position >= this.tokens.length || this.tokens[this.position].type !== 'RPAREN') {
        throw new Error('缺少右括号')
      }
      this.position++
      return expr
    }

    throw new Error(`意外的标记: ${token.value}`)
  }
}

// 表达式计算器 - 主要的客户端类
export class ExpressionCalculator {
  private context: Context

  constructor() {
    this.context = new Context()
  }

  // 设置变量
  setVariable(name: string, value: number): void {
    this.context.setVariable(name, value)
  }

  // 获取变量
  getVariable(name: string): number {
    return this.context.getVariable(name)
  }

  // 获取所有变量
  getVariables(): Map<string, number> {
    return this.context.getVariables()
  }

  // 计算表达式
  calculate(expression: string): number {
    try {
      // 词法分析
      const lexer = new Lexer(expression)
      const tokens: Token[] = []
      let token = lexer.getNextToken()
      while (token) {
        tokens.push(token)
        token = lexer.getNextToken()
      }

      // 语法分析
      const parser = new Parser(tokens)
      const expr = parser.parse()

      // 解释执行
      const result = expr.interpret(this.context)
      
      // 记录历史
      this.context.addToHistory(`${expression} = ${result}`)
      
      return result
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error)
      this.context.addToHistory(`${expression} = 错误: ${errorMsg}`)
      throw error
    }
  }

  // 获取计算历史
  getHistory(): string[] {
    return this.context.getHistory()
  }

  // 清空历史
  clearHistory(): void {
    this.context.clearHistory()
  }

  // 清空变量
  clearVariables(): void {
    this.context.clearVariables()
  }

  // 获取上下文
  getContext(): Context {
    return this.context
  }

  // 验证表达式语法
  validateExpression(expression: string): { valid: boolean; error?: string } {
    try {
      const lexer = new Lexer(expression)
      const tokens: Token[] = []
      let token = lexer.getNextToken()
      while (token) {
        tokens.push(token)
        token = lexer.getNextToken()
      }

      const parser = new Parser(tokens)
      parser.parse()
      
      return { valid: true }
    } catch (error) {
      return { 
        valid: false, 
        error: error instanceof Error ? error.message : String(error) 
      }
    }
  }
}
