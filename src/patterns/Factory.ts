/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 11:50:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 11:52:31
 * @FilePath     : /src/patterns/Factory.ts
 * @Description  : 工厂模式实现
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 支付处理器接口
interface PaymentProcessor {
  process(amount: number): string;
  getType(): string;
  getProvider(): string;
  getFeeRate(): number;
}

// 具体的支付处理器实现
class AlipayProcessor implements PaymentProcessor {
  process(amount: number): string {
    const fee = (amount * this.getFeeRate()) / 100;
    return `支付宝支付成功！金额：¥${amount}，手续费：¥${fee.toFixed(2)}`;
  }

  getType(): string {
    return "支付宝";
  }

  getProvider(): string {
    return "蚂蚁金服";
  }

  getFeeRate(): number {
    return 0.6;
  }
}

class WechatProcessor implements PaymentProcessor {
  process(amount: number): string {
    const fee = (amount * this.getFeeRate()) / 100;
    return `微信支付成功！金额：¥${amount}，手续费：¥${fee.toFixed(2)}`;
  }

  getType(): string {
    return "微信支付";
  }

  getProvider(): string {
    return "腾讯";
  }

  getFeeRate(): number {
    return 0.6;
  }
}

class UnionPayProcessor implements PaymentProcessor {
  process(amount: number): string {
    const fee = (amount * this.getFeeRate()) / 100;
    return `银联支付成功！金额：¥${amount}，手续费：¥${fee.toFixed(2)}`;
  }

  getType(): string {
    return "银联支付";
  }

  getProvider(): string {
    return "中国银联";
  }

  getFeeRate(): number {
    return 0.5;
  }
}

// 支付处理器工厂
class PaymentProcessorFactory {
  static createProcessor(type: string): PaymentProcessor {
    switch (type) {
      case "alipay":
        return new AlipayProcessor();
      case "wechat":
        return new WechatProcessor();
      case "unionpay":
        return new UnionPayProcessor();
      default:
        throw new Error(`不支持的支付类型: ${type}`);
    }
  }

  static getSupportedTypes(): string[] {
    return ["alipay", "wechat", "unionpay"];
  }
}

// 导出工厂实例和类型
export const paymentFactory = PaymentProcessorFactory;
export type PaymentProcessorType = PaymentProcessor;
export type PaymentFactoryType = typeof PaymentProcessorFactory;
