/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 11:56:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 12:00:13
 * @FilePath     : /src/patterns/Decorator.ts
 * @Description  : 装饰器模式实现
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 咖啡组件接口
interface Coffee {
  getDescription(): string;
  getCost(): number;
}

// 基础咖啡类
class BaseCoffee implements Coffee {
  protected description: string;
  protected cost: number;

  constructor(description: string, cost: number) {
    this.description = description;
    this.cost = cost;
  }

  getDescription(): string {
    return this.description;
  }

  getCost(): number {
    return this.cost;
  }
}

// 具体咖啡实现
class Espresso extends BaseCoffee {
  constructor() {
    super("浓缩咖啡", 15.0);
  }
}

class Americano extends BaseCoffee {
  constructor() {
    super("美式咖啡", 18.0);
  }
}

class Latte extends BaseCoffee {
  constructor() {
    super("拿铁咖啡", 25.0);
  }
}

class Cappuccino extends BaseCoffee {
  constructor() {
    super("卡布奇诺", 22.0);
  }
}

// 装饰器基类
abstract class CoffeeDecorator implements Coffee {
  protected coffee: Coffee;

  constructor(coffee: Coffee) {
    this.coffee = coffee;
  }

  getDescription(): string {
    return this.coffee.getDescription();
  }

  getCost(): number {
    return this.coffee.getCost();
  }
}

// 具体装饰器实现
class MilkDecorator extends CoffeeDecorator {
  getDescription(): string {
    return this.coffee.getDescription() + " + 牛奶";
  }

  getCost(): number {
    return this.coffee.getCost() + 3.0;
  }
}

class SugarDecorator extends CoffeeDecorator {
  getDescription(): string {
    return this.coffee.getDescription() + " + 糖";
  }

  getCost(): number {
    return this.coffee.getCost() + 1.0;
  }
}

class WhipDecorator extends CoffeeDecorator {
  getDescription(): string {
    return this.coffee.getDescription() + " + 奶泡";
  }

  getCost(): number {
    return this.coffee.getCost() + 4.0;
  }
}

class VanillaDecorator extends CoffeeDecorator {
  getDescription(): string {
    return this.coffee.getDescription() + " + 香草糖浆";
  }

  getCost(): number {
    return this.coffee.getCost() + 5.0;
  }
}

class CaramelDecorator extends CoffeeDecorator {
  getDescription(): string {
    return this.coffee.getDescription() + " + 焦糖糖浆";
  }

  getCost(): number {
    return this.coffee.getCost() + 6.0;
  }
}

// 咖啡工厂
class CoffeeFactory {
  static createBaseCoffee(type: string): Coffee {
    switch (type) {
      case "espresso":
        return new Espresso();
      case "americano":
        return new Americano();
      case "latte":
        return new Latte();
      case "cappuccino":
        return new Cappuccino();
      default:
        throw new Error(`不支持的咖啡类型: ${type}`);
    }
  }

  static createDecorator(type: string, coffee: Coffee): Coffee {
    switch (type) {
      case "milk":
        return new MilkDecorator(coffee);
      case "sugar":
        return new SugarDecorator(coffee);
      case "whip":
        return new WhipDecorator(coffee);
      case "vanilla":
        return new VanillaDecorator(coffee);
      case "caramel":
        return new CaramelDecorator(coffee);
      default:
        throw new Error(`不支持的装饰器类型: ${type}`);
    }
  }

  static getBaseCoffeeTypes(): Array<{
    id: string;
    name: string;
    price: number;
    icon: string;
  }> {
    return [
      { id: "espresso", name: "浓缩咖啡", price: 15.0, icon: "☕" },
      { id: "americano", name: "美式咖啡", price: 18.0, icon: "🇺🇸" },
      { id: "latte", name: "拿铁咖啡", price: 25.0, icon: "🥛" },
      { id: "cappuccino", name: "卡布奇诺", price: 22.0, icon: "🫖" },
    ];
  }

  static getDecoratorTypes(): Array<{
    id: string;
    name: string;
    price: number;
    icon: string;
  }> {
    return [
      { id: "milk", name: "牛奶", price: 3.0, icon: "🥛" },
      { id: "sugar", name: "糖", price: 1.0, icon: "🍯" },
      { id: "whip", name: "奶泡", price: 4.0, icon: "☁️" },
      { id: "vanilla", name: "香草糖浆", price: 5.0, icon: "🌿" },
      { id: "caramel", name: "焦糖糖浆", price: 6.0, icon: "🍮" },
    ];
  }
}

// 导出工厂和类型
export { CoffeeFactory };
export type CoffeeType = Coffee;
export type CoffeeFactoryType = typeof CoffeeFactory;
