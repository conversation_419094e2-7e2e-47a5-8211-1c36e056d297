/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-27 17:30:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-27 17:30:00
 * @FilePath     : /src/patterns/ReaderWriterLock.ts
 * @Description  : 读写锁模式的TypeScript实现
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-27 17:30:00
 */

// 锁策略类型
export type LockPolicy = 'reader-priority' | 'writer-priority' | 'fair';

// 锁状态接口
export interface LockStatus {
  activeReaders: number;
  waitingReaders: number;
  isWriting: boolean;
  waitingWriters: number;
}

// 等待队列项接口
interface WaitingItem {
  id: string;
  type: 'read' | 'write';
  resolve: () => void;
  timestamp: number;
}

// 读写锁类
export class ReaderWriterLock {
  private policy: LockPolicy;
  private maxReaders: number;
  
  // 锁状态
  private activeReaders: number = 0;
  private isWriting: boolean = false;
  
  // 等待队列
  private waitingQueue: WaitingItem[] = [];
  
  // 统计信息
  private totalReads: number = 0;
  private totalWrites: number = 0;
  private readStartTimes: Map<string, number> = new Map();
  private writeStartTimes: Map<string, number> = new Map();

  constructor(policy: LockPolicy = 'reader-priority', maxReaders: number = 10) {
    this.policy = policy;
    this.maxReaders = maxReaders;
  }

  // 获取读锁
  async readLock(): Promise<void> {
    return new Promise<void>((resolve) => {
      const readerId = this.generateId();
      this.readStartTimes.set(readerId, Date.now());

      // 检查是否可以立即获得读锁
      if (this.canAcquireReadLock()) {
        this.activeReaders++;
        this.totalReads++;
        resolve();
        return;
      }

      // 加入等待队列
      this.waitingQueue.push({
        id: readerId,
        type: 'read',
        resolve,
        timestamp: Date.now()
      });

      // 根据策略排序队列
      this.sortWaitingQueue();
    });
  }

  // 释放读锁
  readUnlock(): void {
    if (this.activeReaders > 0) {
      this.activeReaders--;
      
      // 如果没有活跃的读者，尝试唤醒等待的写者
      if (this.activeReaders === 0) {
        this.tryWakeUpNext();
      }
    }
  }

  // 获取写锁
  async writeLock(): Promise<void> {
    return new Promise<void>((resolve) => {
      const writerId = this.generateId();
      this.writeStartTimes.set(writerId, Date.now());

      // 检查是否可以立即获得写锁
      if (this.canAcquireWriteLock()) {
        this.isWriting = true;
        this.totalWrites++;
        resolve();
        return;
      }

      // 加入等待队列
      this.waitingQueue.push({
        id: writerId,
        type: 'write',
        resolve,
        timestamp: Date.now()
      });

      // 根据策略排序队列
      this.sortWaitingQueue();
    });
  }

  // 释放写锁
  writeUnlock(): void {
    if (this.isWriting) {
      this.isWriting = false;
      
      // 尝试唤醒等待的读者或写者
      this.tryWakeUpNext();
    }
  }

  // 检查是否可以获得读锁
  private canAcquireReadLock(): boolean {
    // 如果正在写入，不能获得读锁
    if (this.isWriting) {
      return false;
    }

    // 如果已达到最大读者数，不能获得读锁
    if (this.activeReaders >= this.maxReaders) {
      return false;
    }

    // 根据策略判断
    switch (this.policy) {
      case 'reader-priority':
        // 读者优先：只要没有写者在写，就可以获得读锁
        return true;

      case 'writer-priority':
        // 写者优先：如果有写者在等待，新的读者不能获得锁
        return !this.hasWaitingWriters();

      case 'fair':
        // 公平策略：按照请求顺序
        const nextWaiting = this.waitingQueue[0];
        return !nextWaiting || nextWaiting.type === 'read';

      default:
        return true;
    }
  }

  // 检查是否可以获得写锁
  private canAcquireWriteLock(): boolean {
    // 如果有活跃的读者或正在写入，不能获得写锁
    return this.activeReaders === 0 && !this.isWriting;
  }

  // 检查是否有等待的写者
  private hasWaitingWriters(): boolean {
    return this.waitingQueue.some(item => item.type === 'write');
  }

  // 尝试唤醒下一个等待的线程
  private tryWakeUpNext(): void {
    if (this.waitingQueue.length === 0) {
      return;
    }

    if (this.policy === 'reader-priority') {
      this.tryWakeUpReaders();
    } else if (this.policy === 'writer-priority') {
      this.tryWakeUpWriters() || this.tryWakeUpReaders();
    } else { // fair
      this.tryWakeUpFair();
    }
  }

  // 尝试唤醒读者（读者优先策略）
  private tryWakeUpReaders(): void {
    while (this.waitingQueue.length > 0 && this.canAcquireReadLock()) {
      const nextReader = this.waitingQueue.find(item => item.type === 'read');
      if (!nextReader) {
        break;
      }

      // 从队列中移除并唤醒
      const index = this.waitingQueue.indexOf(nextReader);
      this.waitingQueue.splice(index, 1);
      
      this.activeReaders++;
      this.totalReads++;
      nextReader.resolve();
    }
  }

  // 尝试唤醒写者（写者优先策略）
  private tryWakeUpWriters(): boolean {
    if (this.canAcquireWriteLock()) {
      const nextWriter = this.waitingQueue.find(item => item.type === 'write');
      if (nextWriter) {
        // 从队列中移除并唤醒
        const index = this.waitingQueue.indexOf(nextWriter);
        this.waitingQueue.splice(index, 1);
        
        this.isWriting = true;
        this.totalWrites++;
        nextWriter.resolve();
        return true;
      }
    }
    return false;
  }

  // 公平策略唤醒
  private tryWakeUpFair(): void {
    while (this.waitingQueue.length > 0) {
      const next = this.waitingQueue[0];
      
      if (next.type === 'read' && this.canAcquireReadLock()) {
        this.waitingQueue.shift();
        this.activeReaders++;
        this.totalReads++;
        next.resolve();
        
        // 继续唤醒后续的读者（如果可能）
        while (this.waitingQueue.length > 0 && 
               this.waitingQueue[0].type === 'read' && 
               this.canAcquireReadLock()) {
          const nextReader = this.waitingQueue.shift()!;
          this.activeReaders++;
          this.totalReads++;
          nextReader.resolve();
        }
        break;
      } else if (next.type === 'write' && this.canAcquireWriteLock()) {
        this.waitingQueue.shift();
        this.isWriting = true;
        this.totalWrites++;
        next.resolve();
        break;
      } else {
        // 无法唤醒下一个，退出
        break;
      }
    }
  }

  // 根据策略排序等待队列
  private sortWaitingQueue(): void {
    switch (this.policy) {
      case 'reader-priority':
        // 读者优先：读者排在前面
        this.waitingQueue.sort((a, b) => {
          if (a.type === 'read' && b.type === 'write') return -1;
          if (a.type === 'write' && b.type === 'read') return 1;
          return a.timestamp - b.timestamp;
        });
        break;

      case 'writer-priority':
        // 写者优先：写者排在前面
        this.waitingQueue.sort((a, b) => {
          if (a.type === 'write' && b.type === 'read') return -1;
          if (a.type === 'read' && b.type === 'write') return 1;
          return a.timestamp - b.timestamp;
        });
        break;

      case 'fair':
        // 公平策略：按时间戳排序
        this.waitingQueue.sort((a, b) => a.timestamp - b.timestamp);
        break;
    }
  }

  // 生成唯一ID
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // 获取锁状态
  getStatus(): LockStatus {
    return {
      activeReaders: this.activeReaders,
      waitingReaders: this.waitingQueue.filter(item => item.type === 'read').length,
      isWriting: this.isWriting,
      waitingWriters: this.waitingQueue.filter(item => item.type === 'write').length
    };
  }

  // 设置策略
  setPolicy(policy: LockPolicy): void {
    this.policy = policy;
    this.sortWaitingQueue();
    
    // 策略改变后，尝试唤醒等待的线程
    if (!this.isWriting && this.activeReaders === 0) {
      this.tryWakeUpNext();
    }
  }

  // 获取统计信息
  getStatistics(): {
    totalReads: number;
    totalWrites: number;
    currentPolicy: LockPolicy;
    queueLength: number;
  } {
    return {
      totalReads: this.totalReads,
      totalWrites: this.totalWrites,
      currentPolicy: this.policy,
      queueLength: this.waitingQueue.length
    };
  }

  // 重置锁状态
  reset(): void {
    // 清空等待队列（注意：这会导致等待的Promise永远不会resolve）
    this.waitingQueue = [];
    this.activeReaders = 0;
    this.isWriting = false;
    this.totalReads = 0;
    this.totalWrites = 0;
    this.readStartTimes.clear();
    this.writeStartTimes.clear();
  }

  // 检查是否有死锁风险
  hasDeadlockRisk(): boolean {
    // 简单的死锁检测：如果等待队列过长，可能存在死锁风险
    return this.waitingQueue.length > this.maxReaders * 2;
  }

  // 获取等待时间统计
  getWaitingTimeStats(): {
    averageWaitTime: number;
    maxWaitTime: number;
    waitingCount: number;
  } {
    if (this.waitingQueue.length === 0) {
      return {
        averageWaitTime: 0,
        maxWaitTime: 0,
        waitingCount: 0
      };
    }

    const now = Date.now();
    const waitTimes = this.waitingQueue.map(item => now - item.timestamp);
    const totalWaitTime = waitTimes.reduce((sum, time) => sum + time, 0);
    const averageWaitTime = totalWaitTime / waitTimes.length;
    const maxWaitTime = Math.max(...waitTimes);

    return {
      averageWaitTime: Math.round(averageWaitTime),
      maxWaitTime: Math.round(maxWaitTime),
      waitingCount: this.waitingQueue.length
    };
  }
}

// 读写锁工厂类
export class ReaderWriterLockFactory {
  // 创建读者优先的读写锁
  static createReaderPriorityLock(maxReaders: number = 10): ReaderWriterLock {
    return new ReaderWriterLock('reader-priority', maxReaders);
  }

  // 创建写者优先的读写锁
  static createWriterPriorityLock(maxReaders: number = 10): ReaderWriterLock {
    return new ReaderWriterLock('writer-priority', maxReaders);
  }

  // 创建公平的读写锁
  static createFairLock(maxReaders: number = 10): ReaderWriterLock {
    return new ReaderWriterLock('fair', maxReaders);
  }
}

// 读写锁监控器
export class ReaderWriterLockMonitor {
  private lock: ReaderWriterLock;
  private metrics: {
    readOperations: number;
    writeOperations: number;
    averageReadTime: number;
    averageWriteTime: number;
    peakReaders: number;
    lockContentionCount: number;
  };

  constructor(lock: ReaderWriterLock) {
    this.lock = lock;
    this.metrics = {
      readOperations: 0,
      writeOperations: 0,
      averageReadTime: 0,
      averageWriteTime: 0,
      peakReaders: 0,
      lockContentionCount: 0
    };
  }

  // 监控读操作
  async monitorRead<T>(readOperation: () => Promise<T>): Promise<T> {
    const startTime = Date.now();
    
    await this.lock.readLock();
    
    try {
      const result = await readOperation();
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.updateReadMetrics(duration);
      this.updatePeakReaders();
      
      return result;
    } finally {
      this.lock.readUnlock();
    }
  }

  // 监控写操作
  async monitorWrite<T>(writeOperation: () => Promise<T>): Promise<T> {
    const startTime = Date.now();
    
    await this.lock.writeLock();
    
    try {
      const result = await writeOperation();
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.updateWriteMetrics(duration);
      
      return result;
    } finally {
      this.lock.writeUnlock();
    }
  }

  private updateReadMetrics(duration: number): void {
    this.metrics.readOperations++;
    const totalTime = this.metrics.averageReadTime * (this.metrics.readOperations - 1);
    this.metrics.averageReadTime = (totalTime + duration) / this.metrics.readOperations;
  }

  private updateWriteMetrics(duration: number): void {
    this.metrics.writeOperations++;
    const totalTime = this.metrics.averageWriteTime * (this.metrics.writeOperations - 1);
    this.metrics.averageWriteTime = (totalTime + duration) / this.metrics.writeOperations;
  }

  private updatePeakReaders(): void {
    const currentReaders = this.lock.getStatus().activeReaders;
    if (currentReaders > this.metrics.peakReaders) {
      this.metrics.peakReaders = currentReaders;
    }
  }

  getMetrics() {
    return { ...this.metrics };
  }

  reset(): void {
    this.metrics = {
      readOperations: 0,
      writeOperations: 0,
      averageReadTime: 0,
      averageWriteTime: 0,
      peakReaders: 0,
      lockContentionCount: 0
    };
  }
}

// 导出类型
export type { LockStatus };
