/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-27 18:30:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-27 18:30:00
 * @FilePath     : /src/patterns/MVP.ts
 * @Description  : MVP模式的TypeScript实现
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-27 18:30:00
 */

// 用户数据模型
export interface User {
  id: number;
  name: string;
  email: string;
  active: boolean;
  createdAt: Date;
}

// View接口定义
export interface IUserView {
  showLoading(): void;
  hideLoading(): void;
  showUsers(users: User[]): void;
  showUserDetail(user: User): void;
  showError(message: string): void;
  showSuccess(message: string): void;
  clearSelection(): void;
}

// Model层 - 用户数据模型
export class UserModel {
  private users: User[] = [];
  private nextId: number = 1;
  private observers: Array<(users: User[]) => void> = [];

  constructor() {
    this.initializeData();
  }

  // 初始化示例数据
  private initializeData(): void {
    this.users = [
      {
        id: this.nextId++,
        name: '<PERSON>',
        email: '<EMAIL>',
        active: true,
        createdAt: new Date('2024-01-15')
      },
      {
        id: this.nextId++,
        name: 'Bob <PERSON>',
        email: '<EMAIL>',
        active: false,
        createdAt: new Date('2024-02-20')
      },
      {
        id: this.nextId++,
        name: 'Charlie Brown',
        email: '<EMAIL>',
        active: true,
        createdAt: new Date('2024-03-10')
      }
    ];
  }

  // 添加观察者（用于通知数据变化）
  addObserver(observer: (users: User[]) => void): void {
    this.observers.push(observer);
  }

  // 移除观察者
  removeObserver(observer: (users: User[]) => void): void {
    const index = this.observers.indexOf(observer);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }

  // 通知所有观察者
  private notifyObservers(): void {
    this.observers.forEach(observer => observer([...this.users]));
  }

  // 获取所有用户
  getAllUsers(): User[] {
    return [...this.users];
  }

  // 根据ID获取用户
  getUserById(id: number): User | undefined {
    return this.users.find(user => user.id === id);
  }

  // 添加用户
  addUser(userData: Omit<User, 'id' | 'createdAt'>): User {
    const newUser: User = {
      id: this.nextId++,
      ...userData,
      createdAt: new Date()
    };

    // 业务规则验证
    if (!this.validateUser(newUser)) {
      throw new Error('用户数据验证失败');
    }

    this.users.push(newUser);
    this.notifyObservers();
    return newUser;
  }

  // 更新用户
  updateUser(updatedUser: User): boolean {
    const index = this.users.findIndex(user => user.id === updatedUser.id);
    if (index === -1) {
      return false;
    }

    // 业务规则验证
    if (!this.validateUser(updatedUser)) {
      throw new Error('用户数据验证失败');
    }

    this.users[index] = { ...updatedUser };
    this.notifyObservers();
    return true;
  }

  // 删除用户
  deleteUser(id: number): boolean {
    const index = this.users.findIndex(user => user.id === id);
    if (index === -1) {
      return false;
    }

    this.users.splice(index, 1);
    this.notifyObservers();
    return true;
  }

  // 清空所有用户
  clearUsers(): void {
    this.users = [];
    this.notifyObservers();
  }

  // 搜索用户
  searchUsers(query: string): User[] {
    const lowerQuery = query.toLowerCase();
    return this.users.filter(user =>
      user.name.toLowerCase().includes(lowerQuery) ||
      user.email.toLowerCase().includes(lowerQuery)
    );
  }

  // 获取活跃用户
  getActiveUsers(): User[] {
    return this.users.filter(user => user.active);
  }

  // 用户数据验证（业务逻辑）
  private validateUser(user: User): boolean {
    // 姓名验证
    if (!user.name || user.name.trim().length < 2) {
      return false;
    }

    // 邮箱验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!user.email || !emailRegex.test(user.email)) {
      return false;
    }

    // 检查邮箱唯一性
    const existingUser = this.users.find(u => u.id !== user.id && u.email === user.email);
    if (existingUser) {
      return false;
    }

    return true;
  }

  // 获取用户统计信息
  getUserStats(): {
    total: number;
    active: number;
    inactive: number;
    recentlyAdded: number;
  } {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    return {
      total: this.users.length,
      active: this.users.filter(user => user.active).length,
      inactive: this.users.filter(user => !user.active).length,
      recentlyAdded: this.users.filter(user => user.createdAt > oneWeekAgo).length
    };
  }
}

// Presenter层 - 用户展示器
export class UserPresenter {
  private view: IUserView;
  private model: UserModel;
  private currentUsers: User[] = [];

  constructor(view: IUserView, model: UserModel) {
    this.view = view;
    this.model = model;

    // 订阅模型数据变化
    this.model.addObserver(this.onModelDataChanged.bind(this));
  }

  // 模型数据变化处理
  private onModelDataChanged(users: User[]): void {
    this.currentUsers = users;
    this.view.showUsers(users);
  }

  // 加载用户列表
  async loadUsers(): Promise<void> {
    try {
      this.view.showLoading();
      
      // 模拟异步加载
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const users = this.model.getAllUsers();
      this.currentUsers = users;
      this.view.showUsers(users);
      this.view.hideLoading();
      
    } catch (error) {
      this.view.hideLoading();
      this.view.showError('加载用户失败');
    }
  }

  // 选择用户
  selectUser(userId: number): void {
    const user = this.model.getUserById(userId);
    if (user) {
      this.view.showUserDetail(user);
    } else {
      this.view.showError('用户不存在');
    }
  }

  // 添加用户
  addUser(userData: Omit<User, 'id' | 'createdAt'>): void {
    try {
      const newUser = this.model.addUser(userData);
      this.view.showSuccess(`用户 ${newUser.name} 添加成功`);
    } catch (error) {
      this.view.showError('添加用户失败: ' + (error as Error).message);
    }
  }

  // 更新用户
  updateUser(user: User): void {
    try {
      const success = this.model.updateUser(user);
      if (success) {
        this.view.showSuccess(`用户 ${user.name} 更新成功`);
      } else {
        this.view.showError('用户不存在');
      }
    } catch (error) {
      this.view.showError('更新用户失败: ' + (error as Error).message);
    }
  }

  // 删除用户
  deleteUser(userId: number): void {
    const user = this.model.getUserById(userId);
    if (!user) {
      this.view.showError('用户不存在');
      return;
    }

    const success = this.model.deleteUser(userId);
    if (success) {
      this.view.showSuccess(`用户 ${user.name} 删除成功`);
      this.view.clearSelection();
    } else {
      this.view.showError('删除用户失败');
    }
  }

  // 搜索用户
  searchUsers(query: string): void {
    if (!query.trim()) {
      this.view.showUsers(this.model.getAllUsers());
      return;
    }

    const results = this.model.searchUsers(query);
    this.view.showUsers(results);
  }

  // 切换用户状态
  toggleUserStatus(userId: number): void {
    const user = this.model.getUserById(userId);
    if (!user) {
      this.view.showError('用户不存在');
      return;
    }

    const updatedUser = { ...user, active: !user.active };
    this.updateUser(updatedUser);
  }

  // 获取用户统计
  getUserStats(): {
    total: number;
    active: number;
    inactive: number;
    recentlyAdded: number;
  } {
    return this.model.getUserStats();
  }

  // 批量操作
  batchUpdateUsers(userIds: number[], updates: Partial<User>): void {
    let successCount = 0;
    let errorCount = 0;

    userIds.forEach(id => {
      const user = this.model.getUserById(id);
      if (user) {
        try {
          const updatedUser = { ...user, ...updates };
          this.model.updateUser(updatedUser);
          successCount++;
        } catch (error) {
          errorCount++;
        }
      } else {
        errorCount++;
      }
    });

    if (errorCount === 0) {
      this.view.showSuccess(`成功更新 ${successCount} 个用户`);
    } else {
      this.view.showError(`更新完成，成功 ${successCount} 个，失败 ${errorCount} 个`);
    }
  }

  // 导出用户数据
  exportUsers(): string {
    const users = this.model.getAllUsers();
    return JSON.stringify(users, null, 2);
  }

  // 导入用户数据
  importUsers(jsonData: string): void {
    try {
      const users = JSON.parse(jsonData) as User[];
      
      if (!Array.isArray(users)) {
        throw new Error('数据格式错误');
      }

      let importCount = 0;
      users.forEach(userData => {
        try {
          this.model.addUser({
            name: userData.name,
            email: userData.email,
            active: userData.active
          });
          importCount++;
        } catch (error) {
          // 忽略单个用户导入失败
        }
      });

      this.view.showSuccess(`成功导入 ${importCount} 个用户`);
    } catch (error) {
      this.view.showError('导入失败: ' + (error as Error).message);
    }
  }
}

// MVP测试套件
export class MVPTestSuite {
  // 测试Presenter
  runPresenterTests(): Array<{
    name: string;
    status: 'passed' | 'failed';
    description: string;
  }> {
    const results = [];

    // 测试1: Presenter初始化
    try {
      const mockView = new MockUserView();
      const model = new UserModel();
      const presenter = new UserPresenter(mockView, model);
      
      results.push({
        name: 'Presenter初始化测试',
        status: 'passed' as const,
        description: 'Presenter成功初始化并绑定View和Model'
      });
    } catch (error) {
      results.push({
        name: 'Presenter初始化测试',
        status: 'failed' as const,
        description: '初始化失败: ' + (error as Error).message
      });
    }

    // 测试2: 用户选择功能
    try {
      const mockView = new MockUserView();
      const model = new UserModel();
      const presenter = new UserPresenter(mockView, model);
      
      presenter.selectUser(1);
      
      if (mockView.lastShownUser && mockView.lastShownUser.id === 1) {
        results.push({
          name: '用户选择测试',
          status: 'passed' as const,
          description: '成功选择并显示用户详情'
        });
      } else {
        throw new Error('用户选择失败');
      }
    } catch (error) {
      results.push({
        name: '用户选择测试',
        status: 'failed' as const,
        description: '选择失败: ' + (error as Error).message
      });
    }

    // 测试3: 添加用户功能
    try {
      const mockView = new MockUserView();
      const model = new UserModel();
      const presenter = new UserPresenter(mockView, model);
      
      const initialCount = model.getAllUsers().length;
      presenter.addUser({
        name: 'Test User',
        email: '<EMAIL>',
        active: true
      });
      
      const finalCount = model.getAllUsers().length;
      
      if (finalCount === initialCount + 1) {
        results.push({
          name: '添加用户测试',
          status: 'passed' as const,
          description: '成功添加新用户'
        });
      } else {
        throw new Error('用户数量未增加');
      }
    } catch (error) {
      results.push({
        name: '添加用户测试',
        status: 'failed' as const,
        description: '添加失败: ' + (error as Error).message
      });
    }

    return results;
  }

  // 测试View
  runViewTests(): Array<{
    name: string;
    status: 'passed' | 'failed';
    description: string;
  }> {
    const results = [];

    // 测试1: View接口实现
    try {
      const mockView = new MockUserView();
      
      // 测试所有接口方法
      mockView.showLoading();
      mockView.hideLoading();
      mockView.showUsers([]);
      mockView.showError('test error');
      mockView.showSuccess('test success');
      mockView.clearSelection();
      
      results.push({
        name: 'View接口测试',
        status: 'passed' as const,
        description: 'View接口所有方法正常工作'
      });
    } catch (error) {
      results.push({
        name: 'View接口测试',
        status: 'failed' as const,
        description: '接口测试失败: ' + (error as Error).message
      });
    }

    return results;
  }

  // 测试Model
  runModelTests(): Array<{
    name: string;
    status: 'passed' | 'failed';
    description: string;
  }> {
    const results = [];

    // 测试1: 数据验证
    try {
      const model = new UserModel();
      
      // 测试有效数据
      const validUser = model.addUser({
        name: 'Valid User',
        email: '<EMAIL>',
        active: true
      });
      
      if (validUser.id && validUser.name === 'Valid User') {
        results.push({
          name: '数据验证测试',
          status: 'passed' as const,
          description: '有效数据成功添加'
        });
      } else {
        throw new Error('有效数据添加失败');
      }
    } catch (error) {
      results.push({
        name: '数据验证测试',
        status: 'failed' as const,
        description: '验证失败: ' + (error as Error).message
      });
    }

    // 测试2: 业务规则
    try {
      const model = new UserModel();
      
      let errorThrown = false;
      try {
        model.addUser({
          name: '',
          email: 'invalid-email',
          active: true
        });
      } catch (error) {
        errorThrown = true;
      }
      
      if (errorThrown) {
        results.push({
          name: '业务规则测试',
          status: 'passed' as const,
          description: '无效数据被正确拒绝'
        });
      } else {
        throw new Error('业务规则验证失败');
      }
    } catch (error) {
      results.push({
        name: '业务规则测试',
        status: 'failed' as const,
        description: '规则测试失败: ' + (error as Error).message
      });
    }

    return results;
  }
}

// Mock View实现（用于测试）
class MockUserView implements IUserView {
  public lastShownUser: User | null = null;
  public lastShownUsers: User[] = [];
  public isLoading: boolean = false;
  public lastError: string = '';
  public lastSuccess: string = '';

  showLoading(): void {
    this.isLoading = true;
  }

  hideLoading(): void {
    this.isLoading = false;
  }

  showUsers(users: User[]): void {
    this.lastShownUsers = users;
  }

  showUserDetail(user: User): void {
    this.lastShownUser = user;
  }

  showError(message: string): void {
    this.lastError = message;
  }

  showSuccess(message: string): void {
    this.lastSuccess = message;
  }

  clearSelection(): void {
    this.lastShownUser = null;
  }
}

// 导出类型
export type { IUserView };
