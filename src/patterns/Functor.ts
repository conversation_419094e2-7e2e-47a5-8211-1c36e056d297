/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-27 16:30:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-27 16:30:00
 * @FilePath     : /src/patterns/Functor.ts
 * @Description  : 函子模式的TypeScript实现
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-27 16:30:00
 */

// 函子接口定义
interface Functor<T> {
  map<U>(fn: (value: T) => U): Functor<U>;
}

// 恒等函数
export const identity = <T>(x: T): T => x;

// 函数组合
export const compose = <A, B, C>(g: (b: B) => C, f: (a: A) => B) => (a: A): C => g(f(a));

// Maybe函子 - 处理可能为空的值
export class Maybe<T> implements Functor<T> {
  private constructor(private value: T | null) {}

  // 创建有值的Maybe
  static of<T>(value: T): Maybe<T> {
    return new Maybe(value);
  }

  // 创建空的Maybe
  static none<T>(): Maybe<T> {
    return new Maybe<T>(null);
  }

  // 从可能为空的值创建Maybe
  static fromNullable<T>(value: T | null | undefined): Maybe<T> {
    return value != null ? Maybe.of(value) : Maybe.none<T>();
  }

  // 函子的map操作
  map<U>(fn: (value: T) => U): Maybe<U> {
    if (this.value === null) {
      return Maybe.none<U>();
    }
    try {
      return Maybe.of(fn(this.value));
    } catch (error) {
      return Maybe.none<U>();
    }
  }

  // 获取值（如果存在）
  getValue(): T | null {
    return this.value;
  }

  // 检查是否有值
  hasValue(): boolean {
    return this.value !== null;
  }

  // 获取值或默认值
  getOrElse(defaultValue: T): T {
    return this.value !== null ? this.value : defaultValue;
  }

  // 链式操作（flatMap）
  flatMap<U>(fn: (value: T) => Maybe<U>): Maybe<U> {
    if (this.value === null) {
      return Maybe.none<U>();
    }
    return fn(this.value);
  }

  // 过滤操作
  filter(predicate: (value: T) => boolean): Maybe<T> {
    if (this.value === null || !predicate(this.value)) {
      return Maybe.none<T>();
    }
    return this;
  }

  // 转换为字符串
  toString(): string {
    return this.value !== null ? `Maybe(${this.value})` : 'Maybe(None)';
  }
}

// List函子 - 处理数组/列表
export class ListFunctor<T> implements Functor<T> {
  private constructor(private items: T[]) {}

  // 创建List函子
  static of<T>(items: T[]): ListFunctor<T> {
    return new ListFunctor([...items]);
  }

  // 从单个值创建List
  static single<T>(item: T): ListFunctor<T> {
    return new ListFunctor([item]);
  }

  // 空List
  static empty<T>(): ListFunctor<T> {
    return new ListFunctor<T>([]);
  }

  // 函子的map操作
  map<U>(fn: (value: T) => U): ListFunctor<U> {
    try {
      return ListFunctor.of(this.items.map(fn));
    } catch (error) {
      return ListFunctor.empty<U>();
    }
  }

  // 获取内部数组
  getValue(): T[] {
    return [...this.items];
  }

  // 获取长度
  length(): number {
    return this.items.length;
  }

  // 检查是否为空
  isEmpty(): boolean {
    return this.items.length === 0;
  }

  // 过滤操作
  filter(predicate: (value: T) => boolean): ListFunctor<T> {
    return ListFunctor.of(this.items.filter(predicate));
  }

  // 链式操作（flatMap）
  flatMap<U>(fn: (value: T) => ListFunctor<U>): ListFunctor<U> {
    const result: U[] = [];
    for (const item of this.items) {
      result.push(...fn(item).getValue());
    }
    return ListFunctor.of(result);
  }

  // 折叠操作
  fold<U>(initial: U, fn: (acc: U, value: T) => U): U {
    return this.items.reduce(fn, initial);
  }

  // 连接操作
  concat(other: ListFunctor<T>): ListFunctor<T> {
    return ListFunctor.of([...this.items, ...other.getValue()]);
  }

  // 转换为字符串
  toString(): string {
    return `List([${this.items.join(', ')}])`;
  }
}

// Either函子 - 处理错误或成功的值
export abstract class Either<L, R> implements Functor<R> {
  abstract map<U>(fn: (value: R) => U): Either<L, U>;
  abstract isLeft(): boolean;
  abstract isRight(): boolean;
}

export class Left<L, R> extends Either<L, R> {
  constructor(private value: L) {
    super();
  }

  map<U>(_fn: (value: R) => U): Either<L, U> {
    return new Left<L, U>(this.value);
  }

  isLeft(): boolean {
    return true;
  }

  isRight(): boolean {
    return false;
  }

  getValue(): L {
    return this.value;
  }

  toString(): string {
    return `Left(${this.value})`;
  }
}

export class Right<L, R> extends Either<L, R> {
  constructor(private value: R) {
    super();
  }

  map<U>(fn: (value: R) => U): Either<L, U> {
    try {
      return new Right<L, U>(fn(this.value));
    } catch (error) {
      return new Left<L, U>(error as L);
    }
  }

  isLeft(): boolean {
    return false;
  }

  isRight(): boolean {
    return true;
  }

  getValue(): R {
    return this.value;
  }

  toString(): string {
    return `Right(${this.value})`;
  }
}

// Either工厂函数
export const left = <L, R>(value: L): Either<L, R> => new Left(value);
export const right = <L, R>(value: R): Either<L, R> => new Right(value);

// IO函子 - 处理副作用
export class IO<T> implements Functor<T> {
  constructor(private effect: () => T) {}

  // 创建IO函子
  static of<T>(value: T): IO<T> {
    return new IO(() => value);
  }

  // 从副作用函数创建IO
  static from<T>(effect: () => T): IO<T> {
    return new IO(effect);
  }

  // 函子的map操作
  map<U>(fn: (value: T) => U): IO<U> {
    return new IO(() => fn(this.effect()));
  }

  // 执行副作用
  run(): T {
    return this.effect();
  }

  // 链式操作（flatMap）
  flatMap<U>(fn: (value: T) => IO<U>): IO<U> {
    return new IO(() => fn(this.effect()).run());
  }

  toString(): string {
    return 'IO(...)';
  }
}

// 函子法则验证工具
export class FunctorLaws {
  // 验证恒等法则: functor.map(identity) === functor
  static verifyIdentity<T>(functor: Functor<T>): boolean {
    const mapped = functor.map(identity);
    // 注意：这里的比较可能需要根据具体的函子类型进行调整
    return JSON.stringify(mapped) === JSON.stringify(functor);
  }

  // 验证组合法则: functor.map(f).map(g) === functor.map(compose(g, f))
  static verifyComposition<T, U, V>(
    functor: Functor<T>,
    f: (x: T) => U,
    g: (x: U) => V
  ): boolean {
    const stepByStep = functor.map(f).map(g);
    const composed = functor.map(compose(g, f));
    return JSON.stringify(stepByStep) === JSON.stringify(composed);
  }
}

// 实用工具函数
export const lift2 = <A, B, C>(
  fn: (a: A, b: B) => C
) => (fa: Maybe<A>, fb: Maybe<B>): Maybe<C> => {
  return fa.flatMap(a => fb.map(b => fn(a, b)));
};

export const sequence = <T>(maybes: Maybe<T>[]): Maybe<T[]> => {
  return maybes.reduce(
    (acc: Maybe<T[]>, maybe: Maybe<T>) => 
      lift2((arr: T[], val: T) => [...arr, val])(acc, maybe),
    Maybe.of([] as T[])
  );
};

// 导出类型
export type { Functor };
