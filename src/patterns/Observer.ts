/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 11:52:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 11:53:48
 * @FilePath     : /src/patterns/Observer.ts
 * @Description  : 观察者模式实现
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 观察者接口
interface Observer {
  id: string;
  name: string;
  type: string;
  notifications: string[];
  update(symbol: string, price: number, change: number): void;
}

// 主题接口
interface Subject {
  addObserver(observer: Observer): void;
  removeObserver(observerId: string): void;
  notifyObservers(): void;
}

// 具体观察者实现
class StockObserver implements Observer {
  id: string;
  name: string;
  type: string;
  notifications: string[] = [];

  constructor(id: string, name: string, type: string) {
    this.id = id;
    this.name = name;
    this.type = type;
  }

  update(symbol: string, price: number, change: number): void {
    const timestamp = new Date().toLocaleTimeString();
    let message = "";

    switch (this.type) {
      case "investor":
        if (change > 0) {
          message = `📈 ${symbol} 上涨了 ¥${change.toFixed(2)}，当前价格 ¥${price.toFixed(2)}`;
        } else if (change < 0) {
          message = `📉 ${symbol} 下跌了 ¥${Math.abs(change).toFixed(2)}，当前价格 ¥${price.toFixed(2)}`;
        } else {
          message = `➡️ ${symbol} 价格保持不变，当前价格 ¥${price.toFixed(2)}`;
        }
        break;
      case "trader":
        const changePercent = ((change / (price - change)) * 100).toFixed(2);
        message = `🔄 交易提醒：${symbol} 变动 ${changePercent}%，价格 ¥${price.toFixed(2)}`;
        break;
      case "analyst":
        if (Math.abs(change) > 5) {
          message = `⚠️ 分析师警告：${symbol} 出现大幅波动 ¥${change.toFixed(2)}，需要关注`;
        } else {
          message = `📊 ${symbol} 正常波动 ¥${change.toFixed(2)}，价格 ¥${price.toFixed(2)}`;
        }
        break;
    }

    this.notifications.unshift(`[${timestamp}] ${message}`);
    // 保持最新的10条通知
    if (this.notifications.length > 10) {
      this.notifications = this.notifications.slice(0, 10);
    }
  }
}

// 股票主题
class Stock implements Subject {
  private observers: Observer[] = [];
  symbol: string;
  name: string;
  price: number;
  previousPrice: number;

  constructor(symbol: string, name: string, initialPrice: number) {
    this.symbol = symbol;
    this.name = name;
    this.price = initialPrice;
    this.previousPrice = initialPrice;
  }

  addObserver(observer: Observer): void {
    this.observers.push(observer);
  }

  removeObserver(observerId: string): void {
    this.observers = this.observers.filter(obs => obs.id !== observerId);
  }

  notifyObservers(): void {
    const change = this.price - this.previousPrice;
    this.observers.forEach(observer => {
      observer.update(this.symbol, this.price, change);
    });
  }

  setPrice(newPrice: number): void {
    this.previousPrice = this.price;
    this.price = newPrice;
    this.notifyObservers();
  }

  getObservers(): Observer[] {
    return [...this.observers];
  }
}

// 创建观察者的工厂函数
function createObserver(name: string, type: string): Observer {
  const id = Math.random().toString(36).slice(2, 11);
  return new StockObserver(id, name, type);
}

// 导出类型和工厂函数
export { createObserver };
export type ObserverType = Observer;
export type SubjectType = Subject;
export type StockType = Stock;
export { Stock };
