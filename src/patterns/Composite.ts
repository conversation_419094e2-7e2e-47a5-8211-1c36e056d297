/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 12:20:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 12:20:00
 * @FilePath     : /src/patterns/Composite.ts
 * @Description  : 组合模式实现 - 文件系统目录树
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 组件接口
interface FileSystemComponent {
  getName(): string;
  getSize(): number;
  getType(): string;
  getPath(): string;
  display(indent?: number): string;
  search(name: string): FileSystemComponent[];
  copy(): FileSystemComponent;
}

// 叶子节点 - 文件
class File implements FileSystemComponent {
  private name: string;
  private size: number;
  private extension: string;
  private content: string;
  private createdAt: Date;
  private parentPath: string;

  constructor(name: string, size: number, content: string = '', parentPath: string = '') {
    this.name = name;
    this.size = size;
    this.content = content;
    this.createdAt = new Date();
    this.parentPath = parentPath;
    
    // 提取文件扩展名
    const dotIndex = name.lastIndexOf('.');
    this.extension = dotIndex > 0 ? name.substring(dotIndex + 1).toLowerCase() : '';
  }

  getName(): string {
    return this.name;
  }

  getSize(): number {
    return this.size;
  }

  getType(): string {
    if (this.extension) {
      const typeMap: Record<string, string> = {
        'txt': '文本文件',
        'js': 'JavaScript文件',
        'ts': 'TypeScript文件',
        'vue': 'Vue组件',
        'css': '样式文件',
        'html': 'HTML文件',
        'json': 'JSON文件',
        'md': 'Markdown文件',
        'png': '图片文件',
        'jpg': '图片文件',
        'pdf': 'PDF文档',
        'doc': 'Word文档',
        'xlsx': 'Excel文档'
      };
      return typeMap[this.extension] || `${this.extension.toUpperCase()}文件`;
    }
    return '文件';
  }

  getPath(): string {
    return this.parentPath ? `${this.parentPath}/${this.name}` : this.name;
  }

  getExtension(): string {
    return this.extension;
  }

  getContent(): string {
    return this.content;
  }

  getCreatedAt(): Date {
    return this.createdAt;
  }

  setContent(content: string): void {
    this.content = content;
    this.size = content.length; // 简单地用内容长度作为文件大小
  }

  display(indent: number = 0): string {
    const spaces = '  '.repeat(indent);
    const sizeStr = this.formatSize(this.size);
    const icon = this.getFileIcon();
    return `${spaces}${icon} ${this.name} (${sizeStr}) - ${this.getType()}`;
  }

  search(name: string): FileSystemComponent[] {
    return this.name.toLowerCase().includes(name.toLowerCase()) ? [this] : [];
  }

  copy(): FileSystemComponent {
    return new File(this.name, this.size, this.content, this.parentPath);
  }

  private formatSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  private getFileIcon(): string {
    const iconMap: Record<string, string> = {
      'txt': '📄',
      'js': '📜',
      'ts': '📘',
      'vue': '💚',
      'css': '🎨',
      'html': '🌐',
      'json': '📋',
      'md': '📝',
      'png': '🖼️',
      'jpg': '🖼️',
      'pdf': '📕',
      'doc': '📄',
      'xlsx': '📊'
    };
    return iconMap[this.extension] || '📄';
  }
}

// 组合节点 - 目录
class Directory implements FileSystemComponent {
  private name: string;
  private children: FileSystemComponent[] = [];
  private createdAt: Date;
  private parentPath: string;

  constructor(name: string, parentPath: string = '') {
    this.name = name;
    this.createdAt = new Date();
    this.parentPath = parentPath;
  }

  getName(): string {
    return this.name;
  }

  getSize(): number {
    return this.children.reduce((total, child) => total + child.getSize(), 0);
  }

  getType(): string {
    return '目录';
  }

  getPath(): string {
    return this.parentPath ? `${this.parentPath}/${this.name}` : this.name;
  }

  getCreatedAt(): Date {
    return this.createdAt;
  }

  getChildren(): FileSystemComponent[] {
    return [...this.children];
  }

  getChildCount(): number {
    return this.children.length;
  }

  getFileCount(): number {
    return this.children.reduce((count, child) => {
      if (child instanceof File) {
        return count + 1;
      } else if (child instanceof Directory) {
        return count + child.getFileCount();
      }
      return count;
    }, 0);
  }

  getDirectoryCount(): number {
    return this.children.reduce((count, child) => {
      if (child instanceof Directory) {
        return count + 1 + child.getDirectoryCount();
      }
      return count;
    }, 0);
  }

  // 添加子组件
  add(component: FileSystemComponent): void {
    this.children.push(component);
  }

  // 移除子组件
  remove(component: FileSystemComponent): boolean {
    const index = this.children.indexOf(component);
    if (index > -1) {
      this.children.splice(index, 1);
      return true;
    }
    return false;
  }

  // 根据名称移除子组件
  removeByName(name: string): boolean {
    const index = this.children.findIndex(child => child.getName() === name);
    if (index > -1) {
      this.children.splice(index, 1);
      return true;
    }
    return false;
  }

  // 查找子组件
  findChild(name: string): FileSystemComponent | null {
    return this.children.find(child => child.getName() === name) || null;
  }

  display(indent: number = 0): string {
    const spaces = '  '.repeat(indent);
    const sizeStr = this.formatSize(this.getSize());
    const childCount = this.getChildCount();
    
    let result = `${spaces}📁 ${this.name}/ (${sizeStr}, ${childCount} 项)`;
    
    // 显示子组件
    for (const child of this.children) {
      result += '\n' + child.display(indent + 1);
    }
    
    return result;
  }

  search(name: string): FileSystemComponent[] {
    let results: FileSystemComponent[] = [];
    
    // 检查当前目录名称
    if (this.name.toLowerCase().includes(name.toLowerCase())) {
      results.push(this);
    }
    
    // 递归搜索子组件
    for (const child of this.children) {
      results = results.concat(child.search(name));
    }
    
    return results;
  }

  copy(): FileSystemComponent {
    const newDirectory = new Directory(this.name, this.parentPath);
    for (const child of this.children) {
      newDirectory.add(child.copy());
    }
    return newDirectory;
  }

  // 获取目录统计信息
  getStatistics(): {
    totalFiles: number;
    totalDirectories: number;
    totalSize: number;
    largestFile: { name: string; size: number } | null;
    fileTypes: Record<string, number>;
  } {
    let totalFiles = 0;
    let totalDirectories = 0;
    let totalSize = 0;
    let largestFile: { name: string; size: number } | null = null;
    const fileTypes: Record<string, number> = {};

    const traverse = (component: FileSystemComponent) => {
      if (component instanceof File) {
        totalFiles++;
        totalSize += component.getSize();
        
        if (!largestFile || component.getSize() > largestFile.size) {
          largestFile = { name: component.getName(), size: component.getSize() };
        }
        
        const extension = component.getExtension() || 'unknown';
        fileTypes[extension] = (fileTypes[extension] || 0) + 1;
      } else if (component instanceof Directory) {
        totalDirectories++;
        for (const child of component.getChildren()) {
          traverse(child);
        }
      }
    };

    for (const child of this.children) {
      traverse(child);
    }

    return {
      totalFiles,
      totalDirectories,
      totalSize,
      largestFile,
      fileTypes
    };
  }

  private formatSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }
}

// 文件系统管理器
class FileSystemManager {
  private root: Directory;

  constructor(rootName: string = 'root') {
    this.root = new Directory(rootName);
  }

  getRoot(): Directory {
    return this.root;
  }

  // 创建示例文件系统
  createSampleFileSystem(): void {
    // 创建项目目录结构
    const srcDir = new Directory('src');
    const componentsDir = new Directory('components');
    const patternsDir = new Directory('patterns');
    const assetsDir = new Directory('assets');
    
    // 添加文件
    srcDir.add(new File('App.vue', 8500, '<template>...</template>', 'src'));
    srcDir.add(new File('main.js', 450, 'import { createApp } from "vue"', 'src'));
    
    componentsDir.add(new File('HelloWorld.vue', 2300, '<template>Hello World</template>', 'src/components'));
    componentsDir.add(new File('CodeDisplay.vue', 3200, '<template>Code Display</template>', 'src/components'));
    
    patternsDir.add(new File('Singleton.ts', 1800, 'class Logger {...}', 'src/patterns'));
    patternsDir.add(new File('Factory.ts', 2100, 'interface PaymentProcessor {...}', 'src/patterns'));
    patternsDir.add(new File('Observer.ts', 2800, 'interface Observer {...}', 'src/patterns'));
    
    assetsDir.add(new File('logo.svg', 1200, '<svg>...</svg>', 'src/assets'));
    assetsDir.add(new File('main.css', 3500, 'body { margin: 0; }', 'src/assets'));
    
    srcDir.add(componentsDir);
    srcDir.add(patternsDir);
    srcDir.add(assetsDir);
    
    // 添加根目录文件
    this.root.add(new File('package.json', 850, '{"name": "design-pattern-demo"}', ''));
    this.root.add(new File('README.md', 2400, '# Design Pattern Demo', ''));
    this.root.add(new File('vite.config.js', 420, 'export default defineConfig({...})', ''));
    this.root.add(srcDir);
    
    // 添加其他目录
    const publicDir = new Directory('public');
    publicDir.add(new File('favicon.ico', 1150, '', 'public'));
    this.root.add(publicDir);
  }

  // 全局搜索
  search(name: string): FileSystemComponent[] {
    return this.root.search(name);
  }

  // 显示整个文件系统
  display(): string {
    return this.root.display();
  }

  // 获取文件系统统计信息
  getStatistics() {
    return this.root.getStatistics();
  }
}

// 导出类和类型
export { FileSystemManager, Directory, File };
export type FileSystemComponentType = FileSystemComponent;
export type DirectoryType = Directory;
export type FileType = File;
export type FileSystemManagerType = FileSystemManager;
