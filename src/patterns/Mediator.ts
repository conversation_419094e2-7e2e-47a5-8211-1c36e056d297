// 中介者模式 (Mediator Pattern) - 聊天室系统

// 中介者接口
interface ChatMediator {
  sendMessage(message: string, user: User): void
  addUser(user: User): void
  removeUser(user: User): void
  getUsers(): User[]
  getUserCount(): number
}

// 抽象用户类
abstract class User {
  protected name: string
  protected mediator: ChatMediator
  protected isOnline: boolean = true

  constructor(name: string, mediator: ChatMediator) {
    this.name = name
    this.mediator = mediator
  }

  abstract send(message: string): void
  abstract receive(message: string, from: User): void

  getName(): string {
    return this.name
  }

  setOnlineStatus(status: boolean): void {
    this.isOnline = status
  }

  isUserOnline(): boolean {
    return this.isOnline
  }
}

// 具体用户 - 普通用户
export class RegularUser extends User {
  private messageHistory: ChatMessage[] = []

  send(message: string): void {
    console.log(`${this.name} 发送消息: ${message}`)
    this.mediator.sendMessage(message, this)
  }

  receive(message: string, from: User): void {
    if (this.isOnline) {
      const chatMessage: ChatMessage = {
        content: message,
        sender: from.getName(),
        receiver: this.name,
        timestamp: new Date(),
        type: 'regular'
      }
      this.messageHistory.push(chatMessage)
      console.log(`${this.name} 收到来自 ${from.getName()} 的消息: ${message}`)
    }
  }

  getMessageHistory(): ChatMessage[] {
    return [...this.messageHistory]
  }

  clearHistory(): void {
    this.messageHistory = []
  }
}

// 具体用户 - 管理员用户
export class AdminUser extends User {
  private messageHistory: ChatMessage[] = []
  private mutedUsers: Set<string> = new Set()

  send(message: string): void {
    console.log(`[管理员] ${this.name} 发送消息: ${message}`)
    this.mediator.sendMessage(message, this)
  }

  receive(message: string, from: User): void {
    if (this.isOnline) {
      const chatMessage: ChatMessage = {
        content: message,
        sender: from.getName(),
        receiver: this.name,
        timestamp: new Date(),
        type: 'admin'
      }
      this.messageHistory.push(chatMessage)
      console.log(`[管理员] ${this.name} 收到来自 ${from.getName()} 的消息: ${message}`)
    }
  }

  muteUser(userName: string): void {
    this.mutedUsers.add(userName)
    console.log(`[管理员] ${this.name} 禁言了用户 ${userName}`)
  }

  unmuteUser(userName: string): void {
    this.mutedUsers.delete(userName)
    console.log(`[管理员] ${this.name} 解除了用户 ${userName} 的禁言`)
  }

  isUserMuted(userName: string): boolean {
    return this.mutedUsers.has(userName)
  }

  getMutedUsers(): string[] {
    return Array.from(this.mutedUsers)
  }

  getMessageHistory(): ChatMessage[] {
    return [...this.messageHistory]
  }
}

// 消息接口
interface ChatMessage {
  content: string
  sender: string
  receiver: string
  timestamp: Date
  type: 'regular' | 'admin' | 'system'
}

// 具体中介者 - 聊天室
export class ChatRoom implements ChatMediator {
  private users: Map<string, User> = new Map()
  private messageHistory: ChatMessage[] = []
  private roomName: string
  private maxUsers: number

  constructor(roomName: string, maxUsers: number = 100) {
    this.roomName = roomName
    this.maxUsers = maxUsers
  }

  sendMessage(message: string, sender: User): void {
    // 检查发送者是否被禁言
    if (this.isUserMuted(sender.getName())) {
      console.log(`用户 ${sender.getName()} 被禁言，无法发送消息`)
      return
    }

    const chatMessage: ChatMessage = {
      content: message,
      sender: sender.getName(),
      receiver: 'all',
      timestamp: new Date(),
      type: sender instanceof AdminUser ? 'admin' : 'regular'
    }

    this.messageHistory.push(chatMessage)

    // 向所有其他在线用户发送消息
    for (const [userName, user] of this.users) {
      if (user !== sender && user.isUserOnline()) {
        user.receive(message, sender)
      }
    }
  }

  addUser(user: User): void {
    if (this.users.size >= this.maxUsers) {
      console.log(`聊天室 ${this.roomName} 已满，无法添加用户 ${user.getName()}`)
      return
    }

    if (this.users.has(user.getName())) {
      console.log(`用户 ${user.getName()} 已在聊天室中`)
      return
    }

    this.users.set(user.getName(), user)
    console.log(`用户 ${user.getName()} 加入了聊天室 ${this.roomName}`)

    // 通知其他用户
    this.broadcastSystemMessage(`${user.getName()} 加入了聊天室`)
  }

  removeUser(user: User): void {
    if (this.users.delete(user.getName())) {
      console.log(`用户 ${user.getName()} 离开了聊天室 ${this.roomName}`)
      
      // 通知其他用户
      this.broadcastSystemMessage(`${user.getName()} 离开了聊天室`)
    }
  }

  getUsers(): User[] {
    return Array.from(this.users.values())
  }

  getUserCount(): number {
    return this.users.size
  }

  getOnlineUserCount(): number {
    return Array.from(this.users.values()).filter(user => user.isUserOnline()).length
  }

  getRoomName(): string {
    return this.roomName
  }

  getMessageHistory(): ChatMessage[] {
    return [...this.messageHistory]
  }

  private isUserMuted(userName: string): boolean {
    // 检查是否有管理员禁言了该用户
    for (const user of this.users.values()) {
      if (user instanceof AdminUser && user.isUserMuted(userName)) {
        return true
      }
    }
    return false
  }

  private broadcastSystemMessage(message: string): void {
    const systemMessage: ChatMessage = {
      content: message,
      sender: 'System',
      receiver: 'all',
      timestamp: new Date(),
      type: 'system'
    }

    this.messageHistory.push(systemMessage)

    // 向所有在线用户发送系统消息
    for (const user of this.users.values()) {
      if (user.isUserOnline()) {
        console.log(`[系统消息] ${user.getName()} 收到: ${message}`)
      }
    }
  }

  // 获取聊天室统计信息
  getStats(): {
    roomName: string
    totalUsers: number
    onlineUsers: number
    totalMessages: number
    adminUsers: number
  } {
    const adminCount = Array.from(this.users.values())
      .filter(user => user instanceof AdminUser).length

    return {
      roomName: this.roomName,
      totalUsers: this.users.size,
      onlineUsers: this.getOnlineUserCount(),
      totalMessages: this.messageHistory.length,
      adminUsers: adminCount
    }
  }

  // 清空消息历史
  clearHistory(): void {
    this.messageHistory = []
    console.log(`聊天室 ${this.roomName} 的消息历史已清空`)
  }
}

// 聊天室管理器
export class ChatRoomManager {
  private rooms: Map<string, ChatRoom> = new Map()

  createRoom(roomName: string, maxUsers?: number): ChatRoom {
    if (this.rooms.has(roomName)) {
      throw new Error(`聊天室 ${roomName} 已存在`)
    }

    const room = new ChatRoom(roomName, maxUsers)
    this.rooms.set(roomName, room)
    console.log(`创建聊天室: ${roomName}`)
    return room
  }

  getRoom(roomName: string): ChatRoom | null {
    return this.rooms.get(roomName) || null
  }

  deleteRoom(roomName: string): boolean {
    if (this.rooms.delete(roomName)) {
      console.log(`删除聊天室: ${roomName}`)
      return true
    }
    return false
  }

  getRooms(): ChatRoom[] {
    return Array.from(this.rooms.values())
  }

  getRoomNames(): string[] {
    return Array.from(this.rooms.keys())
  }

  getTotalUsers(): number {
    return Array.from(this.rooms.values())
      .reduce((total, room) => total + room.getUserCount(), 0)
  }

  getSystemStats(): {
    totalRooms: number
    totalUsers: number
    roomStats: Array<ReturnType<ChatRoom['getStats']>>
  } {
    return {
      totalRooms: this.rooms.size,
      totalUsers: this.getTotalUsers(),
      roomStats: Array.from(this.rooms.values()).map(room => room.getStats())
    }
  }
}
