// 访问者模式 (Visitor Pattern) - 文档处理系统

// 访问者接口
interface DocumentVisitor {
  visitTextElement(element: TextElement): void
  visitImageElement(element: ImageElement): void
  visitTableElement(element: TableElement): void
  visitListElement(element: ListElement): void
}

// 元素接口
interface DocumentElement {
  accept(visitor: DocumentVisitor): void
  getType(): string
  getId(): string
}

// 具体元素 - 文本元素
export class TextElement implements DocumentElement {
  private id: string
  private content: string
  private fontSize: number
  private color: string

  constructor(id: string, content: string, fontSize: number = 14, color: string = '#000000') {
    this.id = id
    this.content = content
    this.fontSize = fontSize
    this.color = color
  }

  accept(visitor: DocumentVisitor): void {
    visitor.visitTextElement(this)
  }

  getType(): string {
    return 'text'
  }

  getId(): string {
    return this.id
  }

  getContent(): string {
    return this.content
  }

  getFontSize(): number {
    return this.fontSize
  }

  getColor(): string {
    return this.color
  }

  setContent(content: string): void {
    this.content = content
  }

  setFontSize(size: number): void {
    this.fontSize = size
  }

  setColor(color: string): void {
    this.color = color
  }
}

// 具体元素 - 图片元素
export class ImageElement implements DocumentElement {
  private id: string
  private src: string
  private width: number
  private height: number
  private alt: string

  constructor(id: string, src: string, width: number, height: number, alt: string = '') {
    this.id = id
    this.src = src
    this.width = width
    this.height = height
    this.alt = alt
  }

  accept(visitor: DocumentVisitor): void {
    visitor.visitImageElement(this)
  }

  getType(): string {
    return 'image'
  }

  getId(): string {
    return this.id
  }

  getSrc(): string {
    return this.src
  }

  getWidth(): number {
    return this.width
  }

  getHeight(): number {
    return this.height
  }

  getAlt(): string {
    return this.alt
  }

  setSrc(src: string): void {
    this.src = src
  }

  setDimensions(width: number, height: number): void {
    this.width = width
    this.height = height
  }

  setAlt(alt: string): void {
    this.alt = alt
  }
}

// 具体元素 - 表格元素
export class TableElement implements DocumentElement {
  private id: string
  private rows: string[][]
  private headers: string[]

  constructor(id: string, headers: string[], rows: string[][]) {
    this.id = id
    this.headers = headers
    this.rows = rows
  }

  accept(visitor: DocumentVisitor): void {
    visitor.visitTableElement(this)
  }

  getType(): string {
    return 'table'
  }

  getId(): string {
    return this.id
  }

  getHeaders(): string[] {
    return [...this.headers]
  }

  getRows(): string[][] {
    return this.rows.map(row => [...row])
  }

  addRow(row: string[]): void {
    this.rows.push(row)
  }

  getRowCount(): number {
    return this.rows.length
  }

  getColumnCount(): number {
    return this.headers.length
  }
}

// 具体元素 - 列表元素
export class ListElement implements DocumentElement {
  private id: string
  private items: string[]
  private ordered: boolean

  constructor(id: string, items: string[], ordered: boolean = false) {
    this.id = id
    this.items = items
    this.ordered = ordered
  }

  accept(visitor: DocumentVisitor): void {
    visitor.visitListElement(this)
  }

  getType(): string {
    return 'list'
  }

  getId(): string {
    return this.id
  }

  getItems(): string[] {
    return [...this.items]
  }

  isOrdered(): boolean {
    return this.ordered
  }

  addItem(item: string): void {
    this.items.push(item)
  }

  getItemCount(): number {
    return this.items.length
  }
}

// 具体访问者 - HTML导出器
export class HTMLExportVisitor implements DocumentVisitor {
  private html: string = ''

  visitTextElement(element: TextElement): void {
    this.html += `<p id="${element.getId()}" style="font-size: ${element.getFontSize()}px; color: ${element.getColor()};">${element.getContent()}</p>\n`
  }

  visitImageElement(element: ImageElement): void {
    this.html += `<img id="${element.getId()}" src="${element.getSrc()}" width="${element.getWidth()}" height="${element.getHeight()}" alt="${element.getAlt()}" />\n`
  }

  visitTableElement(element: TableElement): void {
    this.html += `<table id="${element.getId()}">\n`
    this.html += '  <thead>\n    <tr>\n'
    element.getHeaders().forEach(header => {
      this.html += `      <th>${header}</th>\n`
    })
    this.html += '    </tr>\n  </thead>\n'
    
    this.html += '  <tbody>\n'
    element.getRows().forEach(row => {
      this.html += '    <tr>\n'
      row.forEach(cell => {
        this.html += `      <td>${cell}</td>\n`
      })
      this.html += '    </tr>\n'
    })
    this.html += '  </tbody>\n</table>\n'
  }

  visitListElement(element: ListElement): void {
    const tag = element.isOrdered() ? 'ol' : 'ul'
    this.html += `<${tag} id="${element.getId()}">\n`
    element.getItems().forEach(item => {
      this.html += `  <li>${item}</li>\n`
    })
    this.html += `</${tag}>\n`
  }

  getHTML(): string {
    return this.html
  }

  reset(): void {
    this.html = ''
  }
}

// 具体访问者 - Markdown导出器
export class MarkdownExportVisitor implements DocumentVisitor {
  private markdown: string = ''

  visitTextElement(element: TextElement): void {
    this.markdown += `${element.getContent()}\n\n`
  }

  visitImageElement(element: ImageElement): void {
    this.markdown += `![${element.getAlt()}](${element.getSrc()})\n\n`
  }

  visitTableElement(element: TableElement): void {
    // 表头
    this.markdown += '| ' + element.getHeaders().join(' | ') + ' |\n'
    this.markdown += '| ' + element.getHeaders().map(() => '---').join(' | ') + ' |\n'
    
    // 表格内容
    element.getRows().forEach(row => {
      this.markdown += '| ' + row.join(' | ') + ' |\n'
    })
    this.markdown += '\n'
  }

  visitListElement(element: ListElement): void {
    element.getItems().forEach((item, index) => {
      if (element.isOrdered()) {
        this.markdown += `${index + 1}. ${item}\n`
      } else {
        this.markdown += `- ${item}\n`
      }
    })
    this.markdown += '\n'
  }

  getMarkdown(): string {
    return this.markdown
  }

  reset(): void {
    this.markdown = ''
  }
}

// 具体访问者 - 统计分析器
export class StatisticsVisitor implements DocumentVisitor {
  private stats = {
    textElements: 0,
    imageElements: 0,
    tableElements: 0,
    listElements: 0,
    totalWords: 0,
    totalCharacters: 0,
    totalImages: 0,
    totalTableRows: 0,
    totalListItems: 0
  }

  visitTextElement(element: TextElement): void {
    this.stats.textElements++
    const words = element.getContent().split(/\s+/).filter(word => word.length > 0)
    this.stats.totalWords += words.length
    this.stats.totalCharacters += element.getContent().length
  }

  visitImageElement(element: ImageElement): void {
    this.stats.imageElements++
    this.stats.totalImages++
  }

  visitTableElement(element: TableElement): void {
    this.stats.tableElements++
    this.stats.totalTableRows += element.getRowCount()
  }

  visitListElement(element: ListElement): void {
    this.stats.listElements++
    this.stats.totalListItems += element.getItemCount()
  }

  getStatistics(): typeof this.stats {
    return { ...this.stats }
  }

  reset(): void {
    this.stats = {
      textElements: 0,
      imageElements: 0,
      tableElements: 0,
      listElements: 0,
      totalWords: 0,
      totalCharacters: 0,
      totalImages: 0,
      totalTableRows: 0,
      totalListItems: 0
    }
  }
}

// 文档类 - 包含多个元素
export class Document {
  private elements: DocumentElement[] = []
  private title: string

  constructor(title: string) {
    this.title = title
  }

  addElement(element: DocumentElement): void {
    this.elements.push(element)
  }

  removeElement(elementId: string): boolean {
    const index = this.elements.findIndex(el => el.getId() === elementId)
    if (index > -1) {
      this.elements.splice(index, 1)
      return true
    }
    return false
  }

  getElements(): DocumentElement[] {
    return [...this.elements]
  }

  getTitle(): string {
    return this.title
  }

  setTitle(title: string): void {
    this.title = title
  }

  // 接受访问者访问所有元素
  accept(visitor: DocumentVisitor): void {
    this.elements.forEach(element => element.accept(visitor))
  }

  getElementCount(): number {
    return this.elements.length
  }

  getElementById(id: string): DocumentElement | null {
    return this.elements.find(el => el.getId() === id) || null
  }

  getElementsByType(type: string): DocumentElement[] {
    return this.elements.filter(el => el.getType() === type)
  }
}

// 文档处理器 - 使用访问者模式的客户端
export class DocumentProcessor {
  private document: Document

  constructor(document: Document) {
    this.document = document
  }

  exportToHTML(): string {
    const visitor = new HTMLExportVisitor()
    this.document.accept(visitor)
    return `<!DOCTYPE html>\n<html>\n<head>\n<title>${this.document.getTitle()}</title>\n</head>\n<body>\n${visitor.getHTML()}</body>\n</html>`
  }

  exportToMarkdown(): string {
    const visitor = new MarkdownExportVisitor()
    this.document.accept(visitor)
    return `# ${this.document.getTitle()}\n\n${visitor.getMarkdown()}`
  }

  getStatistics(): ReturnType<StatisticsVisitor['getStatistics']> {
    const visitor = new StatisticsVisitor()
    this.document.accept(visitor)
    return visitor.getStatistics()
  }

  getDocument(): Document {
    return this.document
  }

  setDocument(document: Document): void {
    this.document = document
  }
}
