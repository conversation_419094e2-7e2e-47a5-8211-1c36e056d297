/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-27 19:30:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-27 19:30:00
 * @FilePath     : /src/patterns/CQRS.ts
 * @Description  : CQRS模式的TypeScript实现
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-27 19:30:00
 */

// 基础命令接口
export interface ICommand {
  id: string;
  type: string;
  data: any;
  timestamp: Date;
}

// 基础查询接口
export interface IQuery {
  id: string;
  type: string;
  parameters: any;
  timestamp: Date;
}

// 基础事件接口
export interface IEvent {
  id: string;
  type: string;
  aggregateId: string;
  data: any;
  timestamp: Date;
}

// 用户实体
export interface User {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
}

// 订单实体
export interface Order {
  id: string;
  userId: string;
  amount: number;
  status: 'pending' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

// 命令处理器接口
export interface ICommandHandler<T extends ICommand> {
  handle(command: T): Promise<any>;
}

// 查询处理器接口
export interface IQueryHandler<T extends IQuery> {
  handle(query: T): Promise<any>;
}

// 事件处理器接口
export interface IEventHandler<T extends IEvent> {
  handle(event: T): Promise<void>;
}

// 命令总线
export class CommandBus {
  private handlers: Map<string, ICommandHandler<any>> = new Map();

  register<T extends ICommand>(commandType: string, handler: ICommandHandler<T>): void {
    this.handlers.set(commandType, handler);
  }

  async execute<T extends ICommand>(command: T): Promise<any> {
    const handler = this.handlers.get(command.type);
    if (!handler) {
      throw new Error(`No handler registered for command type: ${command.type}`);
    }

    console.log(`Executing command: ${command.type}`, command);
    return await handler.handle(command);
  }
}

// 查询总线
export class QueryBus {
  private handlers: Map<string, IQueryHandler<any>> = new Map();

  register<T extends IQuery>(queryType: string, handler: IQueryHandler<T>): void {
    this.handlers.set(queryType, handler);
  }

  async execute<T extends IQuery>(query: T): Promise<any> {
    const handler = this.handlers.get(query.type);
    if (!handler) {
      throw new Error(`No handler registered for query type: ${query.type}`);
    }

    console.log(`Executing query: ${query.type}`, query);
    return await handler.handle(query);
  }
}

// 事件总线
export class EventBus {
  private handlers: Map<string, IEventHandler<any>[]> = new Map();

  subscribe<T extends IEvent>(eventType: string, handler: IEventHandler<T>): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }
    this.handlers.get(eventType)!.push(handler);
  }

  async publish<T extends IEvent>(event: T): Promise<void> {
    const handlers = this.handlers.get(event.type) || [];
    
    console.log(`Publishing event: ${event.type}`, event);
    
    // 并行处理所有事件处理器
    await Promise.all(
      handlers.map(handler => handler.handle(event))
    );
  }
}

// 写模型 - 用户聚合
export class UserAggregate {
  private users: Map<string, User> = new Map();
  private eventBus: EventBus;

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
  }

  async createUser(id: string, name: string, email: string): Promise<User> {
    if (this.users.has(id)) {
      throw new Error('User already exists');
    }

    // 检查邮箱唯一性
    for (const user of this.users.values()) {
      if (user.email === email) {
        throw new Error('Email already exists');
      }
    }

    const user: User = {
      id,
      name,
      email,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.users.set(id, user);

    // 发布事件
    await this.eventBus.publish({
      id: this.generateEventId(),
      type: 'UserCreated',
      aggregateId: id,
      data: { user },
      timestamp: new Date()
    });

    return user;
  }

  async updateUser(id: string, updates: Partial<Pick<User, 'name' | 'email'>>): Promise<User> {
    const user = this.users.get(id);
    if (!user) {
      throw new Error('User not found');
    }

    const updatedUser: User = {
      ...user,
      ...updates,
      updatedAt: new Date()
    };

    this.users.set(id, updatedUser);

    // 发布事件
    await this.eventBus.publish({
      id: this.generateEventId(),
      type: 'UserUpdated',
      aggregateId: id,
      data: { user: updatedUser, changes: updates },
      timestamp: new Date()
    });

    return updatedUser;
  }

  getUser(id: string): User | undefined {
    return this.users.get(id);
  }

  getAllUsers(): User[] {
    return Array.from(this.users.values());
  }

  getUserCount(): number {
    return this.users.size;
  }

  private generateEventId(): string {
    return `evt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 写模型 - 订单聚合
export class OrderAggregate {
  private orders: Map<string, Order> = new Map();
  private eventBus: EventBus;

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
  }

  async createOrder(id: string, userId: string, amount: number): Promise<Order> {
    if (this.orders.has(id)) {
      throw new Error('Order already exists');
    }

    if (amount <= 0) {
      throw new Error('Order amount must be positive');
    }

    const order: Order = {
      id,
      userId,
      amount,
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.orders.set(id, order);

    // 发布事件
    await this.eventBus.publish({
      id: this.generateEventId(),
      type: 'OrderCreated',
      aggregateId: id,
      data: { order },
      timestamp: new Date()
    });

    return order;
  }

  async updateOrderStatus(id: string, status: Order['status']): Promise<Order> {
    const order = this.orders.get(id);
    if (!order) {
      throw new Error('Order not found');
    }

    const updatedOrder: Order = {
      ...order,
      status,
      updatedAt: new Date()
    };

    this.orders.set(id, updatedOrder);

    // 发布事件
    await this.eventBus.publish({
      id: this.generateEventId(),
      type: 'OrderStatusUpdated',
      aggregateId: id,
      data: { order: updatedOrder, oldStatus: order.status, newStatus: status },
      timestamp: new Date()
    });

    return updatedOrder;
  }

  getOrder(id: string): Order | undefined {
    return this.orders.get(id);
  }

  getAllOrders(): Order[] {
    return Array.from(this.orders.values());
  }

  getOrdersByUser(userId: string): Order[] {
    return Array.from(this.orders.values()).filter(order => order.userId === userId);
  }

  getOrderCount(): number {
    return this.orders.size;
  }

  private generateEventId(): string {
    return `evt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 读模型 - 用户视图
export class UserReadModel {
  private users: Map<string, User> = new Map();

  updateUser(user: User): void {
    this.users.set(user.id, { ...user });
  }

  removeUser(id: string): void {
    this.users.delete(id);
  }

  getUser(id: string): User | undefined {
    return this.users.get(id);
  }

  getAllUsers(): User[] {
    return Array.from(this.users.values());
  }

  getUsersByName(name: string): User[] {
    const lowerName = name.toLowerCase();
    return Array.from(this.users.values()).filter(user =>
      user.name.toLowerCase().includes(lowerName)
    );
  }

  getUserCount(): number {
    return this.users.size;
  }
}

// 读模型 - 订单视图
export class OrderReadModel {
  private orders: Map<string, Order> = new Map();

  updateOrder(order: Order): void {
    this.orders.set(order.id, { ...order });
  }

  removeOrder(id: string): void {
    this.orders.delete(id);
  }

  getOrder(id: string): Order | undefined {
    return this.orders.get(id);
  }

  getAllOrders(): Order[] {
    return Array.from(this.orders.values());
  }

  getOrdersByUser(userId: string): Order[] {
    return Array.from(this.orders.values()).filter(order => order.userId === userId);
  }

  getOrdersByStatus(status: Order['status']): Order[] {
    return Array.from(this.orders.values()).filter(order => order.status === status);
  }

  getOrderCount(): number {
    return this.orders.size;
  }

  getTotalRevenue(): number {
    return Array.from(this.orders.values())
      .filter(order => order.status === 'completed')
      .reduce((total, order) => total + order.amount, 0);
  }

  getAverageOrderValue(): number {
    const completedOrders = Array.from(this.orders.values())
      .filter(order => order.status === 'completed');
    
    if (completedOrders.length === 0) return 0;
    
    const total = completedOrders.reduce((sum, order) => sum + order.amount, 0);
    return total / completedOrders.length;
  }
}

// 读模型 - 统计视图
export class StatisticsReadModel {
  private stats = {
    totalUsers: 0,
    totalOrders: 0,
    totalRevenue: 0,
    averageOrderValue: 0,
    ordersByStatus: {
      pending: 0,
      completed: 0,
      cancelled: 0
    }
  };

  updateUserStats(userCount: number): void {
    this.stats.totalUsers = userCount;
  }

  updateOrderStats(orderCount: number, revenue: number, averageValue: number): void {
    this.stats.totalOrders = orderCount;
    this.stats.totalRevenue = revenue;
    this.stats.averageOrderValue = averageValue;
  }

  updateOrderStatusStats(status: Order['status'], count: number): void {
    this.stats.ordersByStatus[status] = count;
  }

  getStats() {
    return { ...this.stats };
  }
}

// 命令处理器实现
export class CreateUserCommandHandler implements ICommandHandler<ICommand> {
  constructor(private userAggregate: UserAggregate) {}

  async handle(command: ICommand): Promise<User> {
    const { name, email } = command.data;
    const id = this.generateId();
    return await this.userAggregate.createUser(id, name, email);
  }

  private generateId(): string {
    return `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

export class CreateOrderCommandHandler implements ICommandHandler<ICommand> {
  constructor(private orderAggregate: OrderAggregate) {}

  async handle(command: ICommand): Promise<Order> {
    const { userId, amount } = command.data;
    const id = this.generateId();
    return await this.orderAggregate.createOrder(id, userId, amount);
  }

  private generateId(): string {
    return `order-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 查询处理器实现
export class GetAllUsersQueryHandler implements IQueryHandler<IQuery> {
  constructor(private userReadModel: UserReadModel) {}

  async handle(query: IQuery): Promise<User[]> {
    return this.userReadModel.getAllUsers();
  }
}

export class GetAllOrdersQueryHandler implements IQueryHandler<IQuery> {
  constructor(private orderReadModel: OrderReadModel) {}

  async handle(query: IQuery): Promise<Order[]> {
    return this.orderReadModel.getAllOrders();
  }
}

export class GetUsersByNameQueryHandler implements IQueryHandler<IQuery> {
  constructor(private userReadModel: UserReadModel) {}

  async handle(query: IQuery): Promise<User[]> {
    const { name } = query.parameters;
    return this.userReadModel.getUsersByName(name);
  }
}

export class GetStatsQueryHandler implements IQueryHandler<IQuery> {
  constructor(private statsReadModel: StatisticsReadModel) {}

  async handle(query: IQuery): Promise<any> {
    return this.statsReadModel.getStats();
  }
}

// 事件处理器实现
export class UserCreatedEventHandler implements IEventHandler<IEvent> {
  constructor(private userReadModel: UserReadModel, private statsReadModel: StatisticsReadModel) {}

  async handle(event: IEvent): Promise<void> {
    const { user } = event.data;
    this.userReadModel.updateUser(user);
    this.statsReadModel.updateUserStats(this.userReadModel.getUserCount());
  }
}

export class OrderCreatedEventHandler implements IEventHandler<IEvent> {
  constructor(private orderReadModel: OrderReadModel, private statsReadModel: StatisticsReadModel) {}

  async handle(event: IEvent): Promise<void> {
    const { order } = event.data;
    this.orderReadModel.updateOrder(order);
    
    // 更新统计信息
    const totalRevenue = this.orderReadModel.getTotalRevenue();
    const averageValue = this.orderReadModel.getAverageOrderValue();
    this.statsReadModel.updateOrderStats(
      this.orderReadModel.getOrderCount(),
      totalRevenue,
      averageValue
    );
  }
}

// CQRS系统主类
export class CQRSSystem {
  private commandBus: CommandBus;
  private queryBus: QueryBus;
  private eventBus: EventBus;
  
  // 写模型
  private userAggregate: UserAggregate;
  private orderAggregate: OrderAggregate;
  
  // 读模型
  private userReadModel: UserReadModel;
  private orderReadModel: OrderReadModel;
  private statsReadModel: StatisticsReadModel;

  constructor() {
    // 初始化总线
    this.commandBus = new CommandBus();
    this.queryBus = new QueryBus();
    this.eventBus = new EventBus();
    
    // 初始化聚合
    this.userAggregate = new UserAggregate(this.eventBus);
    this.orderAggregate = new OrderAggregate(this.eventBus);
    
    // 初始化读模型
    this.userReadModel = new UserReadModel();
    this.orderReadModel = new OrderReadModel();
    this.statsReadModel = new StatisticsReadModel();
    
    // 注册命令处理器
    this.commandBus.register('CreateUser', new CreateUserCommandHandler(this.userAggregate));
    this.commandBus.register('CreateOrder', new CreateOrderCommandHandler(this.orderAggregate));
    
    // 注册查询处理器
    this.queryBus.register('GetAllUsers', new GetAllUsersQueryHandler(this.userReadModel));
    this.queryBus.register('GetAllOrders', new GetAllOrdersQueryHandler(this.orderReadModel));
    this.queryBus.register('GetUsersByName', new GetUsersByNameQueryHandler(this.userReadModel));
    this.queryBus.register('GetStats', new GetStatsQueryHandler(this.statsReadModel));
    this.queryBus.register('GetUserStats', new GetStatsQueryHandler(this.statsReadModel));
    this.queryBus.register('GetOrderStats', new GetStatsQueryHandler(this.statsReadModel));
    
    // 注册事件处理器
    this.eventBus.subscribe('UserCreated', new UserCreatedEventHandler(this.userReadModel, this.statsReadModel));
    this.eventBus.subscribe('OrderCreated', new OrderCreatedEventHandler(this.orderReadModel, this.statsReadModel));
  }

  async executeCommand(type: string, data: any): Promise<any> {
    const command: ICommand = {
      id: this.generateId(),
      type,
      data,
      timestamp: new Date()
    };
    
    return await this.commandBus.execute(command);
  }

  async executeQuery(type: string, parameters: any): Promise<any> {
    const query: IQuery = {
      id: this.generateId(),
      type,
      parameters,
      timestamp: new Date()
    };
    
    return await this.queryBus.execute(query);
  }

  async getWriteModelStats(): Promise<{
    userCount: number;
    orderCount: number;
  }> {
    return {
      userCount: this.userAggregate.getUserCount(),
      orderCount: this.orderAggregate.getOrderCount()
    };
  }

  async getReadModelStats(): Promise<{
    userView: { count: number };
    orderView: { count: number };
    statsView: { totalRevenue: number };
  }> {
    return {
      userView: { count: this.userReadModel.getUserCount() },
      orderView: { count: this.orderReadModel.getOrderCount() },
      statsView: { totalRevenue: this.orderReadModel.getTotalRevenue() }
    };
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 导出类型
export type { ICommand, IQuery, IEvent, User, Order };
