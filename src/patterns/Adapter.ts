/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 12:20:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 12:20:00
 * @FilePath     : /src/patterns/Adapter.ts
 * @Description  : 适配器模式实现 - 第三方支付接口适配
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 目标接口 - 我们系统期望的统一支付接口
interface PaymentTarget {
  processPayment(amount: number, currency: string): PaymentResult;
  getPaymentStatus(transactionId: string): PaymentStatus;
  refund(transactionId: string, amount: number): RefundResult;
}

// 支付结果接口
interface PaymentResult {
  success: boolean;
  transactionId: string;
  message: string;
  timestamp: Date;
  fee: number;
}

// 支付状态枚举
enum PaymentStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 退款结果接口
interface RefundResult {
  success: boolean;
  refundId: string;
  message: string;
  timestamp: Date;
}

// === 第三方支付系统 A (支付宝风格) ===
class AlipayService {
  // 支付宝的支付方法 - 接口不同
  pay(money: number, coin: string): {
    code: number;
    data: { trade_no: string; msg: string; fee_amount: number };
    timestamp: number;
  } {
    const success = Math.random() > 0.1; // 90% 成功率
    return {
      code: success ? 200 : 500,
      data: {
        trade_no: `alipay_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`,
        msg: success ? '支付成功' : '支付失败',
        fee_amount: money * 0.006 // 0.6% 手续费
      },
      timestamp: Date.now()
    };
  }

  // 查询订单状态
  queryOrder(tradeNo: string): { code: number; status: string } {
    const statuses = ['TRADE_SUCCESS', 'TRADE_CLOSED', 'WAIT_BUYER_PAY'];
    return {
      code: 200,
      status: statuses[Math.floor(Math.random() * statuses.length)]
    };
  }

  // 退款
  refundOrder(tradeNo: string, refundAmount: number): {
    code: number;
    refund_no: string;
    msg: string;
  } {
    const success = Math.random() > 0.05; // 95% 成功率
    return {
      code: success ? 200 : 500,
      refund_no: `refund_${Date.now()}`,
      msg: success ? '退款成功' : '退款失败'
    };
  }
}

// === 第三方支付系统 B (微信风格) ===
class WechatPayService {
  // 微信的支付方法 - 接口完全不同
  createOrder(orderAmount: number, currencyType: string): {
    result_code: string;
    transaction_id: string;
    return_msg: string;
    total_fee: number;
    create_time: string;
  } {
    const success = Math.random() > 0.08; // 92% 成功率
    return {
      result_code: success ? 'SUCCESS' : 'FAIL',
      transaction_id: `wx_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`,
      return_msg: success ? 'OK' : 'SYSTEM_ERROR',
      total_fee: orderAmount * 0.008, // 0.8% 手续费
      create_time: new Date().toISOString()
    };
  }

  // 查询支付结果
  queryPayment(transactionId: string): { result_code: string; trade_state: string } {
    const states = ['SUCCESS', 'REFUND', 'NOTPAY', 'CLOSED'];
    return {
      result_code: 'SUCCESS',
      trade_state: states[Math.floor(Math.random() * states.length)]
    };
  }

  // 申请退款
  applyRefund(transactionId: string, refundFee: number): {
    result_code: string;
    refund_id: string;
    return_msg: string;
  } {
    const success = Math.random() > 0.03; // 97% 成功率
    return {
      result_code: success ? 'SUCCESS' : 'FAIL',
      refund_id: `wx_refund_${Date.now()}`,
      return_msg: success ? 'OK' : 'REFUND_ERROR'
    };
  }
}

// === 第三方支付系统 C (银联风格) ===
class UnionPayService {
  // 银联的支付方法 - 又是不同的接口
  submitPayment(payAmount: number, currencyCode: string): {
    respCode: string;
    orderId: string;
    respMsg: string;
    settleAmt: number;
    settleDate: string;
  } {
    const success = Math.random() > 0.12; // 88% 成功率
    return {
      respCode: success ? '00' : '01',
      orderId: `union_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`,
      respMsg: success ? '成功' : '失败',
      settleAmt: payAmount * 0.005, // 0.5% 手续费
      settleDate: new Date().toISOString().split('T')[0]
    };
  }

  // 查询交易
  queryTransaction(orderId: string): { respCode: string; origRespCode: string } {
    const codes = ['00', '01', '02', '03']; // 成功、失败、处理中、已撤销
    return {
      respCode: '00',
      origRespCode: codes[Math.floor(Math.random() * codes.length)]
    };
  }

  // 退货
  refundTransaction(orderId: string, refundAmt: number): {
    respCode: string;
    refundOrderId: string;
    respMsg: string;
  } {
    const success = Math.random() > 0.04; // 96% 成功率
    return {
      respCode: success ? '00' : '01',
      refundOrderId: `union_refund_${Date.now()}`,
      respMsg: success ? '退货成功' : '退货失败'
    };
  }
}

// === 适配器实现 ===

// 支付宝适配器
class AlipayAdapter implements PaymentTarget {
  private alipayService: AlipayService;

  constructor(alipayService: AlipayService) {
    this.alipayService = alipayService;
  }

  processPayment(amount: number, currency: string): PaymentResult {
    const result = this.alipayService.pay(amount, currency);
    
    return {
      success: result.code === 200,
      transactionId: result.data.trade_no,
      message: result.data.msg,
      timestamp: new Date(result.timestamp),
      fee: result.data.fee_amount
    };
  }

  getPaymentStatus(transactionId: string): PaymentStatus {
    const result = this.alipayService.queryOrder(transactionId);
    
    switch (result.status) {
      case 'TRADE_SUCCESS':
        return PaymentStatus.SUCCESS;
      case 'TRADE_CLOSED':
        return PaymentStatus.FAILED;
      case 'WAIT_BUYER_PAY':
        return PaymentStatus.PENDING;
      default:
        return PaymentStatus.FAILED;
    }
  }

  refund(transactionId: string, amount: number): RefundResult {
    const result = this.alipayService.refundOrder(transactionId, amount);
    
    return {
      success: result.code === 200,
      refundId: result.refund_no,
      message: result.msg,
      timestamp: new Date()
    };
  }
}

// 微信支付适配器
class WechatPayAdapter implements PaymentTarget {
  private wechatService: WechatPayService;

  constructor(wechatService: WechatPayService) {
    this.wechatService = wechatService;
  }

  processPayment(amount: number, currency: string): PaymentResult {
    const result = this.wechatService.createOrder(amount, currency);
    
    return {
      success: result.result_code === 'SUCCESS',
      transactionId: result.transaction_id,
      message: result.return_msg,
      timestamp: new Date(result.create_time),
      fee: result.total_fee
    };
  }

  getPaymentStatus(transactionId: string): PaymentStatus {
    const result = this.wechatService.queryPayment(transactionId);
    
    switch (result.trade_state) {
      case 'SUCCESS':
        return PaymentStatus.SUCCESS;
      case 'REFUND':
      case 'CLOSED':
        return PaymentStatus.FAILED;
      case 'NOTPAY':
        return PaymentStatus.PENDING;
      default:
        return PaymentStatus.FAILED;
    }
  }

  refund(transactionId: string, amount: number): RefundResult {
    const result = this.wechatService.applyRefund(transactionId, amount);
    
    return {
      success: result.result_code === 'SUCCESS',
      refundId: result.refund_id,
      message: result.return_msg,
      timestamp: new Date()
    };
  }
}

// 银联支付适配器
class UnionPayAdapter implements PaymentTarget {
  private unionPayService: UnionPayService;

  constructor(unionPayService: UnionPayService) {
    this.unionPayService = unionPayService;
  }

  processPayment(amount: number, currency: string): PaymentResult {
    const result = this.unionPayService.submitPayment(amount, currency);
    
    return {
      success: result.respCode === '00',
      transactionId: result.orderId,
      message: result.respMsg,
      timestamp: new Date(result.settleDate),
      fee: result.settleAmt
    };
  }

  getPaymentStatus(transactionId: string): PaymentStatus {
    const result = this.unionPayService.queryTransaction(transactionId);
    
    switch (result.origRespCode) {
      case '00':
        return PaymentStatus.SUCCESS;
      case '01':
        return PaymentStatus.FAILED;
      case '02':
        return PaymentStatus.PENDING;
      case '03':
        return PaymentStatus.CANCELLED;
      default:
        return PaymentStatus.FAILED;
    }
  }

  refund(transactionId: string, amount: number): RefundResult {
    const result = this.unionPayService.refundTransaction(transactionId, amount);
    
    return {
      success: result.respCode === '00',
      refundId: result.refundOrderId,
      message: result.respMsg,
      timestamp: new Date()
    };
  }
}

// 适配器工厂
class PaymentAdapterFactory {
  static createAdapter(type: 'alipay' | 'wechat' | 'unionpay'): PaymentTarget {
    switch (type) {
      case 'alipay':
        return new AlipayAdapter(new AlipayService());
      case 'wechat':
        return new WechatPayAdapter(new WechatPayService());
      case 'unionpay':
        return new UnionPayAdapter(new UnionPayService());
      default:
        throw new Error(`不支持的支付类型: ${type}`);
    }
  }

  static getSupportedTypes(): Array<{
    id: string;
    name: string;
    description: string;
  }> {
    return [
      { id: 'alipay', name: '支付宝', description: '蚂蚁金服支付平台' },
      { id: 'wechat', name: '微信支付', description: '腾讯微信支付平台' },
      { id: 'unionpay', name: '银联支付', description: '中国银联支付平台' }
    ];
  }
}

// 导出类型和工厂
export { PaymentAdapterFactory, PaymentStatus };
export type PaymentTargetType = PaymentTarget;
export type PaymentResultType = PaymentResult;
export type RefundResultType = RefundResult;
