/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 12:25:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-26 12:25:00
 * @FilePath     : /src/patterns/Facade.ts
 * @Description  : 外观模式实现 - 智能家居控制系统
 * Copyright 2025 Bruce, All Rights Reserved.
 */

// 子系统 - 照明系统
class LightingSystem {
  private lights: Map<string, boolean> = new Map();
  private brightness: Map<string, number> = new Map();

  constructor() {
    // 初始化房间灯光
    const rooms = ['客厅', '卧室', '厨房', '书房', '卫生间'];
    rooms.forEach(room => {
      this.lights.set(room, false);
      this.brightness.set(room, 50);
    });
  }

  turnOnLight(room: string): string {
    this.lights.set(room, true);
    return `💡 ${room}灯光已开启`;
  }

  turnOffLight(room: string): string {
    this.lights.set(room, false);
    return `💡 ${room}灯光已关闭`;
  }

  setBrightness(room: string, level: number): string {
    if (level < 0 || level > 100) {
      return `❌ 亮度值必须在0-100之间`;
    }
    this.brightness.set(room, level);
    return `💡 ${room}灯光亮度设置为 ${level}%`;
  }

  turnOnAllLights(): string {
    const results: string[] = [];
    this.lights.forEach((_, room) => {
      this.lights.set(room, true);
      results.push(`${room}灯光已开启`);
    });
    return `💡 所有灯光已开启: ${results.join(', ')}`;
  }

  turnOffAllLights(): string {
    const results: string[] = [];
    this.lights.forEach((_, room) => {
      this.lights.set(room, false);
      results.push(`${room}灯光已关闭`);
    });
    return `💡 所有灯光已关闭: ${results.join(', ')}`;
  }

  getStatus(): string {
    const status: string[] = [];
    this.lights.forEach((isOn, room) => {
      const brightness = this.brightness.get(room) || 0;
      status.push(`${room}: ${isOn ? '开启' : '关闭'} (${brightness}%)`);
    });
    return `💡 灯光状态: ${status.join(', ')}`;
  }
}

// 子系统 - 空调系统
class AirConditioningSystem {
  private temperature: number = 25;
  private mode: string = 'auto';
  private fanSpeed: number = 2;
  private isOn: boolean = false;

  turnOn(): string {
    this.isOn = true;
    return `❄️ 空调已开启 - 温度: ${this.temperature}°C, 模式: ${this.mode}, 风速: ${this.fanSpeed}`;
  }

  turnOff(): string {
    this.isOn = false;
    return `❄️ 空调已关闭`;
  }

  setTemperature(temp: number): string {
    if (temp < 16 || temp > 30) {
      return `❌ 温度设置范围为16-30°C`;
    }
    this.temperature = temp;
    return `❄️ 空调温度设置为 ${temp}°C`;
  }

  setMode(mode: string): string {
    const validModes = ['制冷', '制热', '除湿', '送风', 'auto'];
    if (!validModes.includes(mode)) {
      return `❌ 无效的空调模式，支持: ${validModes.join(', ')}`;
    }
    this.mode = mode;
    return `❄️ 空调模式设置为 ${mode}`;
  }

  setFanSpeed(speed: number): string {
    if (speed < 1 || speed > 5) {
      return `❌ 风速设置范围为1-5`;
    }
    this.fanSpeed = speed;
    return `❄️ 空调风速设置为 ${speed}`;
  }

  getStatus(): string {
    return `❄️ 空调状态: ${this.isOn ? '开启' : '关闭'} - 温度: ${this.temperature}°C, 模式: ${this.mode}, 风速: ${this.fanSpeed}`;
  }
}

// 子系统 - 安防系统
class SecuritySystem {
  private isArmed: boolean = false;
  private sensors: Map<string, boolean> = new Map();
  private cameras: Map<string, boolean> = new Map();

  constructor() {
    // 初始化传感器和摄像头
    const sensorLocations = ['前门', '后门', '窗户1', '窗户2', '阳台'];
    const cameraLocations = ['客厅', '门口', '后院'];
    
    sensorLocations.forEach(location => {
      this.sensors.set(location, true);
    });
    
    cameraLocations.forEach(location => {
      this.cameras.set(location, true);
    });
  }

  armSystem(): string {
    this.isArmed = true;
    return `🔒 安防系统已启动 - 所有传感器和摄像头正在监控`;
  }

  disarmSystem(): string {
    this.isArmed = false;
    return `🔓 安防系统已解除`;
  }

  checkSensors(): string {
    const activeSensors: string[] = [];
    this.sensors.forEach((isActive, location) => {
      if (isActive) activeSensors.push(location);
    });
    return `🔍 传感器状态: ${activeSensors.join(', ')} 正常工作`;
  }

  checkCameras(): string {
    const activeCameras: string[] = [];
    this.cameras.forEach((isActive, location) => {
      if (isActive) activeCameras.push(location);
    });
    return `📹 摄像头状态: ${activeCameras.join(', ')} 正常录制`;
  }

  simulateAlert(): string {
    if (!this.isArmed) {
      return `⚠️ 安防系统未启动，无法触发警报`;
    }
    return `🚨 警报！检测到异常活动 - 已通知安保人员`;
  }

  getStatus(): string {
    return `🔒 安防系统: ${this.isArmed ? '已启动' : '已解除'} - 传感器: ${this.sensors.size}个, 摄像头: ${this.cameras.size}个`;
  }
}

// 子系统 - 音响系统
class AudioSystem {
  private isOn: boolean = false;
  private volume: number = 50;
  private currentSource: string = 'bluetooth';
  private currentTrack: string = '';

  turnOn(): string {
    this.isOn = true;
    return `🔊 音响系统已开启`;
  }

  turnOff(): string {
    this.isOn = false;
    return `🔊 音响系统已关闭`;
  }

  setVolume(level: number): string {
    if (level < 0 || level > 100) {
      return `❌ 音量范围为0-100`;
    }
    this.volume = level;
    return `🔊 音量设置为 ${level}%`;
  }

  setSource(source: string): string {
    const validSources = ['bluetooth', 'wifi', 'aux', 'usb'];
    if (!validSources.includes(source)) {
      return `❌ 无效的音源，支持: ${validSources.join(', ')}`;
    }
    this.currentSource = source;
    return `🔊 音源切换到 ${source}`;
  }

  playMusic(track: string): string {
    if (!this.isOn) {
      return `❌ 请先开启音响系统`;
    }
    this.currentTrack = track;
    return `🎵 正在播放: ${track} (音量: ${this.volume}%, 音源: ${this.currentSource})`;
  }

  stopMusic(): string {
    this.currentTrack = '';
    return `⏹️ 音乐已停止`;
  }

  getStatus(): string {
    return `🔊 音响状态: ${this.isOn ? '开启' : '关闭'} - 音量: ${this.volume}%, 音源: ${this.currentSource}${this.currentTrack ? `, 正在播放: ${this.currentTrack}` : ''}`;
  }
}

// 外观类 - 智能家居控制器
class SmartHomeFacade {
  private lighting: LightingSystem;
  private airConditioning: AirConditioningSystem;
  private security: SecuritySystem;
  private audio: AudioSystem;

  constructor() {
    this.lighting = new LightingSystem();
    this.airConditioning = new AirConditioningSystem();
    this.security = new SecuritySystem();
    this.audio = new AudioSystem();
  }

  // 场景模式 - 回家模式
  activateHomeMode(): string[] {
    const results: string[] = [];
    results.push('🏠 激活回家模式...');
    results.push(this.security.disarmSystem());
    results.push(this.lighting.turnOnLight('客厅'));
    results.push(this.lighting.setBrightness('客厅', 80));
    results.push(this.airConditioning.turnOn());
    results.push(this.airConditioning.setTemperature(24));
    results.push(this.audio.turnOn());
    results.push(this.audio.playMusic('轻松爵士乐'));
    results.push('✅ 回家模式已激活');
    return results;
  }

  // 场景模式 - 离家模式
  activateAwayMode(): string[] {
    const results: string[] = [];
    results.push('🚪 激活离家模式...');
    results.push(this.lighting.turnOffAllLights());
    results.push(this.airConditioning.turnOff());
    results.push(this.audio.turnOff());
    results.push(this.security.armSystem());
    results.push('✅ 离家模式已激活');
    return results;
  }

  // 场景模式 - 睡眠模式
  activateSleepMode(): string[] {
    const results: string[] = [];
    results.push('🌙 激活睡眠模式...');
    results.push(this.lighting.turnOffLight('客厅'));
    results.push(this.lighting.turnOffLight('厨房'));
    results.push(this.lighting.turnOffLight('书房'));
    results.push(this.lighting.setBrightness('卧室', 10));
    results.push(this.airConditioning.setTemperature(22));
    results.push(this.airConditioning.setMode('制冷'));
    results.push(this.audio.setVolume(20));
    results.push(this.audio.playMusic('白噪音'));
    results.push(this.security.armSystem());
    results.push('✅ 睡眠模式已激活');
    return results;
  }

  // 场景模式 - 聚会模式
  activatePartyMode(): string[] {
    const results: string[] = [];
    results.push('🎉 激活聚会模式...');
    results.push(this.lighting.turnOnAllLights());
    results.push(this.lighting.setBrightness('客厅', 100));
    results.push(this.airConditioning.setTemperature(20));
    results.push(this.airConditioning.setFanSpeed(3));
    results.push(this.audio.turnOn());
    results.push(this.audio.setVolume(80));
    results.push(this.audio.playMusic('动感音乐'));
    results.push(this.security.disarmSystem());
    results.push('✅ 聚会模式已激活');
    return results;
  }

  // 场景模式 - 工作模式
  activateWorkMode(): string[] {
    const results: string[] = [];
    results.push('💼 激活工作模式...');
    results.push(this.lighting.turnOnLight('书房'));
    results.push(this.lighting.setBrightness('书房', 90));
    results.push(this.lighting.turnOffLight('客厅'));
    results.push(this.airConditioning.setTemperature(23));
    results.push(this.airConditioning.setMode('送风'));
    results.push(this.audio.turnOn());
    results.push(this.audio.setVolume(30));
    results.push(this.audio.playMusic('专注音乐'));
    results.push('✅ 工作模式已激活');
    return results;
  }

  // 紧急模式
  activateEmergencyMode(): string[] {
    const results: string[] = [];
    results.push('🚨 激活紧急模式...');
    results.push(this.lighting.turnOnAllLights());
    results.push(this.lighting.setBrightness('客厅', 100));
    results.push(this.audio.turnOn());
    results.push(this.audio.setVolume(100));
    results.push(this.security.simulateAlert());
    results.push('⚠️ 紧急模式已激活 - 已通知相关部门');
    return results;
  }

  // 获取所有系统状态
  getSystemStatus(): string[] {
    return [
      '📊 智能家居系统状态:',
      this.lighting.getStatus(),
      this.airConditioning.getStatus(),
      this.security.getStatus(),
      this.audio.getStatus()
    ];
  }

  // 获取子系统实例（用于高级控制）
  getLightingSystem(): LightingSystem {
    return this.lighting;
  }

  getAirConditioningSystem(): AirConditioningSystem {
    return this.airConditioning;
  }

  getSecuritySystem(): SecuritySystem {
    return this.security;
  }

  getAudioSystem(): AudioSystem {
    return this.audio;
  }
}

// 导出外观类和子系统
export { SmartHomeFacade, LightingSystem, AirConditioningSystem, SecuritySystem, AudioSystem };
export type SmartHomeFacadeType = SmartHomeFacade;
export type LightingSystemType = LightingSystem;
export type AirConditioningSystemType = AirConditioningSystem;
export type SecuritySystemType = SecuritySystem;
export type AudioSystemType = AudioSystem;
