// 迭代器模式 (Iterator Pattern) - 播放列表管理系统

// 迭代器接口
interface Iterator<T> {
  hasNext(): boolean
  next(): T | null
  reset(): void
  current(): T | null
}

// 聚合对象接口
interface Iterable<T> {
  createIterator(): Iterator<T>
  getItems(): T[]
  getCount(): number
}

// 歌曲类
export class Song {
  constructor(
    public id: string,
    public title: string,
    public artist: string,
    public duration: number, // 秒
    public genre: string
  ) {}

  getInfo(): string {
    const minutes = Math.floor(this.duration / 60)
    const seconds = this.duration % 60
    return `${this.title} - ${this.artist} (${minutes}:${seconds.toString().padStart(2, '0')})`
  }

  getDurationString(): string {
    const minutes = Math.floor(this.duration / 60)
    const seconds = this.duration % 60
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }
}

// 具体迭代器 - 顺序迭代器
class SequentialIterator implements Iterator<Song> {
  private position: number = 0
  private playlist: Playlist

  constructor(playlist: Playlist) {
    this.playlist = playlist
  }

  hasNext(): boolean {
    return this.position < this.playlist.getCount()
  }

  next(): Song | null {
    if (this.hasNext()) {
      const song = this.playlist.getItems()[this.position]
      this.position++
      return song
    }
    return null
  }

  reset(): void {
    this.position = 0
  }

  current(): Song | null {
    if (this.position > 0 && this.position <= this.playlist.getCount()) {
      return this.playlist.getItems()[this.position - 1]
    }
    return null
  }
}

// 具体迭代器 - 随机迭代器
class RandomIterator implements Iterator<Song> {
  private visitedIndices: Set<number> = new Set()
  private currentIndex: number = -1
  private playlist: Playlist

  constructor(playlist: Playlist) {
    this.playlist = playlist
  }

  hasNext(): boolean {
    return this.visitedIndices.size < this.playlist.getCount()
  }

  next(): Song | null {
    if (!this.hasNext()) {
      return null
    }

    const availableIndices = []
    for (let i = 0; i < this.playlist.getCount(); i++) {
      if (!this.visitedIndices.has(i)) {
        availableIndices.push(i)
      }
    }

    if (availableIndices.length > 0) {
      const randomIndex = availableIndices[Math.floor(Math.random() * availableIndices.length)]
      this.visitedIndices.add(randomIndex)
      this.currentIndex = randomIndex
      return this.playlist.getItems()[randomIndex]
    }

    return null
  }

  reset(): void {
    this.visitedIndices.clear()
    this.currentIndex = -1
  }

  current(): Song | null {
    if (this.currentIndex >= 0) {
      return this.playlist.getItems()[this.currentIndex]
    }
    return null
  }
}

// 具体迭代器 - 按流派过滤迭代器
class GenreFilterIterator implements Iterator<Song> {
  private position: number = 0
  private filteredSongs: Song[] = []
  private currentSong: Song | null = null

  constructor(playlist: Playlist, genre: string) {
    this.filteredSongs = playlist.getItems().filter(song => song.genre === genre)
  }

  hasNext(): boolean {
    return this.position < this.filteredSongs.length
  }

  next(): Song | null {
    if (this.hasNext()) {
      this.currentSong = this.filteredSongs[this.position]
      this.position++
      return this.currentSong
    }
    return null
  }

  reset(): void {
    this.position = 0
    this.currentSong = null
  }

  current(): Song | null {
    return this.currentSong
  }
}

// 具体聚合对象 - 播放列表
export class Playlist implements Iterable<Song> {
  private songs: Song[] = []
  private name: string

  constructor(name: string) {
    this.name = name
  }

  addSong(song: Song): void {
    this.songs.push(song)
  }

  removeSong(songId: string): boolean {
    const index = this.songs.findIndex(song => song.id === songId)
    if (index > -1) {
      this.songs.splice(index, 1)
      return true
    }
    return false
  }

  createIterator(): Iterator<Song> {
    return new SequentialIterator(this)
  }

  createRandomIterator(): Iterator<Song> {
    return new RandomIterator(this)
  }

  createGenreIterator(genre: string): Iterator<Song> {
    return new GenreFilterIterator(this, genre)
  }

  getItems(): Song[] {
    return [...this.songs]
  }

  getCount(): number {
    return this.songs.length
  }

  getName(): string {
    return this.name
  }

  getTotalDuration(): number {
    return this.songs.reduce((total, song) => total + song.duration, 0)
  }

  getGenres(): string[] {
    const genres = new Set(this.songs.map(song => song.genre))
    return Array.from(genres)
  }
}

// 播放器类 - 使用迭代器
export class MusicPlayer {
  private currentPlaylist: Playlist | null = null
  private currentIterator: Iterator<Song> | null = null
  private isPlaying: boolean = false
  private playMode: 'sequential' | 'random' | 'genre' = 'sequential'
  private selectedGenre: string = ''

  setPlaylist(playlist: Playlist): void {
    this.currentPlaylist = playlist
    this.updateIterator()
  }

  setPlayMode(mode: 'sequential' | 'random' | 'genre', genre?: string): void {
    this.playMode = mode
    if (mode === 'genre' && genre) {
      this.selectedGenre = genre
    }
    this.updateIterator()
  }

  private updateIterator(): void {
    if (!this.currentPlaylist) return

    switch (this.playMode) {
      case 'sequential':
        this.currentIterator = this.currentPlaylist.createIterator()
        break
      case 'random':
        this.currentIterator = this.currentPlaylist.createRandomIterator()
        break
      case 'genre':
        this.currentIterator = this.currentPlaylist.createGenreIterator(this.selectedGenre)
        break
    }
  }

  play(): Song | null {
    if (!this.currentIterator) return null

    if (!this.isPlaying) {
      this.isPlaying = true
    }

    return this.currentIterator.current()
  }

  next(): Song | null {
    if (!this.currentIterator) return null

    const nextSong = this.currentIterator.next()
    if (nextSong) {
      this.isPlaying = true
    }
    return nextSong
  }

  hasNext(): boolean {
    return this.currentIterator ? this.currentIterator.hasNext() : false
  }

  reset(): void {
    if (this.currentIterator) {
      this.currentIterator.reset()
    }
    this.isPlaying = false
  }

  getCurrentSong(): Song | null {
    return this.currentIterator ? this.currentIterator.current() : null
  }

  getPlayMode(): string {
    return this.playMode
  }

  isCurrentlyPlaying(): boolean {
    return this.isPlaying
  }

  stop(): void {
    this.isPlaying = false
  }
}
