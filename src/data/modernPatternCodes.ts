export interface ModernPatternCode {
  title: string;
  code: string;
}

export const modernPatternCodes: Record<string, ModernPatternCode> = {
  // 并发型模式
  'producer-consumer': {
    title: '生产者-消费者模式 - 日志处理系统',
    code: `// 生产者-消费者模式实现
interface LogMessage {
  id: string;
  level: 'INFO' | 'WARN' | 'ERROR';
  message: string;
  timestamp: Date;
}

// 缓冲区（队列）
class MessageQueue<T> {
  private queue: T[] = [];
  private maxSize: number;

  constructor(maxSize: number = 100) {
    this.maxSize = maxSize;
  }

  // 生产者添加消息
  enqueue(item: T): boolean {
    if (this.queue.length >= this.maxSize) {
      return false; // 队列已满
    }
    this.queue.push(item);
    return true;
  }

  // 消费者获取消息
  dequeue(): T | null {
    return this.queue.shift() || null;
  }

  size(): number {
    return this.queue.length;
  }

  isEmpty(): boolean {
    return this.queue.length === 0;
  }
}

// 生产者
class LogProducer {
  private queue: MessageQueue<LogMessage>;
  private isRunning = false;

  constructor(queue: MessageQueue<LogMessage>) {
    this.queue = queue;
  }

  start(): void {
    this.isRunning = true;
    this.produce();
  }

  stop(): void {
    this.isRunning = false;
  }

  private async produce(): Promise<void> {
    while (this.isRunning) {
      const message: LogMessage = {
        id: Math.random().toString(36).substr(2, 9),
        level: ['INFO', 'WARN', 'ERROR'][Math.floor(Math.random() * 3)] as any,
        message: \`系统消息 \${Date.now()}\`,
        timestamp: new Date()
      };

      const success = this.queue.enqueue(message);
      if (!success) {
        console.log('队列已满，等待消费者处理...');
      }

      // 模拟生产间隔
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

// 消费者
class LogConsumer {
  private queue: MessageQueue<LogMessage>;
  private isRunning = false;
  private name: string;

  constructor(name: string, queue: MessageQueue<LogMessage>) {
    this.name = name;
    this.queue = queue;
  }

  start(): void {
    this.isRunning = true;
    this.consume();
  }

  stop(): void {
    this.isRunning = false;
  }

  private async consume(): Promise<void> {
    while (this.isRunning) {
      const message = this.queue.dequeue();
      if (message) {
        await this.processMessage(message);
      } else {
        // 队列为空，等待
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
  }

  private async processMessage(message: LogMessage): Promise<void> {
    console.log(\`[\${this.name}] 处理消息: \${message.level} - \${message.message}\`);

    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 根据日志级别进行不同处理
    switch (message.level) {
      case 'ERROR':
        this.saveToErrorLog(message);
        this.sendAlert(message);
        break;
      case 'WARN':
        this.saveToWarningLog(message);
        break;
      case 'INFO':
        this.saveToInfoLog(message);
        break;
    }
  }

  private saveToErrorLog(message: LogMessage): void {
    console.log(\`保存错误日志: \${message.message}\`);
  }

  private saveToWarningLog(message: LogMessage): void {
    console.log(\`保存警告日志: \${message.message}\`);
  }

  private saveToInfoLog(message: LogMessage): void {
    console.log(\`保存信息日志: \${message.message}\`);
  }

  private sendAlert(message: LogMessage): void {
    console.log(\`发送告警: \${message.message}\`);
  }
}

// 使用示例
class LogProcessingSystem {
  private queue: MessageQueue<LogMessage>;
  private producer: LogProducer;
  private consumers: LogConsumer[] = [];

  constructor() {
    this.queue = new MessageQueue<LogMessage>(50);
    this.producer = new LogProducer(this.queue);

    // 创建多个消费者
    this.consumers.push(new LogConsumer('消费者1', this.queue));
    this.consumers.push(new LogConsumer('消费者2', this.queue));
  }

  start(): void {
    console.log('启动日志处理系统...');
    this.producer.start();
    this.consumers.forEach(consumer => consumer.start());
  }

  stop(): void {
    console.log('停止日志处理系统...');
    this.producer.stop();
    this.consumers.forEach(consumer => consumer.stop());
  }

  getQueueStatus(): { size: number; isEmpty: boolean } {
    return {
      size: this.queue.size(),
      isEmpty: this.queue.isEmpty()
    };
  }
}

// 使用
const logSystem = new LogProcessingSystem();
logSystem.start();

// 5秒后停止
setTimeout(() => {
  logSystem.stop();
}, 5000);`
  },

  'future-promise': {
    title: 'Future/Promise模式 - HTTP请求处理',
    code: `// Future/Promise模式实现
interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

interface User {
  id: number;
  name: string;
  email: string;
}

interface Order {
  id: number;
  userId: number;
  amount: number;
  status: string;
}

// Promise链式操作示例
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  // 模拟HTTP请求
  private async request<T>(endpoint: string, delay: number = 1000): Promise<ApiResponse<T>> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.1) { // 90%成功率
          resolve({
            data: this.getMockData<T>(endpoint),
            status: 200,
            message: 'Success'
          });
        } else {
          reject(new Error(\`请求失败: \${endpoint}\`));
        }
      }, delay);
    });
  }

  private getMockData<T>(endpoint: string): T {
    const mockData: Record<string, any> = {
      '/users/1': { id: 1, name: 'Alice', email: '<EMAIL>' },
      '/orders/user/1': [
        { id: 101, userId: 1, amount: 299.99, status: 'completed' },
        { id: 102, userId: 1, amount: 159.99, status: 'pending' }
      ]
    };
    return mockData[endpoint] as T;
  }

  // 获取用户信息
  async getUser(userId: number): Promise<User> {
    const response = await this.request<User>(\`/users/\${userId}\`);
    return response.data;
  }

  // 获取用户订单
  async getUserOrders(userId: number): Promise<Order[]> {
    const response = await this.request<Order[]>(\`/orders/user/\${userId}\`);
    return response.data;
  }
}

// Promise组合和错误处理
class UserService {
  private apiClient: ApiClient;

  constructor() {
    this.apiClient = new ApiClient('https://api.example.com');
  }

  // 串行执行 - 获取用户及其订单
  async getUserWithOrders(userId: number): Promise<{ user: User; orders: Order[] }> {
    try {
      console.log(\`开始获取用户 \${userId} 的信息...\`);

      // 先获取用户信息
      const user = await this.apiClient.getUser(userId);
      console.log('用户信息获取成功:', user);

      // 再获取订单信息
      const orders = await this.apiClient.getUserOrders(userId);
      console.log('订单信息获取成功:', orders);

      return { user, orders };
    } catch (error) {
      console.error('获取用户数据失败:', error);
      throw error;
    }
  }

  // 并行执行 - 同时获取多个用户的信息
  async getMultipleUsers(userIds: number[]): Promise<User[]> {
    try {
      console.log('并行获取多个用户信息...');

      // 创建Promise数组
      const userPromises = userIds.map(id => this.apiClient.getUser(id));

      // 等待所有Promise完成
      const users = await Promise.all(userPromises);
      console.log('所有用户信息获取成功:', users);

      return users;
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw error;
    }
  }

  // Promise.allSettled - 部分失败也能获取成功的结果
  async getUsersWithFallback(userIds: number[]): Promise<{
    successful: User[];
    failed: number[]
  }> {
    console.log('获取用户信息（容错模式）...');

    const userPromises = userIds.map(async (id) => {
      try {
        const user = await this.apiClient.getUser(id);
        return { status: 'fulfilled' as const, value: user, userId: id };
      } catch (error) {
        return { status: 'rejected' as const, reason: error, userId: id };
      }
    });

    const results = await Promise.allSettled(userPromises);

    const successful: User[] = [];
    const failed: number[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.status === 'fulfilled') {
        successful.push(result.value.value);
      } else {
        failed.push(userIds[index]);
      }
    });

    console.log(\`成功获取 \${successful.length} 个用户，失败 \${failed.length} 个\`);
    return { successful, failed };
  }

  // Promise链式操作
  async processUserData(userId: number): Promise<string> {
    return this.apiClient.getUser(userId)
      .then(user => {
        console.log('步骤1: 获取用户信息', user);
        return this.apiClient.getUserOrders(user.id);
      })
      .then(orders => {
        console.log('步骤2: 获取订单信息', orders);
        const totalAmount = orders.reduce((sum, order) => sum + order.amount, 0);
        return \`用户总消费: $\${totalAmount.toFixed(2)}\`;
      })
      .catch(error => {
        console.error('处理用户数据失败:', error);
        return '数据处理失败';
      });
  }
}

// 使用示例
async function demonstratePromisePatterns() {
  const userService = new UserService();

  try {
    // 1. 串行执行
    console.log('=== 串行执行示例 ===');
    const userWithOrders = await userService.getUserWithOrders(1);
    console.log('结果:', userWithOrders);

    // 2. 并行执行
    console.log('\\n=== 并行执行示例 ===');
    const users = await userService.getMultipleUsers([1, 2, 3]);
    console.log('结果:', users);

    // 3. 容错执行
    console.log('\\n=== 容错执行示例 ===');
    const fallbackResult = await userService.getUsersWithFallback([1, 2, 3, 999]);
    console.log('结果:', fallbackResult);

    // 4. 链式操作
    console.log('\\n=== 链式操作示例 ===');
    const processResult = await userService.processUserData(1);
    console.log('结果:', processResult);

  } catch (error) {
    console.error('演示过程中发生错误:', error);
  }
}

// 运行演示
demonstratePromisePatterns();`
  },

  'publish-subscribe': {
    title: '发布-订阅模式 - 新闻发布系统',
    code: `// 发布-订阅模式实现
interface Message {
  id: string;
  type: string;
  data: any;
  timestamp: Date;
  source: string;
}

interface Subscriber {
  id: string;
  name: string;
  callback: (message: Message) => void;
}

// 事件总线 - 发布-订阅模式的核心
class EventBus {
  private subscribers: Map<string, Set<Subscriber>> = new Map();
  private messageHistory: Message[] = [];

  // 订阅事件
  subscribe(eventType: string, subscriber: Subscriber): () => void {
    if (!this.subscribers.has(eventType)) {
      this.subscribers.set(eventType, new Set());
    }

    this.subscribers.get(eventType)!.add(subscriber);
    console.log(\`\${subscriber.name} 订阅了 \${eventType} 事件\`);

    // 返回取消订阅函数
    return () => {
      this.unsubscribe(eventType, subscriber);
    };
  }

  // 取消订阅
  unsubscribe(eventType: string, subscriber: Subscriber): void {
    const eventSubscribers = this.subscribers.get(eventType);
    if (eventSubscribers) {
      eventSubscribers.delete(subscriber);
      console.log(\`\${subscriber.name} 取消订阅了 \${eventType} 事件\`);

      // 如果没有订阅者了，删除事件类型
      if (eventSubscribers.size === 0) {
        this.subscribers.delete(eventType);
      }
    }
  }

  // 发布事件
  publish(eventType: string, data: any, source: string = 'unknown'): void {
    const message: Message = {
      id: Math.random().toString(36).substr(2, 9),
      type: eventType,
      data,
      timestamp: new Date(),
      source
    };

    // 保存消息历史
    this.messageHistory.push(message);

    // 通知所有订阅者
    const eventSubscribers = this.subscribers.get(eventType);
    if (eventSubscribers && eventSubscribers.size > 0) {
      console.log(\`发布 \${eventType} 事件给 \${eventSubscribers.size} 个订阅者\`);
      eventSubscribers.forEach(subscriber => {
        try {
          subscriber.callback(message);
        } catch (error) {
          console.error(\`订阅者 \${subscriber.name} 处理消息时出错:\`, error);
        }
      });
    } else {
      console.log(\`没有订阅者订阅 \${eventType} 事件\`);
    }
  }

  // 获取订阅者数量
  getSubscriberCount(eventType: string): number {
    return this.subscribers.get(eventType)?.size || 0;
  }

  // 获取所有事件类型
  getEventTypes(): string[] {
    return Array.from(this.subscribers.keys());
  }

  // 获取消息历史
  getMessageHistory(eventType?: string): Message[] {
    if (eventType) {
      return this.messageHistory.filter(msg => msg.type === eventType);
    }
    return [...this.messageHistory];
  }

  // 清空消息历史
  clearHistory(): void {
    this.messageHistory = [];
  }
}

// 新闻发布者
class NewsPublisher {
  private eventBus: EventBus;
  private publisherId: string;

  constructor(eventBus: EventBus, publisherId: string) {
    this.eventBus = eventBus;
    this.publisherId = publisherId;
  }

  // 发布科技新闻
  publishTechNews(title: string, content: string): void {
    this.eventBus.publish('tech-news', {
      title,
      content,
      category: '科技'
    }, this.publisherId);
  }

  // 发布体育新闻
  publishSportsNews(title: string, content: string): void {
    this.eventBus.publish('sports-news', {
      title,
      content,
      category: '体育'
    }, this.publisherId);
  }

  // 发布财经新闻
  publishFinanceNews(title: string, content: string): void {
    this.eventBus.publish('finance-news', {
      title,
      content,
      category: '财经'
    }, this.publisherId);
  }

  // 发布紧急通知
  publishUrgentAlert(message: string): void {
    this.eventBus.publish('urgent-alert', {
      message,
      level: 'critical'
    }, this.publisherId);
  }
}

// 新闻订阅者
class NewsSubscriber {
  private id: string;
  private name: string;
  private interests: string[];
  private receivedMessages: Message[] = [];
  private unsubscribeFunctions: (() => void)[] = [];

  constructor(id: string, name: string, interests: string[]) {
    this.id = id;
    this.name = name;
    this.interests = interests;
  }

  // 订阅感兴趣的新闻
  subscribeToNews(eventBus: EventBus): void {
    this.interests.forEach(interest => {
      const unsubscribe = eventBus.subscribe(interest, {
        id: this.id,
        name: this.name,
        callback: (message: Message) => this.handleMessage(message)
      });
      this.unsubscribeFunctions.push(unsubscribe);
    });

    // 订阅紧急通知（所有人都应该收到）
    const urgentUnsubscribe = eventBus.subscribe('urgent-alert', {
      id: this.id,
      name: this.name,
      callback: (message: Message) => this.handleUrgentMessage(message)
    });
    this.unsubscribeFunctions.push(urgentUnsubscribe);
  }

  // 处理普通消息
  private handleMessage(message: Message): void {
    this.receivedMessages.push(message);
    console.log(\`[\${this.name}] 收到消息: \${message.data.title || message.data.message}\`);

    // 模拟处理延迟
    setTimeout(() => {
      console.log(\`[\${this.name}] 已处理消息: \${message.id}\`);
    }, Math.random() * 1000);
  }

  // 处理紧急消息
  private handleUrgentMessage(message: Message): void {
    this.receivedMessages.push(message);
    console.log(\`🚨 [\${this.name}] 收到紧急通知: \${message.data.message}\`);

    // 紧急消息立即处理
    console.log(\`🚨 [\${this.name}] 立即处理紧急通知\`);
  }

  // 取消所有订阅
  unsubscribeAll(): void {
    this.unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    this.unsubscribeFunctions = [];
    console.log(\`\${this.name} 已取消所有订阅\`);
  }

  // 获取收到的消息
  getReceivedMessages(): Message[] {
    return [...this.receivedMessages];
  }

  // 清空消息
  clearMessages(): void {
    this.receivedMessages = [];
  }

  // 获取订阅者信息
  getInfo(): { id: string; name: string; interests: string[]; messageCount: number } {
    return {
      id: this.id,
      name: this.name,
      interests: [...this.interests],
      messageCount: this.receivedMessages.length
    };
  }
}

// 新闻系统管理器
class NewsSystem {
  private eventBus: EventBus;
  private publishers: Map<string, NewsPublisher> = new Map();
  private subscribers: Map<string, NewsSubscriber> = new Map();

  constructor() {
    this.eventBus = new EventBus();
  }

  // 添加发布者
  addPublisher(id: string): NewsPublisher {
    const publisher = new NewsPublisher(this.eventBus, id);
    this.publishers.set(id, publisher);
    return publisher;
  }

  // 添加订阅者
  addSubscriber(id: string, name: string, interests: string[]): NewsSubscriber {
    const subscriber = new NewsSubscriber(id, name, interests);
    subscriber.subscribeToNews(this.eventBus);
    this.subscribers.set(id, subscriber);
    return subscriber;
  }

  // 移除订阅者
  removeSubscriber(id: string): void {
    const subscriber = this.subscribers.get(id);
    if (subscriber) {
      subscriber.unsubscribeAll();
      this.subscribers.delete(id);
    }
  }

  // 获取系统统计
  getSystemStats(): {
    publisherCount: number;
    subscriberCount: number;
    eventTypes: string[];
    totalMessages: number;
  } {
    return {
      publisherCount: this.publishers.size,
      subscriberCount: this.subscribers.size,
      eventTypes: this.eventBus.getEventTypes(),
      totalMessages: this.eventBus.getMessageHistory().length
    };
  }

  // 获取事件总线
  getEventBus(): EventBus {
    return this.eventBus;
  }
}

// 使用示例
function demonstratePublishSubscribe() {
  console.log('=== 发布-订阅模式演示 ===\\n');

  // 创建新闻系统
  const newsSystem = new NewsSystem();

  // 添加发布者
  const techPublisher = newsSystem.addPublisher('tech-publisher');
  const sportsPublisher = newsSystem.addPublisher('sports-publisher');

  // 添加订阅者
  const techFan = newsSystem.addSubscriber('user1', '科技爱好者小王', ['tech-news']);
  const sportsFan = newsSystem.addSubscriber('user2', '体育迷小李', ['sports-news']);
  const newsJunkie = newsSystem.addSubscriber('user3', '新闻达人小张', ['tech-news', 'sports-news', 'finance-news']);

  console.log('系统初始化完成\\n');

  // 发布一些新闻
  setTimeout(() => {
    console.log('--- 发布科技新闻 ---');
    techPublisher.publishTechNews('AI技术突破', '人工智能在医疗领域取得重大突破');
  }, 1000);

  setTimeout(() => {
    console.log('--- 发布体育新闻 ---');
    sportsPublisher.publishSportsNews('世界杯决赛', '激动人心的世界杯决赛即将开始');
  }, 2000);

  setTimeout(() => {
    console.log('--- 发布紧急通知 ---');
    techPublisher.publishUrgentAlert('系统维护通知：今晚22:00-24:00进行系统维护');
  }, 3000);

  setTimeout(() => {
    console.log('--- 系统统计 ---');
    const stats = newsSystem.getSystemStats();
    console.log('系统统计:', stats);

    console.log('\\n--- 订阅者消息统计 ---');
    console.log('科技爱好者小王收到消息:', techFan.getReceivedMessages().length);
    console.log('体育迷小李收到消息:', sportsFan.getReceivedMessages().length);
    console.log('新闻达人小张收到消息:', newsJunkie.getReceivedMessages().length);
  }, 4000);

  setTimeout(() => {
    console.log('\\n--- 取消订阅演示 ---');
    newsSystem.removeSubscriber('user1');

    console.log('--- 再次发布科技新闻 ---');
    techPublisher.publishTechNews('量子计算进展', '量子计算机性能再次提升');
  }, 5000);
}

// 运行演示
demonstratePublishSubscribe();

export { EventBus, NewsPublisher, NewsSubscriber, NewsSystem };`,
  },

  'currying': {
    title: '柯里化模式 - 函数式编程基础',
    code: `// 柯里化模式实现
// 柯里化是将接受多个参数的函数转换为接受单个参数的函数序列的技术

// 基础柯里化实现
function curry<T extends any[], R>(fn: (...args: T) => R): any {
  return function curried(...args: any[]): any {
    if (args.length >= fn.length) {
      return fn.apply(this, args as T);
    } else {
      return function (...nextArgs: any[]) {
        return curried.apply(this, [...args, ...nextArgs]);
      };
    }
  };
}

// 示例1: 基础数学运算
const add = (a: number, b: number, c: number): number => a + b + c;
const curriedAdd = curry(add);

// 使用方式
console.log('=== 基础柯里化演示 ===');
console.log('原始函数:', add(1, 2, 3)); // 6
console.log('柯里化调用:', curriedAdd(1)(2)(3)); // 6
console.log('部分应用:', curriedAdd(1, 2)(3)); // 6
console.log('完全应用:', curriedAdd(1, 2, 3)); // 6

// 创建预配置函数
const addFive = curriedAdd(5);
const addFiveAndTwo = addFive(2);
console.log('预配置函数:', addFiveAndTwo(3)); // 10

// 示例2: 配置函数模式
interface ApiConfig {
  baseUrl: string;
  token: string;
  endpoint: string;
}

// 柯里化的API请求函数
const createApiRequest = (baseUrl: string) =>
  (token: string) =>
  (endpoint: string) =>
  (data?: any): Promise<any> => {
    const config: ApiConfig = { baseUrl, token, endpoint };
    console.log(\`API请求: \${baseUrl}\${endpoint}\`);
    console.log(\`Token: \${token.substring(0, 10)}...\`);
    console.log(\`Data:\`, data);

    // 模拟API请求
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: 'success',
          data: \`Response from \${endpoint}\`,
          config
        });
      }, 100);
    });
  };

// 创建不同环境的API配置
const prodApi = createApiRequest('https://api.prod.com');
const devApi = createApiRequest('https://api.dev.com');

// 创建认证后的API
const prodAuthApi = prodApi('prod-token-12345');
const devAuthApi = devApi('dev-token-67890');

// 创建特定端点的API
const getUsersProd = prodAuthApi('/users');
const getOrdersDev = devAuthApi('/orders');

// 使用预配置的API函数
async function demonstrateApiCurrying() {
  console.log('\\n=== API配置柯里化演示 ===');

  try {
    const usersResult = await getUsersProd({ page: 1, limit: 10 });
    console.log('用户数据:', usersResult);

    const ordersResult = await getOrdersDev({ status: 'pending' });
    console.log('订单数据:', ordersResult);
  } catch (error) {
    console.error('API请求失败:', error);
  }
}

// 示例3: 事件处理器柯里化
type EventType = 'click' | 'hover' | 'focus';
type ActionType = 'log' | 'alert' | 'track';

const createEventHandler = (eventType: EventType) =>
  (action: ActionType) =>
  (element: string) =>
  (actualEvent: EventType, data?: any): void => {
    if (actualEvent === eventType) {
      const timestamp = new Date().toISOString();

      switch (action) {
        case 'log':
          console.log(\`[\${timestamp}] LOG: \${element} triggered \${eventType}\`, data);
          break;
        case 'alert':
          console.warn(\`[\${timestamp}] ALERT: \${element} \${eventType} event!\`, data);
          break;
        case 'track':
          console.info(\`[\${timestamp}] TRACK: \${element} \${eventType} analytics\`, data);
          break;
      }
    }
  };

// 创建不同类型的事件处理器
const clickLogger = createEventHandler('click')('log');
const hoverTracker = createEventHandler('hover')('track');
const focusAlerter = createEventHandler('focus')('alert');

// 为特定元素创建处理器
const buttonClickLogger = clickLogger('submit-button');
const menuHoverTracker = hoverTracker('navigation-menu');
const inputFocusAlerter = focusAlerter('email-input');

console.log('\\n=== 事件处理器柯里化演示 ===');
buttonClickLogger('click', { x: 100, y: 200 });
menuHoverTracker('hover', { item: 'home' });
inputFocusAlerter('focus', { value: '<EMAIL>' });

// 示例4: 数据处理管道
interface DataProcessor<T, R> {
  (data: T): R;
}

// 柯里化的过滤器
const createFilter = <T>(predicate: (item: T) => boolean) =>
  (data: T[]): T[] => data.filter(predicate);

// 柯里化的映射器
const createMapper = <T, R>(transform: (item: T) => R) =>
  (data: T[]): R[] => data.map(transform);

// 柯里化的聚合器
const createReducer = <T, R>(
  reducer: (acc: R, item: T) => R,
  initialValue: R
) =>
  (data: T[]): R => data.reduce(reducer, initialValue);

// 创建具体的处理函数
const filterEven = createFilter((n: number) => n % 2 === 0);
const filterGreaterThan = (threshold: number) =>
  createFilter((n: number) => n > threshold);

const mapDouble = createMapper((n: number) => n * 2);
const mapSquare = createMapper((n: number) => n * n);

const sumReducer = createReducer(
  (acc: number, item: number) => acc + item,
  0
);

const productReducer = createReducer(
  (acc: number, item: number) => acc * item,
  1
);

// 组合处理管道
function createProcessingPipeline<T>(...processors: DataProcessor<T, T>[]) {
  return (data: T): T => {
    return processors.reduce((result, processor) => processor(result), data);
  };
}

console.log('\\n=== 数据处理管道柯里化演示 ===');
const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
console.log('原始数据:', numbers);

// 创建处理管道
const pipeline1 = createProcessingPipeline(
  filterGreaterThan(3),
  mapDouble
);

const pipeline2 = createProcessingPipeline(
  filterEven,
  mapSquare
);

console.log('管道1 (过滤>3, 乘2):', pipeline1(numbers));
console.log('管道2 (过滤偶数, 平方):', pipeline2(numbers));

// 聚合结果
const evenNumbers = filterEven(numbers);
const doubledNumbers = mapDouble(evenNumbers);
const sum = sumReducer(doubledNumbers);
const product = productReducer(evenNumbers);

console.log('偶数:', evenNumbers);
console.log('偶数乘2:', doubledNumbers);
console.log('偶数乘2的和:', sum);
console.log('偶数的积:', product);

// 示例5: 高级柯里化 - 函数组合
const compose = <T>(...fns: Function[]) =>
  (value: T) => fns.reduceRight((acc, fn) => fn(acc), value);

const pipe = <T>(...fns: Function[]) =>
  (value: T) => fns.reduce((acc, fn) => fn(acc), value);

// 创建可组合的函数
const increment = (n: number) => n + 1;
const double = (n: number) => n * 2;
const square = (n: number) => n * n;

// 函数组合
const incrementThenDouble = compose(double, increment);
const doubleAndSquare = pipe(double, square);

console.log('\\n=== 函数组合演示 ===');
console.log('5 -> increment -> double:', incrementThenDouble(5)); // (5+1)*2 = 12
console.log('3 -> double -> square:', doubleAndSquare(3)); // (3*2)^2 = 36

// 示例6: 实际应用 - 表单验证
interface ValidationRule<T> {
  (value: T): boolean;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

const createValidator = <T>(rules: Array<{
  rule: ValidationRule<T>;
  message: string;
}>) =>
  (value: T): ValidationResult => {
    const errors: string[] = [];

    for (const { rule, message } of rules) {
      if (!rule(value)) {
        errors.push(message);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

// 创建验证规则
const required = (value: string) => value.trim().length > 0;
const minLength = (min: number) => (value: string) => value.length >= min;
const maxLength = (max: number) => (value: string) => value.length <= max;
const emailPattern = (value: string) => /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value);

// 创建字段验证器
const usernameValidator = createValidator([
  { rule: required, message: '用户名不能为空' },
  { rule: minLength(3), message: '用户名至少3个字符' },
  { rule: maxLength(20), message: '用户名最多20个字符' }
]);

const emailValidator = createValidator([
  { rule: required, message: '邮箱不能为空' },
  { rule: emailPattern, message: '邮箱格式不正确' }
]);

const passwordValidator = createValidator([
  { rule: required, message: '密码不能为空' },
  { rule: minLength(8), message: '密码至少8个字符' },
  { rule: (value: string) => /[A-Z]/.test(value), message: '密码必须包含大写字母' },
  { rule: (value: string) => /[0-9]/.test(value), message: '密码必须包含数字' }
]);

console.log('\\n=== 表单验证柯里化演示 ===');

// 测试验证
const testValidation = (validator: (value: string) => ValidationResult, value: string, field: string) => {
  const result = validator(value);
  console.log(\`\${field} "\${value}": \${result.isValid ? '✓ 有效' : '✗ 无效'}\`);
  if (!result.isValid) {
    result.errors.forEach(error => console.log(\`  - \${error}\`));
  }
};

testValidation(usernameValidator, 'ab', '用户名');
testValidation(usernameValidator, 'validuser', '用户名');
testValidation(emailValidator, 'invalid-email', '邮箱');
testValidation(emailValidator, '<EMAIL>', '邮箱');
testValidation(passwordValidator, '123', '密码');
testValidation(passwordValidator, 'SecurePass123', '密码');

// 运行演示
demonstrateApiCurrying();

// 柯里化的优势总结
console.log('\\n=== 柯里化模式优势 ===');
console.log('1. 函数复用: 创建预配置的函数变体');
console.log('2. 参数预设: 部分应用参数，延迟执行');
console.log('3. 函数组合: 更容易组合和链式调用');
console.log('4. 代码简洁: 减少重复代码，提高可读性');
console.log('5. 类型安全: TypeScript支持良好的类型推导');

export {
  curry,
  createApiRequest,
  createEventHandler,
  createFilter,
  createMapper,
  createReducer,
  createValidator,
  compose,
  pipe
};`,
  },

  'mvvm': {
    title: 'MVVM模式 - 现代前端架构',
    code: `// MVVM模式实现
// Model-View-ViewModel 是一种架构模式，特别适用于现代前端框架

// 1. Model 层 - 数据模型
interface UserModel {
  firstName: string;
  lastName: string;
  email: string;
  age: number;
}

interface ProductModel {
  name: string;
  price: number;
  stock: number;
  rating: number;
}

// 数据验证器
class ModelValidator {
  static validateUser(user: UserModel): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!user.firstName.trim()) errors.push('名字不能为空');
    if (!user.lastName.trim()) errors.push('姓氏不能为空');
    if (!this.isValidEmail(user.email)) errors.push('邮箱格式不正确');
    if (user.age <= 0) errors.push('年龄必须大于0');

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static validateProduct(product: ProductModel): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!product.name.trim()) errors.push('产品名称不能为空');
    if (product.price <= 0) errors.push('价格必须大于0');
    if (product.stock < 0) errors.push('库存不能为负数');
    if (product.rating < 0 || product.rating > 5) errors.push('评分必须在0-5之间');

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  private static isValidEmail(email: string): boolean {
    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);
  }
}

// 2. ViewModel 层 - 视图模型
class UserViewModel {
  private _model: UserModel;
  private _listeners: Set<() => void> = new Set();

  constructor(model: UserModel) {
    this._model = { ...model };
  }

  // 数据绑定
  get firstName(): string { return this._model.firstName; }
  set firstName(value: string) {
    this._model.firstName = value;
    this.notifyChange();
  }

  get lastName(): string { return this._model.lastName; }
  set lastName(value: string) {
    this._model.lastName = value;
    this.notifyChange();
  }

  get email(): string { return this._model.email; }
  set email(value: string) {
    this._model.email = value;
    this.notifyChange();
  }

  get age(): number { return this._model.age; }
  set age(value: number) {
    this._model.age = value;
    this.notifyChange();
  }

  // 计算属性
  get fullName(): string {
    return \`\${this._model.firstName} \${this._model.lastName}\`.trim() || '未设置';
  }

  get isValid(): boolean {
    return ModelValidator.validateUser(this._model).isValid;
  }

  get validationErrors(): string[] {
    return ModelValidator.validateUser(this._model).errors;
  }

  get displayStatus(): string {
    if (!this._model.firstName || !this._model.lastName) return '信息不完整';
    if (!ModelValidator.isValidEmail(this._model.email)) return '邮箱无效';
    if (this._model.age <= 0) return '年龄无效';
    if (this._model.age < 18) return '未成年';
    if (this._model.age >= 60) return '老年用户';
    return '正常用户';
  }

  get statusClass(): string {
    const status = this.displayStatus;
    if (status === '正常用户') return 'status-success';
    if (['信息不完整', '邮箱无效', '年龄无效'].includes(status)) return 'status-error';
    return 'status-warning';
  }

  // 命令方法
  updateFromModel(model: UserModel): void {
    this._model = { ...model };
    this.notifyChange();
  }

  reset(): void {
    this._model = { firstName: '', lastName: '', email: '', age: 0 };
    this.notifyChange();
  }

  toModel(): UserModel {
    return { ...this._model };
  }

  // 观察者模式 - 变更通知
  subscribe(listener: () => void): () => void {
    this._listeners.add(listener);
    return () => this._listeners.delete(listener);
  }

  private notifyChange(): void {
    this._listeners.forEach(listener => listener());
  }
}

class ProductViewModel {
  private _model: ProductModel;
  private _listeners: Set<() => void> = new Set();

  constructor(model: ProductModel) {
    this._model = { ...model };
  }

  // 数据绑定
  get name(): string { return this._model.name; }
  set name(value: string) {
    this._model.name = value;
    this.notifyChange();
  }

  get price(): number { return this._model.price; }
  set price(value: number) {
    this._model.price = value;
    this.notifyChange();
  }

  get stock(): number { return this._model.stock; }
  set stock(value: number) {
    this._model.stock = value;
    this.notifyChange();
  }

  get rating(): number { return this._model.rating; }
  set rating(value: number) {
    this._model.rating = value;
    this.notifyChange();
  }

  // 计算属性
  get formattedPrice(): string {
    return this._model.price > 0 ? \`¥\${this._model.price.toFixed(2)}\` : '未设置价格';
  }

  get stockStatus(): string {
    if (this._model.stock <= 0) return '缺货';
    if (this._model.stock < 10) return '库存不足';
    if (this._model.stock < 50) return '库存正常';
    return '库存充足';
  }

  get stockClass(): string {
    const status = this.stockStatus;
    if (['库存充足', '库存正常'].includes(status)) return 'status-success';
    if (status === '库存不足') return 'status-warning';
    return 'status-error';
  }

  get recommendLevel(): string {
    if (this._model.rating >= 4.5) return '强烈推荐';
    if (this._model.rating >= 4.0) return '推荐';
    if (this._model.rating >= 3.0) return '一般';
    if (this._model.rating >= 2.0) return '不推荐';
    return '评分过低';
  }

  get recommendClass(): string {
    const level = this.recommendLevel;
    if (['强烈推荐', '推荐'].includes(level)) return 'status-success';
    if (level === '一般') return 'status-warning';
    return 'status-error';
  }

  get isValid(): boolean {
    return ModelValidator.validateProduct(this._model).isValid;
  }

  // 命令方法
  updateFromModel(model: ProductModel): void {
    this._model = { ...model };
    this.notifyChange();
  }

  reset(): void {
    this._model = { name: '', price: 0, stock: 0, rating: 0 };
    this.notifyChange();
  }

  toModel(): ProductModel {
    return { ...this._model };
  }

  // 观察者模式
  subscribe(listener: () => void): () => void {
    this._listeners.add(listener);
    return () => this._listeners.delete(listener);
  }

  private notifyChange(): void {
    this._listeners.forEach(listener => listener());
  }
}

// 3. 命令模式集成
interface Command {
  execute(): Promise<boolean>;
  canExecute(): boolean;
  name: string;
}

class SaveCommand implements Command {
  name = 'save';

  constructor(
    private userVM: UserViewModel,
    private productVM: ProductViewModel
  ) {}

  canExecute(): boolean {
    return this.userVM.isValid && this.productVM.isValid;
  }

  async execute(): Promise<boolean> {
    if (!this.canExecute()) return false;

    try {
      // 模拟保存操作
      const userData = this.userVM.toModel();
      const productData = this.productVM.toModel();

      console.log('保存用户数据:', userData);
      console.log('保存产品数据:', productData);

      // 模拟异步保存
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log('数据保存成功');
      return true;
    } catch (error) {
      console.error('保存失败:', error);
      return false;
    }
  }
}

class ValidateCommand implements Command {
  name = 'validate';

  constructor(
    private userVM: UserViewModel,
    private productVM: ProductViewModel
  ) {}

  canExecute(): boolean {
    return true;
  }

  async execute(): Promise<boolean> {
    const userErrors = this.userVM.validationErrors;
    const productErrors = ModelValidator.validateProduct(this.productVM.toModel()).errors;

    console.log('用户验证结果:', userErrors.length === 0 ? '有效' : userErrors);
    console.log('产品验证结果:', productErrors.length === 0 ? '有效' : productErrors);

    return userErrors.length === 0 && productErrors.length === 0;
  }
}

class ExportCommand implements Command {
  name = 'export';

  constructor(
    private userVM: UserViewModel,
    private productVM: ProductViewModel
  ) {}

  canExecute(): boolean {
    return this.userVM.isValid || this.productVM.isValid;
  }

  async execute(): Promise<boolean> {
    try {
      const exportData = {
        user: this.userVM.toModel(),
        product: this.productVM.toModel(),
        timestamp: new Date().toISOString()
      };

      // 模拟导出操作
      const dataStr = JSON.stringify(exportData, null, 2);
      console.log('导出数据:', dataStr);

      // 在实际应用中，这里可能是下载文件或发送到服务器
      await new Promise(resolve => setTimeout(resolve, 300));

      console.log('数据导出完成');
      return true;
    } catch (error) {
      console.error('导出失败:', error);
      return false;
    }
  }
}

// 4. View 层 - 视图控制器
class MVVMViewController {
  private userVM: UserViewModel;
  private productVM: ProductViewModel;
  private commands: Map<string, Command> = new Map();
  private commandHistory: Array<{ name: string; timestamp: Date; success: boolean }> = [];

  constructor() {
    // 初始化 ViewModels
    this.userVM = new UserViewModel({
      firstName: '',
      lastName: '',
      email: '',
      age: 0
    });

    this.productVM = new ProductViewModel({
      name: '',
      price: 0,
      stock: 0,
      rating: 0
    });

    // 初始化命令
    this.setupCommands();

    // 设置数据绑定
    this.setupDataBinding();
  }

  private setupCommands(): void {
    this.commands.set('save', new SaveCommand(this.userVM, this.productVM));
    this.commands.set('validate', new ValidateCommand(this.userVM, this.productVM));
    this.commands.set('export', new ExportCommand(this.userVM, this.productVM));
  }

  private setupDataBinding(): void {
    // 监听 ViewModel 变化
    this.userVM.subscribe(() => {
      console.log('用户数据变化:', this.userVM.toModel());
      this.updateView();
    });

    this.productVM.subscribe(() => {
      console.log('产品数据变化:', this.productVM.toModel());
      this.updateView();
    });
  }

  // 公共接口
  getUserViewModel(): UserViewModel {
    return this.userVM;
  }

  getProductViewModel(): ProductViewModel {
    return this.productVM;
  }

  async executeCommand(commandName: string): Promise<boolean> {
    const command = this.commands.get(commandName);
    if (!command) {
      console.error(\`命令不存在: \${commandName}\`);
      return false;
    }

    if (!command.canExecute()) {
      console.warn(\`命令无法执行: \${commandName}\`);
      return false;
    }

    try {
      const success = await command.execute();
      this.commandHistory.push({
        name: commandName,
        timestamp: new Date(),
        success
      });

      console.log(\`命令执行\${success ? '成功' : '失败'}: \${commandName}\`);
      return success;
    } catch (error) {
      console.error(\`命令执行异常: \${commandName}\`, error);
      return false;
    }
  }

  canExecuteCommand(commandName: string): boolean {
    const command = this.commands.get(commandName);
    return command ? command.canExecute() : false;
  }

  getCommandHistory(): Array<{ name: string; timestamp: Date; success: boolean }> {
    return [...this.commandHistory];
  }

  // 批量更新
  batchUpdate(userData: Partial<UserModel>, productData: Partial<ProductModel>): void {
    console.log('开始批量更新...');

    // 更新用户数据
    if (userData.firstName !== undefined) this.userVM.firstName = userData.firstName;
    if (userData.lastName !== undefined) this.userVM.lastName = userData.lastName;
    if (userData.email !== undefined) this.userVM.email = userData.email;
    if (userData.age !== undefined) this.userVM.age = userData.age;

    // 更新产品数据
    if (productData.name !== undefined) this.productVM.name = productData.name;
    if (productData.price !== undefined) this.productVM.price = productData.price;
    if (productData.stock !== undefined) this.productVM.stock = productData.stock;
    if (productData.rating !== undefined) this.productVM.rating = productData.rating;

    console.log('批量更新完成');
  }

  // 重置所有数据
  resetAll(): void {
    this.userVM.reset();
    this.productVM.reset();
    this.commandHistory = [];
    console.log('所有数据已重置');
  }

  // 加载示例数据
  loadSampleData(): void {
    this.batchUpdate(
      {
        firstName: '张',
        lastName: '三',
        email: '<EMAIL>',
        age: 25
      },
      {
        name: 'iPhone 15',
        price: 5999,
        stock: 25,
        rating: 4.8
      }
    );
    console.log('示例数据加载完成');
  }

  private updateView(): void {
    // 在实际应用中，这里会更新 DOM 或触发框架的重新渲染
    // 在 Vue.js 中，这通常由响应式系统自动处理
    console.log('视图更新触发');
  }
}

// 5. 使用示例
console.log('=== MVVM模式演示 ===');

// 创建控制器
const controller = new MVVMViewController();

// 获取 ViewModels
const userVM = controller.getUserViewModel();
const productVM = controller.getProductViewModel();

// 演示数据绑定
console.log('\\n--- 数据绑定演示 ---');
userVM.firstName = '李';
userVM.lastName = '四';
userVM.email = '<EMAIL>';
userVM.age = 30;

console.log('用户全名:', userVM.fullName);
console.log('用户状态:', userVM.displayStatus);
console.log('用户有效性:', userVM.isValid);

// 演示计算属性
console.log('\\n--- 计算属性演示 ---');
productVM.name = 'MacBook Pro';
productVM.price = 12999;
productVM.stock = 8;
productVM.rating = 4.9;

console.log('格式化价格:', productVM.formattedPrice);
console.log('库存状态:', productVM.stockStatus);
console.log('推荐等级:', productVM.recommendLevel);

// 演示命令模式
console.log('\\n--- 命令模式演示 ---');
async function demonstrateCommands() {
  console.log('验证命令可执行性:', controller.canExecuteCommand('save'));

  await controller.executeCommand('validate');
  await controller.executeCommand('save');
  await controller.executeCommand('export');

  console.log('命令历史:', controller.getCommandHistory());
}

// 演示批量更新
console.log('\\n--- 批量更新演示 ---');
controller.batchUpdate(
  { firstName: '王', lastName: '五', age: 28 },
  { name: 'AirPods Pro', price: 1999, stock: 50 }
);

// 运行命令演示
demonstrateCommands();

// MVVM模式的优势总结
console.log('\\n=== MVVM模式优势 ===');
console.log('1. 数据绑定: 自动同步Model和View之间的数据');
console.log('2. 关注点分离: Model、View、ViewModel职责清晰');
console.log('3. 可测试性: ViewModel可以独立于View进行测试');
console.log('4. 可维护性: 代码结构清晰，易于维护和扩展');
console.log('5. 响应式: 数据变化自动触发视图更新');

export {
  UserViewModel,
  ProductViewModel,
  MVVMViewController,
  ModelValidator,
  SaveCommand,
  ValidateCommand,
  ExportCommand
};`,
  },

  'functor': {
    title: 'Functor模式 - 函数式容器',
    code: `// Functor模式实现
// 函子是函数式编程中的核心概念，定义了可以被映射的容器

// 1. 函子接口定义
interface Functor<T> {
  map<U>(fn: (value: T) => U): Functor<U>;
}

// 恒等函数和函数组合
const identity = <T>(x: T): T => x;
const compose = <A, B, C>(g: (b: B) => C, f: (a: A) => B) => (a: A): C => g(f(a));

// 2. Maybe函子 - 处理可能为空的值
class Maybe<T> implements Functor<T> {
  private constructor(private value: T | null) {}

  // 创建有值的Maybe
  static of<T>(value: T): Maybe<T> {
    return new Maybe(value);
  }

  // 创建空的Maybe
  static none<T>(): Maybe<T> {
    return new Maybe<T>(null);
  }

  // 从可能为空的值创建Maybe
  static fromNullable<T>(value: T | null | undefined): Maybe<T> {
    return value != null ? Maybe.of(value) : Maybe.none<T>();
  }

  // 函子的map操作 - 核心方法
  map<U>(fn: (value: T) => U): Maybe<U> {
    if (this.value === null) {
      return Maybe.none<U>();
    }
    try {
      return Maybe.of(fn(this.value));
    } catch (error) {
      return Maybe.none<U>();
    }
  }

  // 获取值（如果存在）
  getValue(): T | null {
    return this.value;
  }

  // 检查是否有值
  hasValue(): boolean {
    return this.value !== null;
  }

  // 获取值或默认值
  getOrElse(defaultValue: T): T {
    return this.value !== null ? this.value : defaultValue;
  }

  // 链式操作（flatMap）
  flatMap<U>(fn: (value: T) => Maybe<U>): Maybe<U> {
    if (this.value === null) {
      return Maybe.none<U>();
    }
    return fn(this.value);
  }

  // 过滤操作
  filter(predicate: (value: T) => boolean): Maybe<T> {
    if (this.value === null || !predicate(this.value)) {
      return Maybe.none<T>();
    }
    return this;
  }

  toString(): string {
    return this.value !== null ? \`Maybe(\${this.value})\` : 'Maybe(None)';
  }
}

// 3. List函子 - 处理数组/列表
class ListFunctor<T> implements Functor<T> {
  private constructor(private items: T[]) {}

  // 创建List函子
  static of<T>(items: T[]): ListFunctor<T> {
    return new ListFunctor([...items]);
  }

  // 从单个值创建List
  static single<T>(item: T): ListFunctor<T> {
    return new ListFunctor([item]);
  }

  // 空List
  static empty<T>(): ListFunctor<T> {
    return new ListFunctor<T>([]);
  }

  // 函子的map操作
  map<U>(fn: (value: T) => U): ListFunctor<U> {
    try {
      return ListFunctor.of(this.items.map(fn));
    } catch (error) {
      return ListFunctor.empty<U>();
    }
  }

  // 获取内部数组
  getValue(): T[] {
    return [...this.items];
  }

  // 获取长度
  length(): number {
    return this.items.length;
  }

  // 检查是否为空
  isEmpty(): boolean {
    return this.items.length === 0;
  }

  // 过滤操作
  filter(predicate: (value: T) => boolean): ListFunctor<T> {
    return ListFunctor.of(this.items.filter(predicate));
  }

  // 链式操作（flatMap）
  flatMap<U>(fn: (value: T) => ListFunctor<U>): ListFunctor<U> {
    const result: U[] = [];
    for (const item of this.items) {
      result.push(...fn(item).getValue());
    }
    return ListFunctor.of(result);
  }

  // 折叠操作
  fold<U>(initial: U, fn: (acc: U, value: T) => U): U {
    return this.items.reduce(fn, initial);
  }

  // 连接操作
  concat(other: ListFunctor<T>): ListFunctor<T> {
    return ListFunctor.of([...this.items, ...other.getValue()]);
  }

  toString(): string {
    return \`List([\${this.items.join(', ')}])\`;
  }
}

// 4. Either函子 - 处理错误或成功的值
abstract class Either<L, R> implements Functor<R> {
  abstract map<U>(fn: (value: R) => U): Either<L, U>;
  abstract isLeft(): boolean;
  abstract isRight(): boolean;
}

class Left<L, R> extends Either<L, R> {
  constructor(private value: L) {
    super();
  }

  map<U>(_fn: (value: R) => U): Either<L, U> {
    return new Left<L, U>(this.value);
  }

  isLeft(): boolean {
    return true;
  }

  isRight(): boolean {
    return false;
  }

  getValue(): L {
    return this.value;
  }

  toString(): string {
    return \`Left(\${this.value})\`;
  }
}

class Right<L, R> extends Either<L, R> {
  constructor(private value: R) {
    super();
  }

  map<U>(fn: (value: R) => U): Either<L, U> {
    try {
      return new Right<L, U>(fn(this.value));
    } catch (error) {
      return new Left<L, U>(error as L);
    }
  }

  isLeft(): boolean {
    return false;
  }

  isRight(): boolean {
    return true;
  }

  getValue(): R {
    return this.value;
  }

  toString(): string {
    return \`Right(\${this.value})\`;
  }
}

// Either工厂函数
const left = <L, R>(value: L): Either<L, R> => new Left(value);
const right = <L, R>(value: R): Either<L, R> => new Right(value);

// 5. 使用示例演示

console.log('=== Functor模式演示 ===');

// Maybe函子演示
console.log('\\n--- Maybe函子演示 ---');
const maybeNumber = Maybe.of(42);
const maybeEmpty = Maybe.none<number>();

console.log('原始值:', maybeNumber.toString()); // Maybe(42)
console.log('空值:', maybeEmpty.toString()); // Maybe(None)

// 链式转换
const result1 = maybeNumber
  .map(x => x * 2)
  .map(x => x + 1)
  .map(x => \`结果: \${x}\`);

console.log('链式转换结果:', result1.toString()); // Maybe(结果: 85)

// 空值的安全处理
const result2 = maybeEmpty
  .map(x => x * 2)
  .map(x => x + 1)
  .getOrElse(0);

console.log('空值处理结果:', result2); // 0

// List函子演示
console.log('\\n--- List函子演示 ---');
const numbers = ListFunctor.of([1, 2, 3, 4, 5]);

const doubled = numbers.map(x => x * 2);
console.log('原始数组:', numbers.toString()); // List([1, 2, 3, 4, 5])
console.log('双倍数组:', doubled.toString()); // List([2, 4, 6, 8, 10])

// 复杂转换
const processed = numbers
  .map(x => x * x)
  .filter(x => x > 10)
  .map(x => \`[\${x}]\`);

console.log('复杂转换:', processed.toString()); // List([[16], [25]])

// Either函子演示
console.log('\\n--- Either函子演示 ---');
const divide = (a: number, b: number): Either<string, number> =>
  b === 0 ? left('除零错误') : right(a / b);

const successResult = divide(10, 2)
  .map(x => x * 2)
  .map(x => x + 1);

const errorResult = divide(10, 0)
  .map(x => x * 2)
  .map(x => x + 1);

console.log('成功结果:', successResult.toString()); // Right(11)
console.log('错误结果:', errorResult.toString()); // Left(除零错误)

// 函子法则验证
console.log('\\n--- 函子法则验证 ---');

// 恒等法则: functor.map(identity) === functor
const original = Maybe.of(42);
const identityMapped = original.map(identity);
console.log('恒等法则验证:',
  original.getValue() === identityMapped.getValue()); // true

// 组合法则: functor.map(f).map(g) === functor.map(compose(g, f))
const f = (x: number) => x * 2;
const g = (x: number) => x + 1;

const stepByStep = original.map(f).map(g);
const composed = original.map(compose(g, f));

console.log('组合法则验证:',
  stepByStep.getValue() === composed.getValue()); // true

// 实际应用场景
console.log('\\n--- 实际应用场景 ---');

// 用户数据处理管道
interface User {
  name?: string;
  email?: string;
  age?: number;
}

const processUser = (user: User): string => {
  return Maybe.fromNullable(user.email)
    .map(email => email.toLowerCase())
    .map(email => email.trim())
    .filter(email => email.includes('@'))
    .map(email => \`用户邮箱: \${email}\`)
    .getOrElse('无效邮箱');
};

const user1: User = { name: 'Alice', email: '  <EMAIL>  ', age: 25 };
const user2: User = { name: 'Bob', age: 30 };

console.log('用户1处理结果:', processUser(user1)); // 用户邮箱: <EMAIL>
console.log('用户2处理结果:', processUser(user2)); // 无效邮箱

console.log('\\n=== Functor模式的优势 ===');
console.log('1. 类型安全的容器操作');
console.log('2. 避免空值异常');
console.log('3. 支持函数组合和链式调用');
console.log('4. 统一的接口处理不同类型的容器');
console.log('5. 遵循数学法则，行为可预测');

export { Maybe, ListFunctor, Either, Left, Right, left, right, identity, compose };`,
  },

  'thread-pool': {
    title: 'Thread Pool模式 - 线程池管理',
    code: `// Thread Pool模式实现
// 线程池通过预先创建固定数量的线程来重复使用，避免频繁创建和销毁线程的开销

// 1. 任务接口和实现
interface ITask {
  id: string;
  name: string;
  execute(): Promise<any>;
}

class Task implements ITask {
  public readonly id: string;
  public readonly name: string;
  private readonly taskFunction: () => Promise<any>;

  constructor(name: string, taskFunction: () => Promise<any>) {
    this.id = \`task-\${Date.now()}-\${Math.random().toString(36).substr(2, 9)}\`;
    this.name = name;
    this.taskFunction = taskFunction;
  }

  async execute(): Promise<any> {
    return await this.taskFunction();
  }
}

// 2. 工作线程类
class WorkerThread {
  public readonly id: number;
  private busy: boolean = false;
  private currentTask: Task | null = null;
  private threadPool: ThreadPool;

  constructor(id: number, threadPool: ThreadPool) {
    this.id = id;
    this.threadPool = threadPool;
  }

  isBusy(): boolean {
    return this.busy;
  }

  getCurrentTask(): Task | null {
    return this.currentTask;
  }

  async executeTask(task: Task): Promise<void> {
    this.busy = true;
    this.currentTask = task;

    try {
      // 通知任务开始
      if (this.threadPool.onTaskStart) {
        this.threadPool.onTaskStart(task);
      }

      const startTime = Date.now();
      await task.execute();
      const executionTime = Date.now() - startTime;

      // 通知任务完成
      if (this.threadPool.onTaskComplete) {
        this.threadPool.onTaskComplete(task, executionTime);
      }
    } catch (error) {
      // 通知任务错误
      if (this.threadPool.onTaskError) {
        this.threadPool.onTaskError(task, error);
      }
    } finally {
      this.busy = false;
      this.currentTask = null;

      // 尝试获取下一个任务
      this.threadPool.tryAssignTask(this);
    }
  }
}

// 3. 线程池配置接口
interface ThreadPoolConfig {
  corePoolSize: number;        // 核心线程数
  maximumPoolSize?: number;    // 最大线程数
  queueCapacity: number;       // 队列容量
  keepAliveTime?: number;      // 线程空闲时间（毫秒）
  rejectedExecutionHandler?: (task: Task) => void; // 拒绝策略
}

// 拒绝策略枚举
enum RejectedExecutionPolicy {
  ABORT = 'abort',           // 抛出异常
  DISCARD = 'discard',       // 丢弃任务
  DISCARD_OLDEST = 'discard_oldest', // 丢弃最老的任务
  CALLER_RUNS = 'caller_runs' // 调用者执行
}

// 4. 线程池核心实现
class ThreadPool {
  private config: ThreadPoolConfig;
  private threads: WorkerThread[] = [];
  private taskQueue: Task[] = [];
  private isShutdown: boolean = false;
  private completedTaskCount: number = 0;

  // 事件回调
  public onTaskStart?: (task: Task) => void;
  public onTaskComplete?: (task: Task, executionTime: number) => void;
  public onTaskError?: (task: Task, error: any) => void;

  constructor(config: ThreadPoolConfig) {
    this.config = {
      maximumPoolSize: config.corePoolSize,
      keepAliveTime: 60000, // 默认60秒
      rejectedExecutionHandler: this.defaultRejectedHandler.bind(this),
      ...config
    };

    this.initializeThreads();
  }

  private initializeThreads(): void {
    for (let i = 0; i < this.config.corePoolSize; i++) {
      const thread = new WorkerThread(i + 1, this);
      this.threads.push(thread);
    }
  }

  private defaultRejectedHandler(task: Task): void {
    throw new Error(\`Task \${task.name} rejected: Queue is full\`);
  }

  // 提交任务 - 核心方法
  async submit(task: Task): Promise<void> {
    if (this.isShutdown) {
      throw new Error('ThreadPool is shutdown');
    }

    // 尝试分配给空闲线程
    const idleThread = this.threads.find(thread => !thread.isBusy());
    if (idleThread) {
      idleThread.executeTask(task);
      return;
    }

    // 检查队列容量
    if (this.taskQueue.length >= this.config.queueCapacity) {
      if (this.config.rejectedExecutionHandler) {
        this.config.rejectedExecutionHandler(task);
      }
      return;
    }

    // 添加到队列
    this.taskQueue.push(task);
  }

  // 尝试分配任务给线程
  tryAssignTask(thread: WorkerThread): void {
    if (this.taskQueue.length > 0 && !thread.isBusy()) {
      const task = this.taskQueue.shift();
      if (task) {
        thread.executeTask(task);
      }
    }
  }

  // 调整线程池大小
  resize(newSize: number): void {
    if (newSize < 1) {
      throw new Error('Pool size must be at least 1');
    }

    const currentSize = this.threads.length;

    if (newSize > currentSize) {
      // 增加线程
      for (let i = currentSize; i < newSize; i++) {
        const thread = new WorkerThread(i + 1, this);
        this.threads.push(thread);

        // 尝试分配任务
        this.tryAssignTask(thread);
      }
    } else if (newSize < currentSize) {
      // 减少线程（只移除空闲线程）
      const threadsToRemove = this.threads
        .filter(thread => !thread.isBusy())
        .slice(0, currentSize - newSize);

      this.threads = this.threads.filter(thread =>
        !threadsToRemove.includes(thread)
      );
    }

    this.config.corePoolSize = newSize;
  }

  // 获取线程池状态
  getPoolStatus(): {
    corePoolSize: number;
    activeCount: number;
    queueSize: number;
    completedTaskCount: number;
    isShutdown: boolean;
  } {
    return {
      corePoolSize: this.config.corePoolSize,
      activeCount: this.threads.filter(thread => thread.isBusy()).length,
      queueSize: this.taskQueue.length,
      completedTaskCount: this.completedTaskCount,
      isShutdown: this.isShutdown
    };
  }

  // 等待所有任务完成
  async awaitTermination(timeout?: number): Promise<boolean> {
    const startTime = Date.now();

    while (this.getActiveCount() > 0 || this.taskQueue.length > 0) {
      if (timeout && (Date.now() - startTime) > timeout) {
        return false;
      }

      // 等待一小段时间
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return true;
  }

  // 关闭线程池
  shutdown(): void {
    this.isShutdown = true;
    this.taskQueue = [];
  }

  getActiveCount(): number {
    return this.threads.filter(thread => thread.isBusy()).length;
  }

  getThreads(): WorkerThread[] {
    return [...this.threads];
  }

  getQueuedTasks(): Task[] {
    return [...this.taskQueue];
  }
}

// 5. 线程池工厂类
class ThreadPoolFactory {
  // 创建固定大小的线程池
  static newFixedThreadPool(nThreads: number, queueCapacity: number = 50): ThreadPool {
    return new ThreadPool({
      corePoolSize: nThreads,
      maximumPoolSize: nThreads,
      queueCapacity
    });
  }

  // 创建单线程池
  static newSingleThreadExecutor(queueCapacity: number = 50): ThreadPool {
    return new ThreadPool({
      corePoolSize: 1,
      maximumPoolSize: 1,
      queueCapacity
    });
  }

  // 创建缓存线程池（模拟）
  static newCachedThreadPool(queueCapacity: number = 100): ThreadPool {
    return new ThreadPool({
      corePoolSize: 2,
      maximumPoolSize: 10,
      queueCapacity,
      keepAliveTime: 60000
    });
  }
}

// 6. 使用示例演示

console.log('=== Thread Pool模式演示 ===');

// 创建线程池
const threadPool = ThreadPoolFactory.newFixedThreadPool(4, 10);

// 设置事件监听
threadPool.onTaskStart = (task: Task) => {
  console.log(\`任务开始: \${task.name}\`);
};

threadPool.onTaskComplete = (task: Task, executionTime: number) => {
  console.log(\`任务完成: \${task.name}, 耗时: \${executionTime}ms\`);
};

threadPool.onTaskError = (task: Task, error: any) => {
  console.error(\`任务错误: \${task.name}, 错误: \${error.message}\`);
};

// 提交任务演示
console.log('\\n--- 任务提交演示 ---');

// CPU密集型任务
const cpuTask = new Task('CPU密集型任务', async () => {
  // 模拟CPU密集型计算
  const start = Date.now();
  while (Date.now() - start < 2000) {
    // 忙等待2秒
  }
  return 'CPU任务完成';
});

// I/O密集型任务
const ioTask = new Task('I/O密集型任务', async () => {
  // 模拟I/O操作
  await new Promise(resolve => setTimeout(resolve, 1500));
  return 'I/O任务完成';
});

// 快速任务
const quickTask = new Task('快速任务', async () => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return '快速任务完成';
});

// 提交任务
async function demonstrateThreadPool() {
  console.log('初始状态:', threadPool.getPoolStatus());

  // 批量提交任务
  const tasks = [cpuTask, ioTask, quickTask];
  for (let i = 0; i < 3; i++) {
    for (const task of tasks) {
      const newTask = new Task(\`\${task.name}-\${i}\`, task.execute.bind(task));
      await threadPool.submit(newTask);
    }
  }

  console.log('任务提交后状态:', threadPool.getPoolStatus());

  // 等待所有任务完成
  await threadPool.awaitTermination(10000);
  console.log('所有任务完成后状态:', threadPool.getPoolStatus());
}

// 性能对比演示
console.log('\\n--- 性能对比演示 ---');

async function performanceComparison() {
  // 不使用线程池的情况
  console.log('不使用线程池:');
  const start1 = Date.now();

  const directTasks = [];
  for (let i = 0; i < 10; i++) {
    directTasks.push(new Promise(resolve => {
      setTimeout(() => {
        console.log(\`直接执行任务 \${i}\`);
        resolve(\`任务\${i}完成\`);
      }, Math.random() * 1000 + 500);
    }));
  }

  await Promise.all(directTasks);
  const time1 = Date.now() - start1;
  console.log(\`直接执行总时间: \${time1}ms\`);

  // 使用线程池的情况
  console.log('\\n使用线程池:');
  const start2 = Date.now();

  const poolTasks = [];
  for (let i = 0; i < 10; i++) {
    const task = new Task(\`池任务\${i}\`, async () => {
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));
      return \`池任务\${i}完成\`;
    });
    poolTasks.push(threadPool.submit(task));
  }

  await Promise.all(poolTasks);
  await threadPool.awaitTermination(5000);
  const time2 = Date.now() - start2;
  console.log(\`线程池执行总时间: \${time2}ms\`);

  console.log(\`性能提升: \${((time1 - time2) / time1 * 100).toFixed(2)}%\`);
}

// 运行演示
demonstrateThreadPool().then(() => {
  return performanceComparison();
}).then(() => {
  threadPool.shutdown();
  console.log('\\n=== Thread Pool模式的优势 ===');
  console.log('1. 减少线程创建和销毁的开销');
  console.log('2. 控制并发线程数量，避免资源耗尽');
  console.log('3. 提高系统响应速度和吞吐量');
  console.log('4. 更好的资源管理和控制');
  console.log('5. 支持任务队列，处理突发请求');
});

export { ThreadPool, ThreadPoolFactory, Task, WorkerThread };`,
  },

  'reader-writer-lock': {
    title: '读写锁模式 - 并发访问控制',
    code: `// 读写锁模式实现
// 允许多个读者同时访问共享资源，但写者需要独占访问

// 1. 锁策略类型和状态接口
type LockPolicy = 'reader-priority' | 'writer-priority' | 'fair';

interface LockStatus {
  activeReaders: number;
  waitingReaders: number;
  isWriting: boolean;
  waitingWriters: number;
}

interface WaitingItem {
  id: string;
  type: 'read' | 'write';
  resolve: () => void;
  timestamp: number;
}

// 2. 读写锁核心实现
class ReaderWriterLock {
  private policy: LockPolicy;
  private maxReaders: number;

  // 锁状态
  private activeReaders: number = 0;
  private isWriting: boolean = false;

  // 等待队列
  private waitingQueue: WaitingItem[] = [];

  // 统计信息
  private totalReads: number = 0;
  private totalWrites: number = 0;

  constructor(policy: LockPolicy = 'reader-priority', maxReaders: number = 10) {
    this.policy = policy;
    this.maxReaders = maxReaders;
  }

  // 获取读锁
  async readLock(): Promise<void> {
    return new Promise<void>((resolve) => {
      const readerId = this.generateId();

      // 检查是否可以立即获得读锁
      if (this.canAcquireReadLock()) {
        this.activeReaders++;
        this.totalReads++;
        resolve();
        return;
      }

      // 加入等待队列
      this.waitingQueue.push({
        id: readerId,
        type: 'read',
        resolve,
        timestamp: Date.now()
      });

      // 根据策略排序队列
      this.sortWaitingQueue();
    });
  }

  // 释放读锁
  readUnlock(): void {
    if (this.activeReaders > 0) {
      this.activeReaders--;

      // 如果没有活跃的读者，尝试唤醒等待的写者
      if (this.activeReaders === 0) {
        this.tryWakeUpNext();
      }
    }
  }

  // 获取写锁
  async writeLock(): Promise<void> {
    return new Promise<void>((resolve) => {
      const writerId = this.generateId();

      // 检查是否可以立即获得写锁
      if (this.canAcquireWriteLock()) {
        this.isWriting = true;
        this.totalWrites++;
        resolve();
        return;
      }

      // 加入等待队列
      this.waitingQueue.push({
        id: writerId,
        type: 'write',
        resolve,
        timestamp: Date.now()
      });

      // 根据策略排序队列
      this.sortWaitingQueue();
    });
  }

  // 释放写锁
  writeUnlock(): void {
    if (this.isWriting) {
      this.isWriting = false;

      // 尝试唤醒等待的读者或写者
      this.tryWakeUpNext();
    }
  }

  // 检查是否可以获得读锁
  private canAcquireReadLock(): boolean {
    // 如果正在写入，不能获得读锁
    if (this.isWriting) {
      return false;
    }

    // 如果已达到最大读者数，不能获得读锁
    if (this.activeReaders >= this.maxReaders) {
      return false;
    }

    // 根据策略判断
    switch (this.policy) {
      case 'reader-priority':
        // 读者优先：只要没有写者在写，就可以获得读锁
        return true;

      case 'writer-priority':
        // 写者优先：如果有写者在等待，新的读者不能获得锁
        return !this.hasWaitingWriters();

      case 'fair':
        // 公平策略：按照请求顺序
        const nextWaiting = this.waitingQueue[0];
        return !nextWaiting || nextWaiting.type === 'read';

      default:
        return true;
    }
  }

  // 检查是否可以获得写锁
  private canAcquireWriteLock(): boolean {
    // 如果有活跃的读者或正在写入，不能获得写锁
    return this.activeReaders === 0 && !this.isWriting;
  }

  // 检查是否有等待的写者
  private hasWaitingWriters(): boolean {
    return this.waitingQueue.some(item => item.type === 'write');
  }

  // 尝试唤醒下一个等待的线程
  private tryWakeUpNext(): void {
    if (this.waitingQueue.length === 0) {
      return;
    }

    if (this.policy === 'reader-priority') {
      this.tryWakeUpReaders();
    } else if (this.policy === 'writer-priority') {
      this.tryWakeUpWriters() || this.tryWakeUpReaders();
    } else { // fair
      this.tryWakeUpFair();
    }
  }

  // 尝试唤醒读者（读者优先策略）
  private tryWakeUpReaders(): void {
    while (this.waitingQueue.length > 0 && this.canAcquireReadLock()) {
      const nextReader = this.waitingQueue.find(item => item.type === 'read');
      if (!nextReader) {
        break;
      }

      // 从队列中移除并唤醒
      const index = this.waitingQueue.indexOf(nextReader);
      this.waitingQueue.splice(index, 1);

      this.activeReaders++;
      this.totalReads++;
      nextReader.resolve();
    }
  }

  // 尝试唤醒写者（写者优先策略）
  private tryWakeUpWriters(): boolean {
    if (this.canAcquireWriteLock()) {
      const nextWriter = this.waitingQueue.find(item => item.type === 'write');
      if (nextWriter) {
        // 从队列中移除并唤醒
        const index = this.waitingQueue.indexOf(nextWriter);
        this.waitingQueue.splice(index, 1);

        this.isWriting = true;
        this.totalWrites++;
        nextWriter.resolve();
        return true;
      }
    }
    return false;
  }

  // 公平策略唤醒
  private tryWakeUpFair(): void {
    while (this.waitingQueue.length > 0) {
      const next = this.waitingQueue[0];

      if (next.type === 'read' && this.canAcquireReadLock()) {
        this.waitingQueue.shift();
        this.activeReaders++;
        this.totalReads++;
        next.resolve();

        // 继续唤醒后续的读者（如果可能）
        while (this.waitingQueue.length > 0 &&
               this.waitingQueue[0].type === 'read' &&
               this.canAcquireReadLock()) {
          const nextReader = this.waitingQueue.shift()!;
          this.activeReaders++;
          this.totalReads++;
          nextReader.resolve();
        }
        break;
      } else if (next.type === 'write' && this.canAcquireWriteLock()) {
        this.waitingQueue.shift();
        this.isWriting = true;
        this.totalWrites++;
        next.resolve();
        break;
      } else {
        // 无法唤醒下一个，退出
        break;
      }
    }
  }

  // 根据策略排序等待队列
  private sortWaitingQueue(): void {
    switch (this.policy) {
      case 'reader-priority':
        // 读者优先：读者排在前面
        this.waitingQueue.sort((a, b) => {
          if (a.type === 'read' && b.type === 'write') return -1;
          if (a.type === 'write' && b.type === 'read') return 1;
          return a.timestamp - b.timestamp;
        });
        break;

      case 'writer-priority':
        // 写者优先：写者排在前面
        this.waitingQueue.sort((a, b) => {
          if (a.type === 'write' && b.type === 'read') return -1;
          if (a.type === 'read' && b.type === 'write') return 1;
          return a.timestamp - b.timestamp;
        });
        break;

      case 'fair':
        // 公平策略：按时间戳排序
        this.waitingQueue.sort((a, b) => a.timestamp - b.timestamp);
        break;
    }
  }

  // 生成唯一ID
  private generateId(): string {
    return \`\${Date.now()}-\${Math.random().toString(36).substr(2, 9)}\`;
  }

  // 获取锁状态
  getStatus(): LockStatus {
    return {
      activeReaders: this.activeReaders,
      waitingReaders: this.waitingQueue.filter(item => item.type === 'read').length,
      isWriting: this.isWriting,
      waitingWriters: this.waitingQueue.filter(item => item.type === 'write').length
    };
  }

  // 设置策略
  setPolicy(policy: LockPolicy): void {
    this.policy = policy;
    this.sortWaitingQueue();

    // 策略改变后，尝试唤醒等待的线程
    if (!this.isWriting && this.activeReaders === 0) {
      this.tryWakeUpNext();
    }
  }

  // 获取统计信息
  getStatistics(): {
    totalReads: number;
    totalWrites: number;
    currentPolicy: LockPolicy;
    queueLength: number;
  } {
    return {
      totalReads: this.totalReads,
      totalWrites: this.totalWrites,
      currentPolicy: this.policy,
      queueLength: this.waitingQueue.length
    };
  }

  // 重置锁状态
  reset(): void {
    this.waitingQueue = [];
    this.activeReaders = 0;
    this.isWriting = false;
    this.totalReads = 0;
    this.totalWrites = 0;
  }
}

// 3. 读写锁工厂类
class ReaderWriterLockFactory {
  // 创建读者优先的读写锁
  static createReaderPriorityLock(maxReaders: number = 10): ReaderWriterLock {
    return new ReaderWriterLock('reader-priority', maxReaders);
  }

  // 创建写者优先的读写锁
  static createWriterPriorityLock(maxReaders: number = 10): ReaderWriterLock {
    return new ReaderWriterLock('writer-priority', maxReaders);
  }

  // 创建公平的读写锁
  static createFairLock(maxReaders: number = 10): ReaderWriterLock {
    return new ReaderWriterLock('fair', maxReaders);
  }
}

// 4. 读写锁监控器
class ReaderWriterLockMonitor {
  private lock: ReaderWriterLock;
  private metrics: {
    readOperations: number;
    writeOperations: number;
    averageReadTime: number;
    averageWriteTime: number;
    peakReaders: number;
    lockContentionCount: number;
  };

  constructor(lock: ReaderWriterLock) {
    this.lock = lock;
    this.metrics = {
      readOperations: 0,
      writeOperations: 0,
      averageReadTime: 0,
      averageWriteTime: 0,
      peakReaders: 0,
      lockContentionCount: 0
    };
  }

  // 监控读操作
  async monitorRead<T>(readOperation: () => Promise<T>): Promise<T> {
    const startTime = Date.now();

    await this.lock.readLock();

    try {
      const result = await readOperation();

      const endTime = Date.now();
      const duration = endTime - startTime;

      this.updateReadMetrics(duration);
      this.updatePeakReaders();

      return result;
    } finally {
      this.lock.readUnlock();
    }
  }

  // 监控写操作
  async monitorWrite<T>(writeOperation: () => Promise<T>): Promise<T> {
    const startTime = Date.now();

    await this.lock.writeLock();

    try {
      const result = await writeOperation();

      const endTime = Date.now();
      const duration = endTime - startTime;

      this.updateWriteMetrics(duration);

      return result;
    } finally {
      this.lock.writeUnlock();
    }
  }

  private updateReadMetrics(duration: number): void {
    this.metrics.readOperations++;
    const totalTime = this.metrics.averageReadTime * (this.metrics.readOperations - 1);
    this.metrics.averageReadTime = (totalTime + duration) / this.metrics.readOperations;
  }

  private updateWriteMetrics(duration: number): void {
    this.metrics.writeOperations++;
    const totalTime = this.metrics.averageWriteTime * (this.metrics.writeOperations - 1);
    this.metrics.averageWriteTime = (totalTime + duration) / this.metrics.writeOperations;
  }

  private updatePeakReaders(): void {
    const currentReaders = this.lock.getStatus().activeReaders;
    if (currentReaders > this.metrics.peakReaders) {
      this.metrics.peakReaders = currentReaders;
    }
  }

  getMetrics() {
    return { ...this.metrics };
  }

  reset(): void {
    this.metrics = {
      readOperations: 0,
      writeOperations: 0,
      averageReadTime: 0,
      averageWriteTime: 0,
      peakReaders: 0,
      lockContentionCount: 0
    };
  }
}

// 5. 使用示例演示

console.log('=== 读写锁模式演示 ===');

// 创建读写锁
const rwLock = ReaderWriterLockFactory.createReaderPriorityLock(5);
const monitor = new ReaderWriterLockMonitor(rwLock);

// 共享数据
let sharedData = { value: 0, lastUpdate: Date.now() };

// 读操作
async function readData(readerId: string): Promise<any> {
  return monitor.monitorRead(async () => {
    console.log(\`读者 \${readerId} 开始读取\`);

    // 模拟读取操作
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));

    const data = { ...sharedData };
    console.log(\`读者 \${readerId} 读取完成: \${JSON.stringify(data)}\`);

    return data;
  });
}

// 写操作
async function writeData(writerId: string, newValue: number): Promise<void> {
  return monitor.monitorWrite(async () => {
    console.log(\`写者 \${writerId} 开始写入\`);

    // 模拟写入操作
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1500 + 1000));

    sharedData = {
      value: newValue,
      lastUpdate: Date.now()
    };

    console.log(\`写者 \${writerId} 写入完成: \${newValue}\`);
  });
}

// 缓存系统演示
console.log('\\n--- 缓存系统演示 ---');

class CacheSystem {
  private cache = new Map<string, any>();
  private rwLock = ReaderWriterLockFactory.createReaderPriorityLock(10);

  async get(key: string): Promise<any> {
    await this.rwLock.readLock();
    try {
      console.log(\`缓存读取: \${key}\`);
      return this.cache.get(key);
    } finally {
      this.rwLock.readUnlock();
    }
  }

  async set(key: string, value: any): Promise<void> {
    await this.rwLock.writeLock();
    try {
      console.log(\`缓存写入: \${key} = \${value}\`);
      this.cache.set(key, value);
    } finally {
      this.rwLock.writeUnlock();
    }
  }

  async clear(): Promise<void> {
    await this.rwLock.writeLock();
    try {
      console.log('清空缓存');
      this.cache.clear();
    } finally {
      this.rwLock.writeUnlock();
    }
  }

  getStatus() {
    return this.rwLock.getStatus();
  }
}

// 配置管理演示
console.log('\\n--- 配置管理演示 ---');

class ConfigManager {
  private config: any = { theme: 'light', language: 'zh-CN' };
  private rwLock = ReaderWriterLockFactory.createFairLock(8);

  async getConfig(key?: string): Promise<any> {
    await this.rwLock.readLock();
    try {
      console.log(\`读取配置: \${key || 'all'}\`);
      return key ? this.config[key] : { ...this.config };
    } finally {
      this.rwLock.readUnlock();
    }
  }

  async updateConfig(updates: any): Promise<void> {
    await this.rwLock.writeLock();
    try {
      console.log(\`更新配置: \${JSON.stringify(updates)}\`);
      Object.assign(this.config, updates);
    } finally {
      this.rwLock.writeUnlock();
    }
  }

  async resetConfig(): Promise<void> {
    await this.rwLock.writeLock();
    try {
      console.log('重置配置');
      this.config = { theme: 'light', language: 'zh-CN' };
    } finally {
      this.rwLock.writeUnlock();
    }
  }
}

// 运行演示
async function demonstrateReaderWriterLock() {
  console.log('初始状态:', rwLock.getStatus());

  // 并发读写操作
  const operations = [];

  // 添加多个读者
  for (let i = 1; i <= 5; i++) {
    operations.push(readData(\`R\${i}\`));
  }

  // 添加写者
  operations.push(writeData('W1', 100));
  operations.push(writeData('W2', 200));

  // 再添加读者
  for (let i = 6; i <= 8; i++) {
    operations.push(readData(\`R\${i}\`));
  }

  // 等待所有操作完成
  await Promise.all(operations);

  console.log('\\n最终状态:', rwLock.getStatus());
  console.log('监控指标:', monitor.getMetrics());
}

// 性能对比演示
async function performanceComparison() {
  console.log('\\n--- 性能对比演示 ---');

  const policies: LockPolicy[] = ['reader-priority', 'writer-priority', 'fair'];

  for (const policy of policies) {
    console.log(\`\\n测试策略: \${policy}\`);

    const testLock = new ReaderWriterLock(policy, 5);
    const testMonitor = new ReaderWriterLockMonitor(testLock);

    const startTime = Date.now();
    const testOps = [];

    // 创建读写操作
    for (let i = 0; i < 10; i++) {
      testOps.push(testMonitor.monitorRead(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return \`read-\${i}\`;
      }));
    }

    for (let i = 0; i < 3; i++) {
      testOps.push(testMonitor.monitorWrite(async () => {
        await new Promise(resolve => setTimeout(resolve, 200));
        return \`write-\${i}\`;
      }));
    }

    await Promise.all(testOps);

    const totalTime = Date.now() - startTime;
    const metrics = testMonitor.getMetrics();

    console.log(\`总时间: \${totalTime}ms\`);
    console.log(\`读操作: \${metrics.readOperations}, 平均时间: \${metrics.averageReadTime.toFixed(2)}ms\`);
    console.log(\`写操作: \${metrics.writeOperations}, 平均时间: \${metrics.averageWriteTime.toFixed(2)}ms\`);
    console.log(\`峰值读者数: \${metrics.peakReaders}\`);
  }
}

// 运行演示
demonstrateReaderWriterLock().then(() => {
  return performanceComparison();
}).then(() => {
  console.log('\\n=== 读写锁模式的优势 ===');
  console.log('1. 允许多个读者并发访问，提高读取性能');
  console.log('2. 保证写操作的独占性，确保数据一致性');
  console.log('3. 支持不同的优先策略，适应不同场景');
  console.log('4. 在读多写少的场景下性能优异');
  console.log('5. 提供更细粒度的并发控制');
});

export { ReaderWriterLock, ReaderWriterLockFactory, ReaderWriterLockMonitor };`,
  },

  'monad': {
    title: '单子模式 - 函数式计算上下文',
    code: `// 单子模式实现
// 提供flatMap操作来处理嵌套的计算上下文，遵循单子法则

// 1. 单子接口定义
interface Monad<T> {
  flatMap<U>(fn: (value: T) => Monad<U>): Monad<U>;
  map<U>(fn: (value: T) => U): Monad<U>;
}

// 单位元函数（unit/return）
const unit = <T>(value: T): Maybe<T> => Maybe.of(value);

// 2. Maybe单子 - 处理可能为空的值
class Maybe<T> implements Monad<T> {
  private constructor(private value: T | null) {}

  // 创建有值的Maybe
  static of<T>(value: T): Maybe<T> {
    return new Maybe(value);
  }

  // 创建空的Maybe
  static none<T>(): Maybe<T> {
    return new Maybe<T>(null);
  }

  // 从可能为空的值创建Maybe
  static fromNullable<T>(value: T | null | undefined): Maybe<T> {
    return value != null ? Maybe.of(value) : Maybe.none<T>();
  }

  // 单子的flatMap操作（bind）
  flatMap<U>(fn: (value: T) => Maybe<U>): Maybe<U> {
    if (this.value === null) {
      return Maybe.none<U>();
    }
    try {
      return fn(this.value);
    } catch (error) {
      return Maybe.none<U>();
    }
  }

  // 函子的map操作
  map<U>(fn: (value: T) => U): Maybe<U> {
    return this.flatMap(value => Maybe.of(fn(value)));
  }

  // 获取值（如果存在）
  getValue(): T | null {
    return this.value;
  }

  // 检查是否有值
  hasValue(): boolean {
    return this.value !== null;
  }

  // 获取值或默认值
  getOrElse(defaultValue: T): T {
    return this.value !== null ? this.value : defaultValue;
  }

  // 过滤操作
  filter(predicate: (value: T) => boolean): Maybe<T> {
    if (this.value === null || !predicate(this.value)) {
      return Maybe.none<T>();
    }
    return this;
  }

  // 应用操作（Applicative）
  apply<U>(fn: Maybe<(value: T) => U>): Maybe<U> {
    return fn.flatMap(f => this.map(f));
  }

  // 转换为字符串
  toString(): string {
    return this.value !== null ? \`Maybe(\${this.value})\` : 'Maybe.none()';
  }
}

// 3. IO单子 - 处理副作用
class IO<T> implements Monad<T> {
  constructor(private effect: () => T) {}

  // 创建IO单子
  static of<T>(value: T): IO<T> {
    return new IO(() => value);
  }

  // 从副作用函数创建IO
  static from<T>(effect: () => T): IO<T> {
    return new IO(effect);
  }

  // 单子的flatMap操作
  flatMap<U>(fn: (value: T) => IO<U>): IO<U> {
    return new IO(() => fn(this.effect()).run());
  }

  // 函子的map操作
  map<U>(fn: (value: T) => U): IO<U> {
    return this.flatMap(value => IO.of(fn(value)));
  }

  // 执行副作用
  run(): T {
    return this.effect();
  }

  // 转换为字符串
  toString(): string {
    return 'IO(...)';
  }
}

// 4. State单子 - 处理状态计算
class State<S, T> implements Monad<T> {
  constructor(private computation: (state: S) => [T, S]) {}

  // 创建State单子
  static of<S, T>(value: T): State<S, T> {
    return new State(state => [value, state]);
  }

  // 获取当前状态
  static get<S>(): State<S, S> {
    return new State(state => [state, state]);
  }

  // 设置新状态
  static put<S>(newState: S): State<S, void> {
    return new State(() => [undefined as any, newState]);
  }

  // 修改状态
  static modify<S>(fn: (state: S) => S): State<S, void> {
    return new State(state => [undefined as any, fn(state)]);
  }

  // 单子的flatMap操作
  flatMap<U>(fn: (value: T) => State<S, U>): State<S, U> {
    return new State(state => {
      const [value, newState] = this.computation(state);
      return fn(value).computation(newState);
    });
  }

  // 函子的map操作
  map<U>(fn: (value: T) => U): State<S, U> {
    return this.flatMap(value => State.of<S, U>(fn(value)));
  }

  // 运行状态计算
  run(initialState: S): T {
    return this.computation(initialState)[0];
  }

  // 运行状态计算并返回最终状态
  runState(initialState: S): [T, S] {
    return this.computation(initialState);
  }

  // 只获取最终状态
  execState(initialState: S): S {
    return this.computation(initialState)[1];
  }
}

// 5. Either单子 - 处理错误或成功的值
abstract class Either<L, R> implements Monad<R> {
  abstract flatMap<U>(fn: (value: R) => Either<L, U>): Either<L, U>;
  abstract map<U>(fn: (value: R) => U): Either<L, U>;
  abstract isLeft(): boolean;
  abstract isRight(): boolean;
}

class Left<L, R> extends Either<L, R> {
  constructor(private value: L) {
    super();
  }

  flatMap<U>(_fn: (value: R) => Either<L, U>): Either<L, U> {
    return new Left<L, U>(this.value);
  }

  map<U>(_fn: (value: R) => U): Either<L, U> {
    return new Left<L, U>(this.value);
  }

  isLeft(): boolean {
    return true;
  }

  isRight(): boolean {
    return false;
  }

  getValue(): L {
    return this.value;
  }

  toString(): string {
    return \`Left(\${this.value})\`;
  }
}

class Right<L, R> extends Either<L, R> {
  constructor(private value: R) {
    super();
  }

  flatMap<U>(fn: (value: R) => Either<L, U>): Either<L, U> {
    try {
      return fn(this.value);
    } catch (error) {
      return new Left<L, U>(error as L);
    }
  }

  map<U>(fn: (value: R) => U): Either<L, U> {
    return this.flatMap(value => new Right<L, U>(fn(value)));
  }

  isLeft(): boolean {
    return false;
  }

  isRight(): boolean {
    return true;
  }

  getValue(): R {
    return this.value;
  }

  toString(): string {
    return \`Right(\${this.value})\`;
  }
}

// Either工厂函数
const left = <L, R>(value: L): Either<L, R> => new Left(value);
const right = <L, R>(value: R): Either<L, R> => new Right(value);

// 6. List单子 - 处理非确定性计算
class List<T> implements Monad<T> {
  private constructor(private items: T[]) {}

  // 创建List单子
  static of<T>(...items: T[]): List<T> {
    return new List(items);
  }

  // 创建空List
  static empty<T>(): List<T> {
    return new List<T>([]);
  }

  // 从数组创建List
  static fromArray<T>(items: T[]): List<T> {
    return new List([...items]);
  }

  // 单子的flatMap操作
  flatMap<U>(fn: (value: T) => List<U>): List<U> {
    const result: U[] = [];
    for (const item of this.items) {
      result.push(...fn(item).toArray());
    }
    return List.fromArray(result);
  }

  // 函子的map操作
  map<U>(fn: (value: T) => U): List<U> {
    return this.flatMap(value => List.of(fn(value)));
  }

  // 转换为数组
  toArray(): T[] {
    return [...this.items];
  }

  // 获取长度
  length(): number {
    return this.items.length;
  }

  // 检查是否为空
  isEmpty(): boolean {
    return this.items.length === 0;
  }

  // 过滤操作
  filter(predicate: (value: T) => boolean): List<T> {
    return List.fromArray(this.items.filter(predicate));
  }

  // 折叠操作
  fold<U>(initial: U, fn: (acc: U, value: T) => U): U {
    return this.items.reduce(fn, initial);
  }

  // 连接操作
  concat(other: List<T>): List<T> {
    return List.fromArray([...this.items, ...other.toArray()]);
  }

  toString(): string {
    return \`List([\${this.items.join(', ')}])\`;
  }
}

// 7. 单子法则验证工具
class MonadLaws {
  // 验证左单位元法则: unit(a).flatMap(f) === f(a)
  static verifyLeftIdentity<T, U>(
    a: T,
    f: (x: T) => Maybe<U>
  ): boolean {
    const left = unit(a).flatMap(f);
    const right = f(a);
    return JSON.stringify(left) === JSON.stringify(right);
  }

  // 验证右单位元法则: m.flatMap(unit) === m
  static verifyRightIdentity<T>(m: Maybe<T>): boolean {
    const left = m.flatMap(unit);
    const right = m;
    return JSON.stringify(left) === JSON.stringify(right);
  }

  // 验证结合律: m.flatMap(f).flatMap(g) === m.flatMap(x => f(x).flatMap(g))
  static verifyAssociativity<T, U, V>(
    m: Maybe<T>,
    f: (x: T) => Maybe<U>,
    g: (x: U) => Maybe<V>
  ): boolean {
    const left = m.flatMap(f).flatMap(g);
    const right = m.flatMap(x => f(x).flatMap(g));
    return JSON.stringify(left) === JSON.stringify(right);
  }
}

// 8. 单子组合子
class MonadCombinators {
  // 序列操作：将单子数组转换为数组单子
  static sequence<T>(maybes: Maybe<T>[]): Maybe<T[]> {
    return maybes.reduce(
      (acc: Maybe<T[]>, maybe: Maybe<T>) =>
        acc.flatMap(arr => maybe.map(val => [...arr, val])),
      Maybe.of([] as T[])
    );
  }

  // 遍历操作：对数组中的每个元素应用单子函数
  static traverse<T, U>(
    items: T[],
    fn: (item: T) => Maybe<U>
  ): Maybe<U[]> {
    return MonadCombinators.sequence(items.map(fn));
  }

  // 过滤操作：保留满足条件的元素
  static filterM<T>(
    items: T[],
    predicate: (item: T) => Maybe<boolean>
  ): Maybe<T[]> {
    return items.reduce(
      (acc: Maybe<T[]>, item: T) =>
        acc.flatMap(arr =>
          predicate(item).flatMap(keep =>
            Maybe.of(keep ? [...arr, item] : arr)
          )
        ),
      Maybe.of([] as T[])
    );
  }

  // 折叠操作：从左到右折叠
  static foldM<T, U>(
    items: T[],
    initial: U,
    fn: (acc: U, item: T) => Maybe<U>
  ): Maybe<U> {
    return items.reduce(
      (acc: Maybe<U>, item: T) => acc.flatMap(a => fn(a, item)),
      Maybe.of(initial)
    );
  }
}

// 9. 实用工具函数
const liftM2 = <A, B, C>(
  fn: (a: A, b: B) => C
) => (ma: Maybe<A>, mb: Maybe<B>): Maybe<C> => {
  return ma.flatMap(a => mb.map(b => fn(a, b)));
};

const liftM3 = <A, B, C, D>(
  fn: (a: A, b: B, c: C) => D
) => (ma: Maybe<A>, mb: Maybe<B>, mc: Maybe<C>): Maybe<D> => {
  return ma.flatMap(a =>
    mb.flatMap(b =>
      mc.map(c => fn(a, b, c))
    )
  );
};

// 条件执行
const when = <T>(
  condition: boolean,
  action: () => Maybe<T>
): Maybe<void> => {
  return condition ? action().map(() => undefined) : Maybe.of(undefined);
};

const unless = <T>(
  condition: boolean,
  action: () => Maybe<T>
): Maybe<void> => {
  return when(!condition, action);
};

// 重复执行
const replicateM = <T>(
  n: number,
  action: () => Maybe<T>
): Maybe<T[]> => {
  const actions = Array(n).fill(null).map(() => action());
  return MonadCombinators.sequence(actions);
};

// 10. 使用示例演示

console.log('=== 单子模式演示 ===');

// Maybe单子演示
console.log('\\n--- Maybe单子演示 ---');

// 安全的数学运算链
const safeMathChain = (input: string): Maybe<number> => {
  return Maybe.fromNullable(input)
    .flatMap(str => {
      const num = parseFloat(str);
      return isNaN(num) ? Maybe.none<number>() : Maybe.of(num);
    })
    .flatMap(num => num >= 0 ? Maybe.of(num) : Maybe.none<number>())
    .map(num => Math.sqrt(num))
    .map(num => num * 2)
    .filter(num => num < 100);
};

console.log('安全数学运算:');
console.log('输入 "16":', safeMathChain("16").toString()); // Maybe(8)
console.log('输入 "-4":', safeMathChain("-4").toString()); // Maybe.none()
console.log('输入 "abc":', safeMathChain("abc").toString()); // Maybe.none()

// 用户数据验证
const validateUser = (userData: any): Maybe<any> => {
  return Maybe.fromNullable(userData)
    .flatMap(user => user.name ? Maybe.of(user) : Maybe.none())
    .flatMap(user => user.name.length >= 3 ? Maybe.of(user) : Maybe.none())
    .flatMap(user => {
      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
      return emailRegex.test(user.email) ? Maybe.of(user) : Maybe.none();
    })
    .flatMap(user => user.age >= 18 && user.age <= 100 ? Maybe.of(user) : Maybe.none());
};

const validUser = { name: 'Alice', email: '<EMAIL>', age: 25 };
const invalidUser = { name: 'Bo', email: 'invalid-email', age: 15 };

console.log('\\n用户验证:');
console.log('有效用户:', validateUser(validUser).hasValue()); // true
console.log('无效用户:', validateUser(invalidUser).hasValue()); // false

// IO单子演示
console.log('\\n--- IO单子演示 ---');

// 文件操作链
const fileOperationChain = (filename: string, content: string): IO<string> => {
  return IO.of(content)
    .flatMap(data => IO.from(() => {
      console.log(\`写入文件 \${filename}: \${data}\`);
      return \`文件已写入: \${data}\`;
    }))
    .flatMap(writeResult => IO.from(() => {
      console.log(\`读取文件 \${filename}\`);
      return content; // 模拟读取
    }))
    .flatMap(readData => IO.from(() => {
      console.log(\`处理数据: \${readData}\`);
      return \`处理结果: \${readData.toUpperCase()}\`;
    }));
};

console.log('文件操作链:');
const fileResult = fileOperationChain('example.txt', 'Hello, World!').run();
console.log('最终结果:', fileResult);

// State单子演示
console.log('\\n--- State单子演示 ---');

// 计数器状态管理
const counterOperations = State.get<number>()
  .flatMap(count => {
    console.log(\`当前计数: \${count}\`);
    return State.put(count + 1);
  })
  .flatMap(() => State.get<number>())
  .flatMap(count => {
    console.log(\`递增后: \${count}\`);
    return State.put(count * 2);
  })
  .flatMap(() => State.get<number>())
  .map(count => {
    console.log(\`乘2后: \${count}\`);
    return \`最终计数: \${count}\`;
  });

const [result, finalState] = counterOperations.runState(0);
console.log('状态计算结果:', result);
console.log('最终状态:', finalState);

// Either单子演示
console.log('\\n--- Either单子演示 ---');

// 错误处理链
const errorHandlingChain = (input: number): Either<string, number> => {
  return right<string, number>(input)
    .flatMap(num => num > 0 ? right(num) : left('输入必须为正数'))
    .flatMap(num => num < 100 ? right(num) : left('输入不能超过100'))
    .map(num => num * 2)
    .flatMap(num => num % 2 === 0 ? right(num) : left('结果必须为偶数'));
};

console.log('错误处理链:');
console.log('输入 10:', errorHandlingChain(10).toString()); // Right(20)
console.log('输入 -5:', errorHandlingChain(-5).toString()); // Left(输入必须为正数)
console.log('输入 150:', errorHandlingChain(150).toString()); // Left(输入不能超过100)

// List单子演示
console.log('\\n--- List单子演示 ---');

// 非确定性计算
const nonDeterministicComputation = List.of(1, 2, 3)
  .flatMap(x => List.of(x, x * 10))
  .flatMap(x => List.of(x + 1, x + 2))
  .filter(x => x > 5);

console.log('非确定性计算结果:', nonDeterministicComputation.toString());

// 单子法则验证
console.log('\\n--- 单子法则验证 ---');

const a = 42;
const f = (x: number) => Maybe.of(x * 2);
const g = (x: number) => Maybe.of(x + 1);
const m = Maybe.of(5);

console.log('左单位元法则:', MonadLaws.verifyLeftIdentity(a, f)); // true
console.log('右单位元法则:', MonadLaws.verifyRightIdentity(m)); // true
console.log('结合律:', MonadLaws.verifyAssociativity(m, f, g)); // true

// 组合子演示
console.log('\\n--- 单子组合子演示 ---');

const maybeNumbers = [Maybe.of(1), Maybe.of(2), Maybe.of(3)];
const sequenceResult = MonadCombinators.sequence(maybeNumbers);
console.log('序列操作:', sequenceResult.toString()); // Maybe([1, 2, 3])

const numbers = [1, 2, 3, 4, 5];
const traverseResult = MonadCombinators.traverse(numbers, x =>
  x % 2 === 0 ? Maybe.of(x * 2) : Maybe.none<number>()
);
console.log('遍历操作:', traverseResult.toString()); // Maybe.none() (因为有奇数)

const evenTraverseResult = MonadCombinators.traverse([2, 4, 6], x => Maybe.of(x * 2));
console.log('偶数遍历:', evenTraverseResult.toString()); // Maybe([4, 8, 12])

// 实际应用场景
console.log('\\n--- 实际应用场景 ---');

// 异步操作管道
class AsyncPipeline {
  static process(data: string): IO<string> {
    return IO.of(data)
      .flatMap(input => IO.from(() => {
        console.log(\`步骤1: 验证输入 "\${input}"\`);
        return input.trim();
      }))
      .flatMap(cleaned => IO.from(() => {
        console.log(\`步骤2: 清理数据 "\${cleaned}"\`);
        return cleaned.toLowerCase();
      }))
      .flatMap(normalized => IO.from(() => {
        console.log(\`步骤3: 标准化 "\${normalized}"\`);
        return \`processed_\${normalized}\`;
      }))
      .flatMap(processed => IO.from(() => {
        console.log(\`步骤4: 保存结果 "\${processed}"\`);
        return \`saved_\${processed}\`;
      }));
  }
}

console.log('异步处理管道:');
const pipelineResult = AsyncPipeline.process('  Hello World  ').run();
console.log('管道结果:', pipelineResult);

console.log('\\n=== 单子模式的优势 ===');
console.log('1. 提供统一的接口处理复杂的计算上下文');
console.log('2. 支持安全的链式操作，避免嵌套回调');
console.log('3. 优雅地处理副作用和异步操作');
console.log('4. 遵循数学法则，行为可预测');
console.log('5. 提高代码的可组合性和可重用性');
console.log('6. 强大的错误处理和异常传播机制');

export {
  Maybe, IO, State, Either, Left, Right, List,
  unit, left, right, MonadLaws, MonadCombinators,
  liftM2, liftM3, when, unless, replicateM
};`,
  },

  'mvc': {
    title: 'MVC模式 - 用户管理系统',
    code: `// MVC模式实现
// Model - 数据模型
interface User {
  id: number;
  name: string;
  email: string;
  age: number;
}

class UserModel {
  private users: User[] = [
    { id: 1, name: 'Alice', email: '<EMAIL>', age: 25 },
    { id: 2, name: 'Bob', email: '<EMAIL>', age: 30 },
    { id: 3, name: 'Charlie', email: '<EMAIL>', age: 35 }
  ];
  private nextId = 4;

  // 获取所有用户
  getAllUsers(): User[] {
    return [...this.users];
  }

  // 根据ID获取用户
  getUserById(id: number): User | null {
    return this.users.find(user => user.id === id) || null;
  }

  // 添加用户
  addUser(userData: Omit<User, 'id'>): User {
    const newUser: User = {
      id: this.nextId++,
      ...userData
    };
    this.users.push(newUser);
    return newUser;
  }

  // 更新用户
  updateUser(id: number, userData: Partial<Omit<User, 'id'>>): User | null {
    const userIndex = this.users.findIndex(user => user.id === id);
    if (userIndex === -1) return null;

    this.users[userIndex] = { ...this.users[userIndex], ...userData };
    return this.users[userIndex];
  }

  // 删除用户
  deleteUser(id: number): boolean {
    const userIndex = this.users.findIndex(user => user.id === id);
    if (userIndex === -1) return false;

    this.users.splice(userIndex, 1);
    return true;
  }

  // 搜索用户
  searchUsers(query: string): User[] {
    const lowerQuery = query.toLowerCase();
    return this.users.filter(user =>
      user.name.toLowerCase().includes(lowerQuery) ||
      user.email.toLowerCase().includes(lowerQuery)
    );
  }
}

// View - 视图层
class UserView {
  private container: HTMLElement;

  constructor(containerId: string) {
    this.container = document.getElementById(containerId) || document.body;
  }

  // 渲染用户列表
  renderUserList(users: User[]): void {
    const html = \`
      <div class="user-list">
        <h2>用户列表</h2>
        <div class="users">
          \${users.map(user => \`
            <div class="user-card" data-user-id="\${user.id}">
              <h3>\${user.name}</h3>
              <p>邮箱: \${user.email}</p>
              <p>年龄: \${user.age}</p>
              <div class="user-actions">
                <button class="edit-btn" data-user-id="\${user.id}">编辑</button>
                <button class="delete-btn" data-user-id="\${user.id}">删除</button>
              </div>
            </div>
          \`).join('')}
        </div>
      </div>
    \`;
    this.container.innerHTML = html;
  }

  // 渲染用户表单
  renderUserForm(user?: User): void {
    const isEdit = !!user;
    const html = \`
      <div class="user-form">
        <h2>\${isEdit ? '编辑用户' : '添加用户'}</h2>
        <form id="userForm">
          <div class="form-group">
            <label for="name">姓名:</label>
            <input type="text" id="name" name="name" value="\${user?.name || ''}" required>
          </div>
          <div class="form-group">
            <label for="email">邮箱:</label>
            <input type="email" id="email" name="email" value="\${user?.email || ''}" required>
          </div>
          <div class="form-group">
            <label for="age">年龄:</label>
            <input type="number" id="age" name="age" value="\${user?.age || ''}" required>
          </div>
          <div class="form-actions">
            <button type="submit">\${isEdit ? '更新' : '添加'}</button>
            <button type="button" id="cancelBtn">取消</button>
          </div>
        </form>
      </div>
    \`;
    this.container.innerHTML = html;
  }

  // 渲染搜索界面
  renderSearchInterface(): void {
    const searchHtml = \`
      <div class="search-container">
        <input type="text" id="searchInput" placeholder="搜索用户...">
        <button id="searchBtn">搜索</button>
        <button id="addUserBtn">添加用户</button>
      </div>
    \`;
    this.container.insertAdjacentHTML('afterbegin', searchHtml);
  }

  // 显示消息
  showMessage(message: string, type: 'success' | 'error' = 'success'): void {
    const messageDiv = document.createElement('div');
    messageDiv.className = \`message \${type}\`;
    messageDiv.textContent = message;

    this.container.insertAdjacentElement('afterbegin', messageDiv);

    setTimeout(() => {
      messageDiv.remove();
    }, 3000);
  }

  // 获取表单数据
  getFormData(): Omit<User, 'id'> | null {
    const form = document.getElementById('userForm') as HTMLFormElement;
    if (!form) return null;

    const formData = new FormData(form);
    return {
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      age: parseInt(formData.get('age') as string)
    };
  }
}

// Controller - 控制器
class UserController {
  private model: UserModel;
  private view: UserView;
  private currentEditingUserId: number | null = null;

  constructor(model: UserModel, view: UserView) {
    this.model = model;
    this.view = view;
    this.init();
  }

  private init(): void {
    this.showUserList();
    this.bindEvents();
  }

  // 绑定事件
  private bindEvents(): void {
    // 使用事件委托处理动态生成的元素
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;

      if (target.classList.contains('edit-btn')) {
        const userId = parseInt(target.dataset.userId || '0');
        this.editUser(userId);
      } else if (target.classList.contains('delete-btn')) {
        const userId = parseInt(target.dataset.userId || '0');
        this.deleteUser(userId);
      } else if (target.id === 'addUserBtn') {
        this.showAddUserForm();
      } else if (target.id === 'cancelBtn') {
        this.showUserList();
      } else if (target.id === 'searchBtn') {
        this.searchUsers();
      }
    });

    // 表单提交事件
    document.addEventListener('submit', (e) => {
      if ((e.target as HTMLElement).id === 'userForm') {
        e.preventDefault();
        this.handleFormSubmit();
      }
    });

    // 搜索输入事件
    document.addEventListener('input', (e) => {
      if ((e.target as HTMLElement).id === 'searchInput') {
        this.searchUsers();
      }
    });
  }

  // 显示用户列表
  showUserList(): void {
    const users = this.model.getAllUsers();
    this.view.renderSearchInterface();
    this.view.renderUserList(users);
  }

  // 显示添加用户表单
  showAddUserForm(): void {
    this.currentEditingUserId = null;
    this.view.renderUserForm();
  }

  // 编辑用户
  editUser(userId: number): void {
    const user = this.model.getUserById(userId);
    if (user) {
      this.currentEditingUserId = userId;
      this.view.renderUserForm(user);
    }
  }

  // 删除用户
  deleteUser(userId: number): void {
    if (confirm('确定要删除这个用户吗？')) {
      const success = this.model.deleteUser(userId);
      if (success) {
        this.view.showMessage('用户删除成功');
        this.showUserList();
      } else {
        this.view.showMessage('删除失败', 'error');
      }
    }
  }

  // 搜索用户
  searchUsers(): void {
    const searchInput = document.getElementById('searchInput') as HTMLInputElement;
    const query = searchInput?.value || '';

    const users = query ? this.model.searchUsers(query) : this.model.getAllUsers();
    this.view.renderUserList(users);
  }

  // 处理表单提交
  handleFormSubmit(): void {
    const formData = this.view.getFormData();
    if (!formData) return;

    try {
      if (this.currentEditingUserId) {
        // 更新用户
        const updatedUser = this.model.updateUser(this.currentEditingUserId, formData);
        if (updatedUser) {
          this.view.showMessage('用户更新成功');
          this.showUserList();
        } else {
          this.view.showMessage('更新失败', 'error');
        }
      } else {
        // 添加用户
        const newUser = this.model.addUser(formData);
        this.view.showMessage(\`用户 \${newUser.name} 添加成功\`);
        this.showUserList();
      }
    } catch (error) {
      this.view.showMessage('操作失败', 'error');
    }
  }
}

// 使用示例
class UserManagementApp {
  private controller: UserController;

  constructor(containerId: string) {
    const model = new UserModel();
    const view = new UserView(containerId);
    this.controller = new UserController(model, view);
  }

  // 启动应用
  start(): void {
    console.log('用户管理系统启动成功');
  }
}

// 启动应用
// const app = new UserManagementApp('app-container');
// app.start();

export { UserManagementApp, UserModel, UserView, UserController };`
  },

  'mvp': {
    title: 'MVP模式 - 被动视图架构',
    code: `// MVP模式实现
// Model-View-Presenter 模式，View完全被动，所有UI逻辑由Presenter处理

// 1. Model层 - 数据模型
interface UserModel {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'user' | 'guest';
  isActive: boolean;
  lastLogin?: Date;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

class UserRepository {
  private users: UserModel[] = [
    { id: 1, name: 'Alice', email: '<EMAIL>', role: 'admin', isActive: true, lastLogin: new Date() },
    { id: 2, name: 'Bob', email: '<EMAIL>', role: 'user', isActive: true },
    { id: 3, name: 'Charlie', email: '<EMAIL>', role: 'guest', isActive: false }
  ];

  async getAllUsers(): Promise<UserModel[]> {
    return new Promise(resolve => {
      setTimeout(() => resolve([...this.users]), 100);
    });
  }

  async getUserById(id: number): Promise<UserModel | null> {
    return new Promise(resolve => {
      setTimeout(() => {
        const user = this.users.find(u => u.id === id) || null;
        resolve(user);
      }, 100);
    });
  }

  async createUser(user: Omit<UserModel, 'id'>): Promise<UserModel> {
    return new Promise(resolve => {
      setTimeout(() => {
        const newUser: UserModel = {
          ...user,
          id: Math.max(...this.users.map(u => u.id)) + 1
        };
        this.users.push(newUser);
        resolve(newUser);
      }, 200);
    });
  }

  async updateUser(id: number, updates: Partial<UserModel>): Promise<UserModel | null> {
    return new Promise(resolve => {
      setTimeout(() => {
        const index = this.users.findIndex(u => u.id === id);
        if (index === -1) {
          resolve(null);
          return;
        }
        this.users[index] = { ...this.users[index], ...updates };
        resolve(this.users[index]);
      }, 200);
    });
  }

  async deleteUser(id: number): Promise<boolean> {
    return new Promise(resolve => {
      setTimeout(() => {
        const index = this.users.findIndex(u => u.id === id);
        if (index === -1) {
          resolve(false);
          return;
        }
        this.users.splice(index, 1);
        resolve(true);
      }, 200);
    });
  }
}

// 2. View接口 - 完全被动的视图
interface UserView {
  displayUsers(users: UserModel[]): void;
  displayUser(user: UserModel): void;
  displayError(message: string): void;
  displaySuccess(message: string): void;
  displayLoading(isLoading: boolean): void;
  clearUserList(): void;
  clearUserForm(): void;
  clearMessages(): void;
  getUserFormData(): Partial<UserModel>;
  getSelectedUserId(): number | null;
  setFormEnabled(enabled: boolean): void;
  setDeleteButtonEnabled(enabled: boolean): void;
  setEditMode(isEdit: boolean): void;
}

// 3. Presenter层 - 处理所有UI逻辑
class UserPresenter {
  private view: UserView;
  private repository: UserRepository;
  private currentUser: UserModel | null = null;
  private isEditMode: boolean = false;

  constructor(view: UserView, repository: UserRepository) {
    this.view = view;
    this.repository = repository;
  }

  async initialize(): Promise<void> {
    try {
      this.view.displayLoading(true);
      await this.loadUsers();
      this.view.displayLoading(false);
    } catch (error) {
      this.view.displayLoading(false);
      this.view.displayError('初始化失败: ' + error);
    }
  }

  async loadUsers(): Promise<void> {
    try {
      const users = await this.repository.getAllUsers();
      this.view.displayUsers(users);
      this.view.clearMessages();
    } catch (error) {
      this.view.displayError('加载用户失败: ' + error);
    }
  }

  async selectUser(userId: number): Promise<void> {
    try {
      this.view.displayLoading(true);
      const user = await this.repository.getUserById(userId);
      this.view.displayLoading(false);

      if (user) {
        this.currentUser = user;
        this.view.displayUser(user);
        this.view.setDeleteButtonEnabled(true);
        this.view.setEditMode(false);
        this.isEditMode = false;
      } else {
        this.view.displayError('用户不存在');
      }
    } catch (error) {
      this.view.displayLoading(false);
      this.view.displayError('获取用户失败: ' + error);
    }
  }

  async createUser(): Promise<void> {
    try {
      const formData = this.view.getUserFormData();
      const validation = this.validateUserData(formData);

      if (!validation.isValid) {
        this.view.displayError('验证失败: ' + validation.errors.join(', '));
        return;
      }

      this.view.displayLoading(true);
      this.view.setFormEnabled(false);

      const newUser = await this.repository.createUser(formData as Omit<UserModel, 'id'>);

      this.view.displayLoading(false);
      this.view.setFormEnabled(true);
      this.view.displaySuccess('用户创建成功');
      this.view.clearUserForm();

      await this.loadUsers();
    } catch (error) {
      this.view.displayLoading(false);
      this.view.setFormEnabled(true);
      this.view.displayError('创建用户失败: ' + error);
    }
  }

  private validateUserData(data: Partial<UserModel>): ValidationResult {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push('姓名不能为空');
    }

    if (!data.email || !this.isValidEmail(data.email)) {
      errors.push('邮箱格式不正确');
    }

    if (!data.role || !['admin', 'user', 'guest'].includes(data.role)) {
      errors.push('角色必须是admin、user或guest');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  private isValidEmail(email: string): boolean {
    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);
  }
}

console.log('=== MVP模式演示 ===');
console.log('1. View完全被动，易于单元测试');
console.log('2. Presenter包含所有UI逻辑，便于测试');
console.log('3. View和Model完全解耦');

export { UserModel, UserView, UserPresenter, UserRepository };`
  },

  'microservices': {
    title: '微服务模式 - 分布式架构',
    code: `// 微服务模式实现
// 将单体应用拆分为独立的小服务，每个服务负责特定的业务功能

// 1. 服务注册与发现
interface ServiceInfo {
  id: string;
  name: string;
  host: string;
  port: number;
  health: 'healthy' | 'unhealthy' | 'unknown';
  metadata: Record<string, any>;
  lastHeartbeat: Date;
}

class ServiceRegistry {
  private services: Map<string, ServiceInfo> = new Map();
  private healthCheckInterval: number = 30000; // 30秒

  constructor() {
    this.startHealthCheck();
  }

  // 注册服务
  register(service: Omit<ServiceInfo, 'lastHeartbeat'>): void {
    const serviceInfo: ServiceInfo = {
      ...service,
      lastHeartbeat: new Date()
    };
    this.services.set(service.id, serviceInfo);
    console.log(\`服务注册成功: \${service.name} (\${service.id})\`);
  }

  // 注销服务
  deregister(serviceId: string): void {
    if (this.services.delete(serviceId)) {
      console.log(\`服务注销成功: \${serviceId}\`);
    }
  }

  // 发现服务
  discover(serviceName: string): ServiceInfo[] {
    return Array.from(this.services.values())
      .filter(service => service.name === serviceName && service.health === 'healthy');
  }

  // 获取所有服务
  getAllServices(): ServiceInfo[] {
    return Array.from(this.services.values());
  }

  // 心跳检测
  heartbeat(serviceId: string): void {
    const service = this.services.get(serviceId);
    if (service) {
      service.lastHeartbeat = new Date();
      service.health = 'healthy';
    }
  }

  // 健康检查
  private startHealthCheck(): void {
    setInterval(() => {
      const now = new Date();
      this.services.forEach((service, id) => {
        const timeSinceLastHeartbeat = now.getTime() - service.lastHeartbeat.getTime();
        if (timeSinceLastHeartbeat > this.healthCheckInterval) {
          service.health = 'unhealthy';
          console.log(\`服务健康检查失败: \${service.name} (\${id})\`);
        }
      });
    }, this.healthCheckInterval);
  }
}

// 2. API网关
interface Route {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  serviceName: string;
  targetPath?: string;
  middleware?: string[];
}

class APIGateway {
  private routes: Route[] = [];
  private serviceRegistry: ServiceRegistry;
  private loadBalancer: LoadBalancer;

  constructor(serviceRegistry: ServiceRegistry) {
    this.serviceRegistry = serviceRegistry;
    this.loadBalancer = new LoadBalancer();
  }

  // 添加路由
  addRoute(route: Route): void {
    this.routes.push(route);
    console.log(\`路由添加成功: \${route.method} \${route.path} -> \${route.serviceName}\`);
  }

  // 处理请求
  async handleRequest(method: string, path: string, body?: any): Promise<any> {
    const route = this.findRoute(method as any, path);
    if (!route) {
      throw new Error(\`路由未找到: \${method} \${path}\`);
    }

    // 服务发现
    const services = this.serviceRegistry.discover(route.serviceName);
    if (services.length === 0) {
      throw new Error(\`服务不可用: \${route.serviceName}\`);
    }

    // 负载均衡
    const targetService = this.loadBalancer.selectService(services);
    const targetPath = route.targetPath || path;

    console.log(\`请求转发: \${method} \${path} -> \${targetService.host}:\${targetService.port}\${targetPath}\`);

    // 模拟HTTP请求
    return this.makeRequest(targetService, method, targetPath, body);
  }

  private findRoute(method: string, path: string): Route | undefined {
    return this.routes.find(route =>
      route.method === method && this.matchPath(route.path, path)
    );
  }

  private matchPath(routePath: string, requestPath: string): boolean {
    // 简单的路径匹配，实际应用中会更复杂
    const routeSegments = routePath.split('/');
    const requestSegments = requestPath.split('/');

    if (routeSegments.length !== requestSegments.length) {
      return false;
    }

    return routeSegments.every((segment, index) => {
      return segment.startsWith(':') || segment === requestSegments[index];
    });
  }

  private async makeRequest(service: ServiceInfo, method: string, path: string, body?: any): Promise<any> {
    // 模拟HTTP请求
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          status: 200,
          data: { message: \`响应来自 \${service.name}\`, method, path, body },
          service: service.name
        });
      }, Math.random() * 100 + 50);
    });
  }
}

// 3. 负载均衡器
class LoadBalancer {
  private currentIndex: number = 0;

  // 轮询算法
  selectService(services: ServiceInfo[]): ServiceInfo {
    if (services.length === 0) {
      throw new Error('没有可用的服务');
    }

    const service = services[this.currentIndex % services.length];
    this.currentIndex++;
    return service;
  }

  // 随机算法
  selectServiceRandom(services: ServiceInfo[]): ServiceInfo {
    if (services.length === 0) {
      throw new Error('没有可用的服务');
    }

    const randomIndex = Math.floor(Math.random() * services.length);
    return services[randomIndex];
  }
}

// 4. 微服务基类
abstract class MicroService {
  protected serviceInfo: Omit<ServiceInfo, 'lastHeartbeat'>;
  protected serviceRegistry: ServiceRegistry;
  private heartbeatInterval?: NodeJS.Timeout;

  constructor(serviceInfo: Omit<ServiceInfo, 'lastHeartbeat'>, serviceRegistry: ServiceRegistry) {
    this.serviceInfo = serviceInfo;
    this.serviceRegistry = serviceRegistry;
  }

  // 启动服务
  start(): void {
    this.serviceRegistry.register(this.serviceInfo);
    this.startHeartbeat();
    this.initialize();
    console.log(\`微服务启动: \${this.serviceInfo.name}\`);
  }

  // 停止服务
  stop(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    this.serviceRegistry.deregister(this.serviceInfo.id);
    this.cleanup();
    console.log(\`微服务停止: \${this.serviceInfo.name}\`);
  }

  // 心跳
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.serviceRegistry.heartbeat(this.serviceInfo.id);
    }, 15000); // 15秒发送一次心跳
  }

  // 子类需要实现的方法
  protected abstract initialize(): void;
  protected abstract cleanup(): void;
}

// 5. 具体的微服务实现
class UserService extends MicroService {
  private users: any[] = [
    { id: 1, name: 'Alice', email: '<EMAIL>' },
    { id: 2, name: 'Bob', email: '<EMAIL>' }
  ];

  constructor(serviceRegistry: ServiceRegistry) {
    super({
      id: 'user-service-1',
      name: 'user-service',
      host: 'localhost',
      port: 3001,
      health: 'healthy',
      metadata: { version: '1.0.0', database: 'users_db' }
    }, serviceRegistry);
  }

  protected initialize(): void {
    console.log('用户服务初始化完成');
  }

  protected cleanup(): void {
    console.log('用户服务清理完成');
  }

  // 业务方法
  getUsers(): any[] {
    return this.users;
  }

  getUserById(id: number): any | null {
    return this.users.find(user => user.id === id) || null;
  }

  createUser(userData: any): any {
    const newUser = { id: Date.now(), ...userData };
    this.users.push(newUser);
    return newUser;
  }
}

class OrderService extends MicroService {
  private orders: any[] = [
    { id: 1, userId: 1, product: 'Laptop', amount: 1200 },
    { id: 2, userId: 2, product: 'Phone', amount: 800 }
  ];

  constructor(serviceRegistry: ServiceRegistry) {
    super({
      id: 'order-service-1',
      name: 'order-service',
      host: 'localhost',
      port: 3002,
      health: 'healthy',
      metadata: { version: '1.0.0', database: 'orders_db' }
    }, serviceRegistry);
  }

  protected initialize(): void {
    console.log('订单服务初始化完成');
  }

  protected cleanup(): void {
    console.log('订单服务清理完成');
  }

  // 业务方法
  getOrders(): any[] {
    return this.orders;
  }

  getOrdersByUserId(userId: number): any[] {
    return this.orders.filter(order => order.userId === userId);
  }

  createOrder(orderData: any): any {
    const newOrder = { id: Date.now(), ...orderData };
    this.orders.push(newOrder);
    return newOrder;
  }
}

// 6. 使用示例
console.log('=== 微服务模式演示 ===');

// 创建服务注册中心
const serviceRegistry = new ServiceRegistry();

// 创建API网关
const apiGateway = new APIGateway(serviceRegistry);

// 配置路由
apiGateway.addRoute({
  path: '/api/users',
  method: 'GET',
  serviceName: 'user-service'
});

apiGateway.addRoute({
  path: '/api/users/:id',
  method: 'GET',
  serviceName: 'user-service'
});

apiGateway.addRoute({
  path: '/api/orders',
  method: 'GET',
  serviceName: 'order-service'
});

// 启动微服务
const userService = new UserService(serviceRegistry);
const orderService = new OrderService(serviceRegistry);

userService.start();
orderService.start();

// 模拟请求处理
async function demonstrateMicroservices() {
  console.log('\\n--- 处理用户请求 ---');
  try {
    const usersResponse = await apiGateway.handleRequest('GET', '/api/users');
    console.log('用户列表响应:', usersResponse);
  } catch (error) {
    console.error('请求失败:', error);
  }

  console.log('\\n--- 处理订单请求 ---');
  try {
    const ordersResponse = await apiGateway.handleRequest('GET', '/api/orders');
    console.log('订单列表响应:', ordersResponse);
  } catch (error) {
    console.error('请求失败:', error);
  }

  console.log('\\n--- 服务状态 ---');
  const allServices = serviceRegistry.getAllServices();
  allServices.forEach(service => {
    console.log(\`服务: \${service.name}, 状态: \${service.health}, 地址: \${service.host}:\${service.port}\`);
  });
}

// 运行演示
demonstrateMicroservices();

console.log('\\n=== 微服务模式优势 ===');
console.log('1. 服务独立部署和扩展');
console.log('2. 技术栈多样性');
console.log('3. 故障隔离');
console.log('4. 团队独立开发');
console.log('5. 更好的可维护性');

export {
  ServiceRegistry, APIGateway, LoadBalancer, MicroService,
  UserService, OrderService, ServiceInfo, Route
};`
  },

  'event-sourcing': {
    title: '事件溯源模式 - 事件驱动的状态管理',
    code: `// 事件溯源模式实现
// 通过存储事件序列而不是当前状态来管理数据

// 1. 事件接口和基类
interface DomainEvent {
  id: string;
  aggregateId: string;
  eventType: string;
  eventData: any;
  timestamp: Date;
  version: number;
  metadata?: Record<string, any>;
}

abstract class Event implements DomainEvent {
  public readonly id: string;
  public readonly aggregateId: string;
  public readonly eventType: string;
  public readonly timestamp: Date;
  public readonly version: number;
  public readonly metadata?: Record<string, any>;

  constructor(
    aggregateId: string,
    eventData: any,
    version: number,
    metadata?: Record<string, any>
  ) {
    this.id = this.generateId();
    this.aggregateId = aggregateId;
    this.eventType = this.constructor.name;
    this.eventData = eventData;
    this.timestamp = new Date();
    this.version = version;
    this.metadata = metadata;
  }

  abstract get eventData(): any;

  private generateId(): string {
    return \`\${Date.now()}-\${Math.random().toString(36).substr(2, 9)}\`;
  }
}

// 2. 具体事件类型
class UserCreatedEvent extends Event {
  constructor(aggregateId: string, userData: { name: string; email: string; role: string }, version: number) {
    super(aggregateId, userData, version);
  }

  get eventData() {
    return this._eventData;
  }

  private _eventData: { name: string; email: string; role: string };

  constructor(aggregateId: string, userData: { name: string; email: string; role: string }, version: number) {
    super(aggregateId, userData, version);
    this._eventData = userData;
  }
}

class UserEmailChangedEvent extends Event {
  constructor(aggregateId: string, emailData: { oldEmail: string; newEmail: string }, version: number) {
    super(aggregateId, emailData, version);
  }

  get eventData() {
    return this._eventData;
  }

  private _eventData: { oldEmail: string; newEmail: string };

  constructor(aggregateId: string, emailData: { oldEmail: string; newEmail: string }, version: number) {
    super(aggregateId, emailData, version);
    this._eventData = emailData;
  }
}

class UserDeactivatedEvent extends Event {
  constructor(aggregateId: string, deactivationData: { reason: string; deactivatedBy: string }, version: number) {
    super(aggregateId, deactivationData, version);
  }

  get eventData() {
    return this._eventData;
  }

  private _eventData: { reason: string; deactivatedBy: string };

  constructor(aggregateId: string, deactivationData: { reason: string; deactivatedBy: string }, version: number) {
    super(aggregateId, deactivationData, version);
    this._eventData = deactivationData;
  }
}

// 3. 事件存储
interface EventStore {
  saveEvents(aggregateId: string, events: DomainEvent[], expectedVersion: number): Promise<void>;
  getEvents(aggregateId: string, fromVersion?: number): Promise<DomainEvent[]>;
  getAllEvents(): Promise<DomainEvent[]>;
  getEventsByType(eventType: string): Promise<DomainEvent[]>;
}

class InMemoryEventStore implements EventStore {
  private events: Map<string, DomainEvent[]> = new Map();
  private allEvents: DomainEvent[] = [];

  async saveEvents(aggregateId: string, events: DomainEvent[], expectedVersion: number): Promise<void> {
    const existingEvents = this.events.get(aggregateId) || [];

    // 检查版本冲突
    const currentVersion = existingEvents.length;
    if (currentVersion !== expectedVersion) {
      throw new Error(\`版本冲突: 期望版本 \${expectedVersion}, 当前版本 \${currentVersion}\`);
    }

    // 保存事件
    const updatedEvents = [...existingEvents, ...events];
    this.events.set(aggregateId, updatedEvents);
    this.allEvents.push(...events);

    console.log(\`保存了 \${events.length} 个事件到聚合 \${aggregateId}\`);
  }

  async getEvents(aggregateId: string, fromVersion: number = 0): Promise<DomainEvent[]> {
    const events = this.events.get(aggregateId) || [];
    return events.slice(fromVersion);
  }

  async getAllEvents(): Promise<DomainEvent[]> {
    return [...this.allEvents].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  async getEventsByType(eventType: string): Promise<DomainEvent[]> {
    return this.allEvents.filter(event => event.eventType === eventType);
  }
}

// 4. 聚合根
abstract class AggregateRoot {
  protected id: string;
  protected version: number = 0;
  private uncommittedEvents: DomainEvent[] = [];

  constructor(id: string) {
    this.id = id;
  }

  // 应用事件到聚合
  protected applyEvent(event: DomainEvent): void {
    this.applyChange(event);
    this.uncommittedEvents.push(event);
  }

  // 从历史事件重建聚合
  public loadFromHistory(events: DomainEvent[]): void {
    events.forEach(event => {
      this.applyChange(event);
      this.version = event.version;
    });
  }

  // 获取未提交的事件
  public getUncommittedEvents(): DomainEvent[] {
    return [...this.uncommittedEvents];
  }

  // 标记事件为已提交
  public markEventsAsCommitted(): void {
    this.uncommittedEvents = [];
  }

  // 子类需要实现的方法
  protected abstract applyChange(event: DomainEvent): void;

  public getId(): string {
    return this.id;
  }

  public getVersion(): number {
    return this.version;
  }
}

// 5. 用户聚合
interface UserState {
  id: string;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  createdAt?: Date;
  deactivatedAt?: Date;
  deactivationReason?: string;
}

class User extends AggregateRoot {
  private state: UserState;

  constructor(id: string) {
    super(id);
    this.state = {
      id,
      name: '',
      email: '',
      role: '',
      isActive: false
    };
  }

  // 创建用户
  public static create(id: string, name: string, email: string, role: string): User {
    const user = new User(id);
    const event = new UserCreatedEvent(id, { name, email, role }, 1);
    user.applyEvent(event);
    return user;
  }

  // 更改邮箱
  public changeEmail(newEmail: string): void {
    if (!this.state.isActive) {
      throw new Error('无法更改已停用用户的邮箱');
    }

    if (this.state.email === newEmail) {
      return; // 邮箱没有变化
    }

    const event = new UserEmailChangedEvent(
      this.id,
      { oldEmail: this.state.email, newEmail },
      this.version + 1
    );
    this.applyEvent(event);
  }

  // 停用用户
  public deactivate(reason: string, deactivatedBy: string): void {
    if (!this.state.isActive) {
      throw new Error('用户已经被停用');
    }

    const event = new UserDeactivatedEvent(
      this.id,
      { reason, deactivatedBy },
      this.version + 1
    );
    this.applyEvent(event);
  }

  // 应用事件变更
  protected applyChange(event: DomainEvent): void {
    switch (event.eventType) {
      case 'UserCreatedEvent':
        this.applyUserCreated(event as UserCreatedEvent);
        break;
      case 'UserEmailChangedEvent':
        this.applyEmailChanged(event as UserEmailChangedEvent);
        break;
      case 'UserDeactivatedEvent':
        this.applyUserDeactivated(event as UserDeactivatedEvent);
        break;
      default:
        console.warn(\`未知事件类型: \${event.eventType}\`);
    }
  }

  private applyUserCreated(event: UserCreatedEvent): void {
    this.state.name = event.eventData.name;
    this.state.email = event.eventData.email;
    this.state.role = event.eventData.role;
    this.state.isActive = true;
    this.state.createdAt = event.timestamp;
  }

  private applyEmailChanged(event: UserEmailChangedEvent): void {
    this.state.email = event.eventData.newEmail;
  }

  private applyUserDeactivated(event: UserDeactivatedEvent): void {
    this.state.isActive = false;
    this.state.deactivatedAt = event.timestamp;
    this.state.deactivationReason = event.eventData.reason;
  }

  // 获取当前状态
  public getState(): UserState {
    return { ...this.state };
  }
}

// 6. 仓储模式
class UserRepository {
  constructor(private eventStore: EventStore) {}

  async save(user: User): Promise<void> {
    const events = user.getUncommittedEvents();
    if (events.length === 0) {
      return;
    }

    await this.eventStore.saveEvents(user.getId(), events, user.getVersion() - events.length);
    user.markEventsAsCommitted();
  }

  async getById(id: string): Promise<User | null> {
    const events = await this.eventStore.getEvents(id);
    if (events.length === 0) {
      return null;
    }

    const user = new User(id);
    user.loadFromHistory(events);
    return user;
  }
}

// 7. 事件处理器和投影
interface EventHandler {
  handle(event: DomainEvent): Promise<void>;
  canHandle(eventType: string): boolean;
}

class UserProjectionHandler implements EventHandler {
  private userProjections: Map<string, any> = new Map();

  async handle(event: DomainEvent): Promise<void> {
    switch (event.eventType) {
      case 'UserCreatedEvent':
        await this.handleUserCreated(event as UserCreatedEvent);
        break;
      case 'UserEmailChangedEvent':
        await this.handleEmailChanged(event as UserEmailChangedEvent);
        break;
      case 'UserDeactivatedEvent':
        await this.handleUserDeactivated(event as UserDeactivatedEvent);
        break;
    }
  }

  canHandle(eventType: string): boolean {
    return ['UserCreatedEvent', 'UserEmailChangedEvent', 'UserDeactivatedEvent'].includes(eventType);
  }

  private async handleUserCreated(event: UserCreatedEvent): Promise<void> {
    const projection = {
      id: event.aggregateId,
      name: event.eventData.name,
      email: event.eventData.email,
      role: event.eventData.role,
      isActive: true,
      createdAt: event.timestamp,
      lastUpdated: event.timestamp
    };
    this.userProjections.set(event.aggregateId, projection);
    console.log(\`用户投影已创建: \${event.aggregateId}\`);
  }

  private async handleEmailChanged(event: UserEmailChangedEvent): Promise<void> {
    const projection = this.userProjections.get(event.aggregateId);
    if (projection) {
      projection.email = event.eventData.newEmail;
      projection.lastUpdated = event.timestamp;
      console.log(\`用户邮箱投影已更新: \${event.aggregateId}\`);
    }
  }

  private async handleUserDeactivated(event: UserDeactivatedEvent): Promise<void> {
    const projection = this.userProjections.get(event.aggregateId);
    if (projection) {
      projection.isActive = false;
      projection.deactivatedAt = event.timestamp;
      projection.deactivationReason = event.eventData.reason;
      projection.lastUpdated = event.timestamp;
      console.log(\`用户停用投影已更新: \${event.aggregateId}\`);
    }
  }

  getAllProjections(): any[] {
    return Array.from(this.userProjections.values());
  }
}

// 8. 使用示例
console.log('=== 事件溯源模式演示 ===');

async function demonstrateEventSourcing() {
  // 创建事件存储和仓储
  const eventStore = new InMemoryEventStore();
  const userRepository = new UserRepository(eventStore);
  const projectionHandler = new UserProjectionHandler();

  console.log('\\n--- 创建用户 ---');
  const user = User.create('user-1', 'Alice', '<EMAIL>', 'admin');
  await userRepository.save(user);

  console.log('\\n--- 更改邮箱 ---');
  user.changeEmail('<EMAIL>');
  await userRepository.save(user);

  console.log('\\n--- 停用用户 ---');
  user.deactivate('违反政策', 'admin');
  await userRepository.save(user);

  console.log('\\n--- 从事件重建用户状态 ---');
  const rebuiltUser = await userRepository.getById('user-1');
  if (rebuiltUser) {
    console.log('重建的用户状态:', rebuiltUser.getState());
  }

  console.log('\\n--- 处理事件投影 ---');
  const allEvents = await eventStore.getAllEvents();
  for (const event of allEvents) {
    if (projectionHandler.canHandle(event.eventType)) {
      await projectionHandler.handle(event);
    }
  }

  console.log('\\n--- 查看投影 ---');
  const projections = projectionHandler.getAllProjections();
  console.log('用户投影:', projections);

  console.log('\\n--- 事件历史 ---');
  const userEvents = await eventStore.getEvents('user-1');
  userEvents.forEach((event, index) => {
    console.log(\`事件 \${index + 1}: \${event.eventType} at \${event.timestamp.toISOString()}\`);
    console.log('  数据:', event.eventData);
  });
}

// 运行演示
demonstrateEventSourcing();

console.log('\\n=== 事件溯源模式优势 ===');
console.log('1. 完整的审计日志');
console.log('2. 时间旅行和状态重建');
console.log('3. 事件重放和调试');
console.log('4. 多个读模型投影');
console.log('5. 高性能的写操作');

export {
  DomainEvent, Event, EventStore, InMemoryEventStore,
  AggregateRoot, User, UserRepository, EventHandler, UserProjectionHandler
};`
  },

  'cqrs': {
    title: 'CQRS模式 - 命令查询职责分离',
    code: `// CQRS模式实现
// Command Query Responsibility Segregation - 分离命令（写）和查询（读）的数据模型

// 1. 命令接口和基类
interface Command {
  id: string;
  timestamp: Date;
  userId?: string;
}

abstract class BaseCommand implements Command {
  public readonly id: string;
  public readonly timestamp: Date;
  public readonly userId?: string;

  constructor(userId?: string) {
    this.id = this.generateId();
    this.timestamp = new Date();
    this.userId = userId;
  }

  private generateId(): string {
    return \`cmd-\${Date.now()}-\${Math.random().toString(36).substr(2, 9)}\`;
  }
}

// 2. 具体命令类
class CreateUserCommand extends BaseCommand {
  constructor(
    public readonly name: string,
    public readonly email: string,
    public readonly role: string,
    userId?: string
  ) {
    super(userId);
  }
}

class UpdateUserEmailCommand extends BaseCommand {
  constructor(
    public readonly userId: string,
    public readonly newEmail: string,
    executorId?: string
  ) {
    super(executorId);
  }
}

class DeactivateUserCommand extends BaseCommand {
  constructor(
    public readonly userId: string,
    public readonly reason: string,
    executorId?: string
  ) {
    super(executorId);
  }
}

// 3. 查询接口和基类
interface Query {
  id: string;
  timestamp: Date;
}

abstract class BaseQuery implements Query {
  public readonly id: string;
  public readonly timestamp: Date;

  constructor() {
    this.id = this.generateId();
    this.timestamp = new Date();
  }

  private generateId(): string {
    return \`qry-\${Date.now()}-\${Math.random().toString(36).substr(2, 9)}\`;
  }
}

// 4. 具体查询类
class GetUserByIdQuery extends BaseQuery {
  constructor(public readonly userId: string) {
    super();
  }
}

class GetAllUsersQuery extends BaseQuery {
  constructor(
    public readonly includeInactive: boolean = false,
    public readonly role?: string
  ) {
    super();
  }
}

class GetUsersByRoleQuery extends BaseQuery {
  constructor(public readonly role: string) {
    super();
  }
}

class GetUserStatisticsQuery extends BaseQuery {
  constructor(
    public readonly startDate?: Date,
    public readonly endDate?: Date
  ) {
    super();
  }
}

// 5. 命令处理器接口
interface CommandHandler<T extends Command> {
  handle(command: T): Promise<void>;
  canHandle(command: Command): boolean;
}

// 6. 查询处理器接口
interface QueryHandler<T extends Query, R> {
  handle(query: T): Promise<R>;
  canHandle(query: Query): boolean;
}

// 7. 写模型（命令端）
interface WriteUser {
  id: string;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  version: number;
  createdAt: Date;
  updatedAt: Date;
}

class WriteUserRepository {
  private users: Map<string, WriteUser> = new Map();

  async save(user: WriteUser): Promise<void> {
    user.updatedAt = new Date();
    user.version += 1;
    this.users.set(user.id, { ...user });
    console.log(\`用户已保存到写模型: \${user.id}\`);
  }

  async getById(id: string): Promise<WriteUser | null> {
    return this.users.get(id) || null;
  }

  async exists(id: string): Promise<boolean> {
    return this.users.has(id);
  }

  async emailExists(email: string, excludeId?: string): Promise<boolean> {
    return Array.from(this.users.values()).some(
      user => user.email === email && user.id !== excludeId
    );
  }
}

// 8. 读模型（查询端）
interface ReadUser {
  id: string;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  createdAt: Date;
  lastUpdated: Date;
  // 额外的查询优化字段
  displayName: string;
  roleDisplayName: string;
  statusText: string;
}

interface UserStatistics {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  usersByRole: Record<string, number>;
  recentlyCreated: number;
}

class ReadUserRepository {
  private users: Map<string, ReadUser> = new Map();

  async save(user: ReadUser): Promise<void> {
    this.users.set(user.id, { ...user });
    console.log(\`用户已保存到读模型: \${user.id}\`);
  }

  async getById(id: string): Promise<ReadUser | null> {
    return this.users.get(id) || null;
  }

  async getAll(includeInactive: boolean = false): Promise<ReadUser[]> {
    const users = Array.from(this.users.values());
    return includeInactive ? users : users.filter(user => user.isActive);
  }

  async getByRole(role: string): Promise<ReadUser[]> {
    return Array.from(this.users.values()).filter(user => user.role === role);
  }

  async getStatistics(startDate?: Date, endDate?: Date): Promise<UserStatistics> {
    const users = Array.from(this.users.values());

    let filteredUsers = users;
    if (startDate || endDate) {
      filteredUsers = users.filter(user => {
        const createdAt = user.createdAt;
        return (!startDate || createdAt >= startDate) &&
               (!endDate || createdAt <= endDate);
      });
    }

    const activeUsers = filteredUsers.filter(user => user.isActive);
    const inactiveUsers = filteredUsers.filter(user => !user.isActive);

    const usersByRole = filteredUsers.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentlyCreated = filteredUsers.filter(
      user => user.createdAt >= thirtyDaysAgo
    ).length;

    return {
      totalUsers: filteredUsers.length,
      activeUsers: activeUsers.length,
      inactiveUsers: inactiveUsers.length,
      usersByRole,
      recentlyCreated
    };
  }
}

// 9. 具体命令处理器
class CreateUserCommandHandler implements CommandHandler<CreateUserCommand> {
  constructor(
    private writeRepository: WriteUserRepository,
    private readRepository: ReadUserRepository
  ) {}

  async handle(command: CreateUserCommand): Promise<void> {
    // 验证
    if (await this.writeRepository.emailExists(command.email)) {
      throw new Error('邮箱已存在');
    }

    // 创建写模型
    const writeUser: WriteUser = {
      id: \`user-\${Date.now()}\`,
      name: command.name,
      email: command.email,
      role: command.role,
      isActive: true,
      version: 1,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.writeRepository.save(writeUser);

    // 同步到读模型
    const readUser: ReadUser = {
      id: writeUser.id,
      name: writeUser.name,
      email: writeUser.email,
      role: writeUser.role,
      isActive: writeUser.isActive,
      createdAt: writeUser.createdAt,
      lastUpdated: writeUser.updatedAt,
      displayName: \`\${writeUser.name} (\${writeUser.email})\`,
      roleDisplayName: this.getRoleDisplayName(writeUser.role),
      statusText: writeUser.isActive ? '活跃' : '非活跃'
    };

    await this.readRepository.save(readUser);
    console.log(\`用户创建成功: \${command.name}\`);
  }

  canHandle(command: Command): boolean {
    return command instanceof CreateUserCommand;
  }

  private getRoleDisplayName(role: string): string {
    const roleMap: Record<string, string> = {
      'admin': '管理员',
      'user': '普通用户',
      'guest': '访客'
    };
    return roleMap[role] || role;
  }
}

class UpdateUserEmailCommandHandler implements CommandHandler<UpdateUserEmailCommand> {
  constructor(
    private writeRepository: WriteUserRepository,
    private readRepository: ReadUserRepository
  ) {}

  async handle(command: UpdateUserEmailCommand): Promise<void> {
    // 获取用户
    const writeUser = await this.writeRepository.getById(command.userId);
    if (!writeUser) {
      throw new Error('用户不存在');
    }

    if (!writeUser.isActive) {
      throw new Error('无法更新非活跃用户的邮箱');
    }

    // 验证邮箱
    if (await this.writeRepository.emailExists(command.newEmail, command.userId)) {
      throw new Error('邮箱已被其他用户使用');
    }

    // 更新写模型
    writeUser.email = command.newEmail;
    await this.writeRepository.save(writeUser);

    // 更新读模型
    const readUser = await this.readRepository.getById(command.userId);
    if (readUser) {
      readUser.email = command.newEmail;
      readUser.lastUpdated = new Date();
      readUser.displayName = \`\${readUser.name} (\${readUser.email})\`;
      await this.readRepository.save(readUser);
    }

    console.log(\`用户邮箱更新成功: \${command.userId}\`);
  }

  canHandle(command: Command): boolean {
    return command instanceof UpdateUserEmailCommand;
  }
}

// 10. 具体查询处理器
class GetUserByIdQueryHandler implements QueryHandler<GetUserByIdQuery, ReadUser | null> {
  constructor(private readRepository: ReadUserRepository) {}

  async handle(query: GetUserByIdQuery): Promise<ReadUser | null> {
    console.log(\`查询用户: \${query.userId}\`);
    return await this.readRepository.getById(query.userId);
  }

  canHandle(query: Query): boolean {
    return query instanceof GetUserByIdQuery;
  }
}

class GetAllUsersQueryHandler implements QueryHandler<GetAllUsersQuery, ReadUser[]> {
  constructor(private readRepository: ReadUserRepository) {}

  async handle(query: GetAllUsersQuery): Promise<ReadUser[]> {
    console.log(\`查询所有用户，包含非活跃: \${query.includeInactive}\`);
    let users = await this.readRepository.getAll(query.includeInactive);

    if (query.role) {
      users = users.filter(user => user.role === query.role);
    }

    return users;
  }

  canHandle(query: Query): boolean {
    return query instanceof GetAllUsersQuery;
  }
}

class GetUserStatisticsQueryHandler implements QueryHandler<GetUserStatisticsQuery, UserStatistics> {
  constructor(private readRepository: ReadUserRepository) {}

  async handle(query: GetUserStatisticsQuery): Promise<UserStatistics> {
    console.log('查询用户统计信息');
    return await this.readRepository.getStatistics(query.startDate, query.endDate);
  }

  canHandle(query: Query): boolean {
    return query instanceof GetUserStatisticsQuery;
  }
}

// 11. 命令总线和查询总线
class CommandBus {
  private handlers: CommandHandler<any>[] = [];

  register(handler: CommandHandler<any>): void {
    this.handlers.push(handler);
  }

  async execute(command: Command): Promise<void> {
    const handler = this.handlers.find(h => h.canHandle(command));
    if (!handler) {
      throw new Error(\`没有找到命令处理器: \${command.constructor.name}\`);
    }

    console.log(\`执行命令: \${command.constructor.name}\`);
    await handler.handle(command);
  }
}

class QueryBus {
  private handlers: QueryHandler<any, any>[] = [];

  register(handler: QueryHandler<any, any>): void {
    this.handlers.push(handler);
  }

  async execute<T>(query: Query): Promise<T> {
    const handler = this.handlers.find(h => h.canHandle(query));
    if (!handler) {
      throw new Error(\`没有找到查询处理器: \${query.constructor.name}\`);
    }

    console.log(\`执行查询: \${query.constructor.name}\`);
    return await handler.handle(query);
  }
}

// 12. 使用示例
console.log('=== CQRS模式演示 ===');

async function demonstrateCQRS() {
  // 创建仓储
  const writeRepository = new WriteUserRepository();
  const readRepository = new ReadUserRepository();

  // 创建处理器
  const createUserHandler = new CreateUserCommandHandler(writeRepository, readRepository);
  const updateEmailHandler = new UpdateUserEmailCommandHandler(writeRepository, readRepository);
  const getUserHandler = new GetUserByIdQueryHandler(readRepository);
  const getAllUsersHandler = new GetAllUsersQueryHandler(readRepository);
  const getStatsHandler = new GetUserStatisticsQueryHandler(readRepository);

  // 创建总线
  const commandBus = new CommandBus();
  const queryBus = new QueryBus();

  // 注册处理器
  commandBus.register(createUserHandler);
  commandBus.register(updateEmailHandler);
  queryBus.register(getUserHandler);
  queryBus.register(getAllUsersHandler);
  queryBus.register(getStatsHandler);

  console.log('\\n--- 执行命令 ---');

  // 创建用户
  await commandBus.execute(new CreateUserCommand('Alice', '<EMAIL>', 'admin'));
  await commandBus.execute(new CreateUserCommand('Bob', '<EMAIL>', 'user'));
  await commandBus.execute(new CreateUserCommand('Charlie', '<EMAIL>', 'guest'));

  console.log('\\n--- 执行查询 ---');

  // 查询所有用户
  const allUsers = await queryBus.execute<ReadUser[]>(new GetAllUsersQuery());
  console.log('所有用户:', allUsers.map(u => ({ name: u.name, role: u.roleDisplayName })));

  // 查询统计信息
  const stats = await queryBus.execute<UserStatistics>(new GetUserStatisticsQuery());
  console.log('用户统计:', stats);

  console.log('\\n--- 更新命令 ---');

  // 更新邮箱
  const firstUser = allUsers[0];
  if (firstUser) {
    await commandBus.execute(new UpdateUserEmailCommand(firstUser.id, '<EMAIL>'));

    // 查询更新后的用户
    const updatedUser = await queryBus.execute<ReadUser | null>(new GetUserByIdQuery(firstUser.id));
    console.log('更新后的用户:', updatedUser);
  }
}

// 运行演示
demonstrateCQRS();

console.log('\\n=== CQRS模式优势 ===');
console.log('1. 读写分离，独立优化');
console.log('2. 查询性能优化');
console.log('3. 复杂查询支持');
console.log('4. 可扩展性');
console.log('5. 职责清晰分离');

export {
  Command, Query, CommandHandler, QueryHandler, CommandBus, QueryBus,
  CreateUserCommand, UpdateUserEmailCommand, GetUserByIdQuery, GetAllUsersQuery,
  WriteUser, ReadUser, WriteUserRepository, ReadUserRepository, UserStatistics
};`
  }
};
