export interface PatternCode {
  id: string
  title: string
  code: string
}

export const patternCodes: Record<string, PatternCode> = {
  command: {
    id: 'command',
    title: '命令模式实现',
    code: `// 命令模式 (Command Pattern) - 文本编辑器
// 命令接口
interface Command {
  execute(): void
  undo(): void
  getName(): string
}

// 接收者 - 文本编辑器
class TextEditor {
  private content: string = ''
  private cursorPosition: number = 0

  getContent(): string {
    return this.content
  }

  setCursorPosition(position: number): void {
    this.cursorPosition = Math.min(Math.max(0, position), this.content.length)
  }

  getCursorPosition(): number {
    return this.cursorPosition
  }

  insertText(text: string): void {
    this.content =
      this.content.substring(0, this.cursorPosition) +
      text +
      this.content.substring(this.cursorPosition)
    this.cursorPosition += text.length
  }

  deleteText(start: number, end: number): string {
    const deletedText = this.content.substring(start, end)
    this.content =
      this.content.substring(0, start) +
      this.content.substring(end)
    this.cursorPosition = start
    return deletedText
  }
}

// 具体命令 - 插入文本
class InsertTextCommand implements Command {
  private cursorPosition: number

  constructor(
    private editor: TextEditor,
    private text: string
  ) {
    this.cursorPosition = editor.getCursorPosition()
  }

  execute(): void {
    this.editor.setCursorPosition(this.cursorPosition)
    this.editor.insertText(this.text)
  }

  undo(): void {
    const endPosition = this.cursorPosition + this.text.length
    this.editor.deleteText(this.cursorPosition, endPosition)
    this.editor.setCursorPosition(this.cursorPosition)
  }

  getName(): string {
    return '插入文本'
  }
}

// 具体命令 - 删除文本
class DeleteTextCommand implements Command {
  private deletedText: string = ''

  constructor(
    private editor: TextEditor,
    private start: number,
    private end: number
  ) {}

  execute(): void {
    this.deletedText = this.editor.deleteText(this.start, this.end)
  }

  undo(): void {
    this.editor.setCursorPosition(this.start)
    this.editor.insertText(this.deletedText)
  }

  getName(): string {
    return '删除文本'
  }
}

// 宏命令 - 组合多个命令
class MacroCommand implements Command {
  private commands: Command[] = []

  addCommand(command: Command): void {
    this.commands.push(command)
  }

  execute(): void {
    this.commands.forEach(command => command.execute())
  }

  undo(): void {
    // 反序撤销命令
    for (let i = this.commands.length - 1; i >= 0; i--) {
      this.commands[i].undo()
    }
  }

  getName(): string {
    return '宏命令'
  }
}

// 命令管理器 - 调用者
class CommandManager {
  private history: Command[] = []
  private redoStack: Command[] = []

  executeCommand(command: Command): void {
    command.execute()
    this.history.push(command)
    this.redoStack = [] // 清除重做栈

    console.log(\`执行: \${command.getName()}\`)
    console.log(\`当前内容: \${editor.getContent()}\`)
  }

  undo(): void {
    if (this.history.length === 0) return

    const command = this.history.pop()!
    command.undo()
    this.redoStack.push(command)

    console.log(\`撤销: \${command.getName()}\`)
    console.log(\`当前内容: \${editor.getContent()}\`)
  }

  redo(): void {
    if (this.redoStack.length === 0) return

    const command = this.redoStack.pop()!
    command.execute()
    this.history.push(command)

    console.log(\`重做: \${command.getName()}\`)
    console.log(\`当前内容: \${editor.getContent()}\`)
  }
}

// 客户端代码
const editor = new TextEditor()
const manager = new CommandManager()

// 执行插入命令
const insertHello = new InsertTextCommand(editor, 'Hello, ')
manager.executeCommand(insertHello)

// 执行另一个插入命令
const insertWorld = new InsertTextCommand(editor, 'World!')
manager.executeCommand(insertWorld)

// 执行删除命令
const deleteComma = new DeleteTextCommand(editor, 5, 7)
manager.executeCommand(deleteComma) // 删除逗号和空格

// 撤销删除
manager.undo()

// 撤销第二次插入
manager.undo()

// 重做第二次插入
manager.redo()

// 创建并执行宏命令
const macro = new MacroCommand()
macro.addCommand(new DeleteTextCommand(editor, 5, 12)) // 删除 "World!"
macro.addCommand(new InsertTextCommand(editor, 'Command Pattern!'))

manager.executeCommand(macro)
console.log(\`最终内容: \${editor.getContent()}\`) // "Hello Command Pattern!"`
  },
  singleton: {
    id: 'singleton',
    title: '单例模式实现',
    code: `// 单例模式 (Singleton Pattern) - 日志记录器
class Logger {
  private static instance: Logger | null = null
  private logs: string[] = []
  private id: string

  // 私有构造函数，防止外部实例化
  private constructor() {
    this.id = Math.random().toString(36).slice(2, 11)
  }

  // 获取单例实例的静态方法
  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }

  // 记录日志
  public log(message: string): void {
    const timestamp = new Date().toLocaleTimeString()
    this.logs.push(\`[\${timestamp}] \${message}\`)
  }

  // 获取所有日志
  public getLogs(): string[] {
    return [...this.logs]
  }

  // 清空日志
  public clearLogs(): void {
    this.logs = []
  }

  // 获取实例ID
  public getId(): string {
    return this.id
  }
}

// 使用示例 - 验证单例特性
const logger1 = Logger.getInstance()
const logger2 = Logger.getInstance()

console.log(logger1 === logger2) // true - 同一个实例
console.log(logger1.getId() === logger2.getId()) // true - 相同ID

logger1.log('用户登录成功')
logger2.log('数据库连接建立')

console.log(logger1.getLogs()) // 两条日志都在同一个实例中`
  },

  factory: {
    id: 'factory',
    title: '工厂模式实现',
    code: `// 工厂模式 (Factory Pattern) - 支付处理器
// 支付处理器接口
interface PaymentProcessor {
  process(amount: number): string
  getType(): string
  getProvider(): string
  getFeeRate(): number
}

// 具体支付处理器实现
class AlipayProcessor implements PaymentProcessor {
  process(amount: number): string {
    const fee = (amount * this.getFeeRate()) / 100
    return \`支付宝支付成功！金额：¥\${amount}，手续费：¥\${fee.toFixed(2)}\`
  }

  getType(): string {
    return '支付宝'
  }

  getProvider(): string {
    return '蚂蚁金服'
  }

  getFeeRate(): number {
    return 0.6
  }
}

class WechatProcessor implements PaymentProcessor {
  process(amount: number): string {
    const fee = (amount * this.getFeeRate()) / 100
    return \`微信支付成功！金额：¥\${amount}，手续费：¥\${fee.toFixed(2)}\`
  }

  getType(): string {
    return '微信支付'
  }

  getProvider(): string {
    return '腾讯'
  }

  getFeeRate(): number {
    return 0.6
  }
}

// 支付处理器工厂
class PaymentProcessorFactory {
  static createProcessor(type: string): PaymentProcessor {
    switch (type) {
      case 'alipay':
        return new AlipayProcessor()
      case 'wechat':
        return new WechatProcessor()
      default:
        throw new Error(\`不支持的支付类型: \${type}\`)
    }
  }

  static getSupportedTypes(): string[] {
    return ['alipay', 'wechat']
  }
}

// 使用示例
const alipayProcessor = PaymentProcessorFactory.createProcessor('alipay')
const wechatProcessor = PaymentProcessorFactory.createProcessor('wechat')

console.log(alipayProcessor.process(100))
console.log(wechatProcessor.process(200))`
  },

  observer: {
    id: 'observer',
    title: '观察者模式实现',
    code: `// 观察者模式 (Observer Pattern) - 股票价格通知
// 观察者接口
interface Observer {
  id: string
  name: string
  type: string
  notifications: string[]
  update(symbol: string, price: number, change: number): void
}

// 主题接口
interface Subject {
  addObserver(observer: Observer): void
  removeObserver(observerId: string): void
  notifyObservers(): void
}

// 具体观察者实现
class StockObserver implements Observer {
  id: string
  name: string
  type: string
  notifications: string[] = []

  constructor(name: string, type: string) {
    this.id = Math.random().toString(36).slice(2, 11)
    this.name = name
    this.type = type
  }

  update(symbol: string, price: number, change: number): void {
    const timestamp = new Date().toLocaleTimeString()
    let message = ''

    switch (this.type) {
      case 'investor':
        message = \`📈 \${symbol} 价格变动 ¥\${change.toFixed(2)}，当前价格 ¥\${price.toFixed(2)}\`
        break
      case 'trader':
        message = \`🔄 交易提醒：\${symbol} 变动，价格 ¥\${price.toFixed(2)}\`
        break
      case 'analyst':
        message = \`📊 \${symbol} 波动分析，价格 ¥\${price.toFixed(2)}\`
        break
    }

    this.notifications.unshift(\`[\${timestamp}] \${message}\`)
  }
}

// 股票主题
class Stock implements Subject {
  private observers: Observer[] = []
  symbol: string
  name: string
  price: number
  previousPrice: number

  constructor(symbol: string, name: string, initialPrice: number) {
    this.symbol = symbol
    this.name = name
    this.price = initialPrice
    this.previousPrice = initialPrice
  }

  addObserver(observer: Observer): void {
    this.observers.push(observer)
  }

  removeObserver(observerId: string): void {
    this.observers = this.observers.filter(obs => obs.id !== observerId)
  }

  notifyObservers(): void {
    const change = this.price - this.previousPrice
    this.observers.forEach(observer => {
      observer.update(this.symbol, this.price, change)
    })
  }

  setPrice(newPrice: number): void {
    this.previousPrice = this.price
    this.price = newPrice
    this.notifyObservers()
  }
}

// 使用示例
const stock = new Stock('AAPL', '苹果公司', 150.0)
const investor = new StockObserver('张三', 'investor')
const trader = new StockObserver('李四', 'trader')

stock.addObserver(investor)
stock.addObserver(trader)
stock.setPrice(155.0) // 通知所有观察者`
  },

  strategy: {
    id: 'strategy',
    title: '策略模式实现',
    code: `// 策略模式 (Strategy Pattern) - 折扣计算
// 商品接口
interface CartItem {
  name: string
  price: number
}

// 折扣策略接口
interface DiscountStrategy {
  calculate(total: number, items: CartItem[]): { discount: number; details: string }
}

// 具体折扣策略实现
class NoDiscountStrategy implements DiscountStrategy {
  calculate(total: number): { discount: number; details: string } {
    return {
      discount: 0,
      details: '无折扣优惠'
    }
  }
}

class PercentageDiscountStrategy implements DiscountStrategy {
  private percentage: number

  constructor(percentage: number) {
    this.percentage = percentage
  }

  calculate(total: number): { discount: number; details: string } {
    const discount = total * (this.percentage / 100)
    return {
      discount,
      details: \`\${this.percentage}% 折扣优惠: ¥\${total.toFixed(2)} × \${this.percentage}% = ¥\${discount.toFixed(2)}\`
    }
  }
}

class VipDiscountStrategy implements DiscountStrategy {
  calculate(total: number, items: CartItem[]): { discount: number; details: string } {
    const baseDiscount = total * 0.15 // VIP 85折
    const extraDiscount = Math.floor(total / 100) * 5 // 每满100减5
    const totalDiscount = baseDiscount + extraDiscount

    return {
      discount: totalDiscount,
      details: \`VIP专享: 85折优惠¥\${baseDiscount.toFixed(2)} + 每满100减5优惠¥\${extraDiscount.toFixed(2)}\`
    }
  }
}

// 折扣计算器上下文
class DiscountCalculator {
  private strategy: DiscountStrategy

  constructor(strategy: DiscountStrategy) {
    this.strategy = strategy
  }

  setStrategy(strategy: DiscountStrategy): void {
    this.strategy = strategy
  }

  calculateDiscount(total: number, items: CartItem[]): { discount: number; details: string } {
    return this.strategy.calculate(total, items)
  }
}

// 使用示例
const items: CartItem[] = [
  { name: '商品A', price: 50 },
  { name: '商品B', price: 80 }
]
const total = 130

const calculator = new DiscountCalculator(new NoDiscountStrategy())
console.log(calculator.calculateDiscount(total, items))

calculator.setStrategy(new PercentageDiscountStrategy(10))
console.log(calculator.calculateDiscount(total, items))

calculator.setStrategy(new VipDiscountStrategy())
console.log(calculator.calculateDiscount(total, items))`
  },

  decorator: {
    id: 'decorator',
    title: '装饰器模式实现',
    code: `// 装饰器模式 (Decorator Pattern) - 咖啡定制
// 咖啡组件接口
interface Coffee {
  getDescription(): string
  getCost(): number
}

// 基础咖啡类
class BaseCoffee implements Coffee {
  protected description: string
  protected cost: number

  constructor(description: string, cost: number) {
    this.description = description
    this.cost = cost
  }

  getDescription(): string {
    return this.description
  }

  getCost(): number {
    return this.cost
  }
}

// 具体咖啡实现
class Espresso extends BaseCoffee {
  constructor() {
    super('浓缩咖啡', 15.0)
  }
}

class Latte extends BaseCoffee {
  constructor() {
    super('拿铁咖啡', 25.0)
  }
}

// 装饰器基类
abstract class CoffeeDecorator implements Coffee {
  protected coffee: Coffee

  constructor(coffee: Coffee) {
    this.coffee = coffee
  }

  getDescription(): string {
    return this.coffee.getDescription()
  }

  getCost(): number {
    return this.coffee.getCost()
  }
}

// 具体装饰器实现
class MilkDecorator extends CoffeeDecorator {
  getDescription(): string {
    return this.coffee.getDescription() + ' + 牛奶'
  }

  getCost(): number {
    return this.coffee.getCost() + 3.0
  }
}

class SugarDecorator extends CoffeeDecorator {
  getDescription(): string {
    return this.coffee.getDescription() + ' + 糖'
  }

  getCost(): number {
    return this.coffee.getCost() + 1.0
  }
}

class VanillaDecorator extends CoffeeDecorator {
  getDescription(): string {
    return this.coffee.getDescription() + ' + 香草糖浆'
  }

  getCost(): number {
    return this.coffee.getCost() + 5.0
  }
}

// 使用示例 - 动态组合装饰器
let coffee: Coffee = new Espresso()
console.log(\`\${coffee.getDescription()} - ¥\${coffee.getCost()}\`)

coffee = new MilkDecorator(coffee)
console.log(\`\${coffee.getDescription()} - ¥\${coffee.getCost()}\`)

coffee = new SugarDecorator(coffee)
console.log(\`\${coffee.getDescription()} - ¥\${coffee.getCost()}\`)

coffee = new VanillaDecorator(coffee)
console.log(\`\${coffee.getDescription()} - ¥\${coffee.getCost()}\`)

// 创建另一种组合
let latte: Coffee = new Latte()
latte = new VanillaDecorator(new MilkDecorator(latte))
console.log(\`\${latte.getDescription()} - ¥\${latte.getCost()}\`)`
  },

  state: {
    id: 'state',
    title: '状态模式实现',
    code: `// 状态模式 (State Pattern) - 订单状态管理
// 状态接口
interface OrderState {
  getStateName(): string
  getDescription(): string
  getAvailableActions(): string[]
  handleAction(context: OrderContext, action: string): boolean
}

// 订单上下文
class OrderContext {
  private state: OrderState
  private orderInfo: {
    id: string
    amount: number
    customerName: string
    history: Array<{ state: string; timestamp: Date; action?: string }>
  }

  constructor(orderId: string, amount: number, customerName: string) {
    this.orderInfo = {
      id: orderId,
      amount,
      customerName,
      history: []
    }

    // 初始状态为待支付
    this.state = new PendingPaymentState()
    this.addToHistory(this.state.getStateName(), '订单创建')
  }

  setState(state: OrderState, action?: string): void {
    this.state = state
    this.addToHistory(state.getStateName(), action)
  }

  getState(): OrderState {
    return this.state
  }

  executeAction(action: string): boolean {
    return this.state.handleAction(this, action)
  }

  getOrderInfo() {
    return { ...this.orderInfo }
  }

  private addToHistory(stateName: string, action?: string): void {
    this.orderInfo.history.push({
      state: stateName,
      timestamp: new Date(),
      action
    })
  }
}

// 具体状态实现
class PendingPaymentState implements OrderState {
  getStateName(): string {
    return '待支付'
  }

  getDescription(): string {
    return '订单已创建，等待用户支付'
  }

  getAvailableActions(): string[] {
    return ['支付', '取消订单']
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '支付':
        context.setState(new PaidState(), '用户完成支付')
        return true
      case '取消订单':
        context.setState(new CancelledState(), '用户取消订单')
        return true
      default:
        return false
    }
  }
}

class PaidState implements OrderState {
  getStateName(): string {
    return '已支付'
  }

  getDescription(): string {
    return '支付成功，准备发货'
  }

  getAvailableActions(): string[] {
    return ['发货', '申请退款']
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '发货':
        context.setState(new ShippedState(), '商家发货')
        return true
      case '申请退款':
        context.setState(new RefundingState(), '用户申请退款')
        return true
      default:
        return false
    }
  }
}

class ShippedState implements OrderState {
  getStateName(): string {
    return '已发货'
  }

  getDescription(): string {
    return '商品已发货，正在配送中'
  }

  getAvailableActions(): string[] {
    return ['确认收货', '申请退货']
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '确认收货':
        context.setState(new DeliveredState(), '用户确认收货')
        return true
      case '申请退货':
        context.setState(new ReturningState(), '用户申请退货')
        return true
      default:
        return false
    }
  }
}

class DeliveredState implements OrderState {
  getStateName(): string {
    return '已完成'
  }

  getDescription(): string {
    return '订单已完成，交易成功'
  }

  getAvailableActions(): string[] {
    return ['评价商品']
  }

  handleAction(context: OrderContext, action: string): boolean {
    return action === '评价商品'
  }
}

class CancelledState implements OrderState {
  getStateName(): string {
    return '已取消'
  }

  getDescription(): string {
    return '订单已取消'
  }

  getAvailableActions(): string[] {
    return []
  }

  handleAction(context: OrderContext, action: string): boolean {
    return false
  }
}

class RefundingState implements OrderState {
  getStateName(): string {
    return '退款中'
  }

  getDescription(): string {
    return '正在处理退款申请'
  }

  getAvailableActions(): string[] {
    return ['同意退款', '拒绝退款']
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '同意退款':
        context.setState(new RefundedState(), '商家同意退款')
        return true
      case '拒绝退款':
        context.setState(new PaidState(), '商家拒绝退款')
        return true
      default:
        return false
    }
  }
}

class RefundedState implements OrderState {
  getStateName(): string {
    return '已退款'
  }

  getDescription(): string {
    return '退款已完成'
  }

  getAvailableActions(): string[] {
    return []
  }

  handleAction(context: OrderContext, action: string): boolean {
    return false
  }
}

class ReturningState implements OrderState {
  getStateName(): string {
    return '退货中'
  }

  getDescription(): string {
    return '正在处理退货申请'
  }

  getAvailableActions(): string[] {
    return ['同意退货', '拒绝退货']
  }

  handleAction(context: OrderContext, action: string): boolean {
    switch (action) {
      case '同意退货':
        context.setState(new RefundedState(), '商家同意退货')
        return true
      case '拒绝退货':
        context.setState(new DeliveredState(), '商家拒绝退货')
        return true
      default:
        return false
    }
  }
}

// 使用示例
const order = new OrderContext('ORD-001', 299.99, '张三')
console.log(\`当前状态: \${order.getState().getStateName()}\`)
console.log(\`可执行操作: \${order.getState().getAvailableActions().join(', ')}\`)

// 执行支付操作
order.executeAction('支付')
console.log(\`支付后状态: \${order.getState().getStateName()}\`)

// 执行发货操作
order.executeAction('发货')
console.log(\`发货后状态: \${order.getState().getStateName()}\`)

// 确认收货
order.executeAction('确认收货')
console.log(\`最终状态: \${order.getState().getStateName()}\`)`
  },

  adapter: {
    id: 'adapter',
    title: '适配器模式实现',
    code: `// 适配器模式 (Adapter Pattern) - 第三方支付接口适配
// 目标接口 - 我们系统期望的统一支付接口
interface PaymentTarget {
  processPayment(amount: number, currency: string): PaymentResult
  getPaymentStatus(transactionId: string): PaymentStatus
  refund(transactionId: string, amount: number): RefundResult
}

// 支付结果接口
interface PaymentResult {
  success: boolean
  transactionId: string
  message: string
  timestamp: Date
  fee: number
}

enum PaymentStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

interface RefundResult {
  success: boolean
  refundId: string
  message: string
  timestamp: Date
}

// === 第三方支付系统 A (支付宝风格) ===
class AlipayService {
  // 支付宝的支付方法 - 接口不同
  pay(money: number, coin: string): {
    code: number
    data: { trade_no: string; msg: string; fee_amount: number }
    timestamp: number
  } {
    const success = Math.random() > 0.1
    return {
      code: success ? 200 : 500,
      data: {
        trade_no: \`alipay_\${Date.now()}_\${Math.random().toString(36).slice(2, 8)}\`,
        msg: success ? '支付成功' : '支付失败',
        fee_amount: money * 0.006
      },
      timestamp: Date.now()
    }
  }

  queryOrder(tradeNo: string): { code: number; status: string } {
    const statuses = ['TRADE_SUCCESS', 'TRADE_CLOSED', 'WAIT_BUYER_PAY']
    return {
      code: 200,
      status: statuses[Math.floor(Math.random() * statuses.length)]
    }
  }
}

// === 第三方支付系统 B (微信风格) ===
class WechatPayService {
  // 微信的支付方法 - 接口完全不同
  createOrder(orderAmount: number, currencyType: string): {
    result_code: string
    transaction_id: string
    return_msg: string
    total_fee: number
    create_time: string
  } {
    const success = Math.random() > 0.08
    return {
      result_code: success ? 'SUCCESS' : 'FAIL',
      transaction_id: \`wx_\${Date.now()}_\${Math.random().toString(36).slice(2, 8)}\`,
      return_msg: success ? 'OK' : 'SYSTEM_ERROR',
      total_fee: orderAmount * 0.008,
      create_time: new Date().toISOString()
    }
  }

  queryPayment(transactionId: string): { result_code: string; trade_state: string } {
    const states = ['SUCCESS', 'REFUND', 'NOTPAY', 'CLOSED']
    return {
      result_code: 'SUCCESS',
      trade_state: states[Math.floor(Math.random() * states.length)]
    }
  }
}

// === 适配器实现 ===
class AlipayAdapter implements PaymentTarget {
  private alipayService: AlipayService

  constructor(alipayService: AlipayService) {
    this.alipayService = alipayService
  }

  processPayment(amount: number, currency: string): PaymentResult {
    const result = this.alipayService.pay(amount, currency)

    return {
      success: result.code === 200,
      transactionId: result.data.trade_no,
      message: result.data.msg,
      timestamp: new Date(result.timestamp),
      fee: result.data.fee_amount
    }
  }

  getPaymentStatus(transactionId: string): PaymentStatus {
    const result = this.alipayService.queryOrder(transactionId)

    switch (result.status) {
      case 'TRADE_SUCCESS':
        return PaymentStatus.SUCCESS
      case 'TRADE_CLOSED':
        return PaymentStatus.FAILED
      case 'WAIT_BUYER_PAY':
        return PaymentStatus.PENDING
      default:
        return PaymentStatus.FAILED
    }
  }

  refund(transactionId: string, amount: number): RefundResult {
    // 简化实现
    return {
      success: true,
      refundId: \`refund_\${Date.now()}\`,
      message: '退款成功',
      timestamp: new Date()
    }
  }
}

class WechatPayAdapter implements PaymentTarget {
  private wechatService: WechatPayService

  constructor(wechatService: WechatPayService) {
    this.wechatService = wechatService
  }

  processPayment(amount: number, currency: string): PaymentResult {
    const result = this.wechatService.createOrder(amount, currency)

    return {
      success: result.result_code === 'SUCCESS',
      transactionId: result.transaction_id,
      message: result.return_msg,
      timestamp: new Date(result.create_time),
      fee: result.total_fee
    }
  }

  getPaymentStatus(transactionId: string): PaymentStatus {
    const result = this.wechatService.queryPayment(transactionId)

    switch (result.trade_state) {
      case 'SUCCESS':
        return PaymentStatus.SUCCESS
      case 'REFUND':
      case 'CLOSED':
        return PaymentStatus.FAILED
      case 'NOTPAY':
        return PaymentStatus.PENDING
      default:
        return PaymentStatus.FAILED
    }
  }

  refund(transactionId: string, amount: number): RefundResult {
    // 简化实现
    return {
      success: true,
      refundId: \`wx_refund_\${Date.now()}\`,
      message: '退款成功',
      timestamp: new Date()
    }
  }
}

// 使用示例 - 统一接口处理不同的支付平台
function processUnifiedPayment(adapter: PaymentTarget, amount: number) {
  // 无论是支付宝还是微信，都使用相同的接口
  const result = adapter.processPayment(amount, 'CNY')
  console.log(\`支付结果: \${result.success ? '成功' : '失败'}\`)
  console.log(\`交易ID: \${result.transactionId}\`)
  console.log(\`手续费: \${result.fee}\`)

  // 查询状态
  const status = adapter.getPaymentStatus(result.transactionId)
  console.log(\`支付状态: \${status}\`)
}

// 创建不同的适配器
const alipayAdapter = new AlipayAdapter(new AlipayService())
const wechatAdapter = new WechatPayAdapter(new WechatPayService())

// 使用统一接口
processUnifiedPayment(alipayAdapter, 100)
processUnifiedPayment(wechatAdapter, 200)`
  },

  proxy: {
    id: 'proxy',
    title: '代理模式实现',
    code: `// 代理模式 (Proxy Pattern) - 图片加载代理
// 图片接口 - Subject
interface ImageInterface {
  display(): Promise<string>
  getUrl(): string
  getSize(): number
  getTitle(): string
  getStatus(): 'unloaded' | 'loading' | 'loaded' | 'error'
}

// 真实图片对象 - RealSubject
class RealImage implements ImageInterface {
  private url: string
  private title: string
  private size: number
  private status: 'unloaded' | 'loading' | 'loaded' | 'error'
  private loadingTime: number // 模拟加载时间(毫秒)
  private loadedContent: string | null

  constructor(url: string, title: string, size: number, loadingTime = 2000) {
    this.url = url
    this.title = title
    this.size = size // 图片大小(KB)
    this.status = 'unloaded'
    this.loadingTime = loadingTime
    this.loadedContent = null
  }

  async display(): Promise<string> {
    if (this.status === 'loaded' && this.loadedContent) {
      return this.loadedContent
    }

    this.status = 'loading'

    try {
      // 模拟网络请求延迟
      const content = await new Promise<string>((resolve, reject) => {
        setTimeout(() => {
          // 随机模拟失败的情况
          if (Math.random() < 0.1) {
            this.status = 'error'
            reject(new Error(\`Failed to load image: \${this.url}\`))
          } else {
            this.status = 'loaded'
            const content = \`Image content from \${this.url}\`
            this.loadedContent = content
            resolve(content)
          }
        }, this.loadingTime)
      })

      return content
    } catch (error) {
      this.status = 'error'
      throw error
    }
  }

  getUrl(): string {
    return this.url
  }

  getTitle(): string {
    return this.title
  }

  getSize(): number {
    return this.size
  }

  getStatus(): 'unloaded' | 'loading' | 'loaded' | 'error' {
    return this.status
  }
}

// 图片代理 - Proxy
class ImageProxy implements ImageInterface {
  private realImage: RealImage | null
  private url: string
  private title: string
  private size: number
  private status: 'unloaded' | 'loading' | 'loaded' | 'error'
  private cachedContent: string | null
  private loadingTime: number

  constructor(url: string, title: string, size: number, loadingTime = 2000) {
    this.realImage = null  // 延迟初始化
    this.url = url
    this.title = title
    this.size = size
    this.status = 'unloaded'
    this.cachedContent = null
    this.loadingTime = loadingTime
  }

  async display(): Promise<string> {
    // 已加载过且有缓存，直接返回缓存内容
    if (this.status === 'loaded' && this.cachedContent) {
      console.log(\`[Proxy] 返回缓存图片: \${this.url}\`)
      return this.cachedContent
    }

    // 如果未初始化真实对象，延迟创建
    if (!this.realImage) {
      console.log(\`[Proxy] 创建RealImage实例: \${this.url}\`)
      this.realImage = new RealImage(this.url, this.title, this.size, this.loadingTime)
    }

    try {
      this.status = 'loading'
      // 委托给真实对象
      const content = await this.realImage.display()
      this.status = 'loaded'
      this.cachedContent = content
      return content
    } catch (error) {
      this.status = 'error'
      throw error
    }
  }

  getUrl(): string {
    return this.url
  }

  getTitle(): string {
    return this.title
  }

  getSize(): number {
    return this.size
  }

  getStatus(): 'unloaded' | 'loading' | 'loaded' | 'error' {
    return this.status
  }
}

// 图片画廊 - 使用代理
class ImageGallery {
  private images: ImageProxy[]

  constructor() {
    this.images = []
  }

  addImage(url: string, title: string, size: number, loadingTime?: number): void {
    const image = new ImageProxy(url, title, size, loadingTime)
    this.images.push(image)
  }

  async loadImage(index: number): Promise<string> {
    if (index < 0 || index >= this.images.length) {
      throw new Error('图片索引超出范围')
    }

    try {
      const startTime = performance.now()
      const content = await this.images[index].display()
      const endTime = performance.now()

      console.log(\`加载图片 "\${this.images[index].getTitle()}" 耗时: \${(endTime - startTime).toFixed(2)}ms\`)
      return content
    } catch (error) {
      console.error(\`加载图片失败: \${error}\`)
      throw error
    }
  }
}

// 使用示例
async function demoProxyPattern() {
  const gallery = new ImageGallery()

  // 添加图片
  gallery.addImage('https://example.com/image1.jpg', '自然风景', 1240, 1500)
  gallery.addImage('https://example.com/image2.jpg', '城市夜景', 2100, 3000)

  console.log('=== 首次加载图片 ===')
  try {
    await gallery.loadImage(0)  // 延迟加载并创建RealImage
  } catch (error) {
    console.error(error)
  }

  console.log('=== 再次加载相同图片 ===')
  try {
    await gallery.loadImage(0)  // 使用缓存，无需重新加载
  } catch (error) {
    console.error(error)
  }

  console.log('=== 加载另一张图片 ===')
  try {
    await gallery.loadImage(1)  // 加载新图片
  } catch (error) {
    console.error(error)
  }
}

// 执行示例
demoProxyPattern().then(() => console.log('代理模式演示完成'))`
  },

  abstractFactory: {
    id: 'abstractFactory',
    title: '抽象工厂模式实现',
    code: `// 抽象工厂模式 (Abstract Factory Pattern) - UI组件库主题系统
// 抽象产品接口
interface Button {
  render(): string
  getStyle(): string
  getType(): string
}

interface Input {
  render(): string
  getStyle(): string
  getType(): string
}

interface Card {
  render(): string
  getStyle(): string
  getType(): string
}

// 现代风格产品族
class ModernButton implements Button {
  render(): string {
    return '<button class="modern-btn">现代按钮</button>'
  }

  getStyle(): string {
    return 'border-radius: 8px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 24px;'
  }

  getType(): string {
    return '现代风格按钮'
  }
}

class ModernInput implements Input {
  render(): string {
    return '<input class="modern-input" placeholder="现代输入框" />'
  }

  getStyle(): string {
    return 'border: 2px solid #e1e5e9; border-radius: 8px; padding: 12px 16px; background: #f8f9fa;'
  }

  getType(): string {
    return '现代风格输入框'
  }
}

class ModernCard implements Card {
  render(): string {
    return '<div class="modern-card">现代卡片内容</div>'
  }

  getStyle(): string {
    return 'background: white; border-radius: 12px; padding: 24px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);'
  }

  getType(): string {
    return '现代风格卡片'
  }
}

// 抽象工厂接口
interface UIComponentFactory {
  createButton(): Button
  createInput(): Input
  createCard(): Card
  getThemeName(): string
}

// 现代主题工厂
class ModernThemeFactory implements UIComponentFactory {
  createButton(): Button {
    return new ModernButton()
  }

  createInput(): Input {
    return new ModernInput()
  }

  createCard(): Card {
    return new ModernCard()
  }

  getThemeName(): string {
    return '现代主题'
  }
}

// 经典主题工厂
class ClassicThemeFactory implements UIComponentFactory {
  createButton(): Button {
    return new ClassicButton()
  }

  createInput(): Input {
    return new ClassicInput()
  }

  createCard(): Card {
    return new ClassicCard()
  }

  getThemeName(): string {
    return '经典主题'
  }
}

// 使用示例
const modernFactory = new ModernThemeFactory()
const modernButton = modernFactory.createButton()
const modernInput = modernFactory.createInput()
const modernCard = modernFactory.createCard()

console.log(\`主题: \${modernFactory.getThemeName()}\`)
console.log(\`按钮: \${modernButton.getType()}\`)
console.log(\`输入框: \${modernInput.getType()}\`)
console.log(\`卡片: \${modernCard.getType()}\`)`
  },

  builder: {
    id: 'builder',
    title: '建造者模式实现',
    code: `// 建造者模式 (Builder Pattern) - 电脑配置构建器
// 产品类
class Computer {
  private cpu: string = ''
  private memory: string = ''
  private storage: string = ''
  private graphics: string = ''
  private price: number = 0

  setCPU(cpu: string, price: number): void {
    this.cpu = cpu
    this.price += price
  }

  setMemory(memory: string, price: number): void {
    this.memory = memory
    this.price += price
  }

  setStorage(storage: string, price: number): void {
    this.storage = storage
    this.price += price
  }

  setGraphics(graphics: string, price: number): void {
    this.graphics = graphics
    this.price += price
  }

  getSpecifications(): string {
    return \`电脑配置:
- CPU: \${this.cpu}
- 内存: \${this.memory}
- 存储: \${this.storage}
- 显卡: \${this.graphics}
- 总价: ¥\${this.price.toLocaleString()}\`
  }

  getPerformanceLevel(): string {
    if (this.price >= 15000) return '高端配置'
    if (this.price >= 8000) return '中高端配置'
    if (this.price >= 4000) return '中端配置'
    return '入门配置'
  }
}

// 抽象建造者
interface ComputerBuilder {
  reset(): ComputerBuilder
  buildCPU(): ComputerBuilder
  buildMemory(): ComputerBuilder
  buildStorage(): ComputerBuilder
  buildGraphics(): ComputerBuilder
  getResult(): Computer
}

// 游戏电脑建造者
class GamingComputerBuilder implements ComputerBuilder {
  private computer: Computer

  constructor() {
    this.computer = new Computer()
  }

  reset(): ComputerBuilder {
    this.computer = new Computer()
    return this
  }

  buildCPU(): ComputerBuilder {
    this.computer.setCPU('Intel Core i7-13700K', 3200)
    return this
  }

  buildMemory(): ComputerBuilder {
    this.computer.setMemory('32GB DDR5-5600', 1200)
    return this
  }

  buildStorage(): ComputerBuilder {
    this.computer.setStorage('1TB NVMe SSD + 2TB HDD', 800)
    return this
  }

  buildGraphics(): ComputerBuilder {
    this.computer.setGraphics('NVIDIA RTX 4070 Ti', 6500)
    return this
  }

  getResult(): Computer {
    return this.computer
  }
}

// 指挥者
class ComputerDirector {
  private builder: ComputerBuilder

  constructor(builder: ComputerBuilder) {
    this.builder = builder
  }

  buildFullComputer(): Computer {
    return this.builder
      .reset()
      .buildCPU()
      .buildMemory()
      .buildStorage()
      .buildGraphics()
      .getResult()
  }
}

// 使用示例
const gamingBuilder = new GamingComputerBuilder()
const director = new ComputerDirector(gamingBuilder)
const gamingPC = director.buildFullComputer()

console.log(gamingPC.getSpecifications())
console.log(\`性能等级: \${gamingPC.getPerformanceLevel()}\`)`
  },

  prototype: {
    id: 'prototype',
    title: '原型模式实现',
    code: `// 原型模式 (Prototype Pattern) - 游戏角色克隆系统
// 原型接口
interface Prototype {
  clone(): Prototype
  getInfo(): string
}

// 装备类
class Equipment {
  constructor(
    public name: string,
    public type: string,
    public attack: number,
    public defense: number
  ) {}

  clone(): Equipment {
    return new Equipment(this.name, this.type, this.attack, this.defense)
  }

  getInfo(): string {
    return \`\${this.name} (\${this.type}) - 攻击:\${this.attack} 防御:\${this.defense}\`
  }
}

// 游戏角色类
class GameCharacter implements Prototype {
  private name: string
  private level: number
  private health: number
  private mana: number
  private equipment: Equipment[]

  constructor(name: string, level: number = 1) {
    this.name = name
    this.level = level
    this.health = level * 100
    this.mana = level * 50
    this.equipment = []
  }

  // 深度克隆
  clone(): GameCharacter {
    const cloned = new GameCharacter(this.name, this.level)
    cloned.health = this.health
    cloned.mana = this.mana
    // 克隆装备数组
    cloned.equipment = this.equipment.map(item => item.clone())
    return cloned
  }

  addEquipment(equipment: Equipment): void {
    this.equipment.push(equipment)
  }

  levelUp(): void {
    this.level++
    this.health += 100
    this.mana += 50
  }

  getInfo(): string {
    const equipmentInfo = this.equipment.map(eq => eq.getInfo()).join(', ')
    return \`角色: \${this.name} (等级\${this.level})
生命值: \${this.health}, 魔法值: \${this.mana}
装备: \${equipmentInfo || '无'}\`
  }
}

// 角色原型管理器
class CharacterPrototypeManager {
  private prototypes: Map<string, GameCharacter> = new Map()

  registerPrototype(key: string, prototype: GameCharacter): void {
    this.prototypes.set(key, prototype)
  }

  createCharacter(key: string): GameCharacter | null {
    const prototype = this.prototypes.get(key)
    return prototype ? prototype.clone() : null
  }

  getAvailableTypes(): string[] {
    return Array.from(this.prototypes.keys())
  }
}

// 使用示例
const manager = new CharacterPrototypeManager()

// 创建原型角色
const warriorPrototype = new GameCharacter('战士原型', 10)
warriorPrototype.addEquipment(new Equipment('钢铁剑', '武器', 50, 0))
warriorPrototype.addEquipment(new Equipment('钢铁盾', '防具', 0, 30))

const magePrototype = new GameCharacter('法师原型', 8)
magePrototype.addEquipment(new Equipment('魔法杖', '武器', 30, 0))
magePrototype.addEquipment(new Equipment('法师袍', '防具', 0, 15))

// 注册原型
manager.registerPrototype('warrior', warriorPrototype)
manager.registerPrototype('mage', magePrototype)

// 克隆创建新角色
const player1 = manager.createCharacter('warrior')
const player2 = manager.createCharacter('mage')

if (player1) {
  player1.levelUp()
  console.log('玩家1:')
  console.log(player1.getInfo())
}

if (player2) {
  console.log('\\n玩家2:')
  console.log(player2.getInfo())
}

console.log(\`\\n原型角色仍然保持原始状态:\`)
console.log(warriorPrototype.getInfo())`
  },

  bridge: {
    id: 'bridge',
    title: '桥接模式实现',
    code: `// 桥接模式 (Bridge Pattern) - 图形绘制系统
// 实现接口 - 绘制API
interface DrawingAPI {
  drawCircle(x: number, y: number, radius: number): string
  drawRectangle(x: number, y: number, width: number, height: number): string
  getAPIName(): string
}

// 具体实现 - Canvas API
class CanvasAPI implements DrawingAPI {
  drawCircle(x: number, y: number, radius: number): string {
    return \`Canvas: 在(\${x}, \${y})绘制半径为\${radius}的圆形\`
  }

  drawRectangle(x: number, y: number, width: number, height: number): string {
    return \`Canvas: 在(\${x}, \${y})绘制\${width}x\${height}的矩形\`
  }

  getAPIName(): string {
    return 'Canvas API'
  }
}

// 具体实现 - SVG API
class SVGAPI implements DrawingAPI {
  drawCircle(x: number, y: number, radius: number): string {
    return \`SVG: <circle cx="\${x}" cy="\${y}" r="\${radius}" />\`
  }

  drawRectangle(x: number, y: number, width: number, height: number): string {
    return \`SVG: <rect x="\${x}" y="\${y}" width="\${width}" height="\${height}" />\`
  }

  getAPIName(): string {
    return 'SVG API'
  }
}

// 抽象类 - 图形
abstract class Shape {
  protected drawingAPI: DrawingAPI

  constructor(drawingAPI: DrawingAPI) {
    this.drawingAPI = drawingAPI
  }

  abstract draw(): string
  abstract getShapeInfo(): string

  changeAPI(newAPI: DrawingAPI): void {
    this.drawingAPI = newAPI
  }
}

// 扩展抽象类 - 圆形
class Circle extends Shape {
  private x: number
  private y: number
  private radius: number

  constructor(x: number, y: number, radius: number, drawingAPI: DrawingAPI) {
    super(drawingAPI)
    this.x = x
    this.y = y
    this.radius = radius
  }

  draw(): string {
    return this.drawingAPI.drawCircle(this.x, this.y, this.radius)
  }

  getShapeInfo(): string {
    return \`圆形 - 中心(\${this.x}, \${this.y}), 半径\${this.radius}\`
  }
}

// 扩展抽象类 - 矩形
class Rectangle extends Shape {
  private x: number
  private y: number
  private width: number
  private height: number

  constructor(x: number, y: number, width: number, height: number, drawingAPI: DrawingAPI) {
    super(drawingAPI)
    this.x = x
    this.y = y
    this.width = width
    this.height = height
  }

  draw(): string {
    return this.drawingAPI.drawRectangle(this.x, this.y, this.width, this.height)
  }

  getShapeInfo(): string {
    return \`矩形 - 位置(\${this.x}, \${this.y}), 尺寸\${this.width}x\${this.height}\`
  }
}

// 使用示例
const canvasAPI = new CanvasAPI()
const svgAPI = new SVGAPI()

const circle = new Circle(10, 20, 5, canvasAPI)
const rectangle = new Rectangle(0, 0, 100, 50, svgAPI)

console.log('=== 使用不同API绘制图形 ===')
console.log(\`\${circle.getShapeInfo()} - 使用\${canvasAPI.getAPIName()}\`)
console.log(circle.draw())

console.log(\`\\n\${rectangle.getShapeInfo()} - 使用\${svgAPI.getAPIName()}\`)
console.log(rectangle.draw())

console.log('\\n=== 切换绘制API ===')
circle.changeAPI(svgAPI)
console.log(\`\${circle.getShapeInfo()} - 切换到\${svgAPI.getAPIName()}\`)
console.log(circle.draw())`
  },

  composite: {
    id: 'composite',
    title: '组合模式实现',
    code: `// 组合模式 (Composite Pattern) - 文件系统
// 组件接口
interface FileSystemComponent {
  getName(): string
  getSize(): number
  getType(): string
  display(indent?: string): string
}

// 叶子节点 - 文件
class File implements FileSystemComponent {
  private name: string
  private size: number
  private extension: string

  constructor(name: string, size: number) {
    this.name = name
    this.size = size
    this.extension = name.split('.').pop() || ''
  }

  getName(): string {
    return this.name
  }

  getSize(): number {
    return this.size
  }

  getType(): string {
    return 'file'
  }

  display(indent: string = ''): string {
    return \`\${indent}📄 \${this.name} (\${this.size}KB)\`
  }
}

// 组合节点 - 文件夹
class Directory implements FileSystemComponent {
  private name: string
  private children: FileSystemComponent[] = []

  constructor(name: string) {
    this.name = name
  }

  getName(): string {
    return this.name
  }

  getSize(): number {
    return this.children.reduce((total, child) => total + child.getSize(), 0)
  }

  getType(): string {
    return 'directory'
  }

  add(component: FileSystemComponent): void {
    this.children.push(component)
  }

  remove(component: FileSystemComponent): void {
    const index = this.children.indexOf(component)
    if (index > -1) {
      this.children.splice(index, 1)
    }
  }

  getChildren(): FileSystemComponent[] {
    return [...this.children]
  }

  display(indent: string = ''): string {
    let result = \`\${indent}📁 \${this.name}/ (\${this.getSize()}KB)\\n\`

    this.children.forEach((child, index) => {
      const isLast = index === this.children.length - 1
      const childIndent = indent + (isLast ? '  ' : '│ ')
      const prefix = isLast ? '└─' : '├─'
      result += \`\${indent}\${prefix} \${child.display().replace(/^\\s*/, '')}\\n\`

      if (child.getType() === 'directory') {
        const subResult = child.display(childIndent)
        const lines = subResult.split('\\n').slice(1, -1) // 移除第一行和最后一行空行
        result += lines.join('\\n') + (lines.length > 0 ? '\\n' : '')
      }
    })

    return result.trimEnd()
  }
}

// 使用示例
const root = new Directory('项目根目录')

// 创建源码目录
const src = new Directory('src')
src.add(new File('index.ts', 15))
src.add(new File('app.ts', 25))

const components = new Directory('components')
components.add(new File('Header.vue', 8))
components.add(new File('Footer.vue', 6))
src.add(components)

// 创建配置目录
const config = new Directory('config')
config.add(new File('webpack.config.js', 12))
config.add(new File('tsconfig.json', 3))

// 添加到根目录
root.add(src)
root.add(config)
root.add(new File('package.json', 5))
root.add(new File('README.md', 2))

console.log('=== 文件系统结构 ===')
console.log(root.display())
console.log(\`\\n总大小: \${root.getSize()}KB\`)`
  },

  facade: {
    id: 'facade',
    title: '外观模式实现',
    code: `// 外观模式 (Facade Pattern) - 智能家居控制系统
// 子系统 - 灯光控制
class LightingSystem {
  private lights: Map<string, boolean> = new Map()

  constructor() {
    this.lights.set('客厅', false)
    this.lights.set('卧室', false)
    this.lights.set('厨房', false)
  }

  turnOn(room: string): string {
    this.lights.set(room, true)
    return \`\${room}灯光已开启\`
  }

  turnOff(room: string): string {
    this.lights.set(room, false)
    return \`\${room}灯光已关闭\`
  }

  dimLights(room: string, level: number): string {
    return \`\${room}灯光调节至\${level}%亮度\`
  }

  getStatus(): string {
    const status = Array.from(this.lights.entries())
      .map(([room, isOn]) => \`\${room}: \${isOn ? '开启' : '关闭'}\`)
      .join(', ')
    return \`灯光状态 - \${status}\`
  }
}

// 子系统 - 空调控制
class AirConditioningSystem {
  private temperature: number = 25
  private isOn: boolean = false

  turnOn(): string {
    this.isOn = true
    return \`空调已开启，当前温度\${this.temperature}°C\`
  }

  turnOff(): string {
    this.isOn = false
    return '空调已关闭'
  }

  setTemperature(temp: number): string {
    this.temperature = temp
    return \`空调温度设置为\${temp}°C\`
  }

  getStatus(): string {
    return \`空调状态 - \${this.isOn ? '开启' : '关闭'}, 温度: \${this.temperature}°C\`
  }
}

// 子系统 - 音响控制
class AudioSystem {
  private volume: number = 50
  private isPlaying: boolean = false
  private currentTrack: string = ''

  play(track: string): string {
    this.isPlaying = true
    this.currentTrack = track
    return \`正在播放: \${track}\`
  }

  stop(): string {
    this.isPlaying = false
    this.currentTrack = ''
    return '音响已停止播放'
  }

  setVolume(volume: number): string {
    this.volume = Math.max(0, Math.min(100, volume))
    return \`音量设置为\${this.volume}%\`
  }

  getStatus(): string {
    return \`音响状态 - \${this.isPlaying ? \`播放中: \${this.currentTrack}\` : '停止'}, 音量: \${this.volume}%\`
  }
}

// 外观类 - 智能家居控制器
class SmartHomeFacade {
  private lighting: LightingSystem
  private airConditioning: AirConditioningSystem
  private audio: AudioSystem

  constructor() {
    this.lighting = new LightingSystem()
    this.airConditioning = new AirConditioningSystem()
    this.audio = new AudioSystem()
  }

  // 回家模式
  arriveHome(): string[] {
    const actions = [
      '=== 回家模式启动 ===',
      this.lighting.turnOn('客厅'),
      this.lighting.turnOn('卧室'),
      this.airConditioning.turnOn(),
      this.airConditioning.setTemperature(24),
      this.audio.play('轻松音乐'),
      this.audio.setVolume(30),
      '回家模式设置完成'
    ]
    return actions
  }

  // 离家模式
  leaveHome(): string[] {
    const actions = [
      '=== 离家模式启动 ===',
      this.lighting.turnOff('客厅'),
      this.lighting.turnOff('卧室'),
      this.lighting.turnOff('厨房'),
      this.airConditioning.turnOff(),
      this.audio.stop(),
      '离家模式设置完成'
    ]
    return actions
  }

  // 睡眠模式
  sleepMode(): string[] {
    const actions = [
      '=== 睡眠模式启动 ===',
      this.lighting.turnOff('客厅'),
      this.lighting.turnOff('厨房'),
      this.lighting.dimLights('卧室', 10),
      this.airConditioning.setTemperature(22),
      this.audio.play('白噪音'),
      this.audio.setVolume(15),
      '睡眠模式设置完成'
    ]
    return actions
  }

  // 获取系统状态
  getSystemStatus(): string[] {
    return [
      '=== 智能家居系统状态 ===',
      this.lighting.getStatus(),
      this.airConditioning.getStatus(),
      this.audio.getStatus()
    ]
  }
}

// 使用示例
const smartHome = new SmartHomeFacade()

console.log('初始状态:')
smartHome.getSystemStatus().forEach(status => console.log(status))

console.log('\\n执行回家模式:')
smartHome.arriveHome().forEach(action => console.log(action))

console.log('\\n当前状态:')
smartHome.getSystemStatus().forEach(status => console.log(status))

console.log('\\n执行睡眠模式:')
smartHome.sleepMode().forEach(action => console.log(action))`
  },

  flyweight: {
    id: 'flyweight',
    title: '享元模式实现',
    code: `// 享元模式 (Flyweight Pattern) - 文本编辑器字符渲染
// 享元接口
interface CharacterFlyweight {
  render(x: number, y: number, fontSize: number, color: string): string
}

// 具体享元 - 字符
class Character implements CharacterFlyweight {
  private char: string // 内部状态

  constructor(char: string) {
    this.char = char
  }

  render(x: number, y: number, fontSize: number, color: string): string {
    // 外部状态：位置、字体大小、颜色
    return \`渲染字符 '\${this.char}' 在位置(\${x}, \${y}), 字体大小:\${fontSize}px, 颜色:\${color}\`
  }

  getCharacter(): string {
    return this.char
  }
}

// 享元工厂
class CharacterFactory {
  private static flyweights: Map<string, Character> = new Map()
  private static createdCount: number = 0

  static getCharacter(char: string): Character {
    if (!this.flyweights.has(char)) {
      this.flyweights.set(char, new Character(char))
      this.createdCount++
      console.log(\`创建新的字符享元: '\${char}' (总计: \${this.createdCount})\`)
    }
    return this.flyweights.get(char)!
  }

  static getCreatedCount(): number {
    return this.createdCount
  }

  static getFlyweightCount(): number {
    return this.flyweights.size
  }

  static getAllCharacters(): string[] {
    return Array.from(this.flyweights.keys())
  }
}

// 上下文类 - 文档中的字符实例
class CharacterContext {
  private character: Character
  private x: number
  private y: number
  private fontSize: number
  private color: string

  constructor(char: string, x: number, y: number, fontSize: number, color: string) {
    this.character = CharacterFactory.getCharacter(char) // 获取享元
    this.x = x
    this.y = y
    this.fontSize = fontSize
    this.color = color
  }

  render(): string {
    return this.character.render(this.x, this.y, this.fontSize, this.color)
  }

  getPosition(): { x: number; y: number } {
    return { x: this.x, y: this.y }
  }

  setPosition(x: number, y: number): void {
    this.x = x
    this.y = y
  }

  getCharacter(): string {
    return this.character.getCharacter()
  }
}

// 文档类 - 客户端
class Document {
  private characters: CharacterContext[] = []

  addText(text: string, startX: number, startY: number, fontSize: number, color: string): void {
    let x = startX
    const y = startY

    for (const char of text) {
      if (char === ' ') {
        x += fontSize * 0.3 // 空格宽度
        continue
      }

      const context = new CharacterContext(char, x, y, fontSize, color)
      this.characters.push(context)
      x += fontSize * 0.6 // 字符间距
    }
  }

  render(): string[] {
    return this.characters.map(char => char.render())
  }

  getCharacterCount(): number {
    return this.characters.length
  }

  getUniqueCharacterCount(): number {
    const uniqueChars = new Set(this.characters.map(char => char.getCharacter()))
    return uniqueChars.size
  }

  getStatistics(): string {
    return \`文档统计:
- 总字符数: \${this.getCharacterCount()}
- 唯一字符数: \${this.getUniqueCharacterCount()}
- 享元对象数: \${CharacterFactory.getFlyweightCount()}
- 内存节省率: \${((this.getCharacterCount() - CharacterFactory.getFlyweightCount()) / this.getCharacterCount() * 100).toFixed(1)}%\`
  }
}

// 使用示例
const document = new Document()

console.log('=== 添加文本到文档 ===')
document.addText('Hello World!', 10, 20, 16, 'black')
document.addText('Hello Vue!', 10, 40, 14, 'blue')
document.addText('Design Patterns', 10, 60, 18, 'red')

console.log('\\n=== 文档统计信息 ===')
console.log(document.getStatistics())

console.log(\`\\n=== 创建的享元字符 ===\`)
console.log(\`字符集: [\${CharacterFactory.getAllCharacters().join(', ')}]\`)

console.log('\\n=== 渲染部分字符 ===')
const renderResults = document.render()
renderResults.slice(0, 5).forEach(result => console.log(result))
console.log(\`... 还有 \${renderResults.length - 5} 个字符\`)`
  },

  chainOfResponsibility: {
    id: 'chainOfResponsibility',
    title: '责任链模式实现',
    code: `// 责任链模式 (Chain of Responsibility Pattern) - 请假审批系统
// 请假请求
class LeaveRequest {
  constructor(
    public employeeName: string,
    public days: number,
    public reason: string,
    public type: 'sick' | 'personal' | 'annual' | 'emergency'
  ) {}

  getInfo(): string {
    return \`员工: \${this.employeeName}, 请假天数: \${this.days}天, 类型: \${this.type}, 原因: \${this.reason}\`
  }
}

// 抽象处理者
abstract class ApprovalHandler {
  protected nextHandler: ApprovalHandler | null = null

  setNext(handler: ApprovalHandler): ApprovalHandler {
    this.nextHandler = handler
    return handler
  }

  handle(request: LeaveRequest): string {
    if (this.canHandle(request)) {
      return this.processRequest(request)
    } else if (this.nextHandler) {
      return this.nextHandler.handle(request)
    } else {
      return \`请假申请被拒绝: \${request.getInfo()}\`
    }
  }

  protected abstract canHandle(request: LeaveRequest): boolean
  protected abstract processRequest(request: LeaveRequest): string
  protected abstract getHandlerName(): string
}

// 具体处理者 - 直接主管
class DirectSupervisor extends ApprovalHandler {
  protected canHandle(request: LeaveRequest): boolean {
    // 直接主管可以批准3天以内的病假和个人假
    return request.days <= 3 && (request.type === 'sick' || request.type === 'personal')
  }

  protected processRequest(request: LeaveRequest): string {
    return \`✅ \${this.getHandlerName()}批准了请假申请: \${request.getInfo()}\`
  }

  protected getHandlerName(): string {
    return '直接主管'
  }
}

// 具体处理者 - 部门经理
class DepartmentManager extends ApprovalHandler {
  protected canHandle(request: LeaveRequest): boolean {
    // 部门经理可以批准7天以内的所有类型假期
    return request.days <= 7
  }

  protected processRequest(request: LeaveRequest): string {
    return \`✅ \${this.getHandlerName()}批准了请假申请: \${request.getInfo()}\`
  }

  protected getHandlerName(): string {
    return '部门经理'
  }
}

// 具体处理者 - 人事总监
class HRDirector extends ApprovalHandler {
  protected canHandle(request: LeaveRequest): boolean {
    // 人事总监可以批准15天以内的假期
    return request.days <= 15
  }

  protected processRequest(request: LeaveRequest): string {
    return \`✅ \${this.getHandlerName()}批准了请假申请: \${request.getInfo()}\`
  }

  protected getHandlerName(): string {
    return '人事总监'
  }
}

// 具体处理者 - 总经理
class GeneralManager extends ApprovalHandler {
  protected canHandle(request: LeaveRequest): boolean {
    // 总经理可以批准任何假期，但超过30天需要特殊考虑
    return request.days <= 30
  }

  protected processRequest(request: LeaveRequest): string {
    if (request.days > 15) {
      return \`⚠️ \${this.getHandlerName()}批准了请假申请（需要特别关注）: \${request.getInfo()}\`
    }
    return \`✅ \${this.getHandlerName()}批准了请假申请: \${request.getInfo()}\`
  }

  protected getHandlerName(): string {
    return '总经理'
  }
}

// 审批链构建器
class ApprovalChainBuilder {
  static buildChain(): ApprovalHandler {
    const supervisor = new DirectSupervisor()
    const manager = new DepartmentManager()
    const hrDirector = new HRDirector()
    const generalManager = new GeneralManager()

    // 构建责任链
    supervisor.setNext(manager).setNext(hrDirector).setNext(generalManager)

    return supervisor
  }
}

// 使用示例
const approvalChain = ApprovalChainBuilder.buildChain()

const requests = [
  new LeaveRequest('张三', 2, '感冒发烧', 'sick'),
  new LeaveRequest('李四', 5, '家庭事务', 'personal'),
  new LeaveRequest('王五', 10, '年假旅游', 'annual'),
  new LeaveRequest('赵六', 20, '家人住院', 'emergency'),
  new LeaveRequest('钱七', 35, '长期休假', 'personal')
]

console.log('=== 请假审批系统 ===')
requests.forEach((request, index) => {
  console.log(\`\\n请求 \${index + 1}:\`)
  console.log(request.getInfo())
  console.log(approvalChain.handle(request))
})`
  },

  iterator: {
    id: 'iterator',
    title: '迭代器模式实现',
    code: `// 迭代器模式 (Iterator Pattern) - 播放列表管理系统
// 迭代器接口
interface Iterator<T> {
  hasNext(): boolean
  next(): T | null
  reset(): void
  current(): T | null
}

// 聚合对象接口
interface Iterable<T> {
  createIterator(): Iterator<T>
  getItems(): T[]
  getCount(): number
}

// 歌曲类
class Song {
  constructor(
    public id: string,
    public title: string,
    public artist: string,
    public duration: number,
    public genre: string
  ) {}

  getInfo(): string {
    const minutes = Math.floor(this.duration / 60)
    const seconds = this.duration % 60
    return \`\${this.title} - \${this.artist} (\${minutes}:\${seconds.toString().padStart(2, '0')})\`
  }
}

// 具体迭代器 - 顺序迭代器
class SequentialIterator implements Iterator<Song> {
  private position: number = 0
  private playlist: Playlist

  constructor(playlist: Playlist) {
    this.playlist = playlist
  }

  hasNext(): boolean {
    return this.position < this.playlist.getCount()
  }

  next(): Song | null {
    if (this.hasNext()) {
      const song = this.playlist.getItems()[this.position]
      this.position++
      return song
    }
    return null
  }

  reset(): void {
    this.position = 0
  }

  current(): Song | null {
    if (this.position > 0 && this.position <= this.playlist.getCount()) {
      return this.playlist.getItems()[this.position - 1]
    }
    return null
  }
}

// 具体聚合对象 - 播放列表
class Playlist implements Iterable<Song> {
  private songs: Song[] = []
  private name: string

  constructor(name: string) {
    this.name = name
  }

  addSong(song: Song): void {
    this.songs.push(song)
  }

  createIterator(): Iterator<Song> {
    return new SequentialIterator(this)
  }

  getItems(): Song[] {
    return [...this.songs]
  }

  getCount(): number {
    return this.songs.length
  }
}

// 使用示例
const playlist = new Playlist('我的播放列表')
playlist.addSong(new Song('1', 'Shape of You', 'Ed Sheeran', 233, 'Pop'))
playlist.addSong(new Song('2', 'Bohemian Rhapsody', 'Queen', 355, 'Rock'))

const iterator = playlist.createIterator()
while (iterator.hasNext()) {
  const song = iterator.next()
  console.log(song?.getInfo())
}`
  },

  templateMethod: {
    id: 'templateMethod',
    title: '模板方法模式实现',
    code: `// 模板方法模式 (Template Method Pattern) - 数据处理流程
// 抽象数据处理器
abstract class DataProcessor {
  // 模板方法 - 定义算法骨架
  public processData(data: any[]): ProcessResult {
    console.log('开始数据处理流程...')

    const result: ProcessResult = {
      originalCount: data.length,
      processedCount: 0,
      errors: [],
      processedData: [],
      processingTime: 0,
      processorType: this.getProcessorType()
    }

    const startTime = Date.now()

    try {
      // 步骤1: 验证数据
      if (!this.validateData(data)) {
        throw new Error('数据验证失败')
      }

      // 步骤2: 预处理数据
      const preprocessedData = this.preprocessData(data)

      // 步骤3: 处理核心逻辑（由子类实现）
      const processedData = this.processCore(preprocessedData)

      // 步骤4: 后处理数据
      const finalData = this.postprocessData(processedData)

      // 步骤5: 保存结果（可选，由子类决定是否实现）
      if (this.shouldSaveResult()) {
        this.saveResult(finalData)
      }

      result.processedData = finalData
      result.processedCount = finalData.length

    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : String(error))
    }

    result.processingTime = Date.now() - startTime
    return result
  }

  // 抽象方法 - 子类必须实现
  protected abstract processCore(data: any[]): any[]
  protected abstract getProcessorType(): string

  // 具体方法 - 提供默认实现，子类可以重写
  protected validateData(data: any[]): boolean {
    return Array.isArray(data) && data.length > 0
  }

  protected preprocessData(data: any[]): any[] {
    return data.filter(item => item != null)
  }

  protected postprocessData(data: any[]): any[] {
    return data
  }

  // 钩子方法 - 子类可以重写来改变行为
  protected shouldSaveResult(): boolean {
    return false
  }

  protected saveResult(data: any[]): void {
    console.log('保存处理结果...')
  }
}

// 具体处理器 - 用户数据处理器
class UserDataProcessor extends DataProcessor {
  protected processCore(data: any[]): any[] {
    return data.map(user => ({
      id: user.id,
      name: user.name?.trim(),
      email: user.email?.toLowerCase(),
      age: parseInt(user.age) || 0,
      status: 'processed'
    }))
  }

  protected getProcessorType(): string {
    return '用户数据处理器'
  }

  protected shouldSaveResult(): boolean {
    return true
  }
}

// 使用示例
const processor = new UserDataProcessor()
const userData = [
  { id: '1', name: '  Alice  ', email: '<EMAIL>', age: '28' },
  { id: '2', name: 'Bob', email: '<EMAIL>', age: '35' }
]

const result = processor.processData(userData)
console.log('处理结果:', result)`
  },

  memento: {
    id: 'memento',
    title: '备忘录模式实现',
    code: `// 备忘录模式 (Memento Pattern) - 文档编辑器撤销/重做系统
// 备忘录接口
interface Memento {
  getState(): any
  getTimestamp(): Date
  getDescription(): string
}

// 具体备忘录
class DocumentMemento implements Memento {
  private state: DocumentState
  private timestamp: Date
  private description: string

  constructor(state: DocumentState, description: string) {
    this.state = { ...state }
    this.timestamp = new Date()
    this.description = description
  }

  getState(): DocumentState {
    return { ...this.state }
  }

  getTimestamp(): Date {
    return this.timestamp
  }

  getDescription(): string {
    return this.description
  }
}

// 文档状态接口
interface DocumentState {
  content: string
  fontSize: number
  fontFamily: string
  textColor: string
  backgroundColor: string
}

// 原发器 - 文档编辑器
class DocumentEditor {
  private state: DocumentState = {
    content: '',
    fontSize: 14,
    fontFamily: 'Arial',
    textColor: '#000000',
    backgroundColor: '#ffffff'
  }

  // 创建备忘录
  createMemento(description: string): Memento {
    return new DocumentMemento(this.state, description)
  }

  // 恢复状态
  restoreFromMemento(memento: Memento): void {
    this.state = memento.getState()
  }

  // 文档操作方法
  setContent(content: string): void {
    this.state.content = content
  }

  getContent(): string {
    return this.state.content
  }

  setFontSize(size: number): void {
    this.state.fontSize = size
  }
}

// 管理者 - 历史记录管理器
class HistoryManager {
  private mementos: Memento[] = []
  private currentIndex: number = -1

  save(memento: Memento): void {
    // 如果当前不在历史记录的末尾，删除后面的记录
    if (this.currentIndex < this.mementos.length - 1) {
      this.mementos = this.mementos.slice(0, this.currentIndex + 1)
    }

    this.mementos.push(memento)
    this.currentIndex++
  }

  undo(): Memento | null {
    if (this.canUndo()) {
      this.currentIndex--
      return this.mementos[this.currentIndex]
    }
    return null
  }

  redo(): Memento | null {
    if (this.canRedo()) {
      this.currentIndex++
      return this.mementos[this.currentIndex]
    }
    return null
  }

  canUndo(): boolean {
    return this.currentIndex > 0
  }

  canRedo(): boolean {
    return this.currentIndex < this.mementos.length - 1
  }
}

// 使用示例
const editor = new DocumentEditor()
const history = new HistoryManager()

// 保存初始状态
history.save(editor.createMemento('初始状态'))

// 修改文档
editor.setContent('Hello World')
history.save(editor.createMemento('添加内容'))

editor.setFontSize(16)
history.save(editor.createMemento('修改字体大小'))

// 撤销操作
const undoMemento = history.undo()
if (undoMemento) {
  editor.restoreFromMemento(undoMemento)
  console.log('撤销后内容:', editor.getContent())
}`
  },

  mediator: {
    id: 'mediator',
    title: '中介者模式实现',
    code: `// 中介者模式 (Mediator Pattern) - 聊天室系统
// 中介者接口
interface ChatMediator {
  sendMessage(message: string, user: User): void
  addUser(user: User): void
  removeUser(user: User): void
}

// 抽象用户类
abstract class User {
  protected name: string
  protected mediator: ChatMediator

  constructor(name: string, mediator: ChatMediator) {
    this.name = name
    this.mediator = mediator
  }

  abstract send(message: string): void
  abstract receive(message: string, from: User): void

  getName(): string {
    return this.name
  }
}

// 具体用户 - 普通用户
class RegularUser extends User {
  send(message: string): void {
    console.log(\`\${this.name} 发送消息: \${message}\`)
    this.mediator.sendMessage(message, this)
  }

  receive(message: string, from: User): void {
    console.log(\`\${this.name} 收到来自 \${from.getName()} 的消息: \${message}\`)
  }
}

// 具体中介者 - 聊天室
class ChatRoom implements ChatMediator {
  private users: User[] = []

  sendMessage(message: string, sender: User): void {
    // 向所有其他用户发送消息
    this.users.forEach(user => {
      if (user !== sender) {
        user.receive(message, sender)
      }
    })
  }

  addUser(user: User): void {
    this.users.push(user)
    console.log(\`\${user.getName()} 加入了聊天室\`)
  }

  removeUser(user: User): void {
    const index = this.users.indexOf(user)
    if (index > -1) {
      this.users.splice(index, 1)
      console.log(\`\${user.getName()} 离开了聊天室\`)
    }
  }
}

// 使用示例
const chatRoom = new ChatRoom()

const alice = new RegularUser('Alice', chatRoom)
const bob = new RegularUser('Bob', chatRoom)
const charlie = new RegularUser('Charlie', chatRoom)

chatRoom.addUser(alice)
chatRoom.addUser(bob)
chatRoom.addUser(charlie)

alice.send('大家好！')
bob.send('你好，Alice！')
charlie.send('很高兴认识大家！')`
  },

  interpreter: {
    id: 'interpreter',
    title: '解释器模式实现',
    code: `// 解释器模式 (Interpreter Pattern) - 数学表达式计算器
// 上下文类
class Context {
  private variables: Map<string, number> = new Map()

  setVariable(name: string, value: number): void {
    this.variables.set(name, value)
  }

  getVariable(name: string): number {
    if (!this.variables.has(name)) {
      throw new Error(\`未定义的变量: \${name}\`)
    }
    return this.variables.get(name)!
  }
}

// 抽象表达式接口
interface Expression {
  interpret(context: Context): number
}

// 终结符表达式 - 数字
class NumberExpression implements Expression {
  private value: number

  constructor(value: number) {
    this.value = value
  }

  interpret(context: Context): number {
    return this.value
  }
}

// 终结符表达式 - 变量
class VariableExpression implements Expression {
  private name: string

  constructor(name: string) {
    this.name = name
  }

  interpret(context: Context): number {
    return context.getVariable(this.name)
  }
}

// 非终结符表达式 - 加法
class AddExpression implements Expression {
  private left: Expression
  private right: Expression

  constructor(left: Expression, right: Expression) {
    this.left = left
    this.right = right
  }

  interpret(context: Context): number {
    return this.left.interpret(context) + this.right.interpret(context)
  }
}

// 非终结符表达式 - 减法
class SubtractExpression implements Expression {
  private left: Expression
  private right: Expression

  constructor(left: Expression, right: Expression) {
    this.left = left
    this.right = right
  }

  interpret(context: Context): number {
    return this.left.interpret(context) - this.right.interpret(context)
  }
}

// 使用示例
const context = new Context()
context.setVariable('x', 10)
context.setVariable('y', 5)

// 构建表达式: x + y - 3
const expression = new SubtractExpression(
  new AddExpression(
    new VariableExpression('x'),
    new VariableExpression('y')
  ),
  new NumberExpression(3)
)

const result = expression.interpret(context)
console.log('表达式 x + y - 3 的结果:', result) // 输出: 12`
  },

  visitor: {
    id: 'visitor',
    title: '访问者模式实现',
    code: `// 访问者模式 (Visitor Pattern) - 文档处理系统
// 访问者接口
interface DocumentVisitor {
  visitTextElement(element: TextElement): void
  visitImageElement(element: ImageElement): void
}

// 元素接口
interface DocumentElement {
  accept(visitor: DocumentVisitor): void
}

// 具体元素 - 文本元素
class TextElement implements DocumentElement {
  private content: string

  constructor(content: string) {
    this.content = content
  }

  accept(visitor: DocumentVisitor): void {
    visitor.visitTextElement(this)
  }

  getContent(): string {
    return this.content
  }
}

// 具体元素 - 图片元素
class ImageElement implements DocumentElement {
  private src: string
  private width: number
  private height: number

  constructor(src: string, width: number, height: number) {
    this.src = src
    this.width = width
    this.height = height
  }

  accept(visitor: DocumentVisitor): void {
    visitor.visitImageElement(this)
  }

  getSrc(): string {
    return this.src
  }

  getWidth(): number {
    return this.width
  }

  getHeight(): number {
    return this.height
  }
}

// 具体访问者 - HTML导出器
class HTMLExportVisitor implements DocumentVisitor {
  private html: string = ''

  visitTextElement(element: TextElement): void {
    this.html += \`<p>\${element.getContent()}</p>\\n\`
  }

  visitImageElement(element: ImageElement): void {
    this.html += \`<img src="\${element.getSrc()}" width="\${element.getWidth()}" height="\${element.getHeight()}" />\\n\`
  }

  getHTML(): string {
    return this.html
  }
}

// 具体访问者 - Markdown导出器
class MarkdownExportVisitor implements DocumentVisitor {
  private markdown: string = ''

  visitTextElement(element: TextElement): void {
    this.markdown += \`\${element.getContent()}\\n\\n\`
  }

  visitImageElement(element: ImageElement): void {
    this.markdown += \`![Image](\${element.getSrc()})\\n\\n\`
  }

  getMarkdown(): string {
    return this.markdown
  }
}

// 文档类
class Document {
  private elements: DocumentElement[] = []

  addElement(element: DocumentElement): void {
    this.elements.push(element)
  }

  accept(visitor: DocumentVisitor): void {
    this.elements.forEach(element => element.accept(visitor))
  }
}

// 使用示例
const document = new Document()
document.addElement(new TextElement('这是一段文本'))
document.addElement(new ImageElement('/image.jpg', 300, 200))
document.addElement(new TextElement('这是另一段文本'))

// 导出为HTML
const htmlVisitor = new HTMLExportVisitor()
document.accept(htmlVisitor)
console.log('HTML输出:', htmlVisitor.getHTML())

// 导出为Markdown
const markdownVisitor = new MarkdownExportVisitor()
document.accept(markdownVisitor)
console.log('Markdown输出:', markdownVisitor.getMarkdown())`
  }
}
