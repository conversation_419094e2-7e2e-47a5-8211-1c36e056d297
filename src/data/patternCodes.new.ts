// 新的模块化结构 - 重构后的 patternCodes
// 这个文件将替换原来的 patternCodes.ts

// 重新导出新的模块化结构
export { patternCodes, type PatternCode, type PatternCodeRecord } from './patternCodes'

// 也可以按分类导出
export { 
  creationalPatterns, 
  structuralPatterns, 
  behavioralPatterns 
} from './patternCodes'

/*
重构说明：
1. 原来的 patternCodes.ts (2400+ 行) 已经分割为：
   - src/data/patternCodes/types.ts - 类型定义
   - src/data/patternCodes/creational/ - 创建型模式
   - src/data/patternCodes/structural/ - 结构型模式  
   - src/data/patternCodes/behavioral/ - 行为型模式
   - src/data/patternCodes/index.ts - 主入口

2. 优势：
   - 文件大小合理 (每个文件 < 300 行)
   - 按模式类型组织，便于维护
   - 支持按需导入
   - 保持向后兼容

3. 使用方式：
   // 导入所有模式 (向后兼容)
   import { patternCodes } from './patternCodes'
   
   // 按分类导入
   import { creationalPatterns } from './patternCodes'
   
   // 导入单个模式
   import { singleton } from './patternCodes/creational/singleton'
*/
