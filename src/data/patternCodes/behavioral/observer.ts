import type { PatternCode } from '../types'

export const observer: PatternCode = {
  id: 'observer',
  title: '观察者模式实现',
  code: `// 观察者模式 (Observer Pattern) - 股票价格通知
// 观察者接口
interface Observer {
  update(subject: Subject): void
  getName(): string
}

// 主题接口
interface Subject {
  attach(observer: Observer): void
  detach(observer: Observer): void
  notify(): void
}

// 具体主题 - 股票
class Stock implements Subject {
  private observers: Observer[] = []
  private symbol: string
  private price: number

  constructor(symbol: string, initialPrice: number) {
    this.symbol = symbol
    this.price = initialPrice
  }

  attach(observer: Observer): void {
    this.observers.push(observer)
    console.log(\`\${observer.getName()} 开始关注 \${this.symbol}\`)
  }

  detach(observer: Observer): void {
    const index = this.observers.indexOf(observer)
    if (index > -1) {
      this.observers.splice(index, 1)
      console.log(\`\${observer.getName()} 停止关注 \${this.symbol}\`)
    }
  }

  notify(): void {
    console.log(\`通知所有观察者：\${this.symbol} 价格变动\`)
    this.observers.forEach(observer => observer.update(this))
  }

  setPrice(price: number): void {
    console.log(\`\${this.symbol} 价格从 ¥\${this.price} 变更为 ¥\${price}\`)
    this.price = price
    this.notify()
  }

  getPrice(): number {
    return this.price
  }

  getSymbol(): string {
    return this.symbol
  }
}

// 具体观察者 - 投资者
class Investor implements Observer {
  private name: string

  constructor(name: string) {
    this.name = name
  }

  update(subject: Subject): void {
    if (subject instanceof Stock) {
      console.log(\`投资者 \${this.name} 收到通知：\${subject.getSymbol()} 当前价格 ¥\${subject.getPrice()}\`)
    }
  }

  getName(): string {
    return this.name
  }
}

// 使用示例
const appleStock = new Stock('AAPL', 150)

const investor1 = new Investor('张三')
const investor2 = new Investor('李四')
const investor3 = new Investor('王五')

// 投资者关注股票
appleStock.attach(investor1)
appleStock.attach(investor2)
appleStock.attach(investor3)

// 股票价格变动
appleStock.setPrice(155)
appleStock.setPrice(148)

// 投资者停止关注
appleStock.detach(investor2)
appleStock.setPrice(160)`
}
