import type { PatternCode } from '../types'

export const singleton: PatternCode = {
  id: 'singleton',
  title: '单例模式实现',
  code: `// 单例模式 (Singleton Pattern) - 日志记录器
class Logger {
  private static instance: Logger | null = null
  private logs: string[] = []
  private id: string

  // 私有构造函数，防止外部实例化
  private constructor() {
    this.id = Math.random().toString(36).slice(2, 11)
  }

  // 获取单例实例的静态方法
  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }

  // 记录日志
  public log(message: string): void {
    const timestamp = new Date().toLocaleTimeString()
    this.logs.push(\`[\${timestamp}] \${message}\`)
  }

  // 获取所有日志
  public getLogs(): string[] {
    return [...this.logs]
  }

  // 清空日志
  public clearLogs(): void {
    this.logs = []
  }

  // 获取实例ID
  public getId(): string {
    return this.id
  }
}

// 使用示例 - 验证单例特性
const logger1 = Logger.getInstance()
const logger2 = Logger.getInstance()

console.log(logger1 === logger2) // true - 同一个实例
console.log(logger1.getId() === logger2.getId()) // true - 相同ID

logger1.log('用户登录成功')
logger2.log('数据库连接建立')

console.log(logger1.getLogs()) // 两条日志都在同一个实例中`
}
