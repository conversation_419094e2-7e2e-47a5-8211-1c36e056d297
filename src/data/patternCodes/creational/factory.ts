import type { PatternCode } from '../types'

export const factory: PatternCode = {
  id: 'factory',
  title: '工厂模式实现',
  code: `// 工厂模式 (Factory Pattern) - 支付处理器
// 支付处理器接口
interface PaymentProcessor {
  process(amount: number): string
  getType(): string
  getProvider(): string
  getFeeRate(): number
}

// 具体支付处理器实现
class AlipayProcessor implements PaymentProcessor {
  process(amount: number): string {
    const fee = (amount * this.getFeeRate()) / 100
    return \`支付宝支付成功！金额：¥\${amount}，手续费：¥\${fee.toFixed(2)}\`
  }

  getType(): string {
    return '支付宝'
  }

  getProvider(): string {
    return '蚂蚁金服'
  }

  getFeeRate(): number {
    return 0.6
  }
}

class WechatProcessor implements PaymentProcessor {
  process(amount: number): string {
    const fee = (amount * this.getFeeRate()) / 100
    return \`微信支付成功！金额：¥\${amount}，手续费：¥\${fee.toFixed(2)}\`
  }

  getType(): string {
    return '微信支付'
  }

  getProvider(): string {
    return '腾讯'
  }

  getFeeRate(): number {
    return 0.6
  }
}

// 支付处理器工厂
class PaymentProcessorFactory {
  static createProcessor(type: string): PaymentProcessor {
    switch (type) {
      case 'alipay':
        return new AlipayProcessor()
      case 'wechat':
        return new WechatProcessor()
      default:
        throw new Error(\`不支持的支付类型: \${type}\`)
    }
  }

  static getSupportedTypes(): string[] {
    return ['alipay', 'wechat']
  }
}

// 使用示例
const alipayProcessor = PaymentProcessorFactory.createProcessor('alipay')
const wechatProcessor = PaymentProcessorFactory.createProcessor('wechat')

console.log(alipayProcessor.process(100))
console.log(wechatProcessor.process(200))`
}
