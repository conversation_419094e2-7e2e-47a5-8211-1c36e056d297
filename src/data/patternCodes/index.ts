import type { PatternCodeRecord } from './types'
import { creationalPatterns } from './creational'
import { structuralPatterns } from './structural'
import { behavioralPatterns } from './behavioral'

// 合并所有模式代码
export const patternCodes: PatternCodeRecord = {
  ...creationalPatterns,
  ...structuralPatterns,
  ...behavioralPatterns,
}

// 导出类型
export type { PatternCode, PatternCodeRecord } from './types'

// 导出分类
export { creationalPatterns, structuralPatterns, behavioralPatterns }
