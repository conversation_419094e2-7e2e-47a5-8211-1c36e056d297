import type { PatternCode } from '../types'

export const adapter: PatternCode = {
  id: 'adapter',
  title: '适配器模式实现',
  code: `// 适配器模式 (Adapter Pattern) - 第三方支付接口适配
// 目标接口 - 我们期望的统一支付接口
interface PaymentTarget {
  pay(amount: number): string
  getPaymentInfo(): string
}

// 被适配者 - 支付宝SDK (第三方接口)
class AlipaySDK {
  makePayment(money: number): boolean {
    console.log(\`支付宝支付：¥\${money}\`)
    return true
  }

  getTransactionDetails(): { platform: string; fee: number } {
    return { platform: '支付宝', fee: 0.6 }
  }
}

// 被适配者 - 微信支付SDK (第三方接口)
class WechatPaySDK {
  processPayment(cash: number): { success: boolean; transactionId: string } {
    console.log(\`微信支付：¥\${cash}\`)
    return { success: true, transactionId: 'wx_' + Date.now() }
  }

  getServiceInfo(): { name: string; rate: number } {
    return { name: '微信支付', rate: 0.6 }
  }
}

// 适配器 - 支付宝适配器
class AlipayAdapter implements PaymentTarget {
  private alipaySDK: AlipaySDK

  constructor(alipaySDK: AlipaySDK) {
    this.alipaySDK = alipaySDK
  }

  pay(amount: number): string {
    const success = this.alipaySDK.makePayment(amount)
    return success ? \`支付宝支付成功：¥\${amount}\` : '支付失败'
  }

  getPaymentInfo(): string {
    const details = this.alipaySDK.getTransactionDetails()
    return \`支付平台：\${details.platform}，手续费率：\${details.fee}%\`
  }
}

// 适配器 - 微信支付适配器
class WechatPayAdapter implements PaymentTarget {
  private wechatSDK: WechatPaySDK

  constructor(wechatSDK: WechatPaySDK) {
    this.wechatSDK = wechatSDK
  }

  pay(amount: number): string {
    const result = this.wechatSDK.processPayment(amount)
    return result.success 
      ? \`微信支付成功：¥\${amount}，交易号：\${result.transactionId}\`
      : '支付失败'
  }

  getPaymentInfo(): string {
    const info = this.wechatSDK.getServiceInfo()
    return \`支付平台：\${info.name}，手续费率：\${info.rate}%\`
  }
}

// 客户端代码 - 统一使用PaymentTarget接口
class PaymentProcessor {
  private paymentMethods: PaymentTarget[] = []

  addPaymentMethod(method: PaymentTarget): void {
    this.paymentMethods.push(method)
  }

  processPayments(amount: number): void {
    this.paymentMethods.forEach(method => {
      console.log(method.getPaymentInfo())
      console.log(method.pay(amount))
      console.log('---')
    })
  }
}

// 使用示例
const processor = new PaymentProcessor()

// 通过适配器使用第三方SDK
const alipaySDK = new AlipaySDK()
const wechatSDK = new WechatPaySDK()

const alipayAdapter = new AlipayAdapter(alipaySDK)
const wechatAdapter = new WechatPayAdapter(wechatSDK)

processor.addPaymentMethod(alipayAdapter)
processor.addPaymentMethod(wechatAdapter)

// 统一处理支付
processor.processPayments(100)`
}
