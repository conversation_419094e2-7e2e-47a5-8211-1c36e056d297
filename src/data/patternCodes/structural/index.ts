import { adapter } from './adapter'
// TODO: 添加其他结构型模式的导入
// import { bridge } from './bridge'
// import { composite } from './composite'
// import { decorator } from './decorator'
// import { facade } from './facade'
// import { flyweight } from './flyweight'
// import { proxy } from './proxy'

export const structuralPatterns = {
  adapter,
  // TODO: 添加其他结构型模式
  // bridge,
  // composite,
  // decorator,
  // facade,
  // flyweight,
  // proxy,
}
