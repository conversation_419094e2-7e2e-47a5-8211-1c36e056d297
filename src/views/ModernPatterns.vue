<template>
  <div class="modern-patterns">
    <header class="page-header">
      <div class="breadcrumb">
        <router-link to="/" class="breadcrumb-link">首页</router-link>
        <span class="breadcrumb-separator">></span>
        <span class="breadcrumb-current">现代扩展模式</span>
      </div>
      <div class="header-content">
        <h1 class="page-title">
          🌟 现代扩展设计模式
          <span class="page-subtitle"
            >15个现代模式，应对当今软件开发的新挑战</span
          >
        </h1>
      </div>
    </header>

    <div class="page-content">
      <nav class="pattern-nav">
        <h2 class="nav-title">现代模式分类</h2>
        <div class="pattern-categories">
          <div
            v-for="category in categories"
            :key="category.id"
            class="category"
          >
            <h3 class="category-title">
              <span class="category-icon">{{ category.icon }}</span>
              {{ category.name }}
            </h3>
            <ul class="pattern-list">
              <li
                v-for="pattern in category.patterns"
                :key="pattern.id"
                class="pattern-item"
                :class="{
                  active: activePattern === pattern.id,
                  disabled: pattern.status === 'pending',
                }"
                @click="setActivePattern(pattern.id)"
              >
                <div class="pattern-header">
                  <span class="pattern-name">{{ pattern.name }}</span>
                  <span class="pattern-status" :class="pattern.status">
                    {{ getStatusText(pattern.status) }}
                  </span>
                </div>
                <span class="pattern-desc">{{ pattern.description }}</span>
              </li>
            </ul>
          </div>
        </div>
      </nav>

      <main class="pattern-demo">
        <div
          v-if="getCurrentPattern().status === 'pending'"
          class="coming-soon"
        >
          <div class="coming-soon-content">
            <div class="coming-soon-icon">🚧</div>
            <h2 class="coming-soon-title">{{ getCurrentPattern().name }}</h2>
            <p class="coming-soon-subtitle">
              {{ getCurrentPattern().category }}
            </p>
            <div class="coming-soon-description">
              <p>{{ getCurrentPattern().description }}</p>
              <div class="pattern-details">
                <h4>核心问题：</h4>
                <p>{{ getCurrentPattern().problem }}</p>
                <h4>解决方案：</h4>
                <p>{{ getCurrentPattern().solution }}</p>
                <h4>应用场景：</h4>
                <ul>
                  <li
                    v-for="scenario in getCurrentPattern().scenarios"
                    :key="scenario"
                  >
                    {{ scenario }}
                  </li>
                </ul>
              </div>
              <div class="development-status">
                <p class="status-text">🔨 此模式正在开发中，敬请期待！</p>
                <div class="progress-info">
                  <span
                    >预计完成时间：{{ getCurrentPattern().estimatedDate }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="demo-header">
          <h2 class="demo-title">{{ getCurrentPattern().name }}</h2>
          <span class="demo-category">{{ getCurrentPattern().category }}</span>
        </div>

        <div
          v-if="getCurrentPattern().status === 'completed'"
          class="demo-content"
        >
          <component :is="getCurrentPattern().component" />
        </div>
      </main>

      <aside class="code-panel">
        <CodeDisplay
          v-if="getCurrentPattern().status === 'completed'"
          :title="getPatternCode.title"
          :code="getPatternCode.code"
        />
        <div v-else class="code-placeholder">
          <div class="placeholder-content">
            <div class="placeholder-icon">📝</div>
            <h4>代码示例</h4>
            <p>此模式的代码示例正在开发中</p>
            <div class="placeholder-info">
              <strong>预计完成时间：</strong>
              <span>{{ getCurrentPattern().estimatedDate }}</span>
            </div>
          </div>
        </div>
      </aside>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { modernPatternCodes } from "@/data/modernPatternCodes";
import CodeDisplay from "@/components/CodeDisplay.vue";
import ProducerConsumerDemo from "@/components/modern/ProducerConsumerDemo.vue";
import FuturePromiseDemo from "@/components/modern/FuturePromiseDemo.vue";
import MVCDemo from "@/components/modern/MVCDemo.vue";
import ReaderWriterLockDemo from "@/components/modern/ReaderWriterLockDemo.vue";
import ThreadPoolDemo from "@/components/modern/ThreadPoolDemo.vue";
import MVPDemo from "@/components/modern/MVPDemo.vue";
import MVVMDemo from "@/components/modern/MVVMDemo.vue";
import MicroservicesDemo from "@/components/modern/MicroservicesDemo.vue";
import FunctorDemo from "@/components/modern/FunctorDemo.vue";
import MonadDemo from "@/components/modern/MonadDemo.vue";
import CurryingDemo from "@/components/modern/CurryingDemo.vue";
import PublishSubscribeDemo from "@/components/modern/PublishSubscribeDemo.vue";
import EventSourcingDemo from "@/components/modern/EventSourcingDemo.vue";
import CQRSDemo from "@/components/modern/CQRSDemo.vue";

interface ModernPattern {
  id: string;
  name: string;
  description: string;
  category: string;
  status: "completed" | "pending";
  component?: any;
  problem: string;
  solution: string;
  scenarios: string[];
  estimatedDate: string;
}

interface PatternCategory {
  id: string;
  name: string;
  icon: string;
  patterns: ModernPattern[];
}

const categories: PatternCategory[] = [
  {
    id: "concurrency",
    name: "并发型模式",
    icon: "🔄",
    patterns: [
      {
        id: "producer-consumer",
        name: "生产者-消费者模式",
        description: "解决生产者和消费者速度不匹配问题",
        category: "并发型模式",
        status: "completed",
        component: ProducerConsumerDemo,
        problem: "生产数据的速度与消费数据的速度不匹配",
        solution: "使用缓冲区（队列）解耦生产者和消费者",
        scenarios: ["日志处理系统", "消息队列", "数据流处理", "批量任务处理"],
        estimatedDate: "2025-08-01",
      },
      {
        id: "reader-writer-lock",
        name: "读写锁模式",
        description: "允许多个读者同时访问，写者独占",
        category: "并发型模式",
        status: "completed",
        component: ReaderWriterLockDemo,
        problem: "多个线程同时访问共享资源时的性能优化",
        solution: "允许多个读者同时访问，但写者需要独占访问",
        scenarios: ["缓存系统", "配置管理", "数据库连接池", "共享数据结构"],
        estimatedDate: "2025-08-05",
      },
      {
        id: "thread-pool",
        name: "线程池模式",
        description: "重用线程，减少创建销毁开销",
        category: "并发型模式",
        status: "completed",
        component: ThreadPoolDemo,
        problem: "频繁创建和销毁线程的性能开销",
        solution: "预先创建固定数量的线程，重复使用",
        scenarios: [
          "Web服务器请求处理",
          "批量数据处理",
          "并行计算任务",
          "I/O密集型操作",
        ],
        estimatedDate: "2025-08-10",
      },
      {
        id: "future-promise",
        name: "Future/Promise模式",
        description: "异步计算结果的占位符",
        category: "并发型模式",
        status: "completed",
        component: FuturePromiseDemo,
        problem: "异步操作的结果处理和组合",
        solution: "提供异步计算结果的占位符和链式操作",
        scenarios: ["HTTP请求处理", "文件I/O操作", "数据库查询", "微服务调用"],
        estimatedDate: "2025-07-30",
      },
    ],
  },
  {
    id: "architectural",
    name: "架构型模式",
    icon: "🏗️",
    patterns: [
      {
        id: "mvc",
        name: "MVC模式",
        description: "分离数据、视图和控制逻辑",
        category: "架构型模式",
        status: "completed",
        component: MVCDemo,
        problem: "用户界面与业务逻辑的耦合",
        solution: "分离数据模型、视图展示和控制逻辑",
        scenarios: ["Web应用开发", "桌面应用程序", "移动应用开发", "游戏开发"],
        estimatedDate: "2025-08-02",
      },
      {
        id: "mvp",
        name: "MVP模式",
        description: "MVC的变体，Presenter处理UI逻辑",
        category: "架构型模式",
        status: "completed",
        component: MVPDemo,
        problem: "MVC中View和Model的直接依赖",
        solution: "Presenter作为中介，View完全被动",
        scenarios: [
          "Android应用开发",
          "WinForms应用",
          "单元测试友好的架构",
          "复杂UI逻辑处理",
        ],
        estimatedDate: "2025-08-08",
      },
      {
        id: "mvvm",
        name: "MVVM模式",
        description: "数据绑定驱动的架构模式",
        category: "架构型模式",
        status: "completed",
        component: MVVMDemo,
        problem: "数据绑定和UI状态管理",
        solution: "ViewModel提供数据绑定和命令绑定",
        scenarios: [
          "WPF应用开发",
          "Vue.js/Angular应用",
          "响应式UI开发",
          "数据驱动的界面",
        ],
        estimatedDate: "2025-08-12",
      },
      {
        id: "microservices",
        name: "微服务模式",
        description: "将应用拆分为独立的小服务",
        category: "架构型模式",
        status: "completed",
        component: MicroservicesDemo,
        problem: "单体应用的可扩展性和维护性",
        solution: "将应用拆分为独立部署的小服务",
        scenarios: [
          "大型分布式系统",
          "云原生应用",
          "高可用系统",
          "团队协作开发",
        ],
        estimatedDate: "2025-08-20",
      },
    ],
  },
  {
    id: "functional",
    name: "函数式模式",
    icon: "⚡",
    patterns: [
      {
        id: "functor",
        name: "函子模式",
        description: "可以被映射的容器",
        category: "函数式模式",
        status: "completed",
        component: FunctorDemo,
        problem: "在保持结构的同时转换容器中的值",
        solution: "提供map操作，将函数应用到容器中的值",
        scenarios: ["数据转换管道", "错误处理", "异步操作链", "集合操作"],
        estimatedDate: "2025-08-06",
      },
      {
        id: "monad",
        name: "单子模式",
        description: "处理副作用的函数式结构",
        category: "函数式模式",
        status: "completed",
        component: MonadDemo,
        problem: "函数式编程中的副作用处理",
        solution: "提供flatMap操作，处理嵌套的容器结构",
        scenarios: ["错误处理链", "异步操作组合", "状态管理", "I/O操作"],
        estimatedDate: "2025-08-15",
      },
      {
        id: "currying",
        name: "柯里化模式",
        description: "将多参数函数转换为单参数函数序列",
        category: "函数式模式",
        status: "completed",
        component: CurryingDemo,
        problem: "函数参数的部分应用和复用",
        solution: "将多参数函数转换为单参数函数的序列",
        scenarios: ["函数组合", "参数预设", "事件处理", "配置函数"],
        estimatedDate: "2025-08-04",
      },
    ],
  },
  {
    id: "reactive",
    name: "响应式模式",
    icon: "📡",
    patterns: [
      {
        id: "publish-subscribe",
        name: "发布-订阅模式",
        description: "消息传递的解耦模式",
        category: "响应式模式",
        status: "completed",
        component: PublishSubscribeDemo,
        problem: "组件间的松耦合通信",
        solution: "通过消息代理实现发布者和订阅者的解耦",
        scenarios: ["事件系统", "消息队列", "状态管理", "微服务通信"],
        estimatedDate: "2025-07-31",
      },
      {
        id: "event-sourcing",
        name: "事件溯源模式",
        description: "通过事件序列重建状态",
        category: "响应式模式",
        status: "completed",
        component: EventSourcingDemo,
        problem: "数据状态的历史追踪和重建",
        solution: "存储事件序列而不是当前状态",
        scenarios: ["审计系统", "版本控制", "数据恢复", "分析系统"],
        estimatedDate: "2025-08-18",
      },
      {
        id: "cqrs",
        name: "CQRS模式",
        description: "读写分离的架构模式",
        category: "响应式模式",
        status: "completed",
        component: CQRSDemo,
        problem: "读写操作的不同优化需求",
        solution: "分离命令（写）和查询（读）的数据模型",
        scenarios: [
          "高性能系统",
          "复杂业务逻辑",
          "数据分析平台",
          "事件驱动架构",
        ],
        estimatedDate: "2025-08-25",
      },
    ],
  },
];

const activePattern = ref<string>("producer-consumer");

const setActivePattern = (patternId: string) => {
  const pattern = getAllPatterns().find((p) => p.id === patternId);
  if (pattern && pattern.status !== "pending") {
    activePattern.value = patternId;
  } else {
    activePattern.value = patternId; // 允许查看待开发的模式
  }
};

const getAllPatterns = (): ModernPattern[] => {
  return categories.flatMap((category) => category.patterns);
};

const getCurrentPattern = (): ModernPattern => {
  return (
    getAllPatterns().find((p) => p.id === activePattern.value) ||
    getAllPatterns()[0]
  );
};

const getStatusText = (status: string): string => {
  return status === "completed" ? "已完成" : "开发中";
};

const getPatternCode = computed(() => {
  const pattern = getCurrentPattern();
  return (
    modernPatternCodes[pattern.id] || {
      title: "代码示例",
      code: "// 代码示例开发中...",
    }
  );
});
</script>

<style lang="scss" scoped>
.modern-patterns {
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
}

.page-header {
  position: relative;
  padding: 0.8rem 2rem;
  color: white;
  flex-shrink: 0;

  .breadcrumb {
    position: absolute;
    top: 0.5rem;
    left: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    opacity: 0.9;

    @media (max-width: 768px) {
      position: static;
      justify-content: center;
      margin-bottom: 0.5rem;
    }

    .breadcrumb-link {
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        color: white;
      }
    }

    .breadcrumb-separator {
      opacity: 0.6;
    }

    .breadcrumb-current {
      color: #ffd700;
      font-weight: 500;
    }
  }

  .header-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;

    .page-title {
      font-size: 2rem;
      margin: 0;
      font-weight: 700;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.2rem;

      .page-subtitle {
        font-size: 0.9rem;
        font-weight: 400;
        opacity: 0.9;
        margin: 0;
      }
    }
  }
}

.page-content {
  display: grid;
  grid-template-columns: 280px 1fr 800px;
  gap: 2rem;
  max-width: none;
  margin: 0 auto;
  padding: 0 50px 50px 50px;
  width: 100%;
  flex: 1;
  overflow: hidden;

  @media (max-width: 1800px) {
    grid-template-columns: 260px 1fr 750px;
    gap: 1.8rem;
  }

  @media (max-width: 1600px) {
    grid-template-columns: 260px 1fr 700px;
    gap: 1.8rem;
  }

  @media (max-width: 1400px) {
    grid-template-columns: 260px 1fr 600px;
    gap: 1.5rem;
  }

  @media (max-width: 1200px) {
    grid-template-columns: 260px 1fr;
    gap: 1.5rem;

    .code-panel {
      display: none;
    }
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 1rem 1rem 1rem;
  }
}

.pattern-nav {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  min-height: 0;

  .nav-title {
    margin: 0 0 1.5rem 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
    flex-shrink: 0;
  }
}

.category {
  margin-bottom: 1.5rem;

  .category-title {
    font-size: 1rem;
    color: #333;
    margin: 0 0 0.8rem 0;
    font-weight: 600;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .category-icon {
      font-size: 1.2rem;
    }
  }
}

.pattern-list {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  overflow-y: auto;
}

.pattern-item {
  padding: 0.8rem;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover:not(.disabled) {
    background: #f8f9ff;
    border-color: #e0e7ff;
  }

  &.active {
    background: linear-gradient(135deg, #1890ff, #722ed1);
    color: white;
    border-color: #1890ff;

    .pattern-desc {
      color: rgba(255, 255, 255, 0.9);
    }

    .pattern-status {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }
  }

  &.disabled {
    opacity: 0.7;
    cursor: default;
  }

  .pattern-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.3rem;
    gap: 0.5rem;
  }

  .pattern-name {
    font-weight: 600;
    flex: 1;
  }

  .pattern-status {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    background: #f0f0f0;
    color: #666;
    white-space: nowrap;

    &.completed {
      background: #f6ffed;
      color: #52c41a;
    }

    &.pending {
      background: #fff7e6;
      color: #fa8c16;
    }
  }

  .pattern-desc {
    display: block;
    font-size: 0.85rem;
    color: #666;
    line-height: 1.4;
  }
}

.pattern-demo {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;

  .demo-header {
    background: linear-gradient(135deg, #1890ff, #722ed1);
    color: white;
    padding: 1rem 2rem;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .demo-title {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 600;
    }

    .demo-category {
      background: rgba(255, 255, 255, 0.2);
      padding: 0.3rem 0.8rem;
      border-radius: 20px;
      font-size: 0.85rem;
      font-weight: 500;
    }
  }

  .demo-content {
    padding: 2rem;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }
}

.coming-soon {
  padding: 2rem;
  text-align: center;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  .coming-soon-content {
    max-width: 600px;

    .coming-soon-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    .coming-soon-title {
      font-size: 2rem;
      color: #333;
      margin: 0 0 0.5rem 0;
    }

    .coming-soon-subtitle {
      color: #666;
      margin: 0 0 1.5rem 0;
      font-size: 1.1rem;
    }

    .coming-soon-description {
      text-align: left;

      > p {
        color: #555;
        line-height: 1.6;
        margin-bottom: 1.5rem;
      }

      .pattern-details {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;

        h4 {
          color: #333;
          margin: 0 0 0.5rem 0;
          font-size: 1rem;
        }

        p {
          color: #555;
          margin: 0 0 1rem 0;
          line-height: 1.5;
        }

        ul {
          margin: 0;
          padding-left: 1.2rem;

          li {
            color: #555;
            margin-bottom: 0.3rem;
          }
        }
      }

      .development-status {
        background: linear-gradient(135deg, #fff7e6, #ffffff);
        border: 1px solid #ffd591;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;

        .status-text {
          color: #fa8c16;
          font-weight: 600;
          margin: 0 0 0.5rem 0;
        }

        .progress-info {
          color: #666;
          font-size: 0.9rem;
        }
      }
    }
  }
}

.code-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;

  .code-placeholder {
    padding: 2rem;
    text-align: center;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    .placeholder-content {
      max-width: 250px;

      .placeholder-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
      }

      h4 {
        margin: 0 0 0.5rem 0;
        color: #333;
        font-size: 1.2rem;
      }

      p {
        margin: 0 0 1rem 0;
        color: #666;
        line-height: 1.5;
      }

      .placeholder-info {
        background: #f8f9fa;
        padding: 0.8rem;
        border-radius: 6px;
        font-size: 0.85rem;

        strong {
          color: #333;
          display: block;
          margin-bottom: 0.3rem;
        }

        span {
          color: #666;
        }
      }
    }
  }
}
</style>
