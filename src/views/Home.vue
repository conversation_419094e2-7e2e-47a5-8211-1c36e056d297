<template>
  <div class="home">
    <header class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">🎨 设计模式学习平台</h1>
        <p class="hero-subtitle">
          从经典GoF模式到现代扩展模式，全面掌握软件设计的精髓
        </p>
        <div class="hero-stats">
          <div class="stat-item">
            <span class="stat-number">23</span>
            <span class="stat-label">经典GoF模式</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">15</span>
            <span class="stat-label">现代扩展模式</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">38</span>
            <span class="stat-label">总计模式</span>
          </div>
        </div>
      </div>
    </header>

    <main class="main-content">
      <div class="pattern-categories">
        <div class="category-card classic" @click="navigateTo('/classic-patterns')">
          <div class="card-icon">📚</div>
          <h2 class="card-title">经典GoF设计模式</h2>
          <p class="card-description">
            23个经典模式，软件设计的基础。包含创建型、结构型、行为型三大类别，
            是每个开发者必须掌握的核心知识。
          </p>
          <div class="card-features">
            <div class="feature-item">✅ 创建型模式 (5个)</div>
            <div class="feature-item">✅ 结构型模式 (7个)</div>
            <div class="feature-item">✅ 行为型模式 (11个)</div>
          </div>
          <div class="card-status completed">
            <span class="status-text">已完成 100%</span>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 100%"></div>
            </div>
          </div>
          <button class="card-button">开始学习 →</button>
        </div>

        <div class="category-card modern" @click="navigateTo('/modern-patterns')">
          <div class="card-icon">🌟</div>
          <h2 class="card-title">现代扩展设计模式</h2>
          <p class="card-description">
            15个现代模式，应对当今软件开发的新挑战。涵盖并发、架构、函数式、
            响应式四大领域的前沿模式。
          </p>
          <div class="card-features">
            <div class="feature-item">🔄 并发型模式 (4个)</div>
            <div class="feature-item">🏗️ 架构型模式 (4个)</div>
            <div class="feature-item">⚡ 函数式模式 (3个)</div>
            <div class="feature-item">📡 响应式模式 (4个)</div>
          </div>
          <div class="card-status in-progress">
            <span class="status-text">开发中 0%</span>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 0%"></div>
            </div>
          </div>
          <button class="card-button">探索更多 →</button>
        </div>
      </div>

      <section class="features-section">
        <h2 class="section-title">🚀 平台特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">💻</div>
            <h3>TypeScript实现</h3>
            <p>完整的类型安全代码实现，便于理解和学习</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎮</div>
            <h3>交互式演示</h3>
            <p>每个模式都有可操作的Vue演示组件</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📊</div>
            <h3>架构图解</h3>
            <p>丰富的Mermaid图表和详细的技术分析</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🌍</div>
            <h3>真实场景</h3>
            <p>结合实际业务场景的应用案例</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📚</div>
            <h3>渐进学习</h3>
            <p>从简单到复杂的学习路径设计</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔧</div>
            <h3>模块化设计</h3>
            <p>清晰的代码结构，便于维护和扩展</p>
          </div>
        </div>
      </section>

      <section class="learning-path">
        <h2 class="section-title">📈 学习路径建议</h2>
        <div class="path-container">
          <div class="path-step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3>掌握经典GoF模式</h3>
              <p>从23个经典模式开始，建立扎实的设计模式基础</p>
            </div>
          </div>
          <div class="path-arrow">→</div>
          <div class="path-step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3>学习实用现代模式</h3>
              <p>Promise、发布-订阅、MVC等日常开发常用模式</p>
            </div>
          </div>
          <div class="path-arrow">→</div>
          <div class="path-step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3>深入专业领域</h3>
              <p>并发、函数式、响应式等专业领域的高级模式</p>
            </div>
          </div>
        </div>
      </section>
    </main>

    <footer class="footer">
      <p>&copy; 2025 设计模式学习平台 - 让设计模式学习更简单</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-section {
  padding: 4rem 2rem;
  text-align: center;
  color: white;
  
  .hero-content {
    max-width: 800px;
    margin: 0 auto;
    
    .hero-title {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 1rem;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      
      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }
    
    .hero-subtitle {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
      line-height: 1.6;
    }
    
    .hero-stats {
      display: flex;
      justify-content: center;
      gap: 2rem;
      margin-top: 2rem;
      
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 1rem;
      }
      
      .stat-item {
        text-align: center;
        
        .stat-number {
          display: block;
          font-size: 2.5rem;
          font-weight: 700;
          color: #ffd700;
        }
        
        .stat-label {
          font-size: 0.9rem;
          opacity: 0.8;
        }
      }
    }
  }
}

.main-content {
  background: white;
  padding: 4rem 2rem;
}

.pattern-categories {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 4rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
  
  .category-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    
    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
    }
    
    &.classic {
      border-left: 4px solid #52c41a;
      
      &:hover {
        border-color: #52c41a;
      }
    }
    
    &.modern {
      border-left: 4px solid #1890ff;
      
      &:hover {
        border-color: #1890ff;
      }
    }
    
    .card-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }
    
    .card-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 1rem;
    }
    
    .card-description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }
    
    .card-features {
      margin-bottom: 1.5rem;
      
      .feature-item {
        font-size: 0.9rem;
        color: #555;
        margin-bottom: 0.5rem;
      }
    }
    
    .card-status {
      margin-bottom: 1.5rem;
      
      .status-text {
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
        display: block;
      }
      
      &.completed .status-text {
        color: #52c41a;
      }
      
      &.in-progress .status-text {
        color: #1890ff;
      }
      
      .progress-bar {
        height: 4px;
        background: #f0f0f0;
        border-radius: 2px;
        overflow: hidden;
        
        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #52c41a, #389e0d);
          transition: width 0.3s ease;
        }
      }
    }
    
    .card-button {
      width: 100%;
      padding: 0.8rem;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }
    }
  }
}

.features-section, .learning-path {
  max-width: 1200px;
  margin: 0 auto 4rem auto;
  
  .section-title {
    text-align: center;
    font-size: 2rem;
    color: #333;
    margin-bottom: 2rem;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  
  .feature-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    text-align: center;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-4px);
    }
    
    .feature-icon {
      font-size: 2rem;
      margin-bottom: 1rem;
    }
    
    h3 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    p {
      color: #666;
      font-size: 0.9rem;
      line-height: 1.5;
    }
  }
}

.path-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
  
  .path-step {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    flex: 1;
    max-width: 300px;
    
    .step-number {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      flex-shrink: 0;
    }
    
    .step-content {
      h3 {
        color: #333;
        margin-bottom: 0.5rem;
        font-size: 1rem;
      }
      
      p {
        color: #666;
        font-size: 0.85rem;
        line-height: 1.4;
        margin: 0;
      }
    }
  }
  
  .path-arrow {
    font-size: 1.5rem;
    color: #667eea;
    font-weight: bold;
    
    @media (max-width: 768px) {
      transform: rotate(90deg);
    }
  }
}

.footer {
  background: #333;
  color: white;
  text-align: center;
  padding: 2rem;
  
  p {
    margin: 0;
    opacity: 0.8;
  }
}
</style>
