<script setup lang="ts">
import { ref, computed } from "vue";
import SingletonDemo from "../components/patterns/SingletonDemo.vue";
import FactoryDemo from "../components/patterns/FactoryDemo.vue";
import AbstractFactoryDemo from "../components/patterns/AbstractFactoryDemo.vue";
import BuilderDemo from "../components/patterns/BuilderDemo.vue";
import PrototypeDemo from "../components/patterns/PrototypeDemo.vue";
import ObserverDemo from "../components/patterns/ObserverDemo.vue";
import StrategyDemo from "../components/patterns/StrategyDemo.vue";
import DecoratorDemo from "../components/patterns/DecoratorDemo.vue";
import AdapterDemo from "../components/patterns/AdapterDemo.vue";
import BridgeDemo from "../components/patterns/BridgeDemo.vue";
import CompositeDemo from "../components/patterns/CompositeDemo.vue";
import FacadeDemo from "../components/patterns/FacadeDemo.vue";
import FlyweightDemo from "../components/patterns/FlyweightDemo.vue";
import ProxyDemo from "../components/patterns/ProxyDemo.vue";
import StateDemo from "../components/patterns/StateDemo.vue";
import CommandDemo from "../components/patterns/CommandDemo.vue";
import ChainOfResponsibilityDemo from "../components/patterns/ChainOfResponsibilityDemo.vue";
import IteratorDemo from "../components/patterns/IteratorDemo.vue";
import TemplateMethodDemo from "../components/patterns/TemplateMethodDemo.vue";
import MementoDemo from "../components/patterns/MementoDemo.vue";
import MediatorDemo from "../components/patterns/MediatorDemo.vue";
import InterpreterDemo from "../components/patterns/InterpreterDemo.vue";
import VisitorDemo from "../components/patterns/VisitorDemo.vue";
import CodeDisplay from "../components/CodeDisplay.vue";
import { patternCodes } from "../data/patternCodes";

interface Pattern {
  id: string;
  name: string;
  description: string;
  category: string;
  component: any;
}

const patterns: Pattern[] = [
  {
    id: "singleton",
    name: "单例模式 (Singleton)",
    description: "确保一个类只有一个实例，并提供全局访问点",
    category: "创建型模式",
    component: SingletonDemo,
  },
  {
    id: "factory",
    name: "工厂模式 (Factory)",
    description: "创建对象而不指定其具体类",
    category: "创建型模式",
    component: FactoryDemo,
  },
  {
    id: "abstractFactory",
    name: "抽象工厂模式 (Abstract Factory)",
    description: "提供一个创建一系列相关或相互依赖对象的接口",
    category: "创建型模式",
    component: AbstractFactoryDemo,
  },
  {
    id: "builder",
    name: "建造者模式 (Builder)",
    description: "将复杂对象的构建与表示分离",
    category: "创建型模式",
    component: BuilderDemo,
  },
  {
    id: "prototype",
    name: "原型模式 (Prototype)",
    description: "通过复制现有实例来创建新对象",
    category: "创建型模式",
    component: PrototypeDemo,
  },
  {
    id: "observer",
    name: "观察者模式 (Observer)",
    description: "定义对象间的一对多依赖关系",
    category: "行为型模式",
    component: ObserverDemo,
  },
  {
    id: "strategy",
    name: "策略模式 (Strategy)",
    description: "定义算法族，分别封装起来，让它们之间可以互相替换",
    category: "行为型模式",
    component: StrategyDemo,
  },
  {
    id: "decorator",
    name: "装饰器模式 (Decorator)",
    description: "动态地给对象添加新功能",
    category: "结构型模式",
    component: DecoratorDemo,
  },
  {
    id: "adapter",
    name: "适配器模式 (Adapter)",
    description: "使接口不兼容的类可以一起工作",
    category: "结构型模式",
    component: AdapterDemo,
  },
  {
    id: "bridge",
    name: "桥接模式 (Bridge)",
    description: "将抽象与实现分离，使它们可以独立变化",
    category: "结构型模式",
    component: BridgeDemo,
  },
  {
    id: "composite",
    name: "组合模式 (Composite)",
    description: "将对象组合成树形结构以表示部分-整体的层次结构",
    category: "结构型模式",
    component: CompositeDemo,
  },
  {
    id: "facade",
    name: "外观模式 (Facade)",
    description: "为复杂子系统提供一个简单的接口",
    category: "结构型模式",
    component: FacadeDemo,
  },
  {
    id: "flyweight",
    name: "享元模式 (Flyweight)",
    description: "通过共享技术有效地支持大量细粒度对象",
    category: "结构型模式",
    component: FlyweightDemo,
  },
  {
    id: "proxy",
    name: "代理模式 (Proxy)",
    description: "为其他对象提供代理控制访问",
    category: "结构型模式",
    component: ProxyDemo,
  },

  {
    id: "state",
    name: "状态模式 (State)",
    description: "允许对象在内部状态改变时改变它的行为",
    category: "行为型模式",
    component: StateDemo,
  },
  {
    id: "command",
    name: "命令模式 (Command)",
    description: "将请求封装为对象，支持撤销和重做操作",
    category: "行为型模式",
    component: CommandDemo,
  },
  {
    id: "chainOfResponsibility",
    name: "责任链模式 (Chain of Responsibility)",
    description: "沿着处理者链传递请求，直到某个处理者处理它为止",
    category: "行为型模式",
    component: ChainOfResponsibilityDemo,
  },
  {
    id: "iterator",
    name: "迭代器模式 (Iterator)",
    description: "提供访问聚合对象的统一方法",
    category: "行为型模式",
    component: IteratorDemo,
  },
  {
    id: "templateMethod",
    name: "模板方法模式 (Template Method)",
    description: "定义算法骨架，子类实现细节",
    category: "行为型模式",
    component: TemplateMethodDemo,
  },
  {
    id: "memento",
    name: "备忘录模式 (Memento)",
    description: "保存和恢复对象状态",
    category: "行为型模式",
    component: MementoDemo,
  },
  {
    id: "mediator",
    name: "中介者模式 (Mediator)",
    description: "定义对象间的交互方式",
    category: "行为型模式",
    component: MediatorDemo,
  },
  {
    id: "interpreter",
    name: "解释器模式 (Interpreter)",
    description: "定义语言的文法表示",
    category: "行为型模式",
    component: InterpreterDemo,
  },
  {
    id: "visitor",
    name: "访问者模式 (Visitor)",
    description: "在不修改类的前提下定义新操作",
    category: "行为型模式",
    component: VisitorDemo,
  },
];

const activePattern = ref<string>("singleton");

const setActivePattern = (patternId: string) => {
  activePattern.value = patternId;
};

const getCurrentPattern = () => {
  return patterns.find((p) => p.id === activePattern.value) || patterns[0];
};

const getCurrentCode = computed(() => {
  return patternCodes[activePattern.value] || patternCodes.singleton;
});
</script>

<template>
  <div class="classic-patterns">
    <header class="page-header">
      <div class="breadcrumb">
        <router-link to="/" class="breadcrumb-link">首页</router-link>
        <span class="breadcrumb-separator">></span>
        <span class="breadcrumb-current">经典GoF模式</span>
      </div>
      <div class="header-content">
        <h1 class="page-title">
          📚 经典GoF设计模式
          <span class="page-subtitle">23个经典模式，软件设计的基础</span>
        </h1>
      </div>
    </header>

    <div class="page-content">
      <nav class="pattern-nav">
        <h2 class="nav-title">设计模式列表</h2>
        <div class="pattern-categories">
          <div
            v-for="category in ['创建型模式', '结构型模式', '行为型模式']"
            :key="category"
            class="category"
          >
            <h3 class="category-title">{{ category }}</h3>
            <ul class="pattern-list">
              <li
                v-for="pattern in patterns.filter(
                  (p) => p.category === category
                )"
                :key="pattern.id"
                class="pattern-item"
                :class="{ active: activePattern === pattern.id }"
                @click="setActivePattern(pattern.id)"
              >
                <span class="pattern-name">{{ pattern.name }}</span>
                <span class="pattern-desc">{{ pattern.description }}</span>
              </li>
            </ul>
          </div>
        </div>
      </nav>

      <main class="pattern-demo">
        <div class="demo-header">
          <h2 class="demo-title">{{ getCurrentPattern().name }}</h2>
          <span class="demo-category">{{ getCurrentPattern().category }}</span>
        </div>
        <div class="demo-content">
          <component :is="getCurrentPattern().component" />
        </div>
      </main>

      <aside class="code-panel">
        <CodeDisplay
          :title="getCurrentCode.title"
          :code="getCurrentCode.code"
        />
      </aside>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.classic-patterns {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  position: relative;
  padding: 0.8rem 2rem;
  color: white;
  flex-shrink: 0;

  .breadcrumb {
    position: absolute;
    top: 0.5rem;
    left: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    opacity: 0.9;

    @media (max-width: 768px) {
      position: static;
      justify-content: center;
      margin-bottom: 0.5rem;
    }

    .breadcrumb-link {
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        color: white;
      }
    }

    .breadcrumb-separator {
      opacity: 0.6;
    }

    .breadcrumb-current {
      color: #ffd700;
      font-weight: 500;
    }
  }

  .header-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;

    .page-title {
      font-size: 2rem;
      margin: 0;
      font-weight: 700;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.2rem;

      .page-subtitle {
        font-size: 0.9rem;
        font-weight: 400;
        opacity: 0.9;
        margin: 0;
      }
    }
  }
}

.page-content {
  display: grid;
  grid-template-columns: 280px 1fr 800px;
  gap: 2rem;
  max-width: none;
  margin: 0 auto;
  padding: 0 50px 50px 50px;
  width: 100%;
  flex: 1;
  overflow: hidden;

  @media (max-width: 1800px) {
    grid-template-columns: 260px 1fr 750px;
    gap: 1.8rem;
  }

  @media (max-width: 1600px) {
    grid-template-columns: 260px 1fr 700px;
    gap: 1.8rem;
  }

  @media (max-width: 1400px) {
    grid-template-columns: 260px 1fr 600px;
    gap: 1.5rem;
  }

  @media (max-width: 1200px) {
    grid-template-columns: 260px 1fr;
    gap: 1.5rem;

    .code-panel {
      display: none;
    }
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 1rem 1rem 1rem;
  }
}

.pattern-nav {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  min-height: 0;

  .nav-title {
    margin: 0 0 1.5rem 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
    flex-shrink: 0;
  }
}

.category {
  margin-bottom: 1.5rem;

  .category-title {
    font-size: 1rem;
    color: #666;
    margin: 0 0 0.8rem 0;
    font-weight: 500;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.5rem;
  }
}

.pattern-list {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  overflow-y: auto;
}

.pattern-item {
  padding: 0.8rem;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    background: #f8f9ff;
    border-color: #e0e7ff;
  }

  &.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;

    .pattern-desc {
      color: rgba(255, 255, 255, 0.9);
    }
  }

  .pattern-name {
    display: block;
    font-weight: 600;
    margin-bottom: 0.3rem;
  }

  .pattern-desc {
    display: block;
    font-size: 0.85rem;
    color: #666;
    line-height: 1.4;
  }
}

.pattern-demo {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;

  .demo-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1rem 2rem;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .demo-title {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 600;
    }

    .demo-category {
      background: rgba(255, 255, 255, 0.2);
      padding: 0.3rem 0.8rem;
      border-radius: 20px;
      font-size: 0.85rem;
      font-weight: 500;
    }
  }

  .demo-content {
    padding: 2rem;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
  }
}

.code-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}
</style>
