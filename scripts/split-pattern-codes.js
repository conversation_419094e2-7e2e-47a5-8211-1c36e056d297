/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-26 23:19:38
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-27 00:15:48
 * @FilePath     : /scripts/split-pattern-codes.js
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-26 23:19:38
 */
import fs from "fs";
import path from "path";

// 读取原始文件
const originalFile = fs.readFileSync("src/data/patternCodes.ts", "utf8");

// 模式分类
const patternCategories = {
  creational: [
    "singleton",
    "factory",
    "abstractFactory",
    "builder",
    "prototype",
  ],
  structural: [
    "adapter",
    "bridge",
    "composite",
    "decorator",
    "facade",
    "flyweight",
    "proxy",
  ],
  behavioral: [
    "chainOfResponsibility",
    "command",
    "observer",
    "state",
    "strategy",
  ],
};

// 提取模式代码的正则表达式
function extractPatternCode(content, patternId) {
  const regex = new RegExp(
    `${patternId}:\\s*{[\\s\\S]*?(?=\\n\\s*\\w+:\\s*{|\\n\\s*}\\s*$)`,
    "g"
  );
  const match = content.match(regex);
  if (match) {
    return match[0];
  }
  return null;
}

// 为每个分类创建文件
Object.entries(patternCategories).forEach(([category, patterns]) => {
  patterns.forEach((patternId) => {
    const patternCode = extractPatternCode(originalFile, patternId);
    if (patternCode) {
      // 解析模式代码
      const codeMatch = patternCode.match(/code:\s*\`([\s\S]*?)\`\s*$/);
      const titleMatch = patternCode.match(/title:\s*'([^']*?)'/);

      if (codeMatch && titleMatch) {
        const code = codeMatch[1];
        const title = titleMatch[1];

        const fileContent = `import type { PatternCode } from '../types'

export const ${patternId}: PatternCode = {
  id: '${patternId}',
  title: '${title}',
  code: \`${code}\`
}`;

        // 写入文件
        const filePath = `src/data/patternCodes/${category}/${patternId}.ts`;
        fs.writeFileSync(filePath, fileContent);
        console.log(`Created: ${filePath}`);
      }
    }
  });
});

console.log("Pattern codes split completed!");
