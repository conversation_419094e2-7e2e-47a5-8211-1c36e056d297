{"name": "design-pattern-demo", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.5.17", "vue-router": "4"}, "devDependencies": {"@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.0", "sass": "^1.89.2", "typescript": "^5.8.3", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^3.0.4"}}