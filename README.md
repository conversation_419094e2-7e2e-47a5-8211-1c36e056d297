# 🎨 设计模式学习平台

一个基于 Vue 3 + TypeScript + SCSS 的交互式设计模式学习应用，通过实际代码示例和可视化演示帮助开发者理解和掌握常用的设计模式。

## ✨ 特性

- 🎯 **交互式学习** - 通过实际操作理解设计模式
- 💻 **TypeScript 实现** - 完整的 TypeScript 代码示例
- 🎨 **美观的 UI** - 使用 SCSS 打造的现代化界面
- 📱 **响应式设计** - 支持桌面和移动设备
- 🔍 **详细说明** - 每个模式都包含使用场景、优缺点分析

## 🏗️ 包含的设计模式

### 创建型模式

- **单例模式 (Singleton)** - 日志记录器示例
- **工厂模式 (Factory)** - 支付处理器示例

### 结构型模式

- **装饰器模式 (Decorator)** - 咖啡定制系统示例

### 行为型模式

- **观察者模式 (Observer)** - 股票价格监控示例
- **策略模式 (Strategy)** - 电商折扣计算示例

## 🚀 快速开始

### 环境要求

- Node.js 16+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 🛠️ 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **SCSS** - CSS 预处理器
- **Vite** - 下一代前端构建工具

## 📚 学习指南

1. **选择模式** - 在左侧导航中选择要学习的设计模式
2. **阅读说明** - 了解模式的定义、使用场景和优缺点
3. **查看代码** - 学习 TypeScript 实现代码
4. **交互演示** - 通过实际操作理解模式的工作原理
5. **实践应用** - 在自己的项目中应用所学的模式

## 🎯 设计模式详解

### 单例模式

确保一个类只有一个实例，并提供全局访问点。适用于日志记录、配置管理、数据库连接池等场景。

### 工厂模式

提供创建对象的最佳方式，无需向客户端暴露创建逻辑。适用于数据库连接器、UI 组件库、支付处理器等场景。

### 观察者模式

定义对象间的一对多依赖关系，当一个对象状态改变时，所有依赖对象都会得到通知。适用于事件处理、MVC 架构、消息队列等场景。

### 策略模式

定义算法族，分别封装起来，让它们之间可以互相替换。适用于支付方式选择、排序算法、折扣计算等场景。

### 装饰器模式

动态地给对象添加新功能，同时又不改变其结构。适用于咖啡店点单、文本编辑器、图形界面组件等场景。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

MIT License
